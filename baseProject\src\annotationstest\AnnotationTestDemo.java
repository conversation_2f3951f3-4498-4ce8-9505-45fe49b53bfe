package annotationstest;

import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.util.regex.Pattern;

/**
 * 注解测试演示类
 * 展示自定义注解的定义和使用
 */
public class AnnotationTestDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 注解测试演示 ===");
        
        try {
            // 创建用户对象
            User user = new User();
            user.setUsername(""); // 空用户名，应该验证失败
            user.setEmail("invalid-email"); // 无效邮箱
            user.setPhone("123"); // 无效手机号
            
            // 验证用户对象
            ValidationResult result = AnnotationValidator.validate(user);
            
            System.out.println("验证结果: " + (result.isValid() ? "通过" : "失败"));
            if (!result.isValid()) {
                System.out.println("验证错误:");
                for (String error : result.getErrors()) {
                    System.out.println("  " + error);
                }
            }
            
            // 创建有效的用户对象
            System.out.println("\n=== 测试有效数据 ===");
            User validUser = new User();
            validUser.setUsername("alice");
            validUser.setEmail("<EMAIL>");
            validUser.setPhone("13800138000");
            
            ValidationResult validResult = AnnotationValidator.validate(validUser);
            System.out.println("验证结果: " + (validResult.isValid() ? "通过" : "失败"));
            
        } catch (Exception e) {
            System.out.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 自定义验证注解
     */
    @Target(ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Validate {
        
        ValidationType type() default ValidationType.NOT_NULL;
        
        String message() default "验证失败";
        
        boolean required() default true;
        
        int minLength() default 0;
        
        int maxLength() default Integer.MAX_VALUE;
        
        String pattern() default "";
    }
    
    /**
     * 验证类型枚举
     */
    public enum ValidationType {
        NOT_NULL, EMAIL, PHONE, ID_CARD, CUSTOM
    }
    
    /**
     * 用户实体类
     */
    public static class User {
        
        @Validate(type = ValidationType.NOT_NULL, message = "用户名不能为空")
        private String username;
        
        @Validate(type = ValidationType.EMAIL, message = "邮箱格式不正确")
        private String email;
        
        @Validate(type = ValidationType.PHONE, message = "手机号格式不正确")
        private String phone;
        
        @Validate(type = ValidationType.CUSTOM, pattern = "^[1-9]\\d{17}$", message = "身份证号格式不正确", required = false)
        private String idCard;
        
        // 构造函数
        public User() {}
        
        public User(String username, String email, String phone) {
            this.username = username;
            this.email = email;
            this.phone = phone;
        }
        
        // Getter和Setter方法
        public String getUsername() {
            return username;
        }
        
        public void setUsername(String username) {
            this.username = username;
        }
        
        public String getEmail() {
            return email;
        }
        
        public void setEmail(String email) {
            this.email = email;
        }
        
        public String getPhone() {
            return phone;
        }
        
        public void setPhone(String phone) {
            this.phone = phone;
        }
        
        public String getIdCard() {
            return idCard;
        }
        
        public void setIdCard(String idCard) {
            this.idCard = idCard;
        }
        
        @Override
        public String toString() {
            return "User{" +
                    "username='" + username + '\'' +
                    ", email='" + email + '\'' +
                    ", phone='" + phone + '\'' +
                    ", idCard='" + idCard + '\'' +
                    '}';
        }
    }
}
