server:
  port: 8080

spring:
  application:
    name: mysql-demo
  
  # MySQL数据库配置
  datasource:
    url: ************************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
  
  # 日志配置
logging:
  level:
    com.example.mysql: DEBUG
    org.springframework.web: DEBUG
