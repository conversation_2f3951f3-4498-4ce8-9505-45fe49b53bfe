package examples;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * MyBatis操作示例
 * 展示MyBatis的注解方式和XML方式
 */

// 1. 注解方式的Mapper接口
@Repository
@Mapper
public interface UserMapper {
    
    // 查询所有用户 - 注解方式
    @Select("SELECT id, username, email, created_at FROM users")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "email", column = "email"),
        @Result(property = "createdAt", column = "created_at")
    })
    List<User> findAllUsers();
    
    // 根据ID查询用户 - 注解方式
    @Select("SELECT id, username, email, created_at FROM users WHERE id = #{id}")
    User findUserById(@Param("id") Long id);
    
    // 插入用户 - 注解方式
    @Insert("INSERT INTO users (username, email, created_at) VALUES (#{username}, #{email}, #{createdAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUser(User user);
    
    // 更新用户 - 注解方式
    @Update("UPDATE users SET username = #{username}, email = #{email} WHERE id = #{id}")
    int updateUser(User user);
    
    // 删除用户 - 注解方式
    @Delete("DELETE FROM users WHERE id = #{id}")
    int deleteUser(@Param("id") Long id);
    
    // 根据用户名查询 - 注解方式
    @Select("SELECT * FROM users WHERE username LIKE CONCAT('%', #{keyword}, '%')")
    List<User> findUsersByUsername(@Param("keyword") String keyword);
    
    // 动态查询 - 注解方式（复杂查询建议使用XML）
    @SelectProvider(type = UserSqlProvider.class, method = "findUsersWithConditions")
    List<User> findUsersWithConditions(@Param("username") String username, 
                                     @Param("email") String email);
}

// SQL提供者类 - 用于动态SQL
class UserSqlProvider {
    
    public String findUsersWithConditions(@Param("username") String username, 
                                        @Param("email") String email) {
        StringBuilder sql = new StringBuilder("SELECT * FROM users WHERE 1=1");
        
        if (username != null && !username.isEmpty()) {
            sql.append(" AND username LIKE CONCAT('%', #{username}, '%')");
        }
        
        if (email != null && !email.isEmpty()) {
            sql.append(" AND email LIKE CONCAT('%', #{email}, '%')");
        }
        
        return sql.toString();
    }
}

// Service层使用示例
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    // 使用MyBatis - 代码非常简洁
    public List<User> getAllUsers() {
        return userMapper.findAllUsers();  // 一行代码完成查询
    }
    
    public User getUserById(Long id) {
        return userMapper.findUserById(id);  // 一行代码完成查询
    }
    
    public boolean saveUser(User user) {
        int result = userMapper.insertUser(user);
        return result > 0;
    }
    
    public List<User> searchUsers(String username, String email) {
        return userMapper.findUsersWithConditions(username, email);
    }
}
