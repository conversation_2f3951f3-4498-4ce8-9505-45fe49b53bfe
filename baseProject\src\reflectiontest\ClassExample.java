package reflectiontest;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;

public class ClassExample {

    public static void demonstrateClassAccess() {
        // 方式1：通过类名.class
        Class<String> clazz1 = String.class;

        // 方式2：通过对象.getClass()
        String str = "Hello";
        Class<?> clazz2 = str.getClass();

        // 方式3：通过Class.forName()
        try {
            Class<?> clazz3 = Class.forName("java.lang.String");
            Class<?> clazz4 = Class.forName("com.example.User");
        } catch (ClassNotFoundException e) {
            System.out.println("类未找到: " + e.getMessage());
        }

        // 验证是否为同一个Class对象
        System.out.println(clazz1 == clazz2); // true

        // 基本类型的Class对象
        Class<Integer> intClass = int.class;
        Class<Integer> integerClass = Integer.class;
        Class<Integer> intTypeClass = Integer.TYPE;

        System.out.println(intClass == intTypeClass); // true
        System.out.println(intClass == integerClass); // false
    }
    public static void main(String[] args) {
        demonstrateClassAccess();
        try {
            demonstrateClassMethods();
        } catch (Exception e) {
            System.out.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    public static void demonstrateClassMethods() throws  Exception {

        Class<String> clazz  = String.class;
        System.out.println("简单类名: " + clazz.getSimpleName()); // String
        System.out.println("完整类名: " + clazz.getName()); // java.lang.String
        System.out.println("规范类名: " + clazz.getCanonicalName()); // java.lang.String

        // 获取包信息
        Package pkg = clazz.getPackage();
        System.out.println("包名: " + pkg.getName()); // java.lang

        // 获取父类
        Class<?> superClass = clazz.getSuperclass();
        System.out.println("父类: " + superClass.getName()); // java.lang.Object

        // 获取接口
        Class<?>[] interfaces = clazz.getInterfaces();
        System.out.println("实现的接口数量: " + interfaces.length);
        for (Class<?> intf : interfaces) {
            System.out.println("接口: " + intf.getName());
        }
        System.out.println("是否为接口: " + clazz.isInterface());
        System.out.println("是否为数组: " + clazz.isArray());
        System.out.println("是否为枚举: " + clazz.isEnum());
        System.out.println("是否为注解: " + clazz.isAnnotation());
        System.out.println("是否为基本类型: " + clazz.isPrimitive());

        // 修饰符检查
        int modifiers = clazz.getModifiers();
        System.out.println("是否为public: " + Modifier.isPublic(modifiers));
        System.out.println("是否为final: " + Modifier.isFinal(modifiers));
        System.out.println("是否为abstract: " + Modifier.isAbstract(modifiers));

        // 创建实例
//        Object instance = clazz.newInstance(); // 已废弃，使用Constructor.newInstance()
        try {
            // String类有无参构造函数
            Constructor<String> declaredConstructor = clazz.getDeclaredConstructor();
            Object instance = declaredConstructor.newInstance();
            System.out.println("创建的实例: '" + instance + "'");
        } catch (NoSuchMethodException e) {
            System.out.println("String类没有无参构造函数，使用有参构造函数");
            try {
                // 使用char[]参数的构造函数
                Constructor<String> charArrayConstructor = clazz.getDeclaredConstructor(char[].class);
                Object instance = charArrayConstructor.newInstance(new char[]{'H', 'e', 'l', 'l', 'o'});
                System.out.println("创建的实例: '" + instance + "'");
            } catch (Exception ex) {
                System.out.println("创建实例失败: " + ex.getMessage());
            }
        } catch (Exception e) {
            System.out.println("创建实例时发生异常: " + e.getMessage());
        }
    }
}