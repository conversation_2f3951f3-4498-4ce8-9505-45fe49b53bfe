# MyBatis???????????

# ===== ??????? =====
database.driver=com.mysql.cj.jdbc.Driver
database.url=******************************************************************************************************************************************************************************
database.username=root
database.password=123456

# ===== ????????? =====
database.test.url=*****************************************************************************************************************************************************
database.test.username=test_user
database.test.password=test_password

# ===== ????????? =====
database.prod.url=********************************************************************************************************************
database.prod.username=prod_user
database.prod.password=prod_password

# ===== ????? =====
# ?????
database.pool.initialSize=5
# ???????
database.pool.minIdle=5
# ???????
database.pool.maxActive=20
# ??????(??)
database.pool.maxWait=60000
# ???????????????????????????????
database.pool.timeBetweenEvictionRunsMillis=60000
# ??????????????????????
database.pool.minEvictableIdleTimeMillis=300000
# ???????????sql
database.pool.validationQuery=SELECT 1
# ?????true??????????????
database.pool.testWhileIdle=true
# ???????validationQuery????????
database.pool.testOnBorrow=false
# ???????validationQuery????????
database.pool.testOnReturn=false
# ????preparedStatement????PSCache
database.pool.poolPreparedStatements=true
# ???PSCache???????0????0??poolPreparedStatements???????true
database.pool.maxPoolPreparedStatementPerConnectionSize=20

# ===== Druid???? =====
# ?????????filters????????sql????
database.pool.filters=stat,wall,slf4j
# ??connectProperties?????mergeSql????SQL??
database.pool.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

# ===== ????????? =====
# ????????
database.connection.timeout=30000
# ??socket????
database.connection.socketTimeout=60000
# ??????
database.connection.autoReconnect=true
# ??????
database.connection.maxReconnects=3
# ??????
database.connection.initialTimeout=2
# ????????
database.connection.characterEncoding=utf8
# ???????
database.connection.serverTimezone=GMT+8
# ????Unicode
database.connection.useUnicode=true
# ????SSL
database.connection.useSSL=false
# ????????
database.connection.allowPublicKeyRetrieval=true
# ????????
database.connection.rewriteBatchedStatements=true
# ?????????????
database.connection.useServerPrepStmts=true
# ???????
database.connection.cachePrepStmts=true
# ?????????
database.connection.prepStmtCacheSize=250
# ???????SQL??
database.connection.prepStmtCacheSqlLimit=2048
# ??????????
database.connection.useLocalSessionState=true
# ??????????
database.connection.useLocalTransactionState=true
# ??????????
database.connection.cacheResultSetMetadata=true
# ?????????
database.connection.cacheServerConfiguration=true
# ????????
database.connection.enableQueryTimeouts=true
# ????????
database.connection.tcpKeepAlive=true

# ===== ???? =====
# ????SQL??
database.log.sql.enabled=true
# SQL????
database.log.sql.level=DEBUG
# ????SQL??
database.log.sql.showParameters=true
# ????SQL????
database.log.sql.showExecutionTime=true
# ?SQL??(??)
database.log.sql.slowSqlThreshold=1000

# ===== ???? =====
# ????????
database.cache.enabled=true
# ????
database.cache.type=ehcache
# ??????(?)
database.cache.expireTime=3600
# ??????
database.cache.maxSize=1000

# ===== ???? =====
# ??????
database.page.dialect=mysql
# ?????????
database.page.reasonable=true
# ??????????
database.page.supportMethodsArguments=true
# ????
database.page.params=count=countSql

# ===== ???? =====
# ????????
database.performance.defaultFetchSize=100
# ????????(?)
database.performance.defaultStatementTimeout=30
# ????????
database.performance.lazyLoadingEnabled=true
# ???????????
database.performance.aggressiveLazyLoading=false
# ???????
database.performance.localCacheScope=SESSION

# ===== ???? =====
# ????SQL???
database.security.wallEnabled=true
# ???????
database.security.multiStatementAllow=false
# ??????????
database.security.noneBaseStatementAllow=false
# ?????????
database.security.tableCheck=true

# ===== ???????? =====
# ?????????SQL
dev.showSql=true
# ??????????SQL
dev.formatSql=true
# ?????????SQL??
dev.showComments=true

# ===== ???????? =====
# ??????????
prod.monitorEnabled=true
# ?????????
prod.monitor.username=admin
# ????????
prod.monitor.password=admin123
# ????????????
prod.monitor.resetEnable=false

# ===== ???? =====
# ????????
backup.enabled=false
# ????(cron???)
backup.cron=0 0 2 * * ?
# ??????
backup.retentionDays=30
# ??????
backup.path=/data/backup/mysql

# ===== ???? =====
# ????????
cluster.readWriteSplit.enabled=false
# ????
cluster.master.url=********************************************
cluster.master.username=master_user
cluster.master.password=master_password
# ????
cluster.slave.url=*******************************************
cluster.slave.username=slave_user
cluster.slave.password=slave_password

# ===== ???? =====
# ????
app.name=MyBatis Demo
# ????
app.version=1.0.0
# ????
app.environment=development
