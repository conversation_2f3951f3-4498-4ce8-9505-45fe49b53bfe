import java.awt.*;

//TIP 要<b>运行</b>代码，请按 <shortcut actionId="Run"/> 或
// 点击装订区域中的 <icon src="AllIcons.Actions.Execute"/> 图标。
public class Main {
    public static void main(String[] args) {
        //TIP 当文本光标位于高亮显示的文本处时按 <shortcut actionId="ShowIntentionActions"/>
        // 查看 IntelliJ IDEA 建议如何修正。
        System.out.printf("Hello and welcome!");

        for (int i = 1; i <= 5; i++) {
            //TIP 按 <shortcut actionId="Debug"/> 开始调试代码。我们已经设置了一个 <icon src="AllIcons.Debugger.Db_set_breakpoint"/> 断点
            // 但您始终可以通过按 <shortcut actionId="ToggleLineBreakpoint"/> 添加更多断点。
            System.out.println("i = " + i);
        }
        testbasetype();
        fortest();
    }

    public static void  testbasetype() {
        // 基本数据类型
        byte byteVar = 127;                    // 8位，范围：-128到127
        short shortVar = 32767;                // 16位，范围：-32768到32767
        int intVar = 2147483647;               // 32位，范围：-2^31到2^31-1
        long longVar = 9223372036854775807L;   // 64位，需要L后缀

        float floatVar = 3.14f;                // 32位浮点数，需要f后缀
        double doubleVar = 3.141592653589793;  // 64位浮点数

        char charVar = 'A';                    // 16位Unicode字符
        boolean boolVar = true;                // 布尔值：true或false

        // 引用数据类型
        String stringVar = "Hello, Java!";    // 字符串

        // 输出变量值
        System.out.println("byte: " + byteVar);
        System.out.println("short: " + shortVar);
        System.out.println("int: " + intVar);
        System.out.println("long: " + longVar);
        System.out.println("float: " + floatVar);
        System.out.println("double: " + doubleVar);
        System.out.println("char: " + charVar);
        System.out.println("boolean: " + boolVar);
        System.out.println("String: " + stringVar);
    }
    public static  void fortest(){
        int i =85;
        if(i>90){
            System.out.println("A");
        }else if(i>80){
            System.out.println("B");
        }else if(i>70){
            System.out.println("C");
        }else if(i>60){
            System.out.println("D");
        }else{
            System.out.println("E");
        }
        int dayweek =5;
        switch (dayweek){
            case  1:
                System.out.println("星期1");
                break;
            case  2:
                System.out.println("星期2");
                break;
            case  3:
                System.out.println("星期3");
                break;
            case  4:
                System.out.println("星期4");
                break;
            case  5:
                System.out.println("星期5");
                break;
            case  6:
                System.out.println("星期6");
                break;
            case  7:
                System.out.println("星期7");
        }
        int sum =0;
        for(int j=1;j<=100;j++){
            sum +=j;
        }
        System.out.println("1到100的和为："+sum);

        int sum1=0;
        while(sum1<=100){
            sum1 +=sum1;
        }
        System.out.println("1到100的和为："+sum1);

        int sum2=0;
        int k=1;
        do{
            sum2 +=k;
            k++;
        }while(k<=100);
        System.out.println("1到100的和为："+sum2);

    }}