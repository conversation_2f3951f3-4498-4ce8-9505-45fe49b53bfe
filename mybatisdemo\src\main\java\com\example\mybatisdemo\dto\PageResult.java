package com.example.mybatisdemo.dto;

import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 通用分页结果类
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageResult<T> {

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 是否有下一页
     */
    private Boolean hasNextPage;

    /**
     * 是否有上一页
     */
    private Boolean hasPreviousPage;

    /**
     * 是否为第一页
     */
    private Boolean isFirstPage;

    /**
     * 是否为最后一页
     */
    private Boolean isLastPage;

    /**
     * 导航页码数组
     */
    private int[] navigatepageNums;

    /**
     * 从PageInfo构造分页结果
     */
    public static <T> PageResult<T> of(PageInfo<T> pageInfo) {
        return PageResult.<T>builder()
                .records(pageInfo.getList())
                .total(pageInfo.getTotal())
                .pageNum(pageInfo.getPageNum())
                .pageSize(pageInfo.getPageSize())
                .pages(pageInfo.getPages())
                .hasNextPage(pageInfo.isHasNextPage())
                .hasPreviousPage(pageInfo.isHasPreviousPage())
                .isFirstPage(pageInfo.isIsFirstPage())
                .isLastPage(pageInfo.isIsLastPage())
                .navigatepageNums(pageInfo.getNavigatepageNums())
                .build();
    }

    /**
     * 从PageInfo构造分页结果（支持数据转换）
     */
    public static <T, R> PageResult<R> of(PageInfo<T> pageInfo, List<R> convertedRecords) {
        return PageResult.<R>builder()
                .records(convertedRecords)
                .total(pageInfo.getTotal())
                .pageNum(pageInfo.getPageNum())
                .pageSize(pageInfo.getPageSize())
                .pages(pageInfo.getPages())
                .hasNextPage(pageInfo.isHasNextPage())
                .hasPreviousPage(pageInfo.isHasPreviousPage())
                .isFirstPage(pageInfo.isIsFirstPage())
                .isLastPage(pageInfo.isIsLastPage())
                .navigatepageNums(pageInfo.getNavigatepageNums())
                .build();
    }

    /**
     * 空分页结果
     */
    public static <T> PageResult<T> empty() {
        return PageResult.<T>builder()
                .records(List.of())
                .total(0L)
                .pageNum(1)
                .pageSize(10)
                .pages(0)
                .hasNextPage(false)
                .hasPreviousPage(false)
                .isFirstPage(true)
                .isLastPage(true)
                .navigatepageNums(new int[]{})
                .build();
    }
}
