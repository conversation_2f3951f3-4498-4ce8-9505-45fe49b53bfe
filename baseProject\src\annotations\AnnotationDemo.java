package annotations;

import annotations.CustomAnnotations.*;
import java.lang.annotation.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Java注解演示程序
 * 展示注解的定义、使用和处理
 */
public class AnnotationDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java注解演示 ===");
        
        // 1. 内置注解演示
        builtInAnnotationDemo();
        
        // 2. 自定义注解演示
        customAnnotationDemo();
        
        // 3. 注解处理器演示
        annotationProcessorDemo();
        
        // 4. 反射读取注解演示
        reflectionAnnotationDemo();
    }
    
    /**
     * 内置注解演示
     */
    public static void builtInAnnotationDemo() {
        System.out.println("\n--- 内置注解演示 ---");
        
        // @Override演示
        Animal dog = new Dog();
        dog.makeSound();
        
        // @Deprecated演示
        Calculator calc = new Calculator();
        int result1 = calc.add(5, 3);
        int result2 = calc.oldAdd(5, 3); // 会有警告
        System.out.println("新方法结果: " + result1);
        System.out.println("旧方法结果: " + result2);
        
        // @FunctionalInterface演示
        MathOperation addition = (a, b) -> a + b;
        MathOperation multiplication = (a, b) -> a * b;
        System.out.println("加法: " + addition.operate(10, 5));
        System.out.println("乘法: " + multiplication.operate(10, 5));
    }
    
    /**
     * 自定义注解演示
     */
    public static void customAnnotationDemo() {
        System.out.println("\n--- 自定义注解演示 ---");
        
        // 创建用户对象
        User user1 = new User("john", "invalid-email", "12345", "123", "J");
        User user2 = new User("alice", "<EMAIL>", "13800138000", "password123", "Alice Smith");
        
        // 验证用户1
        System.out.println("验证用户1:");
        ValidationResult result1 = validateUser(user1);
        if (result1.isValid()) {
            System.out.println("✅ 验证通过");
        } else {
            System.out.println("❌ 验证失败:");
            result1.getErrors().forEach((field, error) -> 
                System.out.println("  " + field + ": " + error));
        }
        
        // 验证用户2
        System.out.println("\n验证用户2:");
        ValidationResult result2 = validateUser(user2);
        if (result2.isValid()) {
            System.out.println("✅ 验证通过");
        } else {
            System.out.println("❌ 验证失败:");
            result2.getErrors().forEach((field, error) -> 
                System.out.println("  " + field + ": " + error));
        }
    }
    
    /**
     * 注解处理器演示
     */
    public static void annotationProcessorDemo() {
        System.out.println("\n--- 注解处理器演示 ---");
        
        // 创建产品对象
        Product product = new Product();
        product.setName("iPhone 15");
        product.setPrice(7999.0);
        product.setCategory("电子产品");
        product.setDescription("最新款iPhone");
        
        // 处理注解
        processAnnotations(product);
    }
    
    /**
     * 反射读取注解演示
     */
    public static void reflectionAnnotationDemo() {
        System.out.println("\n--- 反射读取注解演示 ---");
        
        Class<User> userClass = User.class;
        
        // 读取类注解
        if (userClass.isAnnotationPresent(Entity.class)) {
            Entity entity = userClass.getAnnotation(Entity.class);
            System.out.println("实体名称: " + entity.name());
            System.out.println("表名: " + entity.tableName());
        }
        
        // 读取字段注解
        Field[] fields = userClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Validate.class)) {
                Validate validate = field.getAnnotation(Validate.class);
                System.out.println("字段 " + field.getName() + " 验证规则:");
                System.out.println("  类型: " + validate.type());
                System.out.println("  消息: " + validate.message());
                System.out.println("  必填: " + validate.required());
            }
        }
        
        // 读取方法注解
        Method[] methods = userClass.getDeclaredMethods();
        for (Method method : methods) {
            if (method.isAnnotationPresent(Log.class)) {
                Log log = method.getAnnotation(Log.class);
                System.out.println("方法 " + method.getName() + " 日志配置:");
                System.out.println("  描述: " + log.value());
                System.out.println("  级别: " + log.level());
            }
        }
    }
    
    /**
     * 验证用户对象
     */
    private static ValidationResult validateUser(User user) {
        ValidationResult result = new ValidationResult();
        Class<?> clazz = user.getClass();
        
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Validate.class)) {
                Validate validate = field.getAnnotation(Validate.class);
                
                try {
                    field.setAccessible(true);
                    Object value = field.get(user);
                    
                    String error = validateField(field.getName(), value, validate);
                    if (error != null) {
                        result.addError(field.getName(), error);
                    }
                    
                } catch (IllegalAccessException e) {
                    result.addError(field.getName(), "无法访问字段");
                }
            }
        }
        
        return result;
    }
    
    /**
     * 验证单个字段
     */
    private static String validateField(String fieldName, Object value, Validate validate) {
        String stringValue = value == null ? null : value.toString();
        
        // 必填验证
        if (validate.required() && (value == null || stringValue.trim().isEmpty())) {
            return validate.message();
        }
        
        // 如果值为空且不是必填，跳过其他验证
        if (value == null || stringValue.trim().isEmpty()) {
            return null;
        }
        
        // 长度验证
        if (stringValue.length() < validate.minLength() || 
            stringValue.length() > validate.maxLength()) {
            return validate.message();
        }
        
        // 类型验证
        switch (validate.type()) {
            case EMAIL:
                if (!isValidEmail(stringValue)) {
                    return validate.message();
                }
                break;
            case PHONE:
                if (!isValidPhone(stringValue)) {
                    return validate.message();
                }
                break;
            case CUSTOM:
                if (!validate.pattern().isEmpty() && !Pattern.matches(validate.pattern(), stringValue)) {
                    return validate.message();
                }
                break;
        }
        
        return null;
    }
    
    /**
     * 处理注解
     */
    private static void processAnnotations(Object obj) {
        Class<?> clazz = obj.getClass();
        
        // 处理类注解
        if (clazz.isAnnotationPresent(Entity.class)) {
            Entity entity = clazz.getAnnotation(Entity.class);
            System.out.println("处理实体: " + entity.name());
        }
        
        // 处理字段注解
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Column.class)) {
                Column column = field.getAnnotation(Column.class);
                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    System.out.println("字段 " + column.name() + " = " + value);
                } catch (IllegalAccessException e) {
                    System.out.println("无法访问字段: " + field.getName());
                }
            }
        }
    }
    
    // 验证方法
    private static boolean isValidEmail(String email) {
        String emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(emailPattern, email);
    }
    
    private static boolean isValidPhone(String phone) {
        String phonePattern = "^1[3-9]\\d{9}$";
        return Pattern.matches(phonePattern, phone);
    }
}

// 动物类（演示@Override）
class Animal {
    public void makeSound() {
        System.out.println("动物发出声音");
    }
}

class Dog extends Animal {
    @Override
    public void makeSound() {
        System.out.println("🐕 汪汪汪");
    }
}

// 计算器类（演示@Deprecated）
class Calculator {
    
    public int add(int a, int b) {
        return a + b;
    }
    
    /**
     * @deprecated 使用 {@link #add(int, int)} 替代
     */
    @Deprecated
    @SuppressWarnings("unused")
    public int oldAdd(int a, int b) {
        return a + b;
    }
}

// 函数式接口（演示@FunctionalInterface）
@FunctionalInterface
interface MathOperation {
    int operate(int a, int b);
    
    default void printResult(int result) {
        System.out.println("结果: " + result);
    }
}

// 验证结果类
class ValidationResult {
    private boolean valid = true;
    private Map<String, String> errors = new HashMap<>();
    
    public void addError(String field, String message) {
        this.valid = false;
        this.errors.put(field, message);
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public Map<String, String> getErrors() {
        return errors;
    }
}
