-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS mybatisdemo 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE mybatisdemo;

-- 用户表
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入测试数据
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `real_name`, `status`) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOOxrd0UrRiO7levFjmndUn4YKnzqiQ6F.', '<EMAIL>', '13800138000', '管理员', 1),
('zhangsan', '$2a$10$7JB720yubVSOfvVWdBYoOOxrd0UrRiO7levFjmndUn4YKnzqiQ6F.', '<EMAIL>', '13800138001', '张三', 1),
('lisi', '$2a$10$7JB720yubVSOfvVWdBYoOOxrd0UrRiO7levFjmndUn4YKnzqiQ6F.', '<EMAIL>', '13800138002', '李四', 1),
('wangwu', '$2a$10$7JB720yubVSOfvVWdBYoOOxrd0UrRiO7levFjmndUn4YKnzqiQ6F.', '<EMAIL>', '13800138003', '王五', 0);

-- 文章表
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(200) NOT NULL COMMENT '文章标题',
  `content` text COMMENT '文章内容',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `category` varchar(50) DEFAULT NULL COMMENT '分类',
  `tags` varchar(200) DEFAULT NULL COMMENT '标签，逗号分隔',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-草稿，1-发布',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- 插入测试文章数据
INSERT INTO `article` (`title`, `content`, `author_id`, `category`, `tags`, `view_count`, `status`) VALUES
('Spring Boot入门教程', 'Spring Boot是一个基于Spring框架的快速开发框架...', 1, 'Java', 'Spring Boot,Java,教程', 100, 1),
('MyBatis使用指南', 'MyBatis是一个优秀的持久层框架...', 2, 'Java', 'MyBatis,数据库,Java', 80, 1),
('Vue.js前端开发', 'Vue.js是一个渐进式JavaScript框架...', 3, 'Frontend', 'Vue.js,JavaScript,前端', 120, 1),
('MySQL性能优化', '本文介绍MySQL数据库性能优化的方法...', 1, 'Database', 'MySQL,性能优化,数据库', 90, 0);
