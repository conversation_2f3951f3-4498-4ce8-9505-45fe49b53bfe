package com.example.controller;

import com.example.entity.User;
import com.example.service.CacheService;
import com.example.service.UserService;
import com.example.vo.ApiResponse;
import com.example.vo.UserDetailVO;
import com.example.vo.UserStatsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 缓存管理控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/cache")
public class CacheController {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheController.class);
    
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CacheManager cacheManager;
    
    /**
     * 获取用户详情（带缓存）
     */
    @GetMapping("/user/{id}/detail")
    public ApiResponse<UserDetailVO> getUserDetail(@PathVariable Long id) {
        try {
            UserDetailVO detail = cacheService.getUserDetail(id);
            return ApiResponse.success("获取用户详情成功", detail);
        } catch (Exception e) {
            logger.error("获取用户详情失败: {}", id, e);
            return ApiResponse.error("获取用户详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户角色（带缓存）
     */
    @GetMapping("/user/{id}/roles")
    public ApiResponse<List<String>> getUserRoles(@PathVariable Long id) {
        try {
            List<String> roles = cacheService.getUserRoles(id);
            return ApiResponse.success("获取用户角色成功", roles);
        } catch (Exception e) {
            logger.error("获取用户角色失败: {}", id, e);
            return ApiResponse.error("获取用户角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户权限（带缓存）
     */
    @GetMapping("/user/{id}/permissions")
    public ApiResponse<List<String>> getUserPermissions(@PathVariable Long id) {
        try {
            List<String> permissions = cacheService.getUserPermissions(id);
            return ApiResponse.success("获取用户权限成功", permissions);
        } catch (Exception e) {
            logger.error("获取用户权限失败: {}", id, e);
            return ApiResponse.error("获取用户权限失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户统计信息（带缓存）
     */
    @GetMapping("/user/stats")
    public ApiResponse<UserStatsVO> getUserStats() {
        try {
            UserStatsVO stats = cacheService.getUserStats();
            return ApiResponse.success("获取用户统计成功", stats);
        } catch (Exception e) {
            logger.error("获取用户统计失败", e);
            return ApiResponse.error("获取用户统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取活跃用户列表（带缓存）
     */
    @GetMapping("/user/active")
    public ApiResponse<List<User>> getActiveUsers() {
        try {
            List<User> users = cacheService.getActiveUsers();
            return ApiResponse.success("获取活跃用户成功", users);
        } catch (Exception e) {
            logger.error("获取活跃用户失败", e);
            return ApiResponse.error("获取活跃用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统配置（带缓存）
     */
    @GetMapping("/config/{key}")
    public ApiResponse<String> getSystemConfig(@PathVariable String key) {
        try {
            String value = cacheService.getSystemConfig(key);
            return ApiResponse.success("获取系统配置成功", value);
        } catch (Exception e) {
            logger.error("获取系统配置失败: {}", key, e);
            return ApiResponse.error("获取系统配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新系统配置（更新缓存）
     */
    @PutMapping("/config/{key}")
    public ApiResponse<Void> updateSystemConfig(@PathVariable String key, @RequestBody String value) {
        try {
            cacheService.updateSystemConfig(key, value);
            return ApiResponse.success("更新系统配置成功");
        } catch (Exception e) {
            logger.error("更新系统配置失败: {} = {}", key, value, e);
            return ApiResponse.error("更新系统配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量获取用户信息
     */
    @PostMapping("/user/batch")
    public ApiResponse<Map<Long, User>> batchGetUsers(@RequestBody List<Long> userIds) {
        try {
            Map<Long, User> users = cacheService.batchGetUsers(userIds);
            return ApiResponse.success("批量获取用户成功", users);
        } catch (Exception e) {
            logger.error("批量获取用户失败: {}", userIds, e);
            return ApiResponse.error("批量获取用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 预热用户缓存
     */
    @PostMapping("/user/warmup")
    public ApiResponse<Void> warmupUserCache(@RequestBody List<Long> userIds) {
        try {
            cacheService.warmupUserCache(userIds);
            return ApiResponse.success("缓存预热已启动");
        } catch (Exception e) {
            logger.error("缓存预热失败: {}", userIds, e);
            return ApiResponse.error("缓存预热失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除用户缓存
     */
    @DeleteMapping("/user/{id}")
    public ApiResponse<Void> evictUserCache(@PathVariable Long id) {
        try {
            cacheService.evictUserCache(id);
            return ApiResponse.success("用户缓存清除成功");
        } catch (Exception e) {
            logger.error("清除用户缓存失败: {}", id, e);
            return ApiResponse.error("清除用户缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除所有缓存
     */
    @DeleteMapping("/all")
    public ApiResponse<Void> evictAllCache() {
        try {
            cacheService.evictAllCache();
            return ApiResponse.success("所有缓存清除成功");
        } catch (Exception e) {
            logger.error("清除所有缓存失败", e);
            return ApiResponse.error("清除所有缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getCacheStatistics() {
        try {
            Map<String, Object> stats = cacheService.getCacheStatistics();
            return ApiResponse.success("获取缓存统计成功", stats);
        } catch (Exception e) {
            logger.error("获取缓存统计失败", e);
            return ApiResponse.error("获取缓存统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定缓存的内容
     */
    @GetMapping("/{cacheName}/{key}")
    public ApiResponse<Object> getCacheValue(@PathVariable String cacheName, @PathVariable String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    return ApiResponse.success("获取缓存值成功", wrapper.get());
                } else {
                    return ApiResponse.error("缓存值不存在");
                }
            } else {
                return ApiResponse.error("缓存不存在");
            }
        } catch (Exception e) {
            logger.error("获取缓存值失败: {}:{}", cacheName, key, e);
            return ApiResponse.error("获取缓存值失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除指定缓存
     */
    @DeleteMapping("/{cacheName}")
    public ApiResponse<Void> clearCache(@PathVariable String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                return ApiResponse.success("缓存清除成功");
            } else {
                return ApiResponse.error("缓存不存在");
            }
        } catch (Exception e) {
            logger.error("清除缓存失败: {}", cacheName, e);
            return ApiResponse.error("清除缓存失败: " + e.getMessage());
        }
    }
    
    /**
     * 清除指定缓存的特定key
     */
    @DeleteMapping("/{cacheName}/{key}")
    public ApiResponse<Void> evictCacheKey(@PathVariable String cacheName, @PathVariable String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
                return ApiResponse.success("缓存项清除成功");
            } else {
                return ApiResponse.error("缓存不存在");
            }
        } catch (Exception e) {
            logger.error("清除缓存项失败: {}:{}", cacheName, key, e);
            return ApiResponse.error("清除缓存项失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有缓存信息
     */
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> getCacheInfo() {
        try {
            Map<String, Object> info = new HashMap<>();
            info.put("cacheNames", cacheManager.getCacheNames());
            info.put("statistics", cacheService.getCacheStatistics());
            return ApiResponse.success("获取缓存信息成功", info);
        } catch (Exception e) {
            logger.error("获取缓存信息失败", e);
            return ApiResponse.error("获取缓存信息失败: " + e.getMessage());
        }
    }
}
