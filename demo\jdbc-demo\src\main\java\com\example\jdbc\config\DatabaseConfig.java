package com.example.jdbc.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 数据库配置类
 * 管理不同数据库的连接信息
 */
@Configuration
public class DatabaseConfig {
    
    // MySQL配置
    @Value("${mysql.jdbc.url}")
    private String mysqlUrl;
    
    @Value("${mysql.jdbc.username}")
    private String mysqlUsername;
    
    @Value("${mysql.jdbc.password}")
    private String mysqlPassword;
    
    @Value("${mysql.jdbc.driver}")
    private String mysqlDriver;
    
    // PostgreSQL配置
    @Value("${postgresql.jdbc.url}")
    private String postgresqlUrl;
    
    @Value("${postgresql.jdbc.username}")
    private String postgresqlUsername;
    
    @Value("${postgresql.jdbc.password}")
    private String postgresqlPassword;
    
    @Value("${postgresql.jdbc.driver}")
    private String postgresqlDriver;
    
    // H2配置
    @Value("${h2.jdbc.url}")
    private String h2Url;
    
    @Value("${h2.jdbc.username}")
    private String h2Username;
    
    @Value("${h2.jdbc.password}")
    private String h2Password;
    
    @Value("${h2.jdbc.driver}")
    private String h2Driver;
    
    // 当前数据库类型
    @Value("${database.type}")
    private String databaseType;
    
    /**
     * 根据配置获取当前数据库的连接信息
     */
    public DatabaseInfo getCurrentDatabaseInfo() {
        switch (databaseType.toLowerCase()) {
            case "mysql":
                return new DatabaseInfo(mysqlUrl, mysqlUsername, mysqlPassword, mysqlDriver);
            case "postgresql":
                return new DatabaseInfo(postgresqlUrl, postgresqlUsername, postgresqlPassword, postgresqlDriver);
            case "h2":
                return new DatabaseInfo(h2Url, h2Username, h2Password, h2Driver);
            default:
                throw new IllegalArgumentException("不支持的数据库类型: " + databaseType);
        }
    }
    
    /**
     * 数据库连接信息封装类
     */
    public static class DatabaseInfo {
        private final String url;
        private final String username;
        private final String password;
        private final String driver;
        
        public DatabaseInfo(String url, String username, String password, String driver) {
            this.url = url;
            this.username = username;
            this.password = password;
            this.driver = driver;
        }
        
        public String getUrl() { return url; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
        public String getDriver() { return driver; }
        
        @Override
        public String toString() {
            return String.format("DatabaseInfo{url='%s', username='%s', driver='%s'}", 
                               url, username, driver);
        }
    }
    
    // Getter方法
    public String getDatabaseType() { return databaseType; }
    public String getMysqlUrl() { return mysqlUrl; }
    public String getMysqlUsername() { return mysqlUsername; }
    public String getMysqlPassword() { return mysqlPassword; }
    public String getMysqlDriver() { return mysqlDriver; }
}
