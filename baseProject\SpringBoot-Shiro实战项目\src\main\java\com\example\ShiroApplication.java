package com.example;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Spring Boot + Shiro权限管理系统启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@MapperScan("com.example.mapper")
public class ShiroApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(ShiroApplication.class);
    
    public static void main(String[] args) {
        logger.info("正在启动Spring Boot + Shiro权限管理系统...");
        
        SpringApplication.run(ShiroApplication.class, args);
        
        logger.info("Spring Boot + Shiro权限管理系统启动完成！");
        logger.info("访问地址: http://localhost:8080");
        logger.info("Druid监控: http://localhost:8080/druid");
        logger.info("健康检查: http://localhost:8080/actuator/health");
        
        // 输出默认用户信息
        logger.info("=== 默认用户信息 ===");
        logger.info("管理员 - 用户名: admin, 密码: 123456");
        logger.info("经理 - 用户名: manager, 密码: 123456");
        logger.info("用户 - 用户名: user, 密码: 123456");
        logger.info("==================");
    }
}
