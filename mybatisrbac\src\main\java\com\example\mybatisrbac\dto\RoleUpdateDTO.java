package com.example.mybatisrbac.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
 * 角色更新请求对象
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "角色更新请求对象")
public class RoleUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    @Size(min = 2, max = 20, message = "角色名称长度必须在2到20之间")
    @Schema(description = "角色名称", example = "管理员")
    private String roleName;

    /**
     * 角色编码
     */
    @Size(min = 2, max = 20, message = "角色编码长度必须在2到20之间")
    @Pattern(regexp = "^[A-Z_]+$", message = "角色编码只能包含大写字母和下划线")
    @Schema(description = "角色编码", example = "ADMIN")
    private String roleCode;

    /**
     * 角色描述
     */
    @Size(max = 200, message = "角色描述长度不能超过200")
    @Schema(description = "角色描述", example = "系统管理员角色")
    private String description;

    /**
     * 角色状态：0-禁用，1-启用
     */
    @Min(value = 0, message = "角色状态只能为0或1")
    @Max(value = 1, message = "角色状态只能为0或1")
    @Schema(description = "角色状态：0-禁用，1-启用", example = "1")
    private Integer status;
}
