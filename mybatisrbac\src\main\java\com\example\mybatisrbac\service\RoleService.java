package com.example.mybatisrbac.service;

import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.dto.RoleQueryDTO;
import com.example.mybatisrbac.entity.Role;


import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface RoleService {

    /**
     * 分页查询角色列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<Role> getRoleList(RoleQueryDTO queryDTO);

    /**
     * 根据ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    Role getRoleById(Long id);

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 创建的角色
     */
    Role createRole(Role role);

    /**
     * 更新角色
     *
     * @param id 角色ID
     * @param role 角色信息
     * @return 更新的角色
     */
    Role updateRole(Long id, Role role);

    /**
     * 删除角色
     *
     * @param id 角色ID
     */
    void deleteRole(Long id);

    /**
     * 批量删除角色
     *
     * @param ids 角色ID列表
     */
    void batchDeleteRoles(List<Long> ids);
}
