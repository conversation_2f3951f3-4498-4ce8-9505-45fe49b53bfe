<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS基础示例 - 样式设计入门</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="header">
            <h1 class="main-title">🎨 CSS基础学习示例</h1>
            <p class="subtitle">学习CSS样式设计，让网页变得美观</p>
        </header>

        <!-- 导航菜单 -->
        <nav class="navigation">
            <ul class="nav-list">
                <li><a href="#selectors" class="nav-link">选择器</a></li>
                <li><a href="#box-model" class="nav-link">盒模型</a></li>
                <li><a href="#layout" class="nav-link">布局</a></li>
                <li><a href="#flexbox" class="nav-link">Flexbox</a></li>
                <li><a href="#grid" class="nav-link">Grid</a></li>
                <li><a href="#animations" class="nav-link">动画</a></li>
            </ul>
        </nav>

        <!-- CSS选择器示例 -->
        <section id="selectors" class="section">
            <h2 class="section-title">🎯 CSS选择器</h2>
            
            <div class="example-grid">
                <div class="example-item">
                    <h3>标签选择器</h3>
                    <p>选择所有指定的HTML标签</p>
                    <code>p { color: blue; }</code>
                </div>
                
                <div class="example-item highlight">
                    <h3>类选择器</h3>
                    <p>选择具有指定class的元素</p>
                    <code>.highlight { background: yellow; }</code>
                </div>
                
                <div class="example-item" id="unique-item">
                    <h3>ID选择器</h3>
                    <p>选择具有指定id的元素</p>
                    <code>#unique-item { border: 2px solid red; }</code>
                </div>
                
                <div class="example-item">
                    <h3>属性选择器</h3>
                    <p data-info="这是属性选择器示例">选择具有指定属性的元素</p>
                    <code>[data-info] { font-style: italic; }</code>
                </div>
            </div>

            <div class="pseudo-examples">
                <h3>伪类和伪元素</h3>
                <button class="hover-button">悬停我看效果</button>
                <p class="first-letter-demo">这段文字的首字母会有特殊样式。</p>
                <ul class="nth-child-demo">
                    <li>第一项</li>
                    <li>第二项</li>
                    <li>第三项</li>
                    <li>第四项</li>
                </ul>
            </div>
        </section>

        <!-- 盒模型示例 -->
        <section id="box-model" class="section">
            <h2 class="section-title">📦 CSS盒模型</h2>
            
            <div class="box-model-demo">
                <div class="box-example">
                    <div class="content">内容区域</div>
                </div>
                <div class="box-labels">
                    <span class="label margin-label">Margin (外边距)</span>
                    <span class="label border-label">Border (边框)</span>
                    <span class="label padding-label">Padding (内边距)</span>
                    <span class="label content-label">Content (内容)</span>
                </div>
            </div>

            <div class="box-sizing-demo">
                <h3>Box-sizing 属性</h3>
                <div class="box-sizing-examples">
                    <div class="box content-box">
                        <h4>content-box (默认)</h4>
                        <p>宽度 = 内容宽度</p>
                    </div>
                    <div class="box border-box">
                        <h4>border-box</h4>
                        <p>宽度 = 内容 + 内边距 + 边框</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 布局示例 -->
        <section id="layout" class="section">
            <h2 class="section-title">🏗️ CSS布局</h2>
            
            <div class="layout-examples">
                <h3>Position 定位</h3>
                <div class="position-demo">
                    <div class="position-container">
                        <div class="position-item static">static</div>
                        <div class="position-item relative">relative</div>
                        <div class="position-item absolute">absolute</div>
                        <div class="position-item fixed">fixed</div>
                    </div>
                </div>

                <h3>Float 浮动 (传统布局)</h3>
                <div class="float-demo">
                    <div class="float-item left">左浮动</div>
                    <div class="float-item right">右浮动</div>
                    <div class="clearfix">
                        <p>这是清除浮动后的内容。浮动曾经是CSS布局的主要方式，现在主要用于文字环绕效果。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Flexbox示例 -->
        <section id="flexbox" class="section">
            <h2 class="section-title">🔧 Flexbox布局</h2>
            
            <div class="flexbox-examples">
                <h3>基本Flex容器</h3>
                <div class="flex-container basic-flex">
                    <div class="flex-item">项目1</div>
                    <div class="flex-item">项目2</div>
                    <div class="flex-item">项目3</div>
                </div>

                <h3>Justify-content (主轴对齐)</h3>
                <div class="flex-demo-grid">
                    <div class="flex-demo">
                        <h4>flex-start</h4>
                        <div class="flex-container justify-start">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                    
                    <div class="flex-demo">
                        <h4>center</h4>
                        <div class="flex-container justify-center">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                    
                    <div class="flex-demo">
                        <h4>space-between</h4>
                        <div class="flex-container justify-between">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                    
                    <div class="flex-demo">
                        <h4>space-around</h4>
                        <div class="flex-container justify-around">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                </div>

                <h3>Align-items (交叉轴对齐)</h3>
                <div class="flex-demo-grid">
                    <div class="flex-demo">
                        <h4>flex-start</h4>
                        <div class="flex-container align-start tall">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                    
                    <div class="flex-demo">
                        <h4>center</h4>
                        <div class="flex-container align-center tall">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                    
                    <div class="flex-demo">
                        <h4>flex-end</h4>
                        <div class="flex-container align-end tall">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                    
                    <div class="flex-demo">
                        <h4>stretch</h4>
                        <div class="flex-container align-stretch tall">
                            <div class="flex-item small">1</div>
                            <div class="flex-item small">2</div>
                            <div class="flex-item small">3</div>
                        </div>
                    </div>
                </div>

                <h3>Flex项目属性</h3>
                <div class="flex-container flex-properties">
                    <div class="flex-item flex-grow-1">flex-grow: 1</div>
                    <div class="flex-item flex-grow-2">flex-grow: 2</div>
                    <div class="flex-item flex-shrink-0">flex-shrink: 0</div>
                </div>
            </div>
        </section>

        <!-- Grid示例 -->
        <section id="grid" class="section">
            <h2 class="section-title">🎯 CSS Grid布局</h2>
            
            <div class="grid-examples">
                <h3>基本Grid布局</h3>
                <div class="grid-container basic-grid">
                    <div class="grid-item">1</div>
                    <div class="grid-item">2</div>
                    <div class="grid-item">3</div>
                    <div class="grid-item">4</div>
                    <div class="grid-item">5</div>
                    <div class="grid-item">6</div>
                </div>

                <h3>Grid区域命名</h3>
                <div class="grid-container named-grid">
                    <div class="grid-item header">Header</div>
                    <div class="grid-item sidebar">Sidebar</div>
                    <div class="grid-item main">Main Content</div>
                    <div class="grid-item footer">Footer</div>
                </div>

                <h3>响应式Grid</h3>
                <div class="grid-container responsive-grid">
                    <div class="grid-item">卡片1</div>
                    <div class="grid-item">卡片2</div>
                    <div class="grid-item">卡片3</div>
                    <div class="grid-item">卡片4</div>
                    <div class="grid-item">卡片5</div>
                    <div class="grid-item">卡片6</div>
                </div>
            </div>
        </section>

        <!-- 动画示例 -->
        <section id="animations" class="section">
            <h2 class="section-title">✨ CSS动画</h2>
            
            <div class="animation-examples">
                <h3>Transition 过渡</h3>
                <div class="transition-demos">
                    <div class="transition-item hover-scale">悬停放大</div>
                    <div class="transition-item hover-rotate">悬停旋转</div>
                    <div class="transition-item hover-color">悬停变色</div>
                </div>

                <h3>Transform 变换</h3>
                <div class="transform-demos">
                    <div class="transform-item translate">平移</div>
                    <div class="transform-item rotate">旋转</div>
                    <div class="transform-item scale">缩放</div>
                    <div class="transform-item skew">倾斜</div>
                </div>

                <h3>Keyframe 关键帧动画</h3>
                <div class="keyframe-demos">
                    <div class="keyframe-item bounce">弹跳</div>
                    <div class="keyframe-item pulse">脉冲</div>
                    <div class="keyframe-item slide">滑动</div>
                    <div class="keyframe-item fade">淡入淡出</div>
                </div>

                <h3>加载动画</h3>
                <div class="loading-demos">
                    <div class="spinner"></div>
                    <div class="dots">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 响应式设计示例 -->
        <section class="section">
            <h2 class="section-title">📱 响应式设计</h2>
            
            <div class="responsive-demo">
                <h3>媒体查询示例</h3>
                <div class="responsive-grid">
                    <div class="responsive-item">在不同屏幕尺寸下，这些卡片会自动调整布局</div>
                    <div class="responsive-item">桌面：4列</div>
                    <div class="responsive-item">平板：2列</div>
                    <div class="responsive-item">手机：1列</div>
                </div>
                
                <div class="breakpoint-info">
                    <p><strong>当前断点：</strong><span class="current-breakpoint"></span></p>
                </div>
            </div>
        </section>

        <!-- 页面底部 -->
        <footer class="footer">
            <h2>🎉 CSS学习完成！</h2>
            <p>恭喜您学习了CSS的基础知识！现在您可以创建美观的网页了。</p>
            <div class="next-steps">
                <h3>下一步学习建议：</h3>
                <ul>
                    <li>深入学习CSS预处理器 (Sass/Less)</li>
                    <li>学习CSS框架 (Bootstrap/Tailwind CSS)</li>
                    <li>掌握CSS-in-JS技术</li>
                    <li>学习JavaScript为网页添加交互</li>
                </ul>
            </div>
        </footer>
    </div>

    <script>
        // 显示当前断点
        function updateBreakpoint() {
            const width = window.innerWidth;
            const breakpointElement = document.querySelector('.current-breakpoint');
            
            if (width >= 1200) {
                breakpointElement.textContent = '大屏幕 (≥1200px)';
            } else if (width >= 992) {
                breakpointElement.textContent = '桌面 (≥992px)';
            } else if (width >= 768) {
                breakpointElement.textContent = '平板 (≥768px)';
            } else {
                breakpointElement.textContent = '手机 (<768px)';
            }
        }

        // 页面加载和窗口大小改变时更新断点信息
        window.addEventListener('load', updateBreakpoint);
        window.addEventListener('resize', updateBreakpoint);

        // 平滑滚动
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        console.log('🎨 CSS基础示例页面加载完成！');
    </script>
</body>
</html>
