package Threadtest;

public class TheadDemo extends Thread {
    private String threadName;

    public TheadDemo(String name) {
        this.threadName = name;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(threadName + " - 计数: " + i);
            try {
                Thread.sleep(1000); // 暂停1秒
            } catch (InterruptedException e) {
                System.out.println(threadName + " 被中断");
                return;
            }
        }
        System.out.println(threadName + " 执行完成");
    }

    public static void main(String[] args) {
        TheadDemo thread1 = new TheadDemo("线程1");
        TheadDemo thread2 = new TheadDemo("线程2");

        thread1.start(); // 启动线程
        thread2.start();

        System.out.println("主线程继续执行");
    }
}
