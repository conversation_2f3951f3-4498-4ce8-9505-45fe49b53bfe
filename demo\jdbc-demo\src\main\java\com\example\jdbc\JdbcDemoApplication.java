package com.example.jdbc;

import com.example.jdbc.config.ConnectionPoolConfig;
import com.example.jdbc.config.DatabaseConfig;
import com.example.jdbc.entity.User;
import com.example.jdbc.examples.BasicCrudExample;
import com.example.jdbc.examples.TransactionExample;
import com.example.jdbc.util.JdbcUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@SpringBootApplication
public class JdbcDemoApplication implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(JdbcDemoApplication.class);
    
    @Autowired
    private JdbcUtil jdbcUtil;
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    @Autowired
    private BasicCrudExample basicCrudExample;
    
    @Autowired
    private TransactionExample transactionExample;
    
    @Autowired
    private ConnectionPoolConfig connectionPoolConfig;
    
    @Autowired
    @Qualifier("hikariDataSource")
    private DataSource hikariDataSource;
    
    @Autowired
    @Qualifier("dbcp2DataSource")
    private DataSource dbcp2DataSource;
    
    public static void main(String[] args) {
        SpringApplication.run(JdbcDemoApplication.class, args);
    }
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("=================================");
        logger.info("JDBC Demo 应用启动成功！");
        logger.info("当前数据库类型: {}", databaseConfig.getDatabaseType());
        logger.info("=================================");
        
        // 运行演示
        runDemonstrations();
    }
    
    private void runDemonstrations() {
        try {
            // 1. 测试基本连接
            logger.info("\n1. 测试数据库连接...");
            testBasicConnection();
            
            // 2. 创建表
            logger.info("\n2. 创建用户表...");
            basicCrudExample.createUserTable();
            
            // 3. 测试CRUD操作
            logger.info("\n3. 测试CRUD操作...");
            testCrudOperations();
            
            // 4. 测试事务
            logger.info("\n4. 测试事务操作...");
            testTransactions();
            
            // 5. 测试连接池
            logger.info("\n5. 测试连接池...");
            testConnectionPools();
            
            logger.info("\n=== 所有演示完成 ===");
            
        } catch (Exception e) {
            logger.error("演示过程中发生错误: {}", e.getMessage(), e);
        }
    }
    
    private void testBasicConnection() {
        // 测试连接
        boolean connected = jdbcUtil.testConnection();
        logger.info("数据库连接测试: {}", connected ? "成功" : "失败");
        
        // 打印数据库信息
        jdbcUtil.printDatabaseInfo();
    }
    
    private void testCrudOperations() {
        // 创建测试用户
        User user1 = User.builder()
                .username("jdbc_user_1")
                .email("<EMAIL>")
                .phone("13800138001")
                .age(25)
                .address("北京市朝阳区")
                .build();
        
        User user2 = User.builder()
                .username("jdbc_user_2")
                .email("<EMAIL>")
                .phone("13800138002")
                .age(30)
                .address("上海市浦东新区")
                .build();
        
        // 插入用户
        logger.info("插入用户1...");
        Long id1 = basicCrudExample.insertUserAndReturnId(user1);
        logger.info("用户1插入成功，ID: {}", id1);
        
        logger.info("插入用户2...");
        boolean inserted = basicCrudExample.insertUser(user2);
        logger.info("用户2插入结果: {}", inserted);
        
        // 查询用户
        logger.info("根据ID查询用户...");
        User foundUser = basicCrudExample.findUserById(id1);
        if (foundUser != null) {
            logger.info("查询到用户: {}", foundUser);
        }
        
        logger.info("根据用户名查询用户...");
        User foundByUsername = basicCrudExample.findUserByUsername("jdbc_user_1");
        if (foundByUsername != null) {
            logger.info("根据用户名查询到: {}", foundByUsername.getUsername());
        }
        
        // 查询所有用户
        logger.info("查询所有用户...");
        List<User> allUsers = basicCrudExample.findAllUsers();
        logger.info("总共查询到{}个用户", allUsers.size());
        
        // 更新用户
        if (foundUser != null) {
            logger.info("更新用户信息...");
            foundUser.setAge(26);
            foundUser.setAddress("北京市海淀区");
            boolean updated = basicCrudExample.updateUser(foundUser);
            logger.info("用户更新结果: {}", updated);
        }
        
        // 删除用户（注释掉，保留数据用于后续测试）
        // logger.info("删除用户...");
        // boolean deleted = basicCrudExample.deleteUser(id1);
        // logger.info("用户删除结果: {}", deleted);
    }
    
    private void testTransactions() {
        // 创建批量插入的用户
        List<User> batchUsers = Arrays.asList(
            User.builder()
                .username("batch_user_1")
                .email("<EMAIL>")
                .phone("13900139001")
                .age(22)
                .build(),
            User.builder()
                .username("batch_user_2")
                .email("<EMAIL>")
                .phone("13900139002")
                .age(24)
                .build(),
            User.builder()
                .username("batch_user_3")
                .email("<EMAIL>")
                .phone("13900139003")
                .age(26)
                .build()
        );
        
        // 测试批量插入事务
        logger.info("测试批量插入事务...");
        boolean batchResult = transactionExample.batchInsertUsersWithTransaction(batchUsers);
        logger.info("批量插入事务结果: {}", batchResult);
        
        // 测试复杂事务（保存点）
        logger.info("测试复杂事务（保存点）...");
        boolean complexResult = transactionExample.complexTransactionWithSavepoint();
        logger.info("复杂事务结果: {}", complexResult);
        
        // 演示事务隔离级别
        logger.info("演示事务隔离级别...");
        transactionExample.demonstrateTransactionIsolation();
    }
    
    private void testConnectionPools() {
        // 测试HikariCP连接池
        logger.info("测试HikariCP连接池...");
        connectionPoolConfig.testConnectionPool(hikariDataSource, "HikariCP");
        
        // 测试DBCP2连接池
        logger.info("测试DBCP2连接池...");
        connectionPoolConfig.testConnectionPool(dbcp2DataSource, "DBCP2");
        
        // 性能测试
        logger.info("HikariCP性能测试...");
        connectionPoolConfig.performanceTest(hikariDataSource, "HikariCP", 100);
        
        logger.info("DBCP2性能测试...");
        connectionPoolConfig.performanceTest(dbcp2DataSource, "DBCP2", 100);
    }
}
