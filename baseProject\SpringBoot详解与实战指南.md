# Spring Boot 详解与实战指南

## 📚 目录

1. [Spring Boot 概述](#spring-boot-概述)
2. [核心特性](#核心特性)
3. [自动配置原理](#自动配置原理)
4. [AOP 面向切面编程](#aop-面向切面编程)
5. [常用注解详解](#常用注解详解)
6. [配置管理](#配置管理)
7. [数据访问](#数据访问)
8. [Web 开发](#web-开发)
9. [安全框架](#安全框架)
10. [监控与管理](#监控与管理)
11. [测试](#测试)
12. [部署与运维](#部署与运维)
13. [最佳实践](#最佳实践)

---

## Spring Boot 概述

### 什么是 Spring Boot

Spring Boot 是由 Pivotal 团队提供的全新框架，其设计目的是用来简化新 Spring 应用的初始搭建以及开发过程。

### 核心理念

- **约定优于配置** (Convention over Configuration)
- **开箱即用** (Out of the box)
- **微服务友好** (Microservice Ready)

### 主要优势

1. **快速创建独立的 Spring 应用程序**
2. **直接嵌入 Tomcat、Jetty 或 Undertow**
3. **提供 starter 依赖简化构建配置**
4. **自动配置 Spring 和第三方库**
5. **提供生产就绪功能，如指标、健康检查和外部化配置**
6. **绝对没有代码生成，也不需要 XML 配置**

---

## 核心特性

### 1. 起步依赖 (Starter Dependencies)

Spring Boot 提供了许多起步依赖来简化构建配置：

```xml
<!-- Web 开发 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- 数据访问 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>

<!-- 安全框架 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>

<!-- 测试 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### 2. 自动配置 (Auto Configuration)

Spring Boot 会根据类路径中的依赖自动配置应用程序：

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 嵌入式服务器

Spring Boot 内嵌了 Tomcat、Jetty 和 Undertow 服务器：

```yaml
# application.yml
server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-threads: 200
    min-spare-threads: 10
```

### 4. Actuator 监控

提供生产就绪的监控和管理功能：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

```yaml
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
```

---

## 自动配置原理

### 1. @SpringBootApplication 注解

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(excludeFilters = { @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
        @Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class) })
public @interface SpringBootApplication {
    // ...
}
```

### 2. @EnableAutoConfiguration 原理

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@AutoConfigurationPackage
@Import(AutoConfigurationImportSelector.class)
public @interface EnableAutoConfiguration {
    // ...
}
```

### 3. 自动配置类示例

```java
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(DataSource.class)
@ConditionalOnMissingBean(type = "javax.sql.DataSource")
@EnableConfigurationProperties(DataSourceProperties.class)
@Import({ DataSourcePoolMetadataProvidersConfiguration.class,
        DataSourceInitializationConfiguration.class })
public class DataSourceAutoConfiguration {

    @Configuration(proxyBeanMethods = false)
    @Conditional(EmbeddedDatabaseCondition.class)
    @ConditionalOnMissingBean({ DataSource.class, XADataSource.class })
    @Import(EmbeddedDataSourceConfiguration.class)
    protected static class EmbeddedDatabaseConfiguration {
    }

    @Configuration(proxyBeanMethods = false)
    @Conditional(PooledDataSourceCondition.class)
    @ConditionalOnMissingBean({ DataSource.class, XADataSource.class })
    @Import({ DataSourceConfiguration.Hikari.class, DataSourceConfiguration.Tomcat.class,
            DataSourceConfiguration.Dbcp2.class, DataSourceConfiguration.OracleUcp.class,
            DataSourceConfiguration.Generic.class, DataSourceJmxConfiguration.class })
    protected static class PooledDataSourceConfiguration {
    }
}
```

### 4. 条件注解

| 注解 | 说明 |
|------|------|
| `@ConditionalOnClass` | 当类路径下有指定类时才会加载该配置类 |
| `@ConditionalOnMissingClass` | 当类路径下没有指定类时才会加载该配置类 |
| `@ConditionalOnBean` | 当容器中有指定Bean时才会加载该配置类 |
| `@ConditionalOnMissingBean` | 当容器中没有指定Bean时才会加载该配置类 |
| `@ConditionalOnProperty` | 当指定的属性有指定的值时才会加载该配置类 |
| `@ConditionalOnResource` | 当类路径下有指定资源时才会加载该配置类 |
| `@ConditionalOnWebApplication` | 当前项目是Web项目时才会加载该配置类 |
| `@ConditionalOnNotWebApplication` | 当前项目不是Web项目时才会加载该配置类 |

---

## AOP 面向切面编程

### 1. AOP 概念

AOP (Aspect Oriented Programming) 面向切面编程，是一种编程范式，旨在通过分离横切关注点来增加程序的模块化。

#### 核心概念

- **切面 (Aspect)**: 横切关注点的模块化
- **连接点 (Join Point)**: 程序执行过程中的某个特定点
- **切点 (Pointcut)**: 匹配连接点的断言
- **通知 (Advice)**: 在切点执行的代码
- **目标对象 (Target Object)**: 被一个或多个切面通知的对象
- **织入 (Weaving)**: 将切面应用到目标对象的过程

### 2. Spring Boot 中使用 AOP

#### 添加依赖

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>
```

#### 启用 AOP

```java
@SpringBootApplication
@EnableAspectJAutoProxy
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 通知类型

#### @Before - 前置通知

```java
@Aspect
@Component
@Slf4j
public class LoggingAspect {
    
    @Before("execution(* com.example.service.*.*(..))")
    public void logBefore(JoinPoint joinPoint) {
        log.info("执行方法: {}", joinPoint.getSignature().getName());
        log.info("方法参数: {}", Arrays.toString(joinPoint.getArgs()));
    }
}
```

#### @After - 后置通知

```java
@After("execution(* com.example.service.*.*(..))")
public void logAfter(JoinPoint joinPoint) {
    log.info("方法执行完成: {}", joinPoint.getSignature().getName());
}
```

#### @AfterReturning - 返回通知

```java
@AfterReturning(pointcut = "execution(* com.example.service.*.*(..))", returning = "result")
public void logAfterReturning(JoinPoint joinPoint, Object result) {
    log.info("方法返回值: {}", result);
}
```

#### @AfterThrowing - 异常通知

```java
@AfterThrowing(pointcut = "execution(* com.example.service.*.*(..))", throwing = "exception")
public void logAfterThrowing(JoinPoint joinPoint, Exception exception) {
    log.error("方法执行异常: {}", exception.getMessage());
}
```

#### @Around - 环绕通知

```java
@Around("execution(* com.example.service.*.*(..))")
public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
    long startTime = System.currentTimeMillis();
    
    log.info("方法开始执行: {}", joinPoint.getSignature().getName());
    
    try {
        Object result = joinPoint.proceed();
        long endTime = System.currentTimeMillis();
        log.info("方法执行成功，耗时: {}ms", endTime - startTime);
        return result;
    } catch (Exception e) {
        long endTime = System.currentTimeMillis();
        log.error("方法执行失败，耗时: {}ms，异常: {}", endTime - startTime, e.getMessage());
        throw e;
    }
}
```

### 4. 切点表达式

#### 基本语法

```java
// 匹配所有public方法
@Pointcut("execution(public * *(..))")

// 匹配指定包下的所有方法
@Pointcut("execution(* com.example.service.*.*(..))")

// 匹配指定类的所有方法
@Pointcut("execution(* com.example.service.UserService.*(..))")

// 匹配指定方法名
@Pointcut("execution(* com.example.service.*.get*(..))")

// 匹配指定参数类型
@Pointcut("execution(* com.example.service.*.*(String, ..))")

// 匹配注解
@Pointcut("@annotation(com.example.annotation.Log)")

// 匹配类注解
@Pointcut("@within(org.springframework.stereotype.Service)")

// 匹配参数注解
@Pointcut("@args(com.example.annotation.Valid)")
```

#### 组合切点

```java
@Pointcut("execution(* com.example.service.*.*(..))")
public void serviceLayer() {}

@Pointcut("@annotation(com.example.annotation.Log)")
public void logAnnotation() {}

@Pointcut("serviceLayer() && logAnnotation()")
public void serviceWithLog() {}
```

### 5. 实际应用示例

#### 日志切面

```java
@Aspect
@Component
@Slf4j
public class LoggingAspect {

    @Pointcut("@annotation(com.example.annotation.Log)")
    public void logPointcut() {}

    @Around("logPointcut()")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Log logAnnotation = method.getAnnotation(Log.class);

        // 记录开始日志
        log.info("开始执行: {} - {}", logAnnotation.value(), method.getName());
        log.info("请求参数: {}", JSON.toJSONString(joinPoint.getArgs()));

        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();

            // 记录成功日志
            log.info("执行成功: {} - 耗时: {}ms", method.getName(), endTime - startTime);
            log.info("返回结果: {}", JSON.toJSONString(result));

            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();

            // 记录异常日志
            log.error("执行失败: {} - 耗时: {}ms - 异常: {}",
                     method.getName(), endTime - startTime, e.getMessage());
            throw e;
        }
    }
}
```

#### 自定义日志注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 操作描述
     */
    String value() default "";

    /**
     * 是否记录参数
     */
    boolean recordParams() default true;

    /**
     * 是否记录返回值
     */
    boolean recordResult() default true;
}
```

#### 权限校验切面

```java
@Aspect
@Component
@Slf4j
public class AuthorizationAspect {

    @Autowired
    private UserService userService;

    @Before("@annotation(requiresPermission)")
    public void checkPermission(JoinPoint joinPoint, RequiresPermission requiresPermission) {
        // 获取当前用户
        String currentUser = getCurrentUser();

        // 检查权限
        String[] permissions = requiresPermission.value();
        boolean hasPermission = userService.hasAnyPermission(currentUser, permissions);

        if (!hasPermission) {
            log.warn("用户 {} 尝试访问需要权限 {} 的方法 {}",
                    currentUser, Arrays.toString(permissions), joinPoint.getSignature().getName());
            throw new AccessDeniedException("权限不足");
        }

        log.info("用户 {} 成功通过权限校验: {}", currentUser, Arrays.toString(permissions));
    }

    private String getCurrentUser() {
        // 从SecurityContext或Session中获取当前用户
        return "currentUser";
    }
}
```

#### 缓存切面

```java
@Aspect
@Component
@Slf4j
public class CacheAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Around("@annotation(cacheable)")
    public Object cache(ProceedingJoinPoint joinPoint, Cacheable cacheable) throws Throwable {
        // 生成缓存key
        String key = generateCacheKey(joinPoint, cacheable);

        // 尝试从缓存获取
        Object cachedResult = redisTemplate.opsForValue().get(key);
        if (cachedResult != null) {
            log.info("缓存命中: {}", key);
            return cachedResult;
        }

        // 执行方法
        Object result = joinPoint.proceed();

        // 存入缓存
        if (result != null) {
            redisTemplate.opsForValue().set(key, result,
                Duration.ofSeconds(cacheable.expireTime()));
            log.info("结果已缓存: {}", key);
        }

        return result;
    }

    private String generateCacheKey(ProceedingJoinPoint joinPoint, Cacheable cacheable) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(cacheable.prefix());
        keyBuilder.append(":");
        keyBuilder.append(joinPoint.getSignature().getName());

        Object[] args = joinPoint.getArgs();
        if (args.length > 0) {
            keyBuilder.append(":");
            keyBuilder.append(Arrays.hashCode(args));
        }

        return keyBuilder.toString();
    }
}
```

---

## 常用注解详解

### 1. 核心注解

#### @SpringBootApplication

```java
@SpringBootApplication
// 等价于以下三个注解的组合
// @SpringBootConfiguration
// @EnableAutoConfiguration
// @ComponentScan
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

#### @Configuration

```java
@Configuration
public class AppConfig {

    @Bean
    @Primary // 当有多个相同类型的Bean时，优先使用此Bean
    public DataSource primaryDataSource() {
        return new HikariDataSource();
    }

    @Bean
    @Qualifier("secondary") // 指定Bean的名称
    public DataSource secondaryDataSource() {
        return new HikariDataSource();
    }
}
```

#### @Component 系列

```java
@Component // 通用组件
public class CommonComponent {
}

@Service // 业务逻辑层
public class UserService {
}

@Repository // 数据访问层
public class UserRepository {
}

@Controller // 控制层
public class UserController {
}

@RestController // REST控制层，等价于 @Controller + @ResponseBody
public class UserRestController {
}
```

### 2. 依赖注入注解

#### @Autowired

```java
@Service
public class UserService {

    // 字段注入（不推荐）
    @Autowired
    private UserRepository userRepository;

    // 构造器注入（推荐）
    private final UserRepository userRepository;
    private final EmailService emailService;

    @Autowired
    public UserService(UserRepository userRepository, EmailService emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }

    // Setter注入
    private NotificationService notificationService;

    @Autowired
    public void setNotificationService(NotificationService notificationService) {
        this.notificationService = notificationService;
    }
}
```

#### @Qualifier 和 @Primary

```java
@Service
public class UserService {

    @Autowired
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;

    @Autowired
    @Qualifier("secondaryDataSource")
    private DataSource secondaryDataSource;
}

@Configuration
public class DataSourceConfig {

    @Bean
    @Primary
    public DataSource primaryDataSource() {
        return new HikariDataSource();
    }

    @Bean
    public DataSource secondaryDataSource() {
        return new HikariDataSource();
    }
}
```

#### @Value

```java
@Component
public class AppProperties {

    @Value("${app.name}")
    private String appName;

    @Value("${app.version:1.0.0}") // 默认值
    private String appVersion;

    @Value("#{systemProperties['java.home']}")
    private String javaHome;

    @Value("#{T(java.lang.Math).random() * 100}")
    private double randomNumber;
}
```

### 3. Web 相关注解

#### @RequestMapping 系列

```java
@RestController
@RequestMapping("/api/users")
public class UserController {

    @GetMapping // 等价于 @RequestMapping(method = RequestMethod.GET)
    public List<User> getAllUsers() {
        return userService.getAllUsers();
    }

    @GetMapping("/{id}")
    public User getUserById(@PathVariable Long id) {
        return userService.getUserById(id);
    }

    @PostMapping
    public User createUser(@RequestBody @Valid User user) {
        return userService.createUser(user);
    }

    @PutMapping("/{id}")
    public User updateUser(@PathVariable Long id, @RequestBody @Valid User user) {
        return userService.updateUser(id, user);
    }

    @DeleteMapping("/{id}")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
    }

    @GetMapping("/search")
    public List<User> searchUsers(@RequestParam String keyword,
                                  @RequestParam(defaultValue = "0") int page,
                                  @RequestParam(defaultValue = "10") int size) {
        return userService.searchUsers(keyword, page, size);
    }
}
```

#### 参数绑定注解

```java
@RestController
public class ParameterController {

    // 路径变量
    @GetMapping("/users/{id}/posts/{postId}")
    public Post getUserPost(@PathVariable Long id, @PathVariable Long postId) {
        return postService.getUserPost(id, postId);
    }

    // 请求参数
    @GetMapping("/users")
    public List<User> getUsers(@RequestParam(required = false) String name,
                               @RequestParam(defaultValue = "0") int page) {
        return userService.getUsers(name, page);
    }

    // 请求头
    @GetMapping("/info")
    public String getInfo(@RequestHeader("User-Agent") String userAgent,
                          @RequestHeader(value = "Accept-Language", defaultValue = "en") String language) {
        return "User-Agent: " + userAgent + ", Language: " + language;
    }

    // Cookie
    @GetMapping("/session")
    public String getSession(@CookieValue("JSESSIONID") String sessionId) {
        return "Session ID: " + sessionId;
    }

    // 请求体
    @PostMapping("/users")
    public User createUser(@RequestBody @Valid User user) {
        return userService.createUser(user);
    }

    // 表单数据
    @PostMapping("/upload")
    public String uploadFile(@RequestParam("file") MultipartFile file,
                             @RequestParam("description") String description) {
        return "File uploaded: " + file.getOriginalFilename();
    }
}
```

### 4. 验证注解

#### 基本验证注解

```java
@Entity
public class User {

    @NotNull(message = "ID不能为空")
    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    private String username;

    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Min(value = 0, message = "年龄不能小于0")
    @Max(value = 150, message = "年龄不能大于150")
    private Integer age;

    @DecimalMin(value = "0.0", message = "工资不能小于0")
    @DecimalMax(value = "999999.99", message = "工资不能大于999999.99")
    private BigDecimal salary;

    @Past(message = "生日必须是过去的日期")
    private LocalDate birthday;

    @Future(message = "过期时间必须是将来的日期")
    private LocalDateTime expireTime;
}
```

#### 自定义验证注解

```java
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PhoneValidator.class)
@Documented
public @interface Phone {
    String message() default "手机号格式不正确";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

public class PhoneValidator implements ConstraintValidator<Phone, String> {

    private static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";
    private static final Pattern pattern = Pattern.compile(PHONE_PATTERN);

    @Override
    public boolean isValid(String phone, ConstraintValidatorContext context) {
        if (phone == null || phone.isEmpty()) {
            return true; // 让@NotBlank处理空值
        }
        return pattern.matcher(phone).matches();
    }
}
```

### 5. 事务注解

#### @Transactional

```java
@Service
@Transactional(readOnly = true) // 类级别默认只读事务
public class UserService {

    @Transactional // 方法级别覆盖类级别设置
    public User createUser(User user) {
        // 创建用户
        User savedUser = userRepository.save(user);

        // 发送欢迎邮件
        emailService.sendWelcomeEmail(savedUser.getEmail());

        return savedUser;
    }

    @Transactional(
        propagation = Propagation.REQUIRES_NEW, // 传播行为
        isolation = Isolation.READ_COMMITTED,   // 隔离级别
        timeout = 30,                           // 超时时间（秒）
        rollbackFor = Exception.class,          // 回滚异常
        noRollbackFor = BusinessException.class // 不回滚异常
    )
    public void complexOperation() {
        // 复杂操作
    }

    public List<User> getAllUsers() {
        // 只读操作，使用类级别的只读事务
        return userRepository.findAll();
    }
}
```

### 6. 缓存注解

#### Spring Cache 注解

```java
@Service
public class UserService {

    @Cacheable(value = "users", key = "#id")
    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    @Cacheable(value = "users", key = "#username", condition = "#username.length() > 3")
    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @CachePut(value = "users", key = "#user.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    @CacheEvict(value = "users", key = "#id")
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }

    @CacheEvict(value = "users", allEntries = true)
    public void clearAllUsers() {
        // 清空所有用户缓存
    }

    @Caching(
        cacheable = @Cacheable(value = "users", key = "#id"),
        put = @CachePut(value = "userStats", key = "#id")
    )
    public User getUserWithStats(Long id) {
        return userRepository.findByIdWithStats(id);
    }
}
```

---

## 配置管理

### 1. 配置文件

#### application.properties

```properties
# 服务器配置
server.port=8080
server.servlet.context-path=/api

# 数据源配置
spring.datasource.url=********************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# 日志配置
logging.level.com.example=DEBUG
logging.file.name=logs/application.log
```

#### application.yml

```yaml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: ********************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.example: DEBUG
  file:
    name: logs/application.log
```

### 2. 多环境配置

#### Profile 配置

```yaml
# application.yml
spring:
  profiles:
    active: dev

---
# 开发环境
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ********************************_dev
    username: dev_user
    password: dev_password

logging:
  level:
    com.example: DEBUG

---
# 测试环境
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: ***************************************
    username: test_user
    password: test_password

logging:
  level:
    com.example: INFO

---
# 生产环境
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ***************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

logging:
  level:
    com.example: WARN
```

### 3. 配置属性绑定

#### @ConfigurationProperties

```java
@ConfigurationProperties(prefix = "app")
@Component
@Data
public class AppProperties {

    private String name;
    private String version;
    private Security security = new Security();
    private List<String> servers = new ArrayList<>();
    private Map<String, String> database = new HashMap<>();

    @Data
    public static class Security {
        private boolean enabled = true;
        private String secretKey;
        private int tokenExpiration = 3600;
    }
}
```

```yaml
app:
  name: MyApp
  version: 1.0.0
  security:
    enabled: true
    secret-key: mySecretKey
    token-expiration: 7200
  servers:
    - server1.example.com
    - server2.example.com
  database:
    driver: mysql
    pool-size: 10
```

#### @Value 注解

```java
@Component
public class DatabaseConfig {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${app.database.pool-size:5}") // 默认值为5
    private int poolSize;

    @Value("#{systemProperties['java.home']}")
    private String javaHome;

    @Value("#{T(java.lang.Math).random() * 100}")
    private double randomNumber;
}
```

### 4. 外部化配置

#### 配置优先级

1. 命令行参数
2. JNDI属性
3. Java系统属性
4. 操作系统环境变量
5. jar包外的application-{profile}.properties
6. jar包内的application-{profile}.properties
7. jar包外的application.properties
8. jar包内的application.properties

#### 命令行配置

```bash
# 指定配置文件
java -jar app.jar --spring.config.location=classpath:/custom.properties

# 指定Profile
java -jar app.jar --spring.profiles.active=prod

# 指定属性
java -jar app.jar --server.port=9090 --spring.datasource.url=*************************
```

#### 环境变量配置

```bash
# 设置环境变量
export SPRING_DATASOURCE_URL=********************************
export SPRING_DATASOURCE_USERNAME=root
export SPRING_DATASOURCE_PASSWORD=123456
export SERVER_PORT=8080
```

---

## 数据访问

### 1. Spring Data JPA

#### 实体类

```java
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false, length = 50)
    private String username;

    @Column(nullable = false, length = 100)
    private String email;

    @Column(nullable = false)
    private String password;

    @Enumerated(EnumType.STRING)
    private UserStatus status;

    @CreationTimestamp
    private LocalDateTime createTime;

    @UpdateTimestamp
    private LocalDateTime updateTime;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Order> orders = new ArrayList<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();
}
```

#### Repository 接口

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    // 方法名查询
    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    List<User> findByStatus(UserStatus status);

    List<User> findByCreateTimeBetween(LocalDateTime start, LocalDateTime end);

    List<User> findByUsernameContainingIgnoreCase(String username);

    // @Query 注解查询
    @Query("SELECT u FROM User u WHERE u.email = ?1")
    Optional<User> findByEmailQuery(String email);

    @Query("SELECT u FROM User u WHERE u.username LIKE %:username%")
    List<User> findByUsernameLike(@Param("username") String username);

    @Query(value = "SELECT * FROM users WHERE status = ?1", nativeQuery = true)
    List<User> findByStatusNative(String status);

    // 更新查询
    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.id = :id")
    int updateUserStatus(@Param("id") Long id, @Param("status") UserStatus status);

    // 删除查询
    @Modifying
    @Query("DELETE FROM User u WHERE u.status = :status")
    int deleteByStatus(@Param("status") UserStatus status);

    // 分页查询
    Page<User> findByStatus(UserStatus status, Pageable pageable);

    // 投影查询
    @Query("SELECT u.username, u.email FROM User u WHERE u.status = :status")
    List<UserProjection> findUserProjectionByStatus(@Param("status") UserStatus status);

    interface UserProjection {
        String getUsername();
        String getEmail();
    }
}
```

#### Service 层

```java
@Service
@Transactional(readOnly = true)
public class UserService {

    @Autowired
    private UserRepository userRepository;

    public Page<User> findUsers(String username, UserStatus status, Pageable pageable) {
        return userRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(username)) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("username")),
                    "%" + username.toLowerCase() + "%"
                ));
            }

            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }, pageable);
    }

    @Transactional
    public User createUser(User user) {
        // 检查用户名是否存在
        if (userRepository.findByUsername(user.getUsername()).isPresent()) {
            throw new BusinessException("用户名已存在");
        }

        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setStatus(UserStatus.ACTIVE);

        return userRepository.save(user);
    }

    @Transactional
    public User updateUser(Long id, User userDetails) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("用户不存在"));

        user.setEmail(userDetails.getEmail());
        // 更新其他字段...

        return userRepository.save(user);
    }

    @Transactional
    public void deleteUser(Long id) {
        if (!userRepository.existsById(id)) {
            throw new EntityNotFoundException("用户不存在");
        }
        userRepository.deleteById(id);
    }
}
```

### 2. MyBatis 集成

#### 配置

```yaml
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
```

#### Mapper 接口

```java
@Mapper
public interface UserMapper {

    @Select("SELECT * FROM users WHERE id = #{id}")
    User findById(@Param("id") Long id);

    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    @Insert("INSERT INTO users(username, email, password) VALUES(#{username}, #{email}, #{password})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);

    @Update("UPDATE users SET email = #{email} WHERE id = #{id}")
    int updateEmail(@Param("id") Long id, @Param("email") String email);

    @Delete("DELETE FROM users WHERE id = #{id}")
    int deleteById(@Param("id") Long id);

    // 复杂查询使用XML
    List<User> findByCondition(UserQueryDTO queryDTO);

    Page<User> findByPage(UserQueryDTO queryDTO);
}
```

### 3. Redis 集成

#### 配置

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
```

#### Redis 配置类

```java
@Configuration
@EnableCaching
public class RedisConfig {

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(objectMapper);

        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);

        template.afterPropertiesSet();
        return template;
    }

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new Jackson2JsonRedisSerializer<>(Object.class)));

        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .build();
    }
}
```

#### Redis 使用示例

```java
@Service
public class CacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public void setValue(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public void setValue(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public Object getValue(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void deleteKey(String key) {
        redisTemplate.delete(key);
    }

    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    public void setExpire(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }

    // Hash操作
    public void hashSet(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    public Object hashGet(String key, String hashKey) {
        return redisTemplate.opsForHash().get(key, hashKey);
    }

    // List操作
    public void listPush(String key, Object value) {
        redisTemplate.opsForList().rightPush(key, value);
    }

    public Object listPop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }

    // Set操作
    public void setAdd(String key, Object... values) {
        redisTemplate.opsForSet().add(key, values);
    }

    public Set<Object> setMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }
}
```

---

## Web 开发

### 1. RESTful API 设计

#### 控制器最佳实践

```java
@RestController
@RequestMapping("/api/v1/users")
@Validated
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    @GetMapping
    public ResponseEntity<PageResult<UserDTO>> getUsers(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) UserStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort,
            @RequestParam(defaultValue = "asc") String direction) {

        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction)
            ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        Page<User> userPage = userService.findUsers(username, status, pageable);
        PageResult<UserDTO> result = PageResult.of(userPage, UserDTO::fromEntity);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserDTO> getUser(@PathVariable @Min(1) Long id) {
        User user = userService.findById(id);
        return ResponseEntity.ok(UserDTO.fromEntity(user));
    }

    @PostMapping
    public ResponseEntity<UserDTO> createUser(@RequestBody @Valid CreateUserRequest request) {
        User user = userService.createUser(request.toEntity());
        UserDTO userDTO = UserDTO.fromEntity(user);

        URI location = ServletUriComponentsBuilder
            .fromCurrentRequest()
            .path("/{id}")
            .buildAndExpand(user.getId())
            .toUri();

        return ResponseEntity.created(location).body(userDTO);
    }

    @PutMapping("/{id}")
    public ResponseEntity<UserDTO> updateUser(
            @PathVariable @Min(1) Long id,
            @RequestBody @Valid UpdateUserRequest request) {

        User user = userService.updateUser(id, request.toEntity());
        return ResponseEntity.ok(UserDTO.fromEntity(user));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable @Min(1) Long id) {
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<Void> updateUserStatus(
            @PathVariable @Min(1) Long id,
            @RequestBody @Valid UpdateStatusRequest request) {

        userService.updateUserStatus(id, request.getStatus());
        return ResponseEntity.ok().build();
    }
}
```
```
