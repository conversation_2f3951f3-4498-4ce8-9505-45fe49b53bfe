# Java反射和代理详解

## 目录
1. [反射简介](#反射简介)
2. [Class类](#class类)
3. [字段反射](#字段反射)
4. [方法反射](#方法反射)
5. [构造函数反射](#构造函数反射)
6. [注解反射](#注解反射)
7. [动态代理](#动态代理)
8. [CGLIB代理](#cglib代理)
9. [实际应用](#实际应用)
10. [最佳实践](#最佳实践)

## 反射简介

### 什么是反射
反射（Reflection）是Java语言的一个重要特性，它允许程序在运行时检查和操作类、接口、字段和方法的信息。通过反射，程序可以在运行时动态地创建对象、调用方法、访问字段，而不需要在编译时知道这些类的具体信息。

### 反射的优势
- **动态性** - 在运行时获取类的信息和操作对象
- **灵活性** - 可以操作任何类，包括私有成员
- **通用性** - 编写通用的框架和工具
- **扩展性** - 支持插件化架构

### 反射的缺点
- **性能开销** - 反射操作比直接调用慢
- **安全限制** - 可能破坏封装性
- **代码复杂** - 反射代码难以理解和维护
- **编译时检查** - 失去编译时类型检查

### 反射的应用场景
- **框架开发** - Spring、Hibernate等框架大量使用反射
- **序列化** - JSON、XML序列化工具
- **依赖注入** - IoC容器实现
- **单元测试** - 测试私有方法和字段
- **代码生成** - 动态生成代码和类

## Class类

### 获取Class对象
```java
public class ClassExample {
    
    public static void demonstrateClassAccess() {
        // 方式1：通过类名.class
        Class<String> clazz1 = String.class;
        
        // 方式2：通过对象.getClass()
        String str = "Hello";
        Class<?> clazz2 = str.getClass();
        
        // 方式3：通过Class.forName()
        try {
            Class<?> clazz3 = Class.forName("java.lang.String");
            Class<?> clazz4 = Class.forName("com.example.User");
        } catch (ClassNotFoundException e) {
            System.out.println("类未找到: " + e.getMessage());
        }
        
        // 验证是否为同一个Class对象
        System.out.println(clazz1 == clazz2); // true
        
        // 基本类型的Class对象
        Class<Integer> intClass = int.class;
        Class<Integer> integerClass = Integer.class;
        Class<Integer> intTypeClass = Integer.TYPE;
        
        System.out.println(intClass == intTypeClass); // true
        System.out.println(intClass == integerClass); // false
    }
}
```

### Class类的常用方法
```java
public class ClassMethods {
    
    public static void demonstrateClassMethods() throws Exception {
        Class<?> clazz = String.class;
        
        // 获取类名
        System.out.println("简单类名: " + clazz.getSimpleName());     // String
        System.out.println("完整类名: " + clazz.getName());           // java.lang.String
        System.out.println("规范类名: " + clazz.getCanonicalName()); // java.lang.String
        
        // 获取包信息
        Package pkg = clazz.getPackage();
        System.out.println("包名: " + pkg.getName()); // java.lang
        
        // 获取父类
        Class<?> superClass = clazz.getSuperclass();
        System.out.println("父类: " + superClass.getName()); // java.lang.Object
        
        // 获取接口
        Class<?>[] interfaces = clazz.getInterfaces();
        System.out.println("实现的接口数量: " + interfaces.length);
        for (Class<?> intf : interfaces) {
            System.out.println("接口: " + intf.getName());
        }
        
        // 类型检查
        System.out.println("是否为接口: " + clazz.isInterface());
        System.out.println("是否为数组: " + clazz.isArray());
        System.out.println("是否为枚举: " + clazz.isEnum());
        System.out.println("是否为注解: " + clazz.isAnnotation());
        System.out.println("是否为基本类型: " + clazz.isPrimitive());
        
        // 修饰符检查
        int modifiers = clazz.getModifiers();
        System.out.println("是否为public: " + Modifier.isPublic(modifiers));
        System.out.println("是否为final: " + Modifier.isFinal(modifiers));
        System.out.println("是否为abstract: " + Modifier.isAbstract(modifiers));
        
        // 创建实例
        Object instance = clazz.newInstance(); // 已废弃，使用Constructor.newInstance()
    }
}
```

### 数组和泛型的Class对象
```java
public class ArrayAndGenericClass {
    
    public static void demonstrateArrayClass() {
        // 数组的Class对象
        int[] intArray = new int[5];
        Class<?> intArrayClass = intArray.getClass();
        
        System.out.println("数组类名: " + intArrayClass.getName());        // [I
        System.out.println("是否为数组: " + intArrayClass.isArray());       // true
        System.out.println("组件类型: " + intArrayClass.getComponentType()); // int
        
        // 多维数组
        int[][] int2DArray = new int[3][4];
        Class<?> int2DArrayClass = int2DArray.getClass();
        System.out.println("二维数组类名: " + int2DArrayClass.getName()); // [[I
        
        // 对象数组
        String[] stringArray = new String[3];
        Class<?> stringArrayClass = stringArray.getClass();
        System.out.println("字符串数组类名: " + stringArrayClass.getName()); // [Ljava.lang.String;
    }
    
    public static void demonstrateGenericClass() {
        // 泛型擦除：运行时List<String>和List<Integer>是同一个Class对象
        List<String> stringList = new ArrayList<>();
        List<Integer> integerList = new ArrayList<>();
        
        Class<?> stringListClass = stringList.getClass();
        Class<?> integerListClass = integerList.getClass();
        
        System.out.println("泛型擦除: " + (stringListClass == integerListClass)); // true
        System.out.println("List类名: " + stringListClass.getName()); // java.util.ArrayList
    }
}
```

## 字段反射

### 获取字段信息
```java
public class FieldReflection {
    
    public static void demonstrateFieldAccess() throws Exception {
        Class<?> clazz = User.class;
        
        // 获取所有public字段（包括继承的）
        Field[] publicFields = clazz.getFields();
        System.out.println("Public字段数量: " + publicFields.length);
        
        // 获取所有声明的字段（不包括继承的）
        Field[] declaredFields = clazz.getDeclaredFields();
        System.out.println("声明的字段数量: " + declaredFields.length);
        
        for (Field field : declaredFields) {
            System.out.println("字段名: " + field.getName());
            System.out.println("字段类型: " + field.getType().getName());
            System.out.println("字段修饰符: " + Modifier.toString(field.getModifiers()));
            System.out.println("---");
        }
        
        // 获取特定字段
        try {
            Field nameField = clazz.getDeclaredField("name");
            Field ageField = clazz.getDeclaredField("age");
            
            System.out.println("name字段类型: " + nameField.getType());
            System.out.println("age字段类型: " + ageField.getType());
        } catch (NoSuchFieldException e) {
            System.out.println("字段不存在: " + e.getMessage());
        }
    }
}

// 示例类
class User {
    public static final String ROLE_USER = "USER";
    
    private Long id;
    private String name;
    private int age;
    private String email;
    protected String status;
    public String description;
    
    // 构造函数、getter和setter省略...
}
```

### 字段值的读取和设置
```java
public class FieldValueAccess {
    
    public static void demonstrateFieldValueAccess() throws Exception {
        User user = new User();
        Class<?> clazz = user.getClass();
        
        // 设置字段值
        Field nameField = clazz.getDeclaredField("name");
        nameField.setAccessible(true); // 设置可访问私有字段
        nameField.set(user, "Alice");
        
        Field ageField = clazz.getDeclaredField("age");
        ageField.setAccessible(true);
        ageField.set(user, 25);
        
        Field emailField = clazz.getDeclaredField("email");
        emailField.setAccessible(true);
        emailField.set(user, "<EMAIL>");
        
        // 读取字段值
        String name = (String) nameField.get(user);
        int age = ageField.getInt(user); // 直接获取int值
        String email = (String) emailField.get(user);
        
        System.out.println("姓名: " + name);
        System.out.println("年龄: " + age);
        System.out.println("邮箱: " + email);
        
        // 静态字段访问
        Field roleField = clazz.getDeclaredField("ROLE_USER");
        String role = (String) roleField.get(null); // 静态字段传null
        System.out.println("角色: " + role);
        
        // 字段类型检查
        System.out.println("name字段是否为String: " + (nameField.getType() == String.class));
        System.out.println("age字段是否为int: " + (ageField.getType() == int.class));
    }
}
```

### 字段注解处理
```java
public class FieldAnnotationProcessing {
    
    public static void processFieldAnnotations() throws Exception {
        Class<?> clazz = AnnotatedUser.class;
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            // 检查字段是否有特定注解
            if (field.isAnnotationPresent(Required.class)) {
                Required required = field.getAnnotation(Required.class);
                System.out.println("必填字段: " + field.getName() + 
                                 ", 消息: " + required.message());
            }
            
            if (field.isAnnotationPresent(MaxLength.class)) {
                MaxLength maxLength = field.getAnnotation(MaxLength.class);
                System.out.println("字段: " + field.getName() + 
                                 ", 最大长度: " + maxLength.value());
            }
            
            // 获取所有注解
            Annotation[] annotations = field.getAnnotations();
            if (annotations.length > 0) {
                System.out.println("字段 " + field.getName() + " 的注解:");
                for (Annotation annotation : annotations) {
                    System.out.println("  " + annotation.annotationType().getSimpleName());
                }
            }
        }
    }
}

// 注解定义
@interface Required {
    String message() default "字段不能为空";
}

@interface MaxLength {
    int value();
}

// 带注解的用户类
class AnnotatedUser {
    @Required(message = "用户名不能为空")
    @MaxLength(50)
    private String username;
    
    @Required
    private String password;
    
    @MaxLength(100)
    private String email;
    
    private int age;
}
```

## 方法反射

### 获取方法信息
```java
public class MethodReflection {

    public static void demonstrateMethodAccess() throws Exception {
        Class<?> clazz = Calculator.class;

        // 获取所有public方法（包括继承的）
        Method[] publicMethods = clazz.getMethods();
        System.out.println("Public方法数量: " + publicMethods.length);

        // 获取所有声明的方法（不包括继承的）
        Method[] declaredMethods = clazz.getDeclaredMethods();
        System.out.println("声明的方法数量: " + declaredMethods.length);

        for (Method method : declaredMethods) {
            System.out.println("方法名: " + method.getName());
            System.out.println("返回类型: " + method.getReturnType().getName());
            System.out.println("参数类型: " + Arrays.toString(method.getParameterTypes()));
            System.out.println("修饰符: " + Modifier.toString(method.getModifiers()));
            System.out.println("---");
        }

        // 获取特定方法
        Method addMethod = clazz.getMethod("add", int.class, int.class);
        Method subtractMethod = clazz.getDeclaredMethod("subtract", int.class, int.class);

        System.out.println("add方法: " + addMethod.getName());
        System.out.println("subtract方法: " + subtractMethod.getName());
    }
}

// 示例计算器类
class Calculator {

    public int add(int a, int b) {
        return a + b;
    }

    public double add(double a, double b) {
        return a + b;
    }

    private int subtract(int a, int b) {
        return a - b;
    }

    protected int multiply(int a, int b) {
        return a * b;
    }

    public static int divide(int a, int b) {
        if (b == 0) throw new IllegalArgumentException("除数不能为0");
        return a / b;
    }

    public void printResult(String operation, int result) {
        System.out.println(operation + " = " + result);
    }
}
```

### 方法调用
```java
public class MethodInvocation {

    public static void demonstrateMethodInvocation() throws Exception {
        Calculator calculator = new Calculator();
        Class<?> clazz = calculator.getClass();

        // 调用public方法
        Method addMethod = clazz.getMethod("add", int.class, int.class);
        Object result = addMethod.invoke(calculator, 10, 5);
        System.out.println("10 + 5 = " + result);

        // 调用private方法
        Method subtractMethod = clazz.getDeclaredMethod("subtract", int.class, int.class);
        subtractMethod.setAccessible(true); // 设置可访问
        Object subtractResult = subtractMethod.invoke(calculator, 10, 5);
        System.out.println("10 - 5 = " + subtractResult);

        // 调用静态方法
        Method divideMethod = clazz.getMethod("divide", int.class, int.class);
        Object divideResult = divideMethod.invoke(null, 10, 2); // 静态方法传null
        System.out.println("10 / 2 = " + divideResult);

        // 调用void方法
        Method printMethod = clazz.getMethod("printResult", String.class, int.class);
        printMethod.invoke(calculator, "计算结果", 42);

        // 处理方法重载
        Method addDoubleMethod = clazz.getMethod("add", double.class, double.class);
        Object doubleResult = addDoubleMethod.invoke(calculator, 3.14, 2.86);
        System.out.println("3.14 + 2.86 = " + doubleResult);
    }
}
```

### 方法参数和异常处理
```java
public class MethodParametersAndExceptions {

    public static void demonstrateMethodDetails() throws Exception {
        Class<?> clazz = AdvancedCalculator.class;

        // 获取方法参数信息
        Method complexMethod = clazz.getMethod("complexCalculation", String.class, int[].class, boolean.class);

        // 参数类型
        Class<?>[] parameterTypes = complexMethod.getParameterTypes();
        System.out.println("参数类型: " + Arrays.toString(parameterTypes));

        // 参数名称（需要编译时保留参数名信息）
        Parameter[] parameters = complexMethod.getParameters();
        for (Parameter parameter : parameters) {
            System.out.println("参数名: " + parameter.getName() +
                             ", 类型: " + parameter.getType().getName());
        }

        // 异常类型
        Class<?>[] exceptionTypes = complexMethod.getExceptionTypes();
        System.out.println("异常类型: " + Arrays.toString(exceptionTypes));

        // 泛型信息
        Type[] genericParameterTypes = complexMethod.getGenericParameterTypes();
        Type genericReturnType = complexMethod.getGenericReturnType();

        System.out.println("泛型参数类型: " + Arrays.toString(genericParameterTypes));
        System.out.println("泛型返回类型: " + genericReturnType);

        // 方法调用示例
        AdvancedCalculator calc = new AdvancedCalculator();
        try {
            Object result = complexMethod.invoke(calc, "test", new int[]{1, 2, 3}, true);
            System.out.println("方法调用结果: " + result);
        } catch (InvocationTargetException e) {
            System.out.println("方法执行异常: " + e.getCause().getMessage());
        }
    }
}

class AdvancedCalculator {

    public List<Integer> complexCalculation(String operation, int[] numbers, boolean sorted)
            throws IllegalArgumentException, UnsupportedOperationException {

        if (numbers == null || numbers.length == 0) {
            throw new IllegalArgumentException("数组不能为空");
        }

        List<Integer> result = new ArrayList<>();
        for (int num : numbers) {
            result.add(num * 2);
        }

        if (sorted) {
            Collections.sort(result);
        }

        return result;
    }
}
```

## 构造函数反射

### 获取和使用构造函数
```java
public class ConstructorReflection {

    public static void demonstrateConstructorAccess() throws Exception {
        Class<?> clazz = Person.class;

        // 获取所有public构造函数
        Constructor<?>[] constructors = clazz.getConstructors();
        System.out.println("Public构造函数数量: " + constructors.length);

        // 获取所有声明的构造函数
        Constructor<?>[] declaredConstructors = clazz.getDeclaredConstructors();
        System.out.println("声明的构造函数数量: " + declaredConstructors.length);

        for (Constructor<?> constructor : declaredConstructors) {
            System.out.println("构造函数参数: " + Arrays.toString(constructor.getParameterTypes()));
            System.out.println("修饰符: " + Modifier.toString(constructor.getModifiers()));
            System.out.println("---");
        }

        // 获取特定构造函数
        Constructor<?> defaultConstructor = clazz.getConstructor();
        Constructor<?> paramConstructor = clazz.getConstructor(String.class, int.class);
        Constructor<?> privateConstructor = clazz.getDeclaredConstructor(String.class);

        // 创建实例
        Object person1 = defaultConstructor.newInstance();
        Object person2 = paramConstructor.newInstance("Alice", 25);

        // 访问私有构造函数
        privateConstructor.setAccessible(true);
        Object person3 = privateConstructor.newInstance("Bob");

        System.out.println("创建的实例: " + person1);
        System.out.println("创建的实例: " + person2);
        System.out.println("创建的实例: " + person3);
    }
}

class Person {
    private String name;
    private int age;

    // 默认构造函数
    public Person() {
        this.name = "Unknown";
        this.age = 0;
    }

    // 参数构造函数
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // 私有构造函数
    private Person(String name) {
        this.name = name;
        this.age = 18;
    }

    @Override
    public String toString() {
        return "Person{name='" + name + "', age=" + age + "}";
    }
}
```

## 动态代理

### JDK动态代理
```java
public class JDKDynamicProxy {

    public static void demonstrateJDKProxy() {
        // 创建目标对象
        UserService userService = new UserServiceImpl();

        // 创建代理对象
        UserService proxy = (UserService) Proxy.newProxyInstance(
            userService.getClass().getClassLoader(),
            userService.getClass().getInterfaces(),
            new LoggingInvocationHandler(userService)
        );

        // 使用代理对象
        proxy.createUser("Alice");
        proxy.getUserById(1L);
        proxy.updateUser(1L, "Alice Updated");
        proxy.deleteUser(1L);
    }
}

// 业务接口
interface UserService {
    void createUser(String name);
    String getUserById(Long id);
    void updateUser(Long id, String name);
    void deleteUser(Long id);
}

// 业务实现类
class UserServiceImpl implements UserService {

    @Override
    public void createUser(String name) {
        System.out.println("创建用户: " + name);
    }

    @Override
    public String getUserById(Long id) {
        System.out.println("获取用户: " + id);
        return "User-" + id;
    }

    @Override
    public void updateUser(Long id, String name) {
        System.out.println("更新用户: " + id + " -> " + name);
    }

    @Override
    public void deleteUser(Long id) {
        System.out.println("删除用户: " + id);
    }
}

// 日志记录调用处理器
class LoggingInvocationHandler implements InvocationHandler {
    private final Object target;

    public LoggingInvocationHandler(Object target) {
        this.target = target;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        long startTime = System.currentTimeMillis();

        System.out.println("=== 方法调用开始 ===");
        System.out.println("方法名: " + method.getName());
        System.out.println("参数: " + Arrays.toString(args));

        try {
            // 调用目标方法
            Object result = method.invoke(target, args);

            long endTime = System.currentTimeMillis();
            System.out.println("执行时间: " + (endTime - startTime) + "ms");
            System.out.println("返回值: " + result);
            System.out.println("=== 方法调用结束 ===\n");

            return result;
        } catch (InvocationTargetException e) {
            System.out.println("方法执行异常: " + e.getCause().getMessage());
            throw e.getCause();
        }
    }
}
```

### 动态代理工厂
```java
public class ProxyFactory {

    /**
     * 创建代理对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T createProxy(T target, InvocationHandler handler) {
        return (T) Proxy.newProxyInstance(
            target.getClass().getClassLoader(),
            target.getClass().getInterfaces(),
            handler
        );
    }

    /**
     * 创建带日志的代理
     */
    public static <T> T createLoggingProxy(T target) {
        return createProxy(target, new LoggingInvocationHandler(target));
    }

    /**
     * 创建带性能监控的代理
     */
    public static <T> T createPerformanceProxy(T target) {
        return createProxy(target, new PerformanceInvocationHandler(target));
    }

    /**
     * 创建带缓存的代理
     */
    public static <T> T createCachingProxy(T target) {
        return createProxy(target, new CachingInvocationHandler(target));
    }
}

// 性能监控处理器
class PerformanceInvocationHandler implements InvocationHandler {
    private final Object target;

    public PerformanceInvocationHandler(Object target) {
        this.target = target;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        long startTime = System.nanoTime();

        try {
            Object result = method.invoke(target, args);

            long endTime = System.nanoTime();
            long duration = endTime - startTime;

            System.out.printf("方法 %s 执行时间: %.2f ms%n",
                            method.getName(), duration / 1_000_000.0);

            return result;
        } catch (InvocationTargetException e) {
            throw e.getCause();
        }
    }
}

// 缓存处理器
class CachingInvocationHandler implements InvocationHandler {
    private final Object target;
    private final Map<String, Object> cache = new ConcurrentHashMap<>();

    public CachingInvocationHandler(Object target) {
        this.target = target;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 只缓存无参数的getter方法
        if (method.getName().startsWith("get") &&
            (args == null || args.length == 0)) {

            String cacheKey = method.getName();
            Object cachedResult = cache.get(cacheKey);

            if (cachedResult != null) {
                System.out.println("从缓存返回: " + cacheKey);
                return cachedResult;
            }

            try {
                Object result = method.invoke(target, args);
                cache.put(cacheKey, result);
                System.out.println("缓存结果: " + cacheKey);
                return result;
            } catch (InvocationTargetException e) {
                throw e.getCause();
            }
        } else {
            // 非getter方法直接调用
            try {
                return method.invoke(target, args);
            } catch (InvocationTargetException e) {
                throw e.getCause();
            }
        }
    }
}
```

## CGLIB代理

### CGLIB代理简介
CGLIB（Code Generation Library）是一个强大的代码生成库，它可以在运行时动态生成字节码。与JDK动态代理不同，CGLIB可以代理没有实现接口的类。

```java
// 注意：CGLIB需要添加依赖
// <dependency>
//     <groupId>cglib</groupId>
//     <artifactId>cglib</artifactId>
//     <version>3.3.0</version>
// </dependency>

import net.sf.cglib.proxy.Enhancer;
import net.sf.cglib.proxy.MethodInterceptor;
import net.sf.cglib.proxy.MethodProxy;

public class CGLIBProxyExample {

    public static void demonstrateCGLIBProxy() {
        // 创建目标对象
        ProductService productService = new ProductService();

        // 创建CGLIB代理
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(ProductService.class);
        enhancer.setCallback(new ProductServiceInterceptor());

        ProductService proxy = (ProductService) enhancer.create();

        // 使用代理对象
        proxy.addProduct("iPhone 15");
        proxy.getProduct(1L);
        proxy.updateProduct(1L, "iPhone 15 Pro");
        proxy.deleteProduct(1L);
    }
}

// 目标类（无需实现接口）
class ProductService {

    public void addProduct(String name) {
        System.out.println("添加产品: " + name);
    }

    public String getProduct(Long id) {
        System.out.println("获取产品: " + id);
        return "Product-" + id;
    }

    public void updateProduct(Long id, String name) {
        System.out.println("更新产品: " + id + " -> " + name);
    }

    public void deleteProduct(Long id) {
        System.out.println("删除产品: " + id);
    }

    // final方法无法被代理
    public final void finalMethod() {
        System.out.println("Final方法");
    }
}

// CGLIB方法拦截器
class ProductServiceInterceptor implements MethodInterceptor {

    @Override
    public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy)
            throws Throwable {

        System.out.println("=== CGLIB代理开始 ===");
        System.out.println("方法名: " + method.getName());
        System.out.println("参数: " + Arrays.toString(args));

        long startTime = System.currentTimeMillis();

        try {
            // 调用父类方法
            Object result = proxy.invokeSuper(obj, args);

            long endTime = System.currentTimeMillis();
            System.out.println("执行时间: " + (endTime - startTime) + "ms");
            System.out.println("返回值: " + result);
            System.out.println("=== CGLIB代理结束 ===\n");

            return result;
        } catch (Exception e) {
            System.out.println("方法执行异常: " + e.getMessage());
            throw e;
        }
    }
}
```

### JDK代理 vs CGLIB代理
```java
public class ProxyComparison {

    public static void compareProxies() {
        System.out.println("=== JDK动态代理 vs CGLIB代理 ===");

        // JDK动态代理 - 需要接口
        UserService userService = new UserServiceImpl();
        UserService jdkProxy = (UserService) Proxy.newProxyInstance(
            userService.getClass().getClassLoader(),
            userService.getClass().getInterfaces(),
            new LoggingInvocationHandler(userService)
        );

        System.out.println("JDK代理类型: " + jdkProxy.getClass().getName());
        System.out.println("是否为Proxy实例: " + Proxy.isProxyClass(jdkProxy.getClass()));

        // CGLIB代理 - 无需接口
        ProductService productService = new ProductService();
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(ProductService.class);
        enhancer.setCallback(new ProductServiceInterceptor());
        ProductService cglibProxy = (ProductService) enhancer.create();

        System.out.println("CGLIB代理类型: " + cglibProxy.getClass().getName());
        System.out.println("父类: " + cglibProxy.getClass().getSuperclass().getName());

        // 性能比较
        performanceComparison(jdkProxy, cglibProxy);
    }

    private static void performanceComparison(UserService jdkProxy, ProductService cglibProxy) {
        int iterations = 1000000;

        // JDK代理性能测试
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            jdkProxy.getUserById((long) i);
        }
        long jdkTime = System.nanoTime() - startTime;

        // CGLIB代理性能测试
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            cglibProxy.getProduct((long) i);
        }
        long cglibTime = System.nanoTime() - startTime;

        System.out.printf("JDK代理执行时间: %.2f ms%n", jdkTime / 1_000_000.0);
        System.out.printf("CGLIB代理执行时间: %.2f ms%n", cglibTime / 1_000_000.0);
    }
}
```

## 实际应用

### 简单的IoC容器实现
```java
public class SimpleIoCContainer {
    private final Map<Class<?>, Object> beans = new ConcurrentHashMap<>();
    private final Map<Class<?>, Class<?>> bindings = new ConcurrentHashMap<>();

    /**
     * 绑定接口到实现类
     */
    public <T> void bind(Class<T> interfaceClass, Class<? extends T> implementationClass) {
        bindings.put(interfaceClass, implementationClass);
    }

    /**
     * 获取Bean实例
     */
    @SuppressWarnings("unchecked")
    public <T> T getInstance(Class<T> clazz) {
        // 检查是否已经创建实例
        Object instance = beans.get(clazz);
        if (instance != null) {
            return (T) instance;
        }

        // 获取实际的实现类
        Class<?> implementationClass = bindings.getOrDefault(clazz, clazz);

        try {
            // 创建实例
            instance = createInstance(implementationClass);

            // 依赖注入
            injectDependencies(instance);

            // 缓存实例
            beans.put(clazz, instance);

            return (T) instance;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create instance of " + clazz.getName(), e);
        }
    }

    private Object createInstance(Class<?> clazz) throws Exception {
        Constructor<?>[] constructors = clazz.getConstructors();

        // 查找带@Inject注解的构造函数
        for (Constructor<?> constructor : constructors) {
            if (constructor.isAnnotationPresent(Inject.class)) {
                return createInstanceWithConstructor(constructor);
            }
        }

        // 使用默认构造函数
        return clazz.newInstance();
    }

    private Object createInstanceWithConstructor(Constructor<?> constructor) throws Exception {
        Class<?>[] parameterTypes = constructor.getParameterTypes();
        Object[] parameters = new Object[parameterTypes.length];

        for (int i = 0; i < parameterTypes.length; i++) {
            parameters[i] = getInstance(parameterTypes[i]);
        }

        return constructor.newInstance(parameters);
    }

    private void injectDependencies(Object instance) throws Exception {
        Class<?> clazz = instance.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(Inject.class)) {
                field.setAccessible(true);
                Object dependency = getInstance(field.getType());
                field.set(instance, dependency);
            }
        }
    }
}

// 依赖注入注解
@interface Inject {}

// 示例服务
interface EmailService {
    void sendEmail(String to, String subject, String body);
}

class EmailServiceImpl implements EmailService {
    @Override
    public void sendEmail(String to, String subject, String body) {
        System.out.println("发送邮件到: " + to + ", 主题: " + subject);
    }
}

interface NotificationService {
    void notify(String message);
}

class NotificationServiceImpl implements NotificationService {

    @Inject
    private EmailService emailService;

    @Override
    public void notify(String message) {
        System.out.println("通知: " + message);
        emailService.sendEmail("<EMAIL>", "通知", message);
    }
}

// 使用示例
class IoCExample {
    public static void demonstrateIoC() {
        SimpleIoCContainer container = new SimpleIoCContainer();

        // 绑定接口到实现
        container.bind(EmailService.class, EmailServiceImpl.class);
        container.bind(NotificationService.class, NotificationServiceImpl.class);

        // 获取服务实例
        NotificationService notificationService = container.getInstance(NotificationService.class);
        notificationService.notify("系统启动完成");
    }
}
```

### 简单的ORM框架
```java
public class SimpleORM {

    /**
     * 将对象转换为SQL插入语句
     */
    public static String generateInsertSQL(Object obj) throws Exception {
        Class<?> clazz = obj.getClass();

        // 获取表名
        String tableName = getTableName(clazz);

        // 获取字段和值
        List<String> columns = new ArrayList<>();
        List<Object> values = new ArrayList<>();

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Column.class)) {
                field.setAccessible(true);
                Object value = field.get(obj);

                if (value != null) {
                    Column column = field.getAnnotation(Column.class);
                    String columnName = column.name().isEmpty() ? field.getName() : column.name();

                    columns.add(columnName);
                    values.add(value);
                }
            }
        }

        // 生成SQL
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        sql.append(String.join(", ", columns));
        sql.append(") VALUES (");

        for (int i = 0; i < values.size(); i++) {
            if (i > 0) sql.append(", ");
            Object value = values.get(i);
            if (value instanceof String) {
                sql.append("'").append(value).append("'");
            } else {
                sql.append(value);
            }
        }

        sql.append(")");
        return sql.toString();
    }

    /**
     * 从ResultSet创建对象
     */
    public static <T> T createFromResultSet(Class<T> clazz, Map<String, Object> resultData)
            throws Exception {

        T instance = clazz.newInstance();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(Column.class)) {
                field.setAccessible(true);

                Column column = field.getAnnotation(Column.class);
                String columnName = column.name().isEmpty() ? field.getName() : column.name();

                Object value = resultData.get(columnName);
                if (value != null) {
                    field.set(instance, value);
                }
            }
        }

        return instance;
    }

    private static String getTableName(Class<?> clazz) {
        if (clazz.isAnnotationPresent(Table.class)) {
            Table table = clazz.getAnnotation(Table.class);
            return table.name().isEmpty() ? clazz.getSimpleName().toLowerCase() : table.name();
        }
        return clazz.getSimpleName().toLowerCase();
    }
}

// ORM注解
@interface Table {
    String name() default "";
}

@interface Column {
    String name() default "";
    boolean nullable() default true;
}

@interface Id {}

// 实体类示例
@Table(name = "users")
class UserEntity {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "username")
    private String username;

    @Column(name = "email")
    private String email;

    @Column(name = "age")
    private Integer age;

    // 构造函数、getter和setter
    public UserEntity() {}

    public UserEntity(Long id, String username, String email, Integer age) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.age = age;
    }

    // getters and setters...
}

// 使用示例
class ORMExample {
    public static void demonstrateORM() throws Exception {
        UserEntity user = new UserEntity(1L, "alice", "<EMAIL>", 25);

        // 生成插入SQL
        String insertSQL = SimpleORM.generateInsertSQL(user);
        System.out.println("生成的SQL: " + insertSQL);

        // 模拟从数据库结果创建对象
        Map<String, Object> resultData = new HashMap<>();
        resultData.put("id", 2L);
        resultData.put("username", "bob");
        resultData.put("email", "<EMAIL>");
        resultData.put("age", 30);

        UserEntity userFromDB = SimpleORM.createFromResultSet(UserEntity.class, resultData);
        System.out.println("从结果集创建的对象: " + userFromDB);
    }
}
```

## 最佳实践

### 1. 反射性能优化
```java
public class ReflectionPerformanceOptimization {

    // 缓存Class对象
    private static final Map<String, Class<?>> classCache = new ConcurrentHashMap<>();

    // 缓存Method对象
    private static final Map<String, Method> methodCache = new ConcurrentHashMap<>();

    // 缓存Field对象
    private static final Map<String, Field> fieldCache = new ConcurrentHashMap<>();

    /**
     * 获取Class对象（带缓存）
     */
    public static Class<?> getClassWithCache(String className) throws ClassNotFoundException {
        return classCache.computeIfAbsent(className, name -> {
            try {
                return Class.forName(name);
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 获取Method对象（带缓存）
     */
    public static Method getMethodWithCache(Class<?> clazz, String methodName, Class<?>... parameterTypes)
            throws NoSuchMethodException {

        String key = clazz.getName() + "#" + methodName + "#" + Arrays.toString(parameterTypes);
        return methodCache.computeIfAbsent(key, k -> {
            try {
                Method method = clazz.getMethod(methodName, parameterTypes);
                method.setAccessible(true);
                return method;
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 获取Field对象（带缓存）
     */
    public static Field getFieldWithCache(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        String key = clazz.getName() + "#" + fieldName;
        return fieldCache.computeIfAbsent(key, k -> {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field;
            } catch (NoSuchFieldException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 性能测试
     */
    public static void performanceTest() throws Exception {
        int iterations = 100000;

        // 测试无缓存的反射调用
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            Class<?> clazz = Class.forName("java.lang.String");
            Method method = clazz.getMethod("length");
            method.invoke("test");
        }
        long noCacheTime = System.nanoTime() - startTime;

        // 测试有缓存的反射调用
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            Class<?> clazz = getClassWithCache("java.lang.String");
            Method method = getMethodWithCache(clazz, "length");
            method.invoke("test");
        }
        long cacheTime = System.nanoTime() - startTime;

        System.out.printf("无缓存反射时间: %.2f ms%n", noCacheTime / 1_000_000.0);
        System.out.printf("有缓存反射时间: %.2f ms%n", cacheTime / 1_000_000.0);
        System.out.printf("性能提升: %.2fx%n", (double) noCacheTime / cacheTime);
    }
}
```

### 2. 安全的反射使用
```java
public class SafeReflectionUtils {

    /**
     * 安全地调用方法
     */
    public static Object safeInvokeMethod(Object target, String methodName, Object... args) {
        try {
            Class<?> clazz = target.getClass();
            Class<?>[] parameterTypes = new Class[args.length];

            for (int i = 0; i < args.length; i++) {
                parameterTypes[i] = args[i].getClass();
            }

            Method method = clazz.getMethod(methodName, parameterTypes);
            return method.invoke(target, args);

        } catch (NoSuchMethodException e) {
            System.err.println("方法不存在: " + methodName);
            return null;
        } catch (IllegalAccessException e) {
            System.err.println("方法访问被拒绝: " + methodName);
            return null;
        } catch (InvocationTargetException e) {
            System.err.println("方法执行异常: " + e.getCause().getMessage());
            return null;
        } catch (Exception e) {
            System.err.println("反射调用异常: " + e.getMessage());
            return null;
        }
    }

    /**
     * 安全地设置字段值
     */
    public static boolean safeSetField(Object target, String fieldName, Object value) {
        try {
            Class<?> clazz = target.getClass();
            Field field = clazz.getDeclaredField(fieldName);

            // 检查字段类型兼容性
            if (!isAssignable(field.getType(), value)) {
                System.err.println("类型不兼容: " + fieldName);
                return false;
            }

            field.setAccessible(true);
            field.set(target, value);
            return true;

        } catch (NoSuchFieldException e) {
            System.err.println("字段不存在: " + fieldName);
            return false;
        } catch (IllegalAccessException e) {
            System.err.println("字段访问被拒绝: " + fieldName);
            return false;
        } catch (Exception e) {
            System.err.println("设置字段异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查类型兼容性
     */
    private static boolean isAssignable(Class<?> fieldType, Object value) {
        if (value == null) {
            return !fieldType.isPrimitive();
        }

        Class<?> valueType = value.getClass();

        // 基本类型检查
        if (fieldType.isPrimitive()) {
            return isPrimitiveAssignable(fieldType, valueType);
        }

        return fieldType.isAssignableFrom(valueType);
    }

    private static boolean isPrimitiveAssignable(Class<?> primitiveType, Class<?> valueType) {
        if (primitiveType == int.class) return valueType == Integer.class;
        if (primitiveType == long.class) return valueType == Long.class;
        if (primitiveType == double.class) return valueType == Double.class;
        if (primitiveType == float.class) return valueType == Float.class;
        if (primitiveType == boolean.class) return valueType == Boolean.class;
        if (primitiveType == char.class) return valueType == Character.class;
        if (primitiveType == byte.class) return valueType == Byte.class;
        if (primitiveType == short.class) return valueType == Short.class;
        return false;
    }
}
```

### 3. 反射工具类
```java
public class ReflectionUtils {

    /**
     * 获取所有字段（包括父类）
     */
    public static List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields;
    }

    /**
     * 获取所有方法（包括父类）
     */
    public static List<Method> getAllMethods(Class<?> clazz) {
        List<Method> methods = new ArrayList<>();

        while (clazz != null && clazz != Object.class) {
            methods.addAll(Arrays.asList(clazz.getDeclaredMethods()));
            clazz = clazz.getSuperclass();
        }

        return methods;
    }

    /**
     * 复制对象属性
     */
    public static void copyProperties(Object source, Object target) throws Exception {
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();

        List<Field> sourceFields = getAllFields(sourceClass);
        List<Field> targetFields = getAllFields(targetClass);

        Map<String, Field> targetFieldMap = targetFields.stream()
                .collect(Collectors.toMap(Field::getName, Function.identity()));

        for (Field sourceField : sourceFields) {
            Field targetField = targetFieldMap.get(sourceField.getName());

            if (targetField != null &&
                targetField.getType().isAssignableFrom(sourceField.getType())) {

                sourceField.setAccessible(true);
                targetField.setAccessible(true);

                Object value = sourceField.get(source);
                targetField.set(target, value);
            }
        }
    }

    /**
     * 查找带特定注解的字段
     */
    public static List<Field> findFieldsWithAnnotation(Class<?> clazz,
                                                      Class<? extends Annotation> annotationClass) {
        return getAllFields(clazz).stream()
                .filter(field -> field.isAnnotationPresent(annotationClass))
                .collect(Collectors.toList());
    }

    /**
     * 查找带特定注解的方法
     */
    public static List<Method> findMethodsWithAnnotation(Class<?> clazz,
                                                        Class<? extends Annotation> annotationClass) {
        return getAllMethods(clazz).stream()
                .filter(method -> method.isAnnotationPresent(annotationClass))
                .collect(Collectors.toList());
    }

    /**
     * 创建实例（支持有参构造函数）
     */
    public static <T> T createInstance(Class<T> clazz, Object... args) throws Exception {
        if (args.length == 0) {
            return clazz.newInstance();
        }

        Class<?>[] parameterTypes = Arrays.stream(args)
                .map(Object::getClass)
                .toArray(Class<?>[]::new);

        Constructor<T> constructor = clazz.getConstructor(parameterTypes);
        return constructor.newInstance(args);
    }
}
```

### 4. 代理最佳实践
```java
public class ProxyBestPractices {

    /**
     * 通用代理工厂
     */
    public static class GenericProxyFactory {

        public static <T> T createProxy(T target, ProxyHandler... handlers) {
            return createProxy(target, Arrays.asList(handlers));
        }

        @SuppressWarnings("unchecked")
        public static <T> T createProxy(T target, List<ProxyHandler> handlers) {
            return (T) Proxy.newProxyInstance(
                target.getClass().getClassLoader(),
                target.getClass().getInterfaces(),
                new CompositeInvocationHandler(target, handlers)
            );
        }
    }

    /**
     * 代理处理器接口
     */
    public interface ProxyHandler {
        Object handle(Object target, Method method, Object[] args,
                     InvocationChain chain) throws Throwable;
    }

    /**
     * 调用链
     */
    public interface InvocationChain {
        Object proceed() throws Throwable;
    }

    /**
     * 组合调用处理器
     */
    public static class CompositeInvocationHandler implements InvocationHandler {
        private final Object target;
        private final List<ProxyHandler> handlers;

        public CompositeInvocationHandler(Object target, List<ProxyHandler> handlers) {
            this.target = target;
            this.handlers = handlers;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            return new DefaultInvocationChain(target, method, args, handlers, 0).proceed();
        }
    }

    /**
     * 默认调用链实现
     */
    public static class DefaultInvocationChain implements InvocationChain {
        private final Object target;
        private final Method method;
        private final Object[] args;
        private final List<ProxyHandler> handlers;
        private final int index;

        public DefaultInvocationChain(Object target, Method method, Object[] args,
                                    List<ProxyHandler> handlers, int index) {
            this.target = target;
            this.method = method;
            this.args = args;
            this.handlers = handlers;
            this.index = index;
        }

        @Override
        public Object proceed() throws Throwable {
            if (index < handlers.size()) {
                ProxyHandler handler = handlers.get(index);
                InvocationChain nextChain = new DefaultInvocationChain(
                    target, method, args, handlers, index + 1);
                return handler.handle(target, method, args, nextChain);
            } else {
                return method.invoke(target, args);
            }
        }
    }

    // 示例处理器
    public static class LoggingHandler implements ProxyHandler {
        @Override
        public Object handle(Object target, Method method, Object[] args,
                           InvocationChain chain) throws Throwable {
            System.out.println("调用方法: " + method.getName());
            Object result = chain.proceed();
            System.out.println("方法返回: " + result);
            return result;
        }
    }

    public static class TimingHandler implements ProxyHandler {
        @Override
        public Object handle(Object target, Method method, Object[] args,
                           InvocationChain chain) throws Throwable {
            long start = System.nanoTime();
            Object result = chain.proceed();
            long duration = System.nanoTime() - start;
            System.out.printf("方法 %s 执行时间: %.2f ms%n",
                            method.getName(), duration / 1_000_000.0);
            return result;
        }
    }
}
```

### 5. 注意事项和限制

#### 反射的限制
```java
public class ReflectionLimitations {

    public static void demonstrateLimitations() {
        System.out.println("=== 反射的限制 ===");

        // 1. 性能开销
        System.out.println("1. 反射调用比直接调用慢10-100倍");

        // 2. 安全限制
        System.out.println("2. SecurityManager可能禁止反射操作");

        // 3. 破坏封装
        System.out.println("3. 可以访问私有成员，破坏封装性");

        // 4. 编译时检查
        System.out.println("4. 失去编译时类型检查，容易出现运行时错误");

        // 5. 代码可读性
        System.out.println("5. 反射代码难以理解和维护");
    }
}
```

#### 代理的限制
```java
public class ProxyLimitations {

    public static void demonstrateProxyLimitations() {
        System.out.println("=== 代理的限制 ===");

        // 1. JDK动态代理只能代理接口
        System.out.println("1. JDK动态代理只能代理实现了接口的类");

        // 2. CGLIB不能代理final类和方法
        System.out.println("2. CGLIB不能代理final类和final方法");

        // 3. 性能开销
        System.out.println("3. 代理调用有额外的性能开销");

        // 4. 调试困难
        System.out.println("4. 代理对象的调试比较困难");

        // 5. 内存占用
        System.out.println("5. 代理对象会增加内存占用");
    }
}
```

## 总结

Java反射和代理是强大的特性，它们为框架开发、依赖注入、AOP等提供了基础支持。但是使用时需要注意：

### 反射使用建议
1. **缓存反射对象** - 避免重复获取Class、Method、Field对象
2. **异常处理** - 妥善处理反射可能抛出的各种异常
3. **性能考虑** - 在性能敏感的场景中谨慎使用反射
4. **安全检查** - 验证输入参数，避免安全漏洞
5. **可读性** - 添加充分的注释，提高代码可读性

### 代理使用建议
1. **选择合适的代理方式** - 有接口用JDK代理，无接口用CGLIB
2. **避免过度代理** - 不要为了代理而代理
3. **处理器链模式** - 使用责任链模式组合多个处理器
4. **缓存代理对象** - 避免重复创建代理对象
5. **异常传播** - 正确处理和传播异常

反射和代理是Java高级特性的重要组成部分，掌握它们对于理解和使用各种Java框架至关重要。
