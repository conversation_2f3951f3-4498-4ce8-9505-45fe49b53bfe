package com.example.service.impl;

import com.example.entity.User;
import com.example.dto.UserQueryDTO;
import com.example.vo.PageResult;
import com.example.mapper.UserMapper;
import com.example.service.UserService;
import com.example.util.PasswordUtil;
import com.example.util.ValidationUtil;
import com.example.exception.BusinessException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
@CacheConfig(cacheNames = "users")
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;
    
    // ===== 基本CRUD操作 =====
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(key = "#id", unless = "#result == null")
    public User getUserById(Long id) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        logger.info("从数据库查询用户: {}", id);
        return userMapper.selectById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "users", key = "'username:' + #username", unless = "#result == null")
    public User getUserByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            throw new BusinessException("用户名不能为空");
        }
        logger.info("从数据库查询用户: {}", username);
        return userMapper.selectByUsername(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "users", key = "'email:' + #email", unless = "#result == null")
    public User getUserByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            throw new BusinessException("邮箱不能为空");
        }
        logger.info("从数据库查询用户: {}", email);
        return userMapper.selectByEmail(email);
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "users", key = "'phone:' + #phone", unless = "#result == null")
    public User getUserByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            throw new BusinessException("手机号不能为空");
        }
        logger.info("从数据库查询用户: {}", phone);
        return userMapper.selectByPhone(phone);
    }
    
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "userList", key = "'all'")
    public List<User> getAllUsers() {
        logger.info("从数据库查询所有用户");
        return userMapper.selectAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByCondition(UserQueryDTO query) {
        if (query == null) {
            query = new UserQueryDTO();
        }
        query.setDefaults();
        return userMapper.selectByCondition(query);
    }
    
    @Override
    @Transactional(readOnly = true)
    public PageResult<User> getUsersByPage(UserQueryDTO query) {
        if (query == null) {
            query = new UserQueryDTO();
        }
        query.setDefaults();
        
        // 查询总数
        int total = userMapper.countByCondition(query);
        
        // 查询数据
        List<User> list = userMapper.selectByCondition(query);
        
        return PageResult.<User>builder()
                .pageNum(query.getPageNum())
                .pageSize(query.getPageSize())
                .total(total)
                .list(list)
                .build();
    }
    
    @Override
    @Caching(
        evict = @CacheEvict(value = "userList", allEntries = true)
    )
    public boolean saveUser(User user) {
        if (user == null) {
            throw new BusinessException("用户信息不能为空");
        }
        
        // 验证用户信息
        validateUser(user, true);
        
        // 设置默认值
        setUserDefaults(user);
        
        // 加密密码
        if (StringUtils.hasText(user.getPassword())) {
            String salt = PasswordUtil.generateSalt();
            String encryptedPassword = PasswordUtil.encrypt(user.getPassword(), salt);
            user.setPassword(encryptedPassword);
            user.setSalt(salt);
        }
        
        try {
            int result = userMapper.insert(user);
            if (result > 0) {
                logger.info("用户创建成功: {}", user.getUsername());
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户创建失败: {}", user.getUsername(), e);
            throw new BusinessException("用户创建失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean batchSaveUsers(List<User> users) {
        if (users == null || users.isEmpty()) {
            throw new BusinessException("用户列表不能为空");
        }
        
        // 验证所有用户信息
        for (User user : users) {
            validateUser(user, true);
            setUserDefaults(user);
            
            // 加密密码
            if (StringUtils.hasText(user.getPassword())) {
                String salt = PasswordUtil.generateSalt();
                String encryptedPassword = PasswordUtil.encrypt(user.getPassword(), salt);
                user.setPassword(encryptedPassword);
                user.setSalt(salt);
            }
        }
        
        try {
            int result = userMapper.batchInsert(users);
            if (result > 0) {
                logger.info("批量创建用户成功，数量: {}", users.size());
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("批量创建用户失败", e);
            throw new BusinessException("批量创建用户失败: " + e.getMessage());
        }
    }
    
    @Override
    @Caching(
        put = @CachePut(value = "users", key = "#user.id"),
        evict = {
            @CacheEvict(value = "users", key = "'username:' + #user.username"),
            @CacheEvict(value = "users", key = "'email:' + #user.email"),
            @CacheEvict(value = "users", key = "'phone:' + #user.phone"),
            @CacheEvict(value = "userList", allEntries = true)
        }
    )
    public boolean updateUser(User user) {
        if (user == null || user.getId() == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 检查用户是否存在
        User existingUser = userMapper.selectById(user.getId());
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 验证用户信息
        validateUser(user, false);
        
        // 设置更新信息
        user.setUpdateTime(LocalDateTime.now());
        
        try {
            int result = userMapper.update(user);
            if (result > 0) {
                logger.info("用户更新成功: {}", user.getUsername());
                return true;
            } else {
                throw new BusinessException("用户更新失败，可能是版本冲突");
            }
        } catch (Exception e) {
            logger.error("用户更新失败: {}", user.getUsername(), e);
            throw new BusinessException("用户更新失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean updateUserSelective(User user) {
        if (user == null || user.getId() == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 检查用户是否存在
        User existingUser = userMapper.selectById(user.getId());
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 设置更新信息
        user.setUpdateTime(LocalDateTime.now());
        
        try {
            int result = userMapper.updateSelective(user);
            if (result > 0) {
                logger.info("用户选择性更新成功: ID={}", user.getId());
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户选择性更新失败: ID={}", user.getId(), e);
            throw new BusinessException("用户更新失败: " + e.getMessage());
        }
    }
    
    @Override
    @Caching(
        evict = {
            @CacheEvict(value = "users", key = "#id"),
            @CacheEvict(value = "userList", allEntries = true)
        }
    )
    public boolean deleteUser(Long id) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        try {
            int result = userMapper.deleteById(id);
            if (result > 0) {
                logger.info("用户删除成功: ID={}", id);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户删除失败: ID={}", id, e);
            throw new BusinessException("用户删除失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean batchDeleteUsers(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("用户ID列表不能为空");
        }
        
        try {
            int result = userMapper.batchDelete(ids);
            if (result > 0) {
                logger.info("批量删除用户成功，数量: {}", result);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("批量删除用户失败", e);
            throw new BusinessException("批量删除用户失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean logicDeleteUser(Long id, Long operatorId) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        try {
            int result = userMapper.logicDelete(id, operatorId);
            if (result > 0) {
                logger.info("用户逻辑删除成功: ID={}, 操作人: {}", id, operatorId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户逻辑删除失败: ID={}", id, e);
            throw new BusinessException("用户删除失败: " + e.getMessage());
        }
    }
    
    // ===== 私有辅助方法 =====
    
    /**
     * 验证用户信息
     */
    private void validateUser(User user, boolean isCreate) {
        // 验证用户名
        if (!StringUtils.hasText(user.getUsername())) {
            throw new BusinessException("用户名不能为空");
        }
        if (!ValidationUtil.isValidUsername(user.getUsername())) {
            throw new BusinessException("用户名格式不正确");
        }
        
        // 验证邮箱
        if (!StringUtils.hasText(user.getEmail())) {
            throw new BusinessException("邮箱不能为空");
        }
        if (!ValidationUtil.isValidEmail(user.getEmail())) {
            throw new BusinessException("邮箱格式不正确");
        }
        
        // 验证密码（仅创建时）
        if (isCreate && !StringUtils.hasText(user.getPassword())) {
            throw new BusinessException("密码不能为空");
        }
        if (StringUtils.hasText(user.getPassword()) && !ValidationUtil.isValidPassword(user.getPassword())) {
            throw new BusinessException("密码格式不正确");
        }
        
        // 验证手机号
        if (StringUtils.hasText(user.getPhone()) && !ValidationUtil.isValidPhone(user.getPhone())) {
            throw new BusinessException("手机号格式不正确");
        }
        
        // 检查唯一性
        Long excludeId = isCreate ? null : user.getId();
        if (isUsernameExists(user.getUsername(), excludeId)) {
            throw new BusinessException("用户名已存在");
        }
        if (isEmailExists(user.getEmail(), excludeId)) {
            throw new BusinessException("邮箱已存在");
        }
        if (StringUtils.hasText(user.getPhone()) && isPhoneExists(user.getPhone(), excludeId)) {
            throw new BusinessException("手机号已存在");
        }
    }
    
    /**
     * 设置用户默认值
     */
    private void setUserDefaults(User user) {
        LocalDateTime now = LocalDateTime.now();
        
        if (user.getCreateTime() == null) {
            user.setCreateTime(now);
        }
        if (user.getUpdateTime() == null) {
            user.setUpdateTime(now);
        }
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        if (user.getEmailVerified() == null) {
            user.setEmailVerified(0); // 默认未验证
        }
        if (user.getPhoneVerified() == null) {
            user.setPhoneVerified(0); // 默认未验证
        }
        if (user.getLoginCount() == null) {
            user.setLoginCount(0); // 默认登录次数为0
        }
        if (user.getVersion() == null) {
            user.setVersion(1); // 默认版本号为1
        }
        if (user.getDeleted() == null) {
            user.setDeleted(0); // 默认未删除
        }
    }

    // ===== 关联查询 =====

    @Override
    @Transactional(readOnly = true)
    public User getUserWithRoles(Long id) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        return userMapper.selectUserWithRoles(id);
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserWithPermissions(Long id) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        return userMapper.selectUserWithPermissions(id);
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserWithRolesAndPermissions(Long id) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        return userMapper.selectUserWithRolesAndPermissions(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersWithRoles() {
        return userMapper.selectUsersWithRoles();
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByRoleId(Long roleId) {
        if (roleId == null) {
            throw new BusinessException("角色ID不能为空");
        }
        return userMapper.selectUsersByRoleId(roleId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            throw new BusinessException("角色编码不能为空");
        }
        return userMapper.selectUsersByRoleCode(roleCode);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByPermissionCode(String permissionCode) {
        if (!StringUtils.hasText(permissionCode)) {
            throw new BusinessException("权限编码不能为空");
        }
        return userMapper.selectUsersByPermissionCode(permissionCode);
    }

    // ===== 统计查询 =====

    @Override
    @Transactional(readOnly = true)
    public int getUserCount() {
        return userMapper.count();
    }

    @Override
    @Transactional(readOnly = true)
    public int getUserCountByCondition(UserQueryDTO query) {
        if (query == null) {
            query = new UserQueryDTO();
        }
        query.setDefaults();
        return userMapper.countByCondition(query);
    }

    @Override
    @Transactional(readOnly = true)
    public int getUserCountByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return userMapper.countByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public int getTodayNewUserCount() {
        return userMapper.countTodayNew();
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getUserStatistics() {
        return userMapper.getUserStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getAgeDistribution() {
        return userMapper.getAgeDistribution();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getGenderDistribution() {
        return userMapper.getGenderDistribution();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getRegistrationTrend(int days) {
        if (days <= 0) {
            throw new BusinessException("统计天数必须大于0");
        }
        return userMapper.getRegistrationTrend(days);
    }

    // ===== 业务操作 =====

    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameExists(String username, Long excludeId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return userMapper.existsByUsername(username, excludeId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isEmailExists(String email, Long excludeId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return userMapper.existsByEmail(email, excludeId);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isPhoneExists(String phone, Long excludeId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return userMapper.existsByPhone(phone, excludeId);
    }

    @Override
    public boolean updateUserStatus(Long id, Integer status, Long operatorId) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        if (status < 0 || status > 2) {
            throw new BusinessException("状态值不正确");
        }

        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        try {
            int result = userMapper.updateStatus(id, status, operatorId);
            if (result > 0) {
                logger.info("用户状态更新成功: ID={}, 状态={}, 操作人={}", id, status, operatorId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户状态更新失败: ID={}", id, e);
            throw new BusinessException("用户状态更新失败: " + e.getMessage());
        }
    }

    @Override
    public boolean updateUserPassword(Long id, String newPassword, Long operatorId) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (!StringUtils.hasText(newPassword)) {
            throw new BusinessException("新密码不能为空");
        }
        if (!ValidationUtil.isValidPassword(newPassword)) {
            throw new BusinessException("密码格式不正确");
        }

        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 加密新密码
        String salt = PasswordUtil.generateSalt();
        String encryptedPassword = PasswordUtil.encrypt(newPassword, salt);

        try {
            int result = userMapper.updatePassword(id, encryptedPassword, salt, operatorId);
            if (result > 0) {
                logger.info("用户密码更新成功: ID={}, 操作人={}", id, operatorId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户密码更新失败: ID={}", id, e);
            throw new BusinessException("密码更新失败: " + e.getMessage());
        }
    }

    @Override
    public String resetUserPassword(Long id, Long operatorId) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 生成新密码
        String newPassword = generateRandomPassword();

        // 更新密码
        boolean result = updateUserPassword(id, newPassword, operatorId);
        if (result) {
            logger.info("用户密码重置成功: ID={}, 操作人={}", id, operatorId);
            return newPassword;
        } else {
            throw new BusinessException("密码重置失败");
        }
    }

    @Override
    public boolean verifyUserEmail(Long id, Long operatorId) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        try {
            int result = userMapper.verifyEmail(id, operatorId);
            if (result > 0) {
                logger.info("用户邮箱验证成功: ID={}, 操作人={}", id, operatorId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户邮箱验证失败: ID={}", id, e);
            throw new BusinessException("邮箱验证失败: " + e.getMessage());
        }
    }

    @Override
    public boolean verifyUserPhone(Long id, Long operatorId) {
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 检查用户是否存在
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }

        try {
            int result = userMapper.verifyPhone(id, operatorId);
            if (result > 0) {
                logger.info("用户手机验证成功: ID={}, 操作人={}", id, operatorId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户手机验证失败: ID={}", id, e);
            throw new BusinessException("手机验证失败: " + e.getMessage());
        }
    }

    @Override
    public User login(String username, String password, String loginIp) {
        if (!StringUtils.hasText(username)) {
            throw new BusinessException("用户名不能为空");
        }
        if (!StringUtils.hasText(password)) {
            throw new BusinessException("密码不能为空");
        }

        // 查询用户
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            // 也尝试用邮箱登录
            user = userMapper.selectByEmail(username);
            if (user == null) {
                // 也尝试用手机号登录
                user = userMapper.selectByPhone(username);
                if (user == null) {
                    throw new BusinessException("用户不存在");
                }
            }
        }

        // 检查用户状态
        if (user.getStatus() == null || user.getStatus() == 0) {
            throw new BusinessException("用户已被禁用");
        }
        if (user.getStatus() == 2) {
            throw new BusinessException("用户已被锁定");
        }

        // 验证密码
        if (!PasswordUtil.verify(password, user.getPassword(), user.getSalt())) {
            throw new BusinessException("密码错误");
        }

        // 更新登录信息
        try {
            userMapper.updateLastLogin(user.getId(), LocalDateTime.now(), loginIp);
            logger.info("用户登录成功: {}, IP: {}", user.getUsername(), loginIp);

            // 清除敏感信息
            user.setPassword(null);
            user.setSalt(null);

            return user;
        } catch (Exception e) {
            logger.error("更新用户登录信息失败: {}", user.getUsername(), e);
            // 登录成功但更新登录信息失败，仍然返回用户信息
            user.setPassword(null);
            user.setSalt(null);
            return user;
        }
    }

    @Override
    public boolean logout(Long userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 这里可以添加登出逻辑，比如清除缓存、记录登出日志等
        logger.info("用户登出: ID={}", userId);
        return true;
    }

    // ===== 私有辅助方法 =====

    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        return UUID.randomUUID().toString().substring(0, 8);
    }
}
