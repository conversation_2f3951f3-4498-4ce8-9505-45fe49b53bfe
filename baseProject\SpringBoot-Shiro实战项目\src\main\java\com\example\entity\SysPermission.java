package com.example.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统权限实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class SysPermission implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 权限ID
     */
    private Long id;
    
    /**
     * 权限名称
     */
    private String permissionName;
    
    /**
     * 权限编码
     */
    private String permissionCode;
    
    /**
     * 权限类型：MENU-菜单，BUTTON-按钮
     */
    private String permissionType;
    
    /**
     * 权限URL
     */
    private String url;
    
    /**
     * 父权限ID
     */
    private Long parentId;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 图标
     */
    private String icon;
    
    /**
     * 权限描述
     */
    private String description;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private Long createBy;
    
    /**
     * 更新人
     */
    private Long updateBy;
    
    /**
     * 子权限列表（树形结构时使用）
     */
    private List<SysPermission> children;
    
    /**
     * 判断权限是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    /**
     * 判断是否为菜单权限
     */
    public boolean isMenu() {
        return "MENU".equals(permissionType);
    }
    
    /**
     * 判断是否为按钮权限
     */
    public boolean isButton() {
        return "BUTTON".equals(permissionType);
    }
    
    /**
     * 判断是否为顶级权限
     */
    public boolean isTopLevel() {
        return parentId == null || parentId == 0L;
    }
}
