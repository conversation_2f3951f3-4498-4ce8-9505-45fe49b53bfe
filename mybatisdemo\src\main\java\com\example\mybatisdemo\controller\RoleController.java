package com.example.mybatisdemo.controller;

import com.example.mybatisdemo.common.result.Result;
import com.example.mybatisdemo.dto.PageResult;
import com.example.mybatisdemo.dto.RoleQueryDTO;
import com.example.mybatisdemo.entity.Role;
import com.example.mybatisdemo.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

@RestController
@RequestMapping("/api/roles")
public class RoleController {
    @Autowired
    private RoleService roleService;

    /**
     * 分页查询角色列表（支持多条件查询和搜索）
     *
     * 支持的查询方式：
     * 1. 分页查询：GET /api/roles?page=1&size=10
     * 2. 条件查询：GET /api/roles?status=1&roleName=管理员&page=1&size=10
     * 3. 关键字搜索：GET /api/roles?keyword=管理&page=1&size=10
     * 4. 组合查询：GET /api/roles?keyword=管理&status=1&page=1&size=10
     * 5. 排序查询：GET /api/roles?sortField=createTime&sortDirection=desc&page=1&size=10
     */
    @GetMapping
    public Result<PageResult<Role>> getRoles(@Valid RoleQueryDTO queryDTO) {
        PageResult<Role> pageResult = roleService.getRolesByPage(queryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询所有角色（不分页，用于下拉选择等场景）
     */
    @GetMapping("/all")
    public Result<List<Role>> getAllRoles() {
        List<Role> roles = roleService.getAllRoles();
        return Result.success(roles);
    }

    @PostMapping
    public Result<Role> createRole(@Valid @RequestBody Role role) {
        Role createdRole = roleService.createRole(role);
        return Result.success("角色创建成功", createdRole);
    }



}
