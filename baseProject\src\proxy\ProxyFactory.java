package proxy;

import java.lang.reflect.Proxy;
import java.lang.reflect.InvocationHandler;
import java.util.Arrays;
import java.util.List;

/**
 * 代理工厂类
 */
public class ProxyFactory {
    
    @SuppressWarnings("unchecked")
    public static <T> T createProxy(T target, InvocationHandler handler) {
        return (T) Proxy.newProxyInstance(
            target.getClass().getClassLoader(),
            target.getClass().getInterfaces(),
            handler
        );
    }
    
    public static <T> T createLoggingProxy(T target) {
        return createProxy(target, new LoggingInvocationHandler(target));
    }
    
    public static <T> T createPerformanceProxy(T target) {
        return createProxy(target, new PerformanceInvocationHandler(target));
    }
    
    public static <T> T createCachingProxy(T target) {
        return createProxy(target, new CachingInvocationHandler(target));
    }
    
    public static <T> T createTransactionProxy(T target) {
        return createProxy(target, new TransactionInvocation<PERSON><PERSON><PERSON>(target));
    }
    
    public static <T> T createCompositeProxy(T target, ProxyHandler... handlers) {
        return createProxy(target, new CompositeInvocationHandler(target, Arrays.asList(handlers)));
    }
}
