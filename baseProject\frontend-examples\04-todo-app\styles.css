/* 待办事项应用样式文件 */

/* ===== CSS变量 ===== */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    
    --transition: all 0.2s ease-in-out;
}

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
}

/* ===== 应用容器 ===== */
.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* ===== 应用头部 ===== */
.app-header {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: 30px;
    overflow: hidden;
}

.header-content {
    padding: 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.app-title i {
    font-size: 2rem;
}

.header-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 500;
}

.stat-item i {
    opacity: 0.8;
}

/* ===== 主要内容 ===== */
.main-content {
    display: grid;
    gap: 30px;
}

/* ===== 添加任务区域 ===== */
.add-task-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 30px;
}

.input-group {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 15px;
    margin-bottom: 20px;
}

#task-input {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 16px;
    transition: var(--transition);
}

#task-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#priority-select, #due-date {
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    font-size: 14px;
    min-width: 120px;
}

.add-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.add-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-add {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-add span {
    color: var(--text-secondary);
    font-weight: 500;
}

.quick-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.quick-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== 过滤区域 ===== */
.filter-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 25px;
}

.filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-tabs {
    display: flex;
    gap: 5px;
}

.filter-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 10px 16px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-btn:hover {
    background: var(--bg-tertiary);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.search-sort {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 12px;
    color: var(--text-muted);
    z-index: 1;
}

#search-input {
    padding: 10px 16px 10px 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    width: 200px;
    transition: var(--transition);
}

#search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-btn {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
}

.clear-btn:hover {
    background: var(--bg-secondary);
}

#sort-select {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    font-size: 14px;
}

.sort-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 10px 12px;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
}

.sort-btn:hover {
    background: var(--bg-tertiary);
}

/* ===== 任务区域 ===== */
.tasks-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    min-height: 400px;
}

.tasks-container {
    padding: 30px;
}

/* ===== 空状态 ===== */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.empty-add-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.empty-add-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* ===== 任务列表 ===== */
.tasks-list {
    display: grid;
    gap: 15px;
}

.task-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 20px;
    transition: var(--transition);
    position: relative;
}

.task-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.task-item.completed {
    opacity: 0.7;
    background: var(--bg-tertiary);
}

.task-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.task-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    margin-top: 2px;
    cursor: pointer;
}

.task-content {
    flex: 1;
}

.task-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    line-height: 1.5;
}

.task-item.completed .task-text {
    text-decoration: line-through;
    color: var(--text-muted);
}

.task-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 14px;
    color: var(--text-secondary);
}

.priority-badge {
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-badge.high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.priority-badge.medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.priority-badge.low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.due-date {
    display: flex;
    align-items: center;
    gap: 4px;
}

.due-date.overdue {
    color: var(--danger-color);
    font-weight: 600;
}

.due-date.today {
    color: var(--warning-color);
    font-weight: 600;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.task-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: 8px 10px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.task-btn:hover {
    background: var(--bg-tertiary);
}

.task-btn.edit:hover {
    background: var(--info-color);
    color: white;
    border-color: var(--info-color);
}

.task-btn.delete:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

/* ===== 批量操作 ===== */
.bulk-actions {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: 20px;
    border: 2px solid var(--primary-color);
}

.bulk-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.bulk-info {
    font-weight: 600;
    color: var(--primary-color);
}

.bulk-buttons {
    display: flex;
    gap: 10px;
}

.bulk-btn {
    padding: 8px 16px;
    border: none;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
}

.bulk-btn.complete {
    background: var(--success-color);
    color: white;
}

.bulk-btn.delete {
    background: var(--danger-color);
    color: white;
}

.bulk-btn.cancel {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.bulk-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

/* ===== 侧边栏 ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--bg-primary);
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    z-index: 1000;
    overflow-y: auto;
}

.sidebar.open {
    right: 0;
}

.sidebar-header {
    padding: 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-content {
    padding: 30px;
}

.sidebar-content h4 {
    margin-bottom: 20px;
    color: var(--text-primary);
    font-size: 1.2rem;
}

/* ===== 进度圆环 ===== */
.progress-section {
    margin-bottom: 40px;
    text-align: center;
}

.progress-circle {
    position: relative;
    display: inline-block;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: var(--bg-tertiary);
    stroke-width: 8;
    stroke-dasharray: 314;
    stroke-dashoffset: 314;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== 优先级统计 ===== */
.priority-section {
    margin-bottom: 40px;
}

.priority-stats {
    display: grid;
    gap: 15px;
}

.priority-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: var(--radius-md);
    border-left: 4px solid;
}

.priority-item.high {
    background: rgba(239, 68, 68, 0.05);
    border-color: var(--danger-color);
}

.priority-item.medium {
    background: rgba(245, 158, 11, 0.05);
    border-color: var(--warning-color);
}

.priority-item.low {
    background: rgba(16, 185, 129, 0.05);
    border-color: var(--success-color);
}

.priority-count {
    font-weight: 700;
    font-size: 1.2rem;
}

/* ===== 今日统计 ===== */
.today-section {
    margin-bottom: 40px;
}

.today-stats {
    display: grid;
    gap: 15px;
}

.today-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.today-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* ===== 操作按钮 ===== */
.actions-section {
    display: grid;
    gap: 15px;
}

.action-btn {
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
}

.action-btn:hover {
    background: var(--bg-tertiary);
}

.action-btn.danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.action-btn.danger:hover {
    background: var(--danger-color);
    color: white;
}

/* ===== 浮动按钮 ===== */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 999;
}

.float-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    background: var(--primary-color);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.float-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 15px 25px -5px rgba(0, 0, 0, 0.2);
}

/* ===== 模态框 ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-content.small {
    max-width: 400px;
}

.modal-header {
    padding: 25px 30px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 0 30px 30px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* ===== 表单样式 ===== */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* ===== 按钮样式 ===== */
.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary:hover {
    background: var(--secondary-color);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.btn-danger:hover {
    background: #dc2626;
}

/* ===== 通知样式 ===== */
.notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 16px 20px;
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: 12px;
    animation: slideInRight 0.3s ease;
    border-left: 4px solid;
}

.notification.success {
    border-color: var(--success-color);
}

.notification.error {
    border-color: var(--danger-color);
}

.notification.info {
    border-color: var(--info-color);
}

.notification.warning {
    border-color: var(--warning-color);
}

.notification i {
    font-size: 1.2rem;
}

.notification.success i {
    color: var(--success-color);
}

.notification.error i {
    color: var(--danger-color);
}

.notification.info i {
    color: var(--info-color);
}

.notification.warning i {
    color: var(--warning-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.notification-message {
    font-size: 14px;
    color: var(--text-secondary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.notification-close:hover {
    background: var(--bg-secondary);
}

/* ===== 动画 ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
    .sidebar {
        width: 350px;
        right: -350px;
    }
    
    .floating-buttons {
        bottom: 20px;
        right: 20px;
    }
}

@media (max-width: 768px) {
    .app-container {
        padding: 15px;
    }
    
    .header-content {
        padding: 20px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .header-stats {
        gap: 20px;
    }
    
    .input-group {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .filter-container {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
    }
    
    .search-sort {
        flex-direction: column;
        gap: 10px;
    }
    
    #search-input {
        width: 100%;
    }
    
    .task-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .task-actions {
        align-self: flex-start;
    }
    
    .bulk-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .bulk-buttons {
        width: 100%;
        justify-content: center;
    }
    
    .sidebar {
        width: 100%;
        right: -100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .notifications {
        top: 10px;
        right: 10px;
        left: 10px;
    }
    
    .notification {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .app-container {
        padding: 10px;
    }
    
    .header-content {
        padding: 15px;
    }
    
    .app-title {
        font-size: 1.5rem;
    }
    
    .header-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .tasks-container,
    .add-task-section,
    .filter-section {
        padding: 20px;
    }
    
    .task-item {
        padding: 15px;
    }
    
    .floating-buttons {
        bottom: 15px;
        right: 15px;
    }
    
    .float-btn {
        width: 48px;
        height: 48px;
        font-size: 1rem;
    }
}
