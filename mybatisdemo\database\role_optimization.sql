-- 企业级角色管理数据库优化脚本

-- ==================== 索引优化 ====================

-- 基础查询索引
CREATE INDEX IF NOT EXISTS idx_role_name ON role(role_name);
CREATE INDEX IF NOT EXISTS idx_role_code ON role(role_code);
CREATE INDEX IF NOT EXISTS idx_role_status ON role(status);
CREATE INDEX IF NOT EXISTS idx_role_create_time ON role(create_time);
CREATE INDEX IF NOT EXISTS idx_role_update_time ON role(update_time);

-- 层级查询索引
CREATE INDEX IF NOT EXISTS idx_role_parent_id ON role(parent_id);
CREATE INDEX IF NOT EXISTS idx_role_level ON role(level);
CREATE INDEX IF NOT EXISTS idx_role_sort_order ON role(sort_order);

-- 复合索引（提高复杂查询性能）
CREATE INDEX IF NOT EXISTS idx_role_status_level ON role(status, level);
CREATE INDEX IF NOT EXISTS idx_role_parent_status ON role(parent_id, status);
CREATE INDEX IF NOT EXISTS idx_role_status_create_time ON role(status, create_time);
CREATE INDEX IF NOT EXISTS idx_role_level_sort ON role(level, sort_order);

-- 用户操作索引
CREATE INDEX IF NOT EXISTS idx_role_create_user ON role(create_user);
CREATE INDEX IF NOT EXISTS idx_role_update_user ON role(update_user);

-- 全文搜索索引（MySQL 5.7+）
-- ALTER TABLE role ADD FULLTEXT(role_name, role_desc);

-- ==================== 统计信息更新 ====================

-- 更新表统计信息
ANALYZE TABLE role;

-- ==================== 分区表设置（可选，大数据量时使用） ====================

-- 按创建时间分区（年度分区）
-- ALTER TABLE role PARTITION BY RANGE (YEAR(create_time)) (
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ==================== 视图创建 ====================

-- 活跃角色视图
CREATE OR REPLACE VIEW v_active_roles AS
SELECT 
    id, role_name, role_code, role_desc, parent_id, level, sort_order,
    create_time, update_time
FROM role 
WHERE status = 1 
ORDER BY level ASC, sort_order ASC, create_time DESC;

-- 角色层次视图
CREATE OR REPLACE VIEW v_role_hierarchy AS
SELECT 
    r1.id,
    r1.role_name,
    r1.role_code,
    r1.level,
    r1.parent_id,
    r2.role_name AS parent_name,
    r1.status
FROM role r1
LEFT JOIN role r2 ON r1.parent_id = r2.id
ORDER BY r1.level, r1.sort_order;

-- 角色统计视图
CREATE OR REPLACE VIEW v_role_statistics AS
SELECT 
    COUNT(*) AS total_roles,
    COUNT(CASE WHEN status = 1 THEN 1 END) AS active_roles,
    COUNT(CASE WHEN status = 0 THEN 1 END) AS disabled_roles,
    MAX(level) AS max_level,
    AVG(level) AS avg_level
FROM role;

-- ==================== 存储过程 ====================

-- 获取角色子树的存储过程
DELIMITER //
CREATE PROCEDURE GetRoleSubtree(IN root_id BIGINT)
BEGIN
    WITH RECURSIVE role_tree AS (
        -- 根节点
        SELECT id, role_name, role_code, parent_id, level, 0 as depth
        FROM role 
        WHERE id = root_id
        
        UNION ALL
        
        -- 递归查找子节点
        SELECT r.id, r.role_name, r.role_code, r.parent_id, r.level, rt.depth + 1
        FROM role r
        INNER JOIN role_tree rt ON r.parent_id = rt.id
        WHERE r.status = 1
    )
    SELECT * FROM role_tree ORDER BY depth, level, role_name;
END //
DELIMITER ;

-- 批量更新角色状态的存储过程
DELIMITER //
CREATE PROCEDURE BatchUpdateRoleStatus(
    IN role_ids TEXT,
    IN new_status TINYINT,
    IN operator_id BIGINT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 更新角色状态
    SET @sql = CONCAT('UPDATE role SET status = ', new_status, 
                     ', update_user = ', operator_id,
                     ', update_time = NOW() WHERE id IN (', role_ids, ')');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    COMMIT;
END //
DELIMITER ;

-- ==================== 触发器 ====================

-- 角色更新时间触发器
DELIMITER //
CREATE TRIGGER tr_role_update_time 
    BEFORE UPDATE ON role
    FOR EACH ROW
BEGIN
    SET NEW.update_time = NOW();
END //
DELIMITER ;

-- 角色层级验证触发器
DELIMITER //
CREATE TRIGGER tr_role_level_check 
    BEFORE INSERT ON role
    FOR EACH ROW
BEGIN
    DECLARE parent_level INT DEFAULT 0;
    
    IF NEW.parent_id IS NOT NULL THEN
        SELECT level INTO parent_level FROM role WHERE id = NEW.parent_id;
        SET NEW.level = parent_level + 1;
    ELSE
        SET NEW.level = 1;
    END IF;
    
    -- 限制最大层级
    IF NEW.level > 10 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '角色层级不能超过10级';
    END IF;
END //
DELIMITER ;

-- ==================== 性能监控查询 ====================

-- 查看索引使用情况
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     SUB_PART,
--     PACKED,
--     NULLABLE,
--     INDEX_TYPE
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'role'
-- ORDER BY TABLE_NAME, SEQ_IN_INDEX;

-- 查看表大小和行数
-- SELECT 
--     TABLE_NAME,
--     TABLE_ROWS,
--     DATA_LENGTH,
--     INDEX_LENGTH,
--     (DATA_LENGTH + INDEX_LENGTH) AS TOTAL_SIZE
-- FROM information_schema.TABLES 
-- WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'role';

-- ==================== 数据清理 ====================

-- 清理过期的软删除数据（可选）
-- DELETE FROM role WHERE deleted = 1 AND update_time < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- ==================== 备份建议 ====================

-- 1. 定期备份角色表数据
-- mysqldump -u username -p database_name role > role_backup_$(date +%Y%m%d).sql

-- 2. 备份角色相关的关联表
-- mysqldump -u username -p database_name role user_role role_permission > role_full_backup_$(date +%Y%m%d).sql

-- ==================== 监控查询 ====================

-- 角色数量统计
SELECT 
    '总角色数' as metric,
    COUNT(*) as value
FROM role
UNION ALL
SELECT 
    '启用角色数' as metric,
    COUNT(*) as value
FROM role WHERE status = 1
UNION ALL
SELECT 
    '禁用角色数' as metric,
    COUNT(*) as value
FROM role WHERE status = 0;

-- 角色层级分布
SELECT 
    level,
    COUNT(*) as role_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM role), 2) as percentage
FROM role 
GROUP BY level 
ORDER BY level;

-- 最近创建的角色
SELECT 
    role_name,
    role_code,
    create_time
FROM role 
ORDER BY create_time DESC 
LIMIT 10;
