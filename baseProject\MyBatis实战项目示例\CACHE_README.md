# Spring Boot + MyBatis + Redis 缓存实战示例

## 📋 项目简介

这是一个完整的Spring Boot缓存实战示例，展示了如何在MyBatis项目中集成Redis缓存，包含了从基础配置到高级应用的所有内容。

## 🚀 功能特性

### 缓存功能
- ✅ **基础缓存**: @Cacheable、@CachePut、@CacheEvict注解使用
- ✅ **多级缓存**: 不同类型数据的差异化缓存策略
- ✅ **批量操作**: 批量获取、批量更新缓存
- ✅ **缓存预热**: 应用启动时预热热点数据
- ✅ **缓存统计**: 实时监控缓存使用情况
- ✅ **缓存管理**: REST API管理缓存

### 业务功能
- ✅ **用户管理**: 用户CRUD操作
- ✅ **用户详情**: 包含角色和权限的复合数据缓存
- ✅ **用户统计**: 统计信息缓存
- ✅ **系统配置**: 配置信息缓存

## 🛠️ 技术栈

- **Spring Boot 2.7.14**: 应用框架
- **MyBatis 3.5.13**: ORM框架
- **Spring Cache**: 缓存抽象
- **Redis**: 缓存存储
- **Lettuce**: Redis客户端
- **MySQL 8.0**: 数据库
- **HikariCP**: 数据库连接池

## 📦 项目结构

```
src/
├── main/
│   ├── java/com/example/
│   │   ├── config/
│   │   │   └── CacheConfig.java           # Redis缓存配置
│   │   ├── controller/
│   │   │   ├── UserController.java        # 用户控制器
│   │   │   └── CacheController.java       # 缓存管理控制器
│   │   ├── service/
│   │   │   ├── UserService.java           # 用户服务接口
│   │   │   ├── CacheService.java          # 缓存服务接口
│   │   │   └── impl/
│   │   │       ├── UserServiceImpl.java   # 用户服务实现（带缓存注解）
│   │   │       └── CacheServiceImpl.java  # 缓存服务实现
│   │   ├── entity/
│   │   │   └── User.java                  # 用户实体
│   │   ├── vo/
│   │   │   ├── UserDetailVO.java          # 用户详情VO
│   │   │   └── UserStatsVO.java           # 用户统计VO
│   │   └── CacheApplication.java          # 启动类（包含演示代码）
│   └── resources/
│       ├── application.yml                # 应用配置
│       └── mapper/
│           └── UserMapper.xml             # MyBatis映射文件
└── test/
    └── java/com/example/
        └── CacheTest.java                 # 缓存功能测试
```

## 🔧 环境准备

### 1. 数据库准备

```sql
-- 创建数据库
CREATE DATABASE mybatis_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE mybatis_demo;

-- 创建用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    status INT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入测试数据
INSERT INTO users (username, password, email, phone, real_name, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '13800000001', '管理员', 1),
('user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '13800000002', '用户1', 1),
('user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '13800000003', '用户2', 1);
```

### 2. Redis准备

```bash
# 启动Redis服务器
redis-server

# 或使用Docker启动Redis
docker run -d --name redis -p 6379:6379 redis:latest
```

### 3. 配置文件

确保 `application.yml` 中的数据库和Redis连接信息正确：

```yaml
spring:
  datasource:
    url: *******************************************************************************************************************
    username: root
    password: 123456
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
```

## 🚀 运行项目

### 1. 编译项目

```bash
mvn clean compile
```

### 2. 运行应用

```bash
mvn spring-boot:run
```

或者运行主类：
```bash
java -cp target/classes com.example.CacheApplication
```

### 3. 查看演示效果

应用启动后会自动执行缓存功能演示，控制台会输出详细的演示过程。

## 📡 API接口测试

### 用户相关接口

```bash
# 获取用户信息（带缓存）
curl http://localhost:8080/api/users/1

# 获取所有用户（带缓存）
curl http://localhost:8080/api/users

# 创建用户（清除列表缓存）
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{"username":"newuser","password":"123456","email":"<EMAIL>"}'

# 更新用户（更新缓存）
curl -X PUT http://localhost:8080/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"username":"updateduser","email":"<EMAIL>"}'

# 删除用户（清除缓存）
curl -X DELETE http://localhost:8080/api/users/1
```

### 缓存管理接口

```bash
# 获取用户详情（包含角色权限）
curl http://localhost:8080/api/cache/user/1/detail

# 获取用户角色
curl http://localhost:8080/api/cache/user/1/roles

# 获取用户权限
curl http://localhost:8080/api/cache/user/1/permissions

# 获取用户统计
curl http://localhost:8080/api/cache/user/stats

# 获取活跃用户
curl http://localhost:8080/api/cache/user/active

# 批量获取用户
curl -X POST http://localhost:8080/api/cache/user/batch \
  -H "Content-Type: application/json" \
  -d '[1,2,3]'

# 预热用户缓存
curl -X POST http://localhost:8080/api/cache/user/warmup \
  -H "Content-Type: application/json" \
  -d '[1,2,3]'

# 获取系统配置
curl http://localhost:8080/api/cache/config/app.name

# 更新系统配置
curl -X PUT http://localhost:8080/api/cache/config/app.name \
  -H "Content-Type: application/json" \
  -d '"新的应用名称"'

# 获取缓存统计
curl http://localhost:8080/api/cache/statistics

# 获取缓存信息
curl http://localhost:8080/api/cache/info

# 清除用户缓存
curl -X DELETE http://localhost:8080/api/cache/user/1

# 清除所有缓存
curl -X DELETE http://localhost:8080/api/cache/all

# 获取指定缓存值
curl http://localhost:8080/api/cache/users/1

# 清除指定缓存
curl -X DELETE http://localhost:8080/api/cache/users

# 清除指定缓存项
curl -X DELETE http://localhost:8080/api/cache/users/1
```

## 🧪 运行测试

```bash
# 运行所有测试
mvn test

# 运行缓存测试
mvn test -Dtest=CacheTest
```

## 📊 监控端点

应用集成了Spring Boot Actuator，可以通过以下端点监控缓存状态：

```bash
# 健康检查
curl http://localhost:8080/actuator/health

# 应用信息
curl http://localhost:8080/actuator/info

# 缓存信息
curl http://localhost:8080/actuator/caches

# 指标信息
curl http://localhost:8080/actuator/metrics
```

## 🔍 缓存验证

### 1. 查看Redis中的缓存数据

```bash
# 连接Redis
redis-cli

# 查看所有缓存key
KEYS cache:*

# 查看特定缓存
GET cache:users::1

# 查看缓存过期时间
TTL cache:users::1
```

### 2. 观察日志输出

应用会在控制台输出详细的缓存操作日志，包括：
- 缓存命中/未命中
- 数据库查询
- 缓存更新/清除
- 性能统计

## 📈 性能对比

运行演示程序可以看到缓存带来的性能提升：

```
第一次查询用户（从数据库）... 耗时: 15ms
第二次查询用户（从缓存）... 耗时: 2ms
缓存效果: 第二次查询比第一次快 13ms
```

## 🎯 学习要点

1. **缓存注解使用**: 掌握@Cacheable、@CachePut、@CacheEvict的使用
2. **缓存配置**: 理解Redis缓存管理器的配置
3. **缓存策略**: 学习不同业务场景的缓存策略
4. **性能优化**: 了解缓存对性能的提升效果
5. **缓存管理**: 掌握缓存的监控和管理方法

## 🔧 常见问题

### 1. Redis连接失败
- 检查Redis服务是否启动
- 确认连接配置是否正确
- 检查防火墙设置

### 2. 缓存不生效
- 确认@EnableCaching注解已添加
- 检查方法是否被Spring代理
- 验证缓存配置是否正确

### 3. 序列化问题
- 确保实体类实现Serializable接口
- 检查Jackson序列化配置

## 📚 扩展学习

- [Spring Cache官方文档](https://docs.spring.io/spring-framework/docs/current/reference/html/integration.html#cache)
- [Redis官方文档](https://redis.io/documentation)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)

---

**注意**: 这是一个学习示例项目，生产环境使用时请根据实际需求调整配置和安全设置。
