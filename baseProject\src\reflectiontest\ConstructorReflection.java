package reflectiontest;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.util.Arrays;

public class ConstructorReflection {

    public static void demonstrateConstructorAccess() throws Exception {
        Class<?> clazz = Person.class;

        // 获取所有public构造函数
        Constructor<?>[] constructors = clazz.getConstructors();
        System.out.println("Public构造函数数量: " + constructors.length);

        // 获取所有声明的构造函数
        Constructor<?>[] declaredConstructors = clazz.getDeclaredConstructors();
        System.out.println("声明的构造函数数量: " + declaredConstructors.length);

        for (Constructor<?> constructor : declaredConstructors) {
            System.out.println("构造函数参数: " + Arrays.toString(constructor.getParameterTypes()));
            System.out.println("修饰符: " + Modifier.toString(constructor.getModifiers()));
            System.out.println("---");
        }

        // 获取特定构造函数
        Constructor<?> defaultConstructor = clazz.getConstructor();
        Constructor<?> paramConstructor = clazz.getConstructor(String.class, int.class);
        Constructor<?> privateConstructor = clazz.getDeclaredConstructor(String.class);

        // 创建实例
        Object person1 = defaultConstructor.newInstance();
        Object person2 = paramConstructor.newInstance("Alice", 25);

        // 访问私有构造函数
        privateConstructor.setAccessible(true);
        Object person3 = privateConstructor.newInstance("Bob");

        System.out.println("创建的实例: " + person1);
        System.out.println("创建的实例: " + person2);
        System.out.println("创建的实例: " + person3);
    }
    public static void main(String[] args) throws Exception {
        demonstrateConstructorAccess();
    }
}

class Person {
    private String name;
    private int age;

    // 默认构造函数
    public Person() {
        this.name = "Unknown";
        this.age = 0;
    }

    // 参数构造函数
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    // 私有构造函数
    private Person(String name) {
        this.name = name;
        this.age = 18;
    }

    @Override
    public String toString() {
        return "Person{name='" + name + "', age=" + age + "}";
    }
}
