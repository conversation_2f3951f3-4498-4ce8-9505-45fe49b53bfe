package com.example.mybatisrbac.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页响应结果类
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页响应结果")
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应状态码
     */
    @Schema(description = "响应状态码", example = "200")
    private Integer code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "查询成功")
    private String message;

    /**
     * 分页数据
     */
    @Schema(description = "分页数据")
    private PageData<T> data;

    /**
     * 分页数据内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分页数据")
    public static class PageData<T> implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 数据列表
         */
        @Schema(description = "数据列表")
        private List<T> records;

        /**
         * 当前页码
         */
        @Schema(description = "当前页码", example = "1")
        private Long current;

        /**
         * 每页大小
         */
        @Schema(description = "每页大小", example = "10")
        private Long size;

        /**
         * 总记录数
         */
        @Schema(description = "总记录数", example = "100")
        private Long total;

        /**
         * 总页数
         */
        @Schema(description = "总页数", example = "10")
        private Long pages;

        /**
         * 是否有上一页
         */
        @Schema(description = "是否有上一页", example = "false")
        private Boolean hasPrevious;

        /**
         * 是否有下一页
         */
        @Schema(description = "是否有下一页", example = "true")
        private Boolean hasNext;

        /**
         * 构造分页数据
         */
        public static <T> PageData<T> of(List<T> records, Long current, Long size, Long total) {
            PageData<T> pageData = new PageData<>();
            pageData.setRecords(records);
            pageData.setCurrent(current);
            pageData.setSize(size);
            pageData.setTotal(total);
            
            // 计算总页数
            Long pages = (total + size - 1) / size;
            pageData.setPages(pages);
            
            // 计算是否有上一页和下一页
            pageData.setHasPrevious(current > 1);
            pageData.setHasNext(current < pages);
            
            return pageData;
        }
    }

    /**
     * 构造成功的分页响应
     */
    public static <T> PageResult<T> success(List<T> records, Long current, Long size, Long total) {
        return success("查询成功", records, current, size, total);
    }

    /**
     * 构造成功的分页响应
     */
    public static <T> PageResult<T> success(String message, List<T> records, Long current, Long size, Long total) {
        PageResult<T> result = new PageResult<>();
        result.setCode(ResultCode.SUCCESS.getCode());
        result.setMessage(message);
        result.setData(PageData.of(records, current, size, total));
        return result;
    }

    /**
     * 构造失败的分页响应
     */
    public static <T> PageResult<T> error(String message) {
        return error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), message);
    }

    /**
     * 构造失败的分页响应
     */
    public static <T> PageResult<T> error(Integer code, String message) {
        PageResult<T> result = new PageResult<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(null);
        return result;
    }

    /**
     * 构造失败的分页响应
     */
    public static <T> PageResult<T> error(ResultCode resultCode) {
        return error(resultCode.getCode(), resultCode.getMessage());
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
}
