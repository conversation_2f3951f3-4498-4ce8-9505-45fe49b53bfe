# Spring Boot 缓存详解与Redis实战

## 📚 目录

1. [缓存基础概念](#缓存基础概念)
2. [Spring Cache抽象](#spring-cache抽象)
3. [Redis集成配置](#redis集成配置)
4. [缓存注解详解](#缓存注解详解)
5. [自定义缓存配置](#自定义缓存配置)
6. [缓存实战案例](#缓存实战案例)
7. [缓存最佳实践](#缓存最佳实践)
8. [性能优化](#性能优化)
9. [监控与管理](#监控与管理)
10. [常见问题解决](#常见问题解决)

---

## 缓存基础概念

### 1. 什么是缓存

缓存是一种存储技术，将经常访问的数据存储在快速存储介质中，以提高数据访问速度。

### 2. 缓存的优势

- **提高性能**: 减少数据库访问，提高响应速度
- **降低负载**: 减轻数据库和网络压力
- **提升用户体验**: 更快的页面加载和数据响应
- **节省资源**: 减少计算和I/O操作

### 3. 缓存策略

| 策略 | 描述 | 适用场景 |
|------|------|----------|
| Cache-Aside | 应用程序管理缓存 | 读多写少的场景 |
| Write-Through | 写入时同时更新缓存和数据库 | 数据一致性要求高 |
| Write-Behind | 异步写入数据库 | 写入性能要求高 |
| Refresh-Ahead | 主动刷新即将过期的缓存 | 缓存命中率要求高 |

---

## Spring Cache抽象

### 1. 核心概念

Spring Cache提供了一套缓存抽象，支持多种缓存实现：

```java
// 缓存管理器接口
public interface CacheManager {
    Cache getCache(String name);
    Collection<String> getCacheNames();
}

// 缓存接口
public interface Cache {
    String getName();
    Object getNativeCache();
    ValueWrapper get(Object key);
    void put(Object key, Object value);
    void evict(Object key);
    void clear();
}
```

### 2. 启用缓存

```java
@SpringBootApplication
@EnableCaching  // 启用缓存支持
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 3. 基本配置

```yaml
# application.yml
spring:
  cache:
    type: redis  # 缓存类型：simple, redis, caffeine, ehcache等
    cache-names:  # 预定义缓存名称
      - users
      - products
      - orders
    redis:
      time-to-live: 600000  # 默认过期时间（毫秒）
      cache-null-values: false  # 是否缓存空值
      key-prefix: "cache:"  # key前缀
      use-key-prefix: true
```

---

## Redis集成配置

### 1. 添加依赖

```xml
<dependencies>
    <!-- Spring Boot Cache Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
    </dependency>
    
    <!-- Spring Boot Redis Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    
    <!-- Lettuce连接池 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
    </dependency>
    
    <!-- Jackson序列化 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
</dependencies>
```

### 2. Redis配置

```yaml
# application.yml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    
    # Lettuce连接池配置
    lettuce:
      pool:
        max-active: 8    # 最大连接数
        max-idle: 8      # 最大空闲连接
        min-idle: 0      # 最小空闲连接
        max-wait: -1ms   # 最大等待时间
    
    # 集群配置（如果使用Redis集群）
    cluster:
      nodes:
        - *************:7000
        - *************:7001
        - *************:7002
      max-redirects: 3
    
    # 哨兵配置（如果使用Redis哨兵）
    sentinel:
      master: mymaster
      nodes:
        - *************:26379
        - *************:26379
        - *************:26379
```

### 3. Redis缓存配置类

```java
@Configuration
@EnableCaching
@Slf4j
public class CacheConfig {
    
    @Value("${spring.redis.host}")
    private String redisHost;
    
    @Value("${spring.redis.port}")
    private int redisPort;
    
    @Value("${spring.redis.password:}")
    private String redisPassword;
    
    /**
     * Redis连接工厂配置
     */
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisHost);
        config.setPort(redisPort);
        if (StringUtils.hasText(redisPassword)) {
            config.setPassword(redisPassword);
        }
        
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .poolConfig(jedisPoolConfig())
            .commandTimeout(Duration.ofSeconds(5))
            .build();
        
        return new LettuceConnectionFactory(config, clientConfig);
    }
    
    /**
     * 连接池配置
     */
    @Bean
    public GenericObjectPoolConfig jedisPoolConfig() {
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxTotal(8);
        poolConfig.setMaxIdle(8);
        poolConfig.setMinIdle(0);
        poolConfig.setMaxWaitMillis(-1);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        return poolConfig;
    }
    
    /**
     * RedisTemplate配置
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        
        // value采用jackson的序列化方式
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * 缓存管理器配置
     */
    @Bean
    @Primary
    public CacheManager cacheManager(LettuceConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))  // 默认过期时间10分钟
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new Jackson2JsonRedisSerializer<>(Object.class)))
            .disableCachingNullValues();  // 不缓存空值
        
        // 针对不同缓存的个性化配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 用户缓存 - 30分钟过期
        cacheConfigurations.put("users", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 产品缓存 - 1小时过期
        cacheConfigurations.put("products", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 订单缓存 - 5分钟过期
        cacheConfigurations.put("orders", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        
        // 热点数据缓存 - 24小时过期
        cacheConfigurations.put("hotdata", defaultConfig.entryTtl(Duration.ofHours(24)));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .transactionAware()  // 支持事务
            .build();
    }
    
    /**
     * 自定义key生成器
     */
    @Bean
    public KeyGenerator customKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName()).append(".");
            sb.append(method.getName()).append("(");
            for (int i = 0; i < params.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                if (params[i] != null) {
                    sb.append(params[i].toString());
                } else {
                    sb.append("null");
                }
            }
            sb.append(")");
            return sb.toString();
        };
    }
    
    /**
     * 缓存异常处理器
     */
    @Bean
    public CacheErrorHandler cacheErrorHandler() {
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
                log.error("缓存获取异常 - cache: {}, key: {}", cache.getName(), key, exception);
            }
            
            @Override
            public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
                log.error("缓存存储异常 - cache: {}, key: {}", cache.getName(), key, exception);
            }
            
            @Override
            public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
                log.error("缓存清除异常 - cache: {}, key: {}", cache.getName(), key, exception);
            }
            
            @Override
            public void handleCacheClearError(RuntimeException exception, Cache cache) {
                log.error("缓存清空异常 - cache: {}", cache.getName(), exception);
            }
        };
    }
}
```

---

## 缓存注解详解

### 1. @Cacheable - 缓存查询结果

```java
@Service
@Slf4j
public class UserService {

    @Autowired
    private UserRepository userRepository;

    /**
     * 基本用法 - 缓存用户信息
     */
    @Cacheable(value = "users", key = "#id")
    public User getUserById(Long id) {
        log.info("从数据库查询用户: {}", id);
        return userRepository.findById(id).orElse(null);
    }

    /**
     * 条件缓存 - 只缓存启用的用户
     */
    @Cacheable(value = "users", key = "#username", condition = "#username.length() > 3")
    public User getUserByUsername(String username) {
        log.info("从数据库查询用户: {}", username);
        return userRepository.findByUsername(username);
    }

    /**
     * 排除条件 - 不缓存管理员用户
     */
    @Cacheable(value = "users", key = "#id", unless = "#result != null and #result.role == 'ADMIN'")
    public User getUserWithRole(Long id) {
        log.info("从数据库查询用户角色: {}", id);
        return userRepository.findById(id).orElse(null);
    }

    /**
     * 复合key - 多参数缓存
     */
    @Cacheable(value = "users", key = "#department + '_' + #status")
    public List<User> getUsersByDepartmentAndStatus(String department, String status) {
        log.info("从数据库查询用户: department={}, status={}", department, status);
        return userRepository.findByDepartmentAndStatus(department, status);
    }

    /**
     * 自定义key生成器
     */
    @Cacheable(value = "users", keyGenerator = "customKeyGenerator")
    public List<User> searchUsers(String keyword, int page, int size) {
        log.info("从数据库搜索用户: keyword={}, page={}, size={}", keyword, page, size);
        return userRepository.searchUsers(keyword, page, size);
    }

    /**
     * 同步缓存 - 防止缓存击穿
     */
    @Cacheable(value = "users", key = "#id", sync = true)
    public User getUserByIdSync(Long id) {
        log.info("同步查询用户: {}", id);
        // 模拟耗时操作
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return userRepository.findById(id).orElse(null);
    }
}
```

### 2. @CachePut - 更新缓存

```java
@Service
public class UserService {

    /**
     * 更新用户信息并刷新缓存
     */
    @CachePut(value = "users", key = "#user.id")
    public User updateUser(User user) {
        log.info("更新用户信息: {}", user.getId());
        User savedUser = userRepository.save(user);

        // 同时更新相关缓存
        evictRelatedCache(user.getId());

        return savedUser;
    }

    /**
     * 创建用户并加入缓存
     */
    @CachePut(value = "users", key = "#result.id", condition = "#result != null")
    public User createUser(User user) {
        log.info("创建用户: {}", user.getUsername());
        return userRepository.save(user);
    }

    /**
     * 条件更新缓存
     */
    @CachePut(value = "users", key = "#user.id", unless = "#result.status == 'INACTIVE'")
    public User updateUserStatus(User user) {
        log.info("更新用户状态: {}", user.getId());
        return userRepository.save(user);
    }

    private void evictRelatedCache(Long userId) {
        // 清除相关的缓存项
        cacheManager.getCache("userProfiles").evict(userId);
        cacheManager.getCache("userPermissions").evict(userId);
    }
}
```

### 3. @CacheEvict - 清除缓存

```java
@Service
public class UserService {

    /**
     * 删除用户并清除缓存
     */
    @CacheEvict(value = "users", key = "#id")
    public void deleteUser(Long id) {
        log.info("删除用户: {}", id);
        userRepository.deleteById(id);
    }

    /**
     * 批量清除缓存
     */
    @CacheEvict(value = "users", allEntries = true)
    public void deleteAllUsers() {
        log.info("删除所有用户");
        userRepository.deleteAll();
    }

    /**
     * 方法执行前清除缓存
     */
    @CacheEvict(value = "users", key = "#id", beforeInvocation = true)
    public void resetUser(Long id) {
        log.info("重置用户: {}", id);
        // 即使方法执行失败，缓存也会被清除
        userRepository.resetUserData(id);
    }

    /**
     * 条件清除缓存
     */
    @CacheEvict(value = "users", key = "#user.id", condition = "#user.status == 'DELETED'")
    public void updateUserWithEviction(User user) {
        log.info("更新用户并可能清除缓存: {}", user.getId());
        userRepository.save(user);
    }

    /**
     * 清除多个缓存
     */
    @Caching(evict = {
        @CacheEvict(value = "users", key = "#id"),
        @CacheEvict(value = "userProfiles", key = "#id"),
        @CacheEvict(value = "userPermissions", key = "#id")
    })
    public void deleteUserCompletely(Long id) {
        log.info("完全删除用户及相关数据: {}", id);
        userRepository.deleteById(id);
        userProfileRepository.deleteByUserId(id);
        userPermissionRepository.deleteByUserId(id);
    }
}
```

### 4. @Caching - 组合缓存操作

```java
@Service
public class UserService {

    /**
     * 复杂的缓存操作组合
     */
    @Caching(
        cacheable = {
            @Cacheable(value = "users", key = "#id")
        },
        put = {
            @CachePut(value = "userProfiles", key = "#id"),
            @CachePut(value = "activeUsers", key = "#id", condition = "#result.status == 'ACTIVE'")
        },
        evict = {
            @CacheEvict(value = "inactiveUsers", key = "#id")
        }
    )
    public User activateUser(Long id) {
        log.info("激活用户: {}", id);
        User user = userRepository.findById(id).orElse(null);
        if (user != null) {
            user.setStatus("ACTIVE");
            user.setLastActiveTime(LocalDateTime.now());
            return userRepository.save(user);
        }
        return null;
    }

    /**
     * 批量操作的缓存处理
     */
    @Caching(
        put = {
            @CachePut(value = "users", key = "#result.id")
        },
        evict = {
            @CacheEvict(value = "userStats", allEntries = true),
            @CacheEvict(value = "departmentUsers", key = "#user.departmentId")
        }
    )
    public User transferUserDepartment(User user, String newDepartment) {
        log.info("转移用户部门: {} -> {}", user.getId(), newDepartment);
        user.setDepartmentId(newDepartment);
        return userRepository.save(user);
    }
}
```

### 5. @CacheConfig - 类级别缓存配置

```java
@Service
@CacheConfig(cacheNames = "users", keyGenerator = "customKeyGenerator")
public class UserService {

    // 继承类级别的缓存配置
    @Cacheable  // 等价于 @Cacheable(value = "users", keyGenerator = "customKeyGenerator")
    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    // 覆盖类级别配置
    @Cacheable(value = "userProfiles", key = "#id")
    public UserProfile getUserProfile(Long id) {
        return userProfileRepository.findByUserId(id);
    }

    @CacheEvict(allEntries = true)
    public void clearAllUsers() {
        userRepository.deleteAll();
    }
}
```

---

## 缓存实战案例

### 1. 用户管理系统缓存

```java
@Service
@Slf4j
public class UserManagementService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CacheManager cacheManager;

    /**
     * 获取用户详情 - 多级缓存
     */
    @Cacheable(value = "userDetails", key = "#userId", unless = "#result == null")
    public UserDetailVO getUserDetail(Long userId) {
        log.info("从数据库加载用户详情: {}", userId);

        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            return null;
        }

        // 构建详情对象
        UserDetailVO detail = new UserDetailVO();
        detail.setId(user.getId());
        detail.setUsername(user.getUsername());
        detail.setEmail(user.getEmail());
        detail.setProfile(getUserProfile(userId));
        detail.setPermissions(getUserPermissions(userId));
        detail.setLastLoginTime(user.getLastLoginTime());

        return detail;
    }

    /**
     * 获取用户配置 - 长期缓存
     */
    @Cacheable(value = "userProfiles", key = "#userId")
    public UserProfile getUserProfile(Long userId) {
        log.info("从数据库加载用户配置: {}", userId);
        return userProfileRepository.findByUserId(userId);
    }

    /**
     * 获取用户权限 - 短期缓存
     */
    @Cacheable(value = "userPermissions", key = "#userId")
    public List<String> getUserPermissions(Long userId) {
        log.info("从数据库加载用户权限: {}", userId);
        return userPermissionRepository.findPermissionsByUserId(userId);
    }

    /**
     * 更新用户信息 - 级联更新缓存
     */
    @Caching(
        put = {
            @CachePut(value = "users", key = "#user.id"),
            @CachePut(value = "userDetails", key = "#user.id")
        },
        evict = {
            @CacheEvict(value = "userList", allEntries = true),
            @CacheEvict(value = "departmentUsers", key = "#user.departmentId")
        }
    )
    public User updateUser(User user) {
        log.info("更新用户信息: {}", user.getId());

        User savedUser = userRepository.save(user);

        // 发布用户更新事件
        applicationEventPublisher.publishEvent(new UserUpdatedEvent(savedUser));

        return savedUser;
    }

    /**
     * 用户登录 - 更新活跃状态缓存
     */
    @CachePut(value = "activeUsers", key = "#userId")
    public UserSession userLogin(Long userId, String sessionId) {
        log.info("用户登录: {}", userId);

        User user = userRepository.findById(userId).orElse(null);
        if (user != null) {
            user.setLastLoginTime(LocalDateTime.now());
            userRepository.save(user);

            UserSession session = new UserSession();
            session.setUserId(userId);
            session.setSessionId(sessionId);
            session.setLoginTime(LocalDateTime.now());
            session.setActive(true);

            return session;
        }

        return null;
    }

    /**
     * 用户登出 - 清除活跃状态缓存
     */
    @CacheEvict(value = "activeUsers", key = "#userId")
    public void userLogout(Long userId) {
        log.info("用户登出: {}", userId);
        // 清除会话信息
        sessionRepository.deleteByUserId(userId);
    }

    /**
     * 批量获取用户 - 缓存穿透防护
     */
    public List<User> getUsersByIds(List<Long> userIds) {
        List<User> users = new ArrayList<>();
        List<Long> uncachedIds = new ArrayList<>();

        // 先从缓存获取
        for (Long id : userIds) {
            Cache cache = cacheManager.getCache("users");
            Cache.ValueWrapper wrapper = cache.get(id);
            if (wrapper != null) {
                users.add((User) wrapper.get());
            } else {
                uncachedIds.add(id);
            }
        }

        // 批量查询未缓存的数据
        if (!uncachedIds.isEmpty()) {
            List<User> uncachedUsers = userRepository.findAllById(uncachedIds);
            users.addAll(uncachedUsers);

            // 将查询结果放入缓存
            Cache cache = cacheManager.getCache("users");
            for (User user : uncachedUsers) {
                cache.put(user.getId(), user);
            }
        }

        return users;
    }

    /**
     * 搜索用户 - 分页缓存
     */
    @Cacheable(value = "userSearch", key = "#keyword + '_' + #page + '_' + #size")
    public PageResult<User> searchUsers(String keyword, int page, int size) {
        log.info("搜索用户: keyword={}, page={}, size={}", keyword, page, size);

        Pageable pageable = PageRequest.of(page, size);
        Page<User> userPage = userRepository.findByUsernameContainingOrEmailContaining(
            keyword, keyword, pageable);

        return PageResult.<User>builder()
            .content(userPage.getContent())
            .totalElements(userPage.getTotalElements())
            .totalPages(userPage.getTotalPages())
            .pageNumber(page)
            .pageSize(size)
            .build();
    }
}
```

### 2. 商品管理系统缓存

```java
@Service
@Slf4j
public class ProductService {

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取商品详情 - 热点数据缓存
     */
    @Cacheable(value = "products", key = "#productId", unless = "#result == null")
    public Product getProductById(Long productId) {
        log.info("从数据库查询商品: {}", productId);
        return productRepository.findById(productId).orElse(null);
    }

    /**
     * 获取商品库存 - 实时性要求高，短期缓存
     */
    @Cacheable(value = "productStock", key = "#productId")
    public Integer getProductStock(Long productId) {
        log.info("从数据库查询商品库存: {}", productId);
        return productRepository.getStockById(productId);
    }

    /**
     * 更新商品库存 - 清除库存缓存
     */
    @CacheEvict(value = "productStock", key = "#productId")
    public void updateProductStock(Long productId, Integer quantity) {
        log.info("更新商品库存: {} -> {}", productId, quantity);
        productRepository.updateStock(productId, quantity);

        // 如果库存为0，也清除商品缓存
        if (quantity <= 0) {
            cacheManager.getCache("products").evict(productId);
        }
    }

    /**
     * 商品秒杀 - 分布式锁 + 缓存
     */
    public boolean seckillProduct(Long productId, Long userId) {
        String lockKey = "seckill:lock:" + productId;
        String stockKey = "seckill:stock:" + productId;

        // 分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, "locked", Duration.ofSeconds(10));

        if (!lockAcquired) {
            return false;
        }

        try {
            // 检查库存
            Integer stock = (Integer) redisTemplate.opsForValue().get(stockKey);
            if (stock == null) {
                stock = getProductStock(productId);
                redisTemplate.opsForValue().set(stockKey, stock, Duration.ofMinutes(5));
            }

            if (stock <= 0) {
                return false;
            }

            // 扣减库存
            redisTemplate.opsForValue().decrement(stockKey);

            // 异步更新数据库
            CompletableFuture.runAsync(() -> {
                updateProductStock(productId, stock - 1);
                createSeckillOrder(productId, userId);
            });

            return true;

        } finally {
            redisTemplate.delete(lockKey);
        }
    }

    /**
     * 获取热门商品 - 定时刷新缓存
     */
    @Cacheable(value = "hotProducts")
    public List<Product> getHotProducts() {
        log.info("从数据库查询热门商品");
        return productRepository.findHotProducts();
    }

    /**
     * 定时刷新热门商品缓存
     */
    @Scheduled(fixedRate = 300000) // 5分钟刷新一次
    @CacheEvict(value = "hotProducts", allEntries = true)
    public void refreshHotProducts() {
        log.info("刷新热门商品缓存");
        // 缓存会在下次访问时重新加载
    }

    private void createSeckillOrder(Long productId, Long userId) {
        // 创建秒杀订单的逻辑
        log.info("创建秒杀订单: productId={}, userId={}", productId, userId);
    }
}
```

---

## 缓存最佳实践

### 1. 缓存设计原则

#### 缓存数据选择
```java
@Service
public class CacheStrategyService {

    /**
     * 适合缓存的数据特征：
     * 1. 读多写少
     * 2. 计算成本高
     * 3. 访问频率高
     * 4. 数据相对稳定
     */

    // ✅ 好的缓存案例 - 用户基本信息
    @Cacheable(value = "users", key = "#id")
    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    // ✅ 好的缓存案例 - 配置信息
    @Cacheable(value = "systemConfig", key = "#configKey")
    public String getSystemConfig(String configKey) {
        return configRepository.findValueByKey(configKey);
    }

    // ❌ 不适合缓存的案例 - 实时性要求极高的数据
    // @Cacheable(value = "stockPrice", key = "#symbol")
    public BigDecimal getCurrentStockPrice(String symbol) {
        // 股票价格变化频繁，不适合缓存
        return stockPriceService.getRealTimePrice(symbol);
    }

    // ❌ 不适合缓存的案例 - 个人敏感信息
    // @Cacheable(value = "passwords", key = "#userId")
    public String getUserPassword(Long userId) {
        // 密码等敏感信息不应该缓存
        return userRepository.getPasswordById(userId);
    }
}
```

#### 缓存Key设计
```java
@Service
public class CacheKeyDesignService {

    /**
     * 缓存Key设计原则：
     * 1. 唯一性 - 避免key冲突
     * 2. 可读性 - 便于调试和监控
     * 3. 层次性 - 便于批量操作
     * 4. 长度适中 - 避免过长的key
     */

    // ✅ 好的Key设计
    @Cacheable(value = "users", key = "'user:' + #id")
    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    // ✅ 复合Key设计
    @Cacheable(value = "userOrders", key = "'user:' + #userId + ':orders:' + #status")
    public List<Order> getUserOrdersByStatus(Long userId, String status) {
        return orderRepository.findByUserIdAndStatus(userId, status);
    }

    // ✅ 分页Key设计
    @Cacheable(value = "productList", key = "'products:' + #category + ':page:' + #page + ':size:' + #size")
    public PageResult<Product> getProductsByCategory(String category, int page, int size) {
        return productService.findByCategory(category, page, size);
    }

    // ❌ 不好的Key设计 - 可能冲突
    // @Cacheable(value = "data", key = "#id")

    // ❌ 不好的Key设计 - 过于复杂
    // @Cacheable(value = "complex", key = "#param1 + '_' + #param2 + '_' + #param3 + '_' + #param4")
}
```

### 2. 缓存过期策略

```java
@Configuration
public class CacheExpirationConfig {

    @Bean
    public CacheManager multiLevelCacheManager() {
        RedisCacheConfiguration shortTermConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(5));   // 短期缓存 - 5分钟

        RedisCacheConfiguration mediumTermConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30));  // 中期缓存 - 30分钟

        RedisCacheConfiguration longTermConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(24));    // 长期缓存 - 24小时

        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // 实时性要求高的数据 - 短期缓存
        cacheConfigurations.put("stockPrices", shortTermConfig);
        cacheConfigurations.put("exchangeRates", shortTermConfig);

        // 一般业务数据 - 中期缓存
        cacheConfigurations.put("users", mediumTermConfig);
        cacheConfigurations.put("products", mediumTermConfig);

        // 相对稳定的数据 - 长期缓存
        cacheConfigurations.put("systemConfig", longTermConfig);
        cacheConfigurations.put("categories", longTermConfig);

        return RedisCacheManager.builder(redisConnectionFactory)
            .cacheDefaults(mediumTermConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
}
```

### 3. 缓存预热策略

```java
@Component
@Slf4j
public class CacheWarmupService {

    @Autowired
    private UserService userService;

    @Autowired
    private ProductService productService;

    @Autowired
    private CacheManager cacheManager;

    /**
     * 应用启动时预热缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmupCache() {
        log.info("开始缓存预热...");

        CompletableFuture.allOf(
            CompletableFuture.runAsync(this::warmupUserCache),
            CompletableFuture.runAsync(this::warmupProductCache),
            CompletableFuture.runAsync(this::warmupSystemConfig)
        ).thenRun(() -> {
            log.info("缓存预热完成");
        });
    }

    private void warmupUserCache() {
        try {
            log.info("预热用户缓存...");
            // 预热活跃用户数据
            List<Long> activeUserIds = userService.getActiveUserIds();
            for (Long userId : activeUserIds) {
                userService.getUserById(userId);
            }
            log.info("用户缓存预热完成，预热用户数: {}", activeUserIds.size());
        } catch (Exception e) {
            log.error("用户缓存预热失败", e);
        }
    }

    private void warmupProductCache() {
        try {
            log.info("预热商品缓存...");
            // 预热热门商品数据
            List<Product> hotProducts = productService.getHotProducts();
            for (Product product : hotProducts) {
                productService.getProductById(product.getId());
            }
            log.info("商品缓存预热完成，预热商品数: {}", hotProducts.size());
        } catch (Exception e) {
            log.error("商品缓存预热失败", e);
        }
    }

    private void warmupSystemConfig() {
        try {
            log.info("预热系统配置缓存...");
            // 预热系统配置
            systemConfigService.getAllConfigs().forEach((key, value) -> {
                systemConfigService.getConfig(key);
            });
            log.info("系统配置缓存预热完成");
        } catch (Exception e) {
            log.error("系统配置缓存预热失败", e);
        }
    }

    /**
     * 定时预热热点数据
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void scheduledWarmup() {
        log.info("定时缓存预热开始...");

        // 预热最近访问的热点数据
        warmupHotData();

        log.info("定时缓存预热完成");
    }

    private void warmupHotData() {
        // 从访问日志或统计数据中获取热点数据进行预热
        List<String> hotDataKeys = getHotDataKeys();
        for (String key : hotDataKeys) {
            try {
                // 根据key类型进行相应的预热操作
                if (key.startsWith("user:")) {
                    Long userId = Long.parseLong(key.substring(5));
                    userService.getUserById(userId);
                } else if (key.startsWith("product:")) {
                    Long productId = Long.parseLong(key.substring(8));
                    productService.getProductById(productId);
                }
            } catch (Exception e) {
                log.warn("预热数据失败: {}", key, e);
            }
        }
    }

    private List<String> getHotDataKeys() {
        // 从Redis或其他存储中获取热点数据key列表
        // 这里简化处理，实际应该从访问统计中获取
        return Arrays.asList("user:1", "user:2", "product:100", "product:101");
    }
}
```

### 4. 缓存雪崩防护

```java
@Service
@Slf4j
public class CacheAvalancheProtectionService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 随机过期时间防止缓存雪崩
     */
    public void setCacheWithRandomExpiration(String key, Object value, long baseExpireSeconds) {
        // 在基础过期时间上增加随机时间（±20%）
        long randomOffset = (long) (baseExpireSeconds * 0.2 * Math.random());
        long finalExpireSeconds = baseExpireSeconds + randomOffset;

        redisTemplate.opsForValue().set(key, value, Duration.ofSeconds(finalExpireSeconds));
        log.debug("设置缓存: key={}, expireSeconds={}", key, finalExpireSeconds);
    }

    /**
     * 多级缓存防护
     */
    @Cacheable(value = "users", key = "#id")
    public User getUserWithMultiLevelCache(Long id) {
        // 一级缓存：Redis
        String redisKey = "user:" + id;
        User user = (User) redisTemplate.opsForValue().get(redisKey);
        if (user != null) {
            return user;
        }

        // 二级缓存：本地缓存（Caffeine）
        user = localCacheService.getUser(id);
        if (user != null) {
            // 回写Redis缓存
            setCacheWithRandomExpiration(redisKey, user, 1800);
            return user;
        }

        // 三级：数据库
        user = userRepository.findById(id).orElse(null);
        if (user != null) {
            // 写入多级缓存
            localCacheService.putUser(id, user);
            setCacheWithRandomExpiration(redisKey, user, 1800);
        }

        return user;
    }

    /**
     * 熔断器模式防护
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public Object getCacheWithCircuitBreaker(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("缓存访问失败: {}", key, e);
            // 降级到数据库查询
            return fallbackToDatabase(key);
        }
    }

    @Recover
    public Object fallbackToDatabase(Exception ex, String key) {
        log.warn("缓存熔断，降级到数据库查询: {}", key);
        // 实现数据库降级逻辑
        return databaseService.queryByKey(key);
    }
}
```

---

## 性能优化

### 1. 缓存命中率优化

```java
@Component
@Slf4j
public class CacheHitRateOptimizer {

    @Autowired
    private MeterRegistry meterRegistry;

    /**
     * 缓存命中率监控
     */
    @EventListener
    public void handleCacheHitEvent(CacheHitEvent event) {
        Counter.builder("cache.hit")
            .tag("cache", event.getCacheName())
            .tag("key", event.getKey().toString())
            .register(meterRegistry)
            .increment();
    }

    @EventListener
    public void handleCacheMissEvent(CacheMissEvent event) {
        Counter.builder("cache.miss")
            .tag("cache", event.getCacheName())
            .tag("key", event.getKey().toString())
            .register(meterRegistry)
            .increment();
    }

    /**
     * 智能预加载
     */
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void intelligentPreload() {
        // 分析访问模式，预加载可能需要的数据
        List<String> predictedKeys = analyzePredictedKeys();

        for (String key : predictedKeys) {
            if (!isCacheExists(key)) {
                preloadData(key);
            }
        }
    }

    private List<String> analyzePredictedKeys() {
        // 基于历史访问模式预测可能需要的key
        // 这里简化处理，实际应该使用机器学习算法
        return Arrays.asList("user:popular", "product:trending");
    }

    private boolean isCacheExists(String key) {
        return redisTemplate.hasKey(key);
    }

    private void preloadData(String key) {
        // 根据key类型预加载相应数据
        log.info("智能预加载数据: {}", key);
    }
}
```

### 2. 缓存压缩优化

```java
@Configuration
public class CacheCompressionConfig {

    /**
     * 支持压缩的Redis序列化器
     */
    @Bean
    public RedisSerializer<Object> compressedJacksonSerializer() {
        return new RedisSerializer<Object>() {
            private final Jackson2JsonRedisSerializer<Object> serializer =
                new Jackson2JsonRedisSerializer<>(Object.class);

            @Override
            public byte[] serialize(Object obj) throws SerializationException {
                if (obj == null) {
                    return new byte[0];
                }

                byte[] jsonBytes = serializer.serialize(obj);

                // 如果数据大于1KB，则进行压缩
                if (jsonBytes.length > 1024) {
                    return compress(jsonBytes);
                }

                return jsonBytes;
            }

            @Override
            public Object deserialize(byte[] bytes) throws SerializationException {
                if (bytes == null || bytes.length == 0) {
                    return null;
                }

                // 检查是否为压缩数据
                if (isCompressed(bytes)) {
                    bytes = decompress(bytes);
                }

                return serializer.deserialize(bytes);
            }

            private byte[] compress(byte[] data) {
                try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
                     GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {

                    gzipOut.write(data);
                    gzipOut.finish();

                    byte[] compressed = baos.toByteArray();

                    // 添加压缩标识
                    byte[] result = new byte[compressed.length + 1];
                    result[0] = 1; // 压缩标识
                    System.arraycopy(compressed, 0, result, 1, compressed.length);

                    return result;
                } catch (IOException e) {
                    throw new SerializationException("压缩失败", e);
                }
            }

            private byte[] decompress(byte[] data) {
                try (ByteArrayInputStream bais = new ByteArrayInputStream(data, 1, data.length - 1);
                     GZIPInputStream gzipIn = new GZIPInputStream(bais);
                     ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

                    byte[] buffer = new byte[1024];
                    int len;
                    while ((len = gzipIn.read(buffer)) != -1) {
                        baos.write(buffer, 0, len);
                    }

                    return baos.toByteArray();
                } catch (IOException e) {
                    throw new SerializationException("解压失败", e);
                }
            }

            private boolean isCompressed(byte[] data) {
                return data.length > 0 && data[0] == 1;
            }
        };
    }
}
```

### 3. 批量操作优化

```java
@Service
@Slf4j
public class BatchCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 批量获取缓存
     */
    public Map<String, Object> batchGet(List<String> keys) {
        if (keys.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Object> values = redisTemplate.opsForValue().multiGet(keys);
        Map<String, Object> result = new HashMap<>();

        for (int i = 0; i < keys.size(); i++) {
            if (values.get(i) != null) {
                result.put(keys.get(i), values.get(i));
            }
        }

        return result;
    }

    /**
     * 批量设置缓存
     */
    public void batchSet(Map<String, Object> keyValues, Duration expiration) {
        if (keyValues.isEmpty()) {
            return;
        }

        // 使用Pipeline提高性能
        redisTemplate.executePipelined(new RedisCallback<Object>() {
            @Override
            public Object doInRedis(RedisConnection connection) throws DataAccessException {
                for (Map.Entry<String, Object> entry : keyValues.entrySet()) {
                    byte[] key = redisTemplate.getStringSerializer().serialize(entry.getKey());
                    byte[] value = redisTemplate.getValueSerializer().serialize(entry.getValue());

                    connection.setEx(key, expiration.getSeconds(), value);
                }
                return null;
            }
        });
    }

    /**
     * 批量删除缓存
     */
    public void batchDelete(List<String> keys) {
        if (keys.isEmpty()) {
            return;
        }

        redisTemplate.delete(keys);
    }

    /**
     * 批量获取用户信息（缓存 + 数据库）
     */
    public List<User> batchGetUsers(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 构建缓存key列表
        List<String> cacheKeys = userIds.stream()
            .map(id -> "user:" + id)
            .collect(Collectors.toList());

        // 批量从缓存获取
        Map<String, Object> cachedUsers = batchGet(cacheKeys);

        List<User> result = new ArrayList<>();
        List<Long> missedUserIds = new ArrayList<>();

        for (int i = 0; i < userIds.size(); i++) {
            Long userId = userIds.get(i);
            String cacheKey = cacheKeys.get(i);

            if (cachedUsers.containsKey(cacheKey)) {
                result.add((User) cachedUsers.get(cacheKey));
            } else {
                missedUserIds.add(userId);
            }
        }

        // 批量从数据库获取未命中的数据
        if (!missedUserIds.isEmpty()) {
            List<User> dbUsers = userRepository.findAllById(missedUserIds);
            result.addAll(dbUsers);

            // 批量写入缓存
            Map<String, Object> toCache = dbUsers.stream()
                .collect(Collectors.toMap(
                    user -> "user:" + user.getId(),
                    user -> user
                ));
            batchSet(toCache, Duration.ofMinutes(30));
        }

        return result;
    }
}
```

---

## 监控与管理

### 1. 缓存监控指标

```java
@Component
@Slf4j
public class CacheMonitoringService {

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存性能监控
     */
    @EventListener
    public void onCacheHit(CacheHitEvent event) {
        Timer.builder("cache.operation.time")
            .tag("cache", event.getCacheName())
            .tag("operation", "hit")
            .register(meterRegistry)
            .record(event.getExecutionTime(), TimeUnit.MILLISECONDS);

        Counter.builder("cache.hit.count")
            .tag("cache", event.getCacheName())
            .register(meterRegistry)
            .increment();
    }

    @EventListener
    public void onCacheMiss(CacheMissEvent event) {
        Timer.builder("cache.operation.time")
            .tag("cache", event.getCacheName())
            .tag("operation", "miss")
            .register(meterRegistry)
            .record(event.getExecutionTime(), TimeUnit.MILLISECONDS);

        Counter.builder("cache.miss.count")
            .tag("cache", event.getCacheName())
            .register(meterRegistry)
            .increment();
    }

    /**
     * 定时收集缓存统计信息
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void collectCacheStatistics() {
        for (String cacheName : cacheManager.getCacheNames()) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                collectCacheMetrics(cacheName, cache);
            }
        }

        // 收集Redis统计信息
        collectRedisStatistics();
    }

    private void collectCacheMetrics(String cacheName, Cache cache) {
        try {
            // 获取缓存大小（如果支持）
            if (cache.getNativeCache() instanceof org.springframework.data.redis.cache.RedisCache) {
                // Redis缓存统计
                String pattern = cacheName + ":*";
                Set<String> keys = redisTemplate.keys(pattern);

                Gauge.builder("cache.size")
                    .tag("cache", cacheName)
                    .register(meterRegistry, keys, Set::size);

                // 计算缓存占用内存
                long memoryUsage = calculateCacheMemoryUsage(keys);
                Gauge.builder("cache.memory.usage")
                    .tag("cache", cacheName)
                    .register(meterRegistry, memoryUsage, Long::longValue);
            }
        } catch (Exception e) {
            log.warn("收集缓存指标失败: {}", cacheName, e);
        }
    }

    private void collectRedisStatistics() {
        try {
            Properties info = redisTemplate.getConnectionFactory().getConnection().info();

            // 内存使用情况
            String usedMemory = info.getProperty("used_memory");
            if (usedMemory != null) {
                Gauge.builder("redis.memory.used")
                    .register(meterRegistry, Long.parseLong(usedMemory), Long::longValue);
            }

            // 连接数
            String connectedClients = info.getProperty("connected_clients");
            if (connectedClients != null) {
                Gauge.builder("redis.connections.active")
                    .register(meterRegistry, Integer.parseInt(connectedClients), Integer::intValue);
            }

            // 命令执行统计
            String totalCommandsProcessed = info.getProperty("total_commands_processed");
            if (totalCommandsProcessed != null) {
                Counter.builder("redis.commands.total")
                    .register(meterRegistry)
                    .increment(Long.parseLong(totalCommandsProcessed));
            }

        } catch (Exception e) {
            log.warn("收集Redis统计信息失败", e);
        }
    }

    private long calculateCacheMemoryUsage(Set<String> keys) {
        long totalSize = 0;
        for (String key : keys) {
            try {
                Long size = redisTemplate.getConnectionFactory().getConnection().memoryUsage(key.getBytes());
                if (size != null) {
                    totalSize += size;
                }
            } catch (Exception e) {
                // 忽略单个key的错误
            }
        }
        return totalSize;
    }

    /**
     * 缓存健康检查
     */
    @Component
    public static class CacheHealthIndicator implements HealthIndicator {

        @Autowired
        private RedisTemplate<String, Object> redisTemplate;

        @Override
        public Health health() {
            try {
                // 测试Redis连接
                String pong = redisTemplate.getConnectionFactory().getConnection().ping();

                if ("PONG".equals(pong)) {
                    return Health.up()
                        .withDetail("redis", "Available")
                        .withDetail("ping", pong)
                        .build();
                } else {
                    return Health.down()
                        .withDetail("redis", "Unavailable")
                        .withDetail("ping", pong)
                        .build();
                }
            } catch (Exception e) {
                return Health.down()
                    .withDetail("redis", "Error")
                    .withDetail("error", e.getMessage())
                    .withException(e)
                    .build();
            }
        }
    }
}
```

### 2. 缓存管理接口

```java
@RestController
@RequestMapping("/api/cache")
@Slf4j
public class CacheManagementController {

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取所有缓存信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getCacheInfo() {
        Map<String, Object> cacheInfo = new HashMap<>();

        // 获取所有缓存名称
        Collection<String> cacheNames = cacheManager.getCacheNames();
        cacheInfo.put("cacheNames", cacheNames);

        // 获取每个缓存的统计信息
        Map<String, Object> cacheStats = new HashMap<>();
        for (String cacheName : cacheNames) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Map<String, Object> stats = getCacheStatistics(cacheName, cache);
                cacheStats.put(cacheName, stats);
            }
        }
        cacheInfo.put("statistics", cacheStats);

        return ResponseEntity.ok(cacheInfo);
    }

    /**
     * 清除指定缓存
     */
    @DeleteMapping("/{cacheName}")
    public ResponseEntity<String> clearCache(@PathVariable String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.info("缓存清除成功: {}", cacheName);
                return ResponseEntity.ok("缓存清除成功: " + cacheName);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("缓存清除失败: {}", cacheName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("缓存清除失败: " + e.getMessage());
        }
    }

    /**
     * 清除指定缓存的特定key
     */
    @DeleteMapping("/{cacheName}/{key}")
    public ResponseEntity<String> evictCacheKey(@PathVariable String cacheName, @PathVariable String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
                log.info("缓存项清除成功: {}:{}", cacheName, key);
                return ResponseEntity.ok("缓存项清除成功: " + cacheName + ":" + key);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("缓存项清除失败: {}:{}", cacheName, key, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("缓存项清除失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存内容
     */
    @GetMapping("/{cacheName}/{key}")
    public ResponseEntity<Object> getCacheValue(@PathVariable String cacheName, @PathVariable String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(key);
                if (wrapper != null) {
                    return ResponseEntity.ok(wrapper.get());
                } else {
                    return ResponseEntity.notFound().build();
                }
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取缓存值失败: {}:{}", cacheName, key, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 预热缓存
     */
    @PostMapping("/{cacheName}/warmup")
    public ResponseEntity<String> warmupCache(@PathVariable String cacheName, @RequestBody List<String> keys) {
        try {
            int warmedCount = 0;
            for (String key : keys) {
                // 根据缓存名称和key进行相应的预热操作
                if (warmupCacheItem(cacheName, key)) {
                    warmedCount++;
                }
            }

            String message = String.format("缓存预热完成: %s, 成功预热 %d/%d 项",
                cacheName, warmedCount, keys.size());
            log.info(message);
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            log.error("缓存预热失败: {}", cacheName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("缓存预热失败: " + e.getMessage());
        }
    }

    private Map<String, Object> getCacheStatistics(String cacheName, Cache cache) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取缓存大小
            if (cache.getNativeCache() instanceof org.springframework.data.redis.cache.RedisCache) {
                String pattern = cacheName + ":*";
                Set<String> keys = redisTemplate.keys(pattern);
                stats.put("size", keys.size());
                stats.put("keys", keys.stream().limit(10).collect(Collectors.toList())); // 只显示前10个key
            }

            stats.put("type", cache.getClass().getSimpleName());
            stats.put("nativeCache", cache.getNativeCache().getClass().getSimpleName());

        } catch (Exception e) {
            stats.put("error", e.getMessage());
        }

        return stats;
    }

    private boolean warmupCacheItem(String cacheName, String key) {
        try {
            // 根据缓存类型和key进行预热
            // 这里需要根据实际业务逻辑实现
            if ("users".equals(cacheName)) {
                Long userId = Long.parseLong(key);
                userService.getUserById(userId);
                return true;
            } else if ("products".equals(cacheName)) {
                Long productId = Long.parseLong(key);
                productService.getProductById(productId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("预热缓存项失败: {}:{}", cacheName, key, e);
            return false;
        }
    }
}
```

---

## 常见问题解决

### 1. 缓存穿透

```java
@Service
@Slf4j
public class CachePenetrationSolution {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private BloomFilter<String> bloomFilter;

    /**
     * 使用布隆过滤器防止缓存穿透
     */
    public User getUserByIdWithBloomFilter(Long userId) {
        String key = "user:" + userId;

        // 1. 先检查布隆过滤器
        if (!bloomFilter.mightContain(key)) {
            log.info("布隆过滤器判断数据不存在: {}", key);
            return null;
        }

        // 2. 检查缓存
        User user = (User) redisTemplate.opsForValue().get(key);
        if (user != null) {
            return user;
        }

        // 3. 查询数据库
        user = userRepository.findById(userId).orElse(null);
        if (user != null) {
            // 存入缓存
            redisTemplate.opsForValue().set(key, user, Duration.ofMinutes(30));
        } else {
            // 缓存空值，防止缓存穿透
            redisTemplate.opsForValue().set(key, "NULL", Duration.ofMinutes(5));
        }

        return user;
    }

    /**
     * 缓存空值防止穿透
     */
    @Cacheable(value = "users", key = "#id", unless = "#result == null")
    public User getUserWithNullCache(Long id) {
        User user = userRepository.findById(id).orElse(null);

        if (user == null) {
            // 缓存特殊标记防止穿透
            cacheManager.getCache("users").put(id, "NULL_VALUE");
        }

        return user;
    }
}
```

### 2. 缓存击穿

```java
@Service
@Slf4j
public class CacheBreakdownSolution {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private final Map<String, Object> localLocks = new ConcurrentHashMap<>();

    /**
     * 使用分布式锁防止缓存击穿
     */
    public User getUserWithDistributedLock(Long userId) {
        String key = "user:" + userId;
        String lockKey = "lock:" + key;

        // 1. 先尝试从缓存获取
        User user = (User) redisTemplate.opsForValue().get(key);
        if (user != null) {
            return user;
        }

        // 2. 获取分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, "locked", Duration.ofSeconds(10));

        if (lockAcquired) {
            try {
                // 再次检查缓存（双重检查）
                user = (User) redisTemplate.opsForValue().get(key);
                if (user != null) {
                    return user;
                }

                // 查询数据库
                user = userRepository.findById(userId).orElse(null);
                if (user != null) {
                    // 设置缓存，随机过期时间防止雪崩
                    long expireSeconds = 1800 + (long) (Math.random() * 600);
                    redisTemplate.opsForValue().set(key, user, Duration.ofSeconds(expireSeconds));
                }

                return user;
            } finally {
                // 释放锁
                redisTemplate.delete(lockKey);
            }
        } else {
            // 获取锁失败，等待一段时间后重试
            try {
                Thread.sleep(100);
                return getUserWithDistributedLock(userId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return null;
            }
        }
    }

    /**
     * 使用本地锁防止击穿（适用于单机）
     */
    public User getUserWithLocalLock(Long userId) {
        String key = "user:" + userId;

        // 1. 先尝试从缓存获取
        User user = (User) redisTemplate.opsForValue().get(key);
        if (user != null) {
            return user;
        }

        // 2. 获取本地锁
        Object lock = localLocks.computeIfAbsent(key, k -> new Object());

        synchronized (lock) {
            // 双重检查
            user = (User) redisTemplate.opsForValue().get(key);
            if (user != null) {
                return user;
            }

            // 查询数据库
            user = userRepository.findById(userId).orElse(null);
            if (user != null) {
                redisTemplate.opsForValue().set(key, user, Duration.ofMinutes(30));
            }

            return user;
        }
    }
}
```

### 3. 缓存雪崩

```java
@Service
@Slf4j
public class CacheAvalancheSolution {

    /**
     * 随机过期时间防止雪崩
     */
    public void setCacheWithRandomExpiration(String key, Object value, long baseSeconds) {
        // 在基础时间上增加随机时间（±20%）
        long randomOffset = (long) (baseSeconds * 0.2 * (Math.random() - 0.5) * 2);
        long finalSeconds = baseSeconds + randomOffset;

        redisTemplate.opsForValue().set(key, value, Duration.ofSeconds(finalSeconds));
    }

    /**
     * 多级缓存防雪崩
     */
    @Cacheable(value = "users", key = "#id")
    public User getUserWithMultiLevel(Long id) {
        // L1: Redis缓存
        String redisKey = "user:" + id;
        User user = (User) redisTemplate.opsForValue().get(redisKey);
        if (user != null) {
            return user;
        }

        // L2: 本地缓存
        user = localCache.get(redisKey);
        if (user != null) {
            // 异步回写Redis
            CompletableFuture.runAsync(() -> {
                setCacheWithRandomExpiration(redisKey, user, 1800);
            });
            return user;
        }

        // L3: 数据库
        user = userRepository.findById(id).orElse(null);
        if (user != null) {
            // 写入多级缓存
            localCache.put(redisKey, user);
            setCacheWithRandomExpiration(redisKey, user, 1800);
        }

        return user;
    }

    /**
     * 熔断降级防雪崩
     */
    @HystrixCommand(fallbackMethod = "getUserFallback")
    public User getUserWithCircuitBreaker(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    public User getUserFallback(Long id) {
        log.warn("缓存服务降级，返回默认用户: {}", id);
        // 返回默认用户或从备用数据源获取
        return createDefaultUser(id);
    }

    private User createDefaultUser(Long id) {
        User user = new User();
        user.setId(id);
        user.setUsername("default_user_" + id);
        return user;
    }
}
```

### 4. 缓存一致性

```java
@Service
@Slf4j
public class CacheConsistencySolution {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 更新数据库后清除缓存
     */
    @Transactional
    public User updateUserWithCacheEviction(User user) {
        // 1. 更新数据库
        User savedUser = userRepository.save(user);

        // 2. 清除相关缓存
        evictUserRelatedCache(user.getId());

        // 3. 发布事件通知其他服务
        eventPublisher.publishEvent(new UserUpdatedEvent(savedUser));

        return savedUser;
    }

    /**
     * 延时双删策略
     */
    @Transactional
    public User updateUserWithDelayedDoubleDelete(User user) {
        // 1. 先删除缓存
        evictUserCache(user.getId());

        // 2. 更新数据库
        User savedUser = userRepository.save(user);

        // 3. 延时再次删除缓存
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(1000); // 延时1秒
                evictUserCache(user.getId());
                log.info("延时双删完成: {}", user.getId());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        return savedUser;
    }

    /**
     * 基于消息队列的缓存同步
     */
    @EventListener
    @Async
    public void handleUserUpdatedEvent(UserUpdatedEvent event) {
        try {
            // 延时处理，确保数据库事务已提交
            Thread.sleep(500);

            User user = event.getUser();

            // 清除缓存
            evictUserRelatedCache(user.getId());

            // 预热新数据
            userService.getUserById(user.getId());

            log.info("缓存同步完成: {}", user.getId());
        } catch (Exception e) {
            log.error("缓存同步失败: {}", event.getUser().getId(), e);
        }
    }

    private void evictUserCache(Long userId) {
        cacheManager.getCache("users").evict(userId);
    }

    private void evictUserRelatedCache(Long userId) {
        // 清除用户相关的所有缓存
        cacheManager.getCache("users").evict(userId);
        cacheManager.getCache("userProfiles").evict(userId);
        cacheManager.getCache("userPermissions").evict(userId);

        // 清除用户相关的列表缓存
        cacheManager.getCache("userList").clear();
    }
}
```

## 📝 总结

Spring Boot缓存与Redis集成提供了强大的缓存解决方案：

### 核心优势
1. **简化开发**: 注解驱动，代码简洁
2. **高性能**: Redis内存存储，毫秒级响应
3. **高可用**: 支持集群、哨兵模式
4. **灵活配置**: 多种缓存策略和过期时间
5. **监控完善**: 丰富的监控指标和管理接口

### 最佳实践要点
1. **合理选择缓存数据**: 读多写少、计算成本高的数据
2. **设计好缓存Key**: 唯一性、可读性、层次性
3. **设置合适的过期时间**: 根据数据特性设置不同的TTL
4. **防止缓存问题**: 穿透、击穿、雪崩的预防措施
5. **保证数据一致性**: 合适的缓存更新策略
6. **监控和管理**: 完善的监控体系和管理工具

通过合理使用Spring Boot缓存和Redis，可以显著提升应用性能，改善用户体验，降低系统负载。
