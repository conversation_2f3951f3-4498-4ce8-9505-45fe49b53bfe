package oop;

import java.util.ArrayList;
import java.util.List;

/**
 * Student类 - 继承Person类
 * 演示继承、封装和多态的使用
 */
public class Student extends Person {
    private String major;           // 专业
    private double gpa;            // 平均绩点
    private List<Course> courses;  // 已选课程列表
    
    /**
     * 构造方法
     * @param name 姓名
     * @param age 年龄
     * @param studentId 学号
     * @param major 专业
     */
    public Student(String name, int age, String studentId, String major) {
        super(name, age, studentId);  // 调用父类构造方法
        this.major = major;
        this.gpa = 0.0;
        this.courses = new ArrayList<>();
    }
    
    /**
     * 实现父类的抽象方法
     */
    @Override
    public void displayInfo() {
        System.out.println("=== 学生信息 ===");
        System.out.println("姓名：" + name);
        System.out.println("年龄：" + age);
        System.out.println("学号：" + id);
        System.out.println("专业：" + major);
        System.out.println("GPA：" + String.format("%.2f", gpa));
        System.out.println("已选课程数：" + courses.size());
        
        if (!courses.isEmpty()) {
            System.out.println("课程列表：");
            for (Course course : courses) {
                System.out.println("  - " + course.getCourseName() + " (" + course.getCredits() + "学分)");
            }
        }
    }
    
    /**
     * 选课方法
     * @param course 要选择的课程
     */
    public void enrollCourse(Course course) {
        if (course == null) {
            System.out.println("课程不能为空");
            return;
        }
        
        if (!courses.contains(course)) {
            if (course.addStudent(this)) {
                courses.add(course);
                System.out.println(name + "成功选课：" + course.getCourseName());
            } else {
                System.out.println("选课失败：" + course.getCourseName() + " 已满员");
            }
        } else {
            System.out.println("已经选择了这门课程：" + course.getCourseName());
        }
    }
    
    /**
     * 退课方法
     * @param course 要退选的课程
     */
    public void dropCourse(Course course) {
        if (courses.remove(course)) {
            course.removeStudent(this);
            System.out.println(name + "成功退课：" + course.getCourseName());
        } else {
            System.out.println("未找到该课程：" + course.getCourseName());
        }
    }
    
    /**
     * 学习方法 - 学生特有的行为
     */
    public void study() {
        System.out.println(name + "正在努力学习" + major + "专业课程");
    }
    
    /**
     * 参加考试
     * @param courseName 课程名称
     * @param score 考试成绩
     */
    public void takeExam(String courseName, double score) {
        System.out.println(name + "参加了" + courseName + "考试，成绩：" + score);
        updateGPA(score);
    }
    
    /**
     * 更新GPA（简化计算）
     * @param newScore 新成绩
     */
    private void updateGPA(double newScore) {
        // 简化的GPA计算，实际应该考虑学分权重
        if (courses.size() > 0) {
            gpa = (gpa * (courses.size() - 1) + (newScore / 25.0)) / courses.size();
        } else {
            gpa = newScore / 25.0;
        }
    }
    
    /**
     * 重写greet方法，展示多态
     */
    @Override
    public void greet() {
        System.out.println("你好，我是" + name + "，我是" + major + "专业的学生");
    }
    
    // Getter和Setter方法
    public String getMajor() {
        return major;
    }
    
    public void setMajor(String major) {
        this.major = major;
    }
    
    public double getGpa() {
        return gpa;
    }
    
    public void setGpa(double gpa) {
        if (gpa >= 0.0 && gpa <= 4.0) {
            this.gpa = gpa;
        } else {
            System.out.println("GPA必须在0.0-4.0之间");
        }
    }
    
    public List<Course> getCourses() {
        return new ArrayList<>(courses);  // 返回副本，保护内部数据
    }
    
    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", studentId='" + id + '\'' +
                ", major='" + major + '\'' +
                ", gpa=" + String.format("%.2f", gpa) +
                ", coursesCount=" + courses.size() +
                '}';
    }
}
