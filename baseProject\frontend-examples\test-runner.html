<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端学习示例 - 测试运行器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .intro {
            text-align: center;
            margin-bottom: 40px;
        }

        .intro h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .intro p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .example-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .example-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .example-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .example-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .example-card p {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .example-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .example-features li {
            color: #27ae60;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .example-features li::before {
            content: "✓ ";
            font-weight: bold;
            margin-right: 5px;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            margin-left: 10px;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .progress-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .progress-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .progress-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .progress-step {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            position: relative;
        }

        .progress-step.completed {
            background: #d4edda;
            border: 2px solid #28a745;
        }

        .progress-step.current {
            background: #fff3cd;
            border: 2px solid #ffc107;
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
        }

        .progress-step.completed .step-number {
            background: #28a745;
        }

        .progress-step.current .step-number {
            background: #ffc107;
            color: #333;
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .step-description {
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .resources-section {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
        }

        .resources-section h3 {
            color: #1976d2;
            margin-bottom: 20px;
            text-align: center;
        }

        .resources-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .resource-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #1976d2;
        }

        .resource-item h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .resource-item p {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .resource-links {
            list-style: none;
        }

        .resource-links li {
            margin-bottom: 5px;
        }

        .resource-links a {
            color: #1976d2;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .resource-links a:hover {
            text-decoration: underline;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }

        .footer h3 {
            margin-bottom: 15px;
        }

        .footer p {
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .social-links a {
            color: white;
            font-size: 1.5rem;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #667eea;
        }

        @media (max-width: 768px) {
            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content {
                padding: 30px 20px;
            }

            .examples-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .progress-steps {
                grid-template-columns: 1fr;
            }

            .resources-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="header">
            <h1>🚀 前端开发完整学习示例</h1>
            <p>从零基础到实战项目的完整前端学习路径</p>
        </header>

        <!-- 主要内容 -->
        <main class="content">
            <!-- 介绍部分 -->
            <section class="intro">
                <h2>欢迎开始前端开发之旅！</h2>
                <p>这里包含了HTML、CSS、JavaScript的基础知识和一个完整的实战项目，帮助你系统地学习前端开发。</p>
            </section>

            <!-- 学习进度 -->
            <section class="progress-section">
                <h3>📈 学习进度跟踪</h3>
                <div class="progress-steps">
                    <div class="progress-step completed">
                        <div class="step-number">1</div>
                        <div class="step-title">HTML基础</div>
                        <div class="step-description">学习网页结构和语义化标签</div>
                    </div>
                    <div class="progress-step current">
                        <div class="step-number">2</div>
                        <div class="step-title">CSS样式</div>
                        <div class="step-description">掌握样式设计和布局技巧</div>
                    </div>
                    <div class="progress-step">
                        <div class="step-number">3</div>
                        <div class="step-title">JavaScript</div>
                        <div class="step-description">学习编程逻辑和DOM操作</div>
                    </div>
                    <div class="progress-step">
                        <div class="step-number">4</div>
                        <div class="step-title">实战项目</div>
                        <div class="step-description">综合运用所学知识</div>
                    </div>
                </div>
            </section>

            <!-- 示例列表 -->
            <section class="examples-grid">
                <!-- HTML基础示例 -->
                <div class="example-card">
                    <div class="example-icon">📝</div>
                    <h3>HTML基础示例</h3>
                    <p>学习HTML的基本语法、常用标签和语义化结构，为网页开发打下坚实基础。</p>
                    <ul class="example-features">
                        <li>文档结构和标签</li>
                        <li>表单和表格</li>
                        <li>语义化标签</li>
                        <li>媒体元素</li>
                        <li>可访问性</li>
                    </ul>
                    <a href="01-html-basics/index.html" class="btn" target="_blank">查看示例</a>
                </div>

                <!-- CSS基础示例 -->
                <div class="example-card">
                    <div class="example-icon">🎨</div>
                    <h3>CSS基础示例</h3>
                    <p>掌握CSS选择器、盒模型、布局方式和动画效果，让网页变得美观。</p>
                    <ul class="example-features">
                        <li>选择器和样式</li>
                        <li>Flexbox布局</li>
                        <li>Grid网格布局</li>
                        <li>响应式设计</li>
                        <li>CSS动画</li>
                    </ul>
                    <a href="02-css-basics/index.html" class="btn" target="_blank">查看示例</a>
                </div>

                <!-- JavaScript基础示例 -->
                <div class="example-card">
                    <div class="example-icon">⚡</div>
                    <h3>JavaScript基础示例</h3>
                    <p>学习JavaScript编程基础、DOM操作和事件处理，为网页添加交互功能。</p>
                    <ul class="example-features">
                        <li>变量和函数</li>
                        <li>DOM操作</li>
                        <li>事件处理</li>
                        <li>异步编程</li>
                        <li>实用工具</li>
                    </ul>
                    <a href="03-javascript-basics/index.html" class="btn" target="_blank">查看示例</a>
                </div>

                <!-- 实战项目 -->
                <div class="example-card">
                    <div class="example-icon">🚀</div>
                    <h3>待办事项应用</h3>
                    <p>完整的实战项目，综合运用HTML、CSS、JavaScript构建功能完善的应用。</p>
                    <ul class="example-features">
                        <li>完整的CRUD操作</li>
                        <li>搜索和过滤</li>
                        <li>数据持久化</li>
                        <li>响应式设计</li>
                        <li>用户体验优化</li>
                    </ul>
                    <a href="04-todo-app/index.html" class="btn" target="_blank">打开应用</a>
                </div>
            </section>

            <!-- 学习资源 -->
            <section class="resources-section">
                <h3>📚 推荐学习资源</h3>
                <div class="resources-grid">
                    <div class="resource-item">
                        <h4>📖 官方文档</h4>
                        <p>权威的技术文档和参考资料</p>
                        <ul class="resource-links">
                            <li><a href="https://developer.mozilla.org/" target="_blank">MDN Web Docs</a></li>
                            <li><a href="https://www.w3.org/" target="_blank">W3C Standards</a></li>
                            <li><a href="https://caniuse.com/" target="_blank">Can I Use</a></li>
                        </ul>
                    </div>

                    <div class="resource-item">
                        <h4>🎓 在线课程</h4>
                        <p>系统的学习课程和教程</p>
                        <ul class="resource-links">
                            <li><a href="https://www.freecodecamp.org/" target="_blank">freeCodeCamp</a></li>
                            <li><a href="https://www.codecademy.com/" target="_blank">Codecademy</a></li>
                            <li><a href="https://www.imooc.com/" target="_blank">慕课网</a></li>
                        </ul>
                    </div>

                    <div class="resource-item">
                        <h4>💻 实践平台</h4>
                        <p>在线编程和项目托管平台</p>
                        <ul class="resource-links">
                            <li><a href="https://codepen.io/" target="_blank">CodePen</a></li>
                            <li><a href="https://jsfiddle.net/" target="_blank">JSFiddle</a></li>
                            <li><a href="https://github.com/" target="_blank">GitHub</a></li>
                        </ul>
                    </div>

                    <div class="resource-item">
                        <h4>🛠️ 开发工具</h4>
                        <p>提高开发效率的工具和插件</p>
                        <ul class="resource-links">
                            <li><a href="https://code.visualstudio.com/" target="_blank">VS Code</a></li>
                            <li><a href="https://www.google.com/chrome/" target="_blank">Chrome DevTools</a></li>
                            <li><a href="https://git-scm.com/" target="_blank">Git</a></li>
                        </ul>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页面底部 -->
        <footer class="footer">
            <h3>🎯 开始你的前端开发之旅</h3>
            <p>从基础到实战，一步步成为优秀的前端开发者！</p>
            <div class="social-links">
                <a href="#" title="GitHub">📱</a>
                <a href="#" title="Twitter">🐦</a>
                <a href="#" title="LinkedIn">💼</a>
                <a href="#" title="Email">📧</a>
            </div>
        </footer>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 前端学习示例测试运行器加载完成！');
            
            // 添加平滑滚动效果
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
            
            // 检查示例文件是否存在
            checkExampleFiles();
            
            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // 按数字键1-4快速打开对应示例
                if (e.key >= '1' && e.key <= '4') {
                    const examples = [
                        '01-html-basics/index.html',
                        '02-css-basics/index.html', 
                        '03-javascript-basics/index.html',
                        '04-todo-app/index.html'
                    ];
                    
                    const index = parseInt(e.key) - 1;
                    if (examples[index]) {
                        window.open(examples[index], '_blank');
                    }
                }
            });
            
            // 显示学习提示
            showLearningTips();
        });
        
        // 检查示例文件
        function checkExampleFiles() {
            const examples = [
                { name: 'HTML基础', path: '01-html-basics/index.html' },
                { name: 'CSS基础', path: '02-css-basics/index.html' },
                { name: 'JavaScript基础', path: '03-javascript-basics/index.html' },
                { name: '待办事项应用', path: '04-todo-app/index.html' }
            ];
            
            examples.forEach(example => {
                fetch(example.path, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            console.log(`✅ ${example.name} 文件存在`);
                        } else {
                            console.warn(`⚠️ ${example.name} 文件不存在`);
                        }
                    })
                    .catch(error => {
                        console.warn(`⚠️ 无法检查 ${example.name} 文件:`, error);
                    });
            });
        }
        
        // 显示学习提示
        function showLearningTips() {
            const tips = [
                '💡 建议按顺序学习：HTML → CSS → JavaScript → 实战项目',
                '🔧 使用浏览器开发者工具调试代码',
                '📝 动手实践比只看理论更重要',
                '🎯 每完成一个示例就尝试修改代码',
                '⌨️ 使用数字键1-4快速打开对应示例'
            ];
            
            tips.forEach((tip, index) => {
                setTimeout(() => {
                    console.log(tip);
                }, index * 1000);
            });
        }
        
        // 更新学习进度
        function updateProgress(step) {
            const steps = document.querySelectorAll('.progress-step');
            steps.forEach((stepEl, index) => {
                stepEl.classList.remove('current', 'completed');
                if (index < step) {
                    stepEl.classList.add('completed');
                } else if (index === step) {
                    stepEl.classList.add('current');
                }
            });
            
            // 保存进度到本地存储
            localStorage.setItem('learningProgress', step);
        }
        
        // 加载学习进度
        function loadProgress() {
            const savedProgress = localStorage.getItem('learningProgress');
            if (savedProgress !== null) {
                updateProgress(parseInt(savedProgress));
            }
        }
        
        // 页面加载时恢复进度
        loadProgress();
        
        console.log(`
🎉 前端学习示例测试运行器

📚 包含的示例:
1. HTML基础 - 网页结构和语义化
2. CSS基础 - 样式设计和布局
3. JavaScript基础 - 编程逻辑和交互
4. 待办事项应用 - 完整实战项目

⌨️ 快捷键:
- 数字键 1-4: 快速打开对应示例

💡 学习建议:
- 按顺序学习，循序渐进
- 多动手实践，修改代码
- 使用开发者工具调试
- 完成每个示例后再进入下一个

🚀 开始你的前端开发之旅吧！
        `);
    </script>
</body>
</html>
