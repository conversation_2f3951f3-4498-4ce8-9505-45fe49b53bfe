<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    
    <!-- 属性配置文件 -->
    <properties resource="database.properties">
        <!-- 可以在这里覆盖properties文件中的属性 -->
    </properties>
    
    <!-- 全局设置 -->
    <settings>
        <!-- 开启驼峰命名自动映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        
        <!-- 开启延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        
        <!-- 设置积极的延迟加载，当设置为false时，按需加载 -->
        <setting name="aggressiveLazyLoading" value="false"/>
        
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true"/>
        
        <!-- 设置超时时间，单位秒 -->
        <setting name="defaultStatementTimeout" value="30"/>
        
        <!-- 设置获取数据的默认大小 -->
        <setting name="defaultFetchSize" value="100"/>
        
        <!-- 允许在嵌套语句中使用分页 -->
        <setting name="safeRowBoundsEnabled" value="false"/>
        
        <!-- 是否开启自动驼峰命名规则映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        
        <!-- 本地缓存机制，SESSION会缓存一个会话中执行的所有查询 -->
        <setting name="localCacheScope" value="SESSION"/>
        
        <!-- 当没有为参数提供特定的JDBC类型时，为空值指定JDBC类型 -->
        <setting name="jdbcTypeForNull" value="OTHER"/>
        
        <!-- 指定哪个对象的方法触发一次延迟加载 -->
        <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
        
        <!-- 设置日志实现 -->
        <setting name="logImpl" value="SLF4J"/>
        
        <!-- 指定MyBatis应如何自动映射列到字段或属性 -->
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        
        <!-- 配置默认的执行器 -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        
        <!-- 指定发现自动映射目标未知列的行为 -->
        <setting name="autoMappingUnknownColumnBehavior" value="WARNING"/>
        
        <!-- 是否允许单一语句返回多结果集 -->
        <setting name="multipleResultSetsEnabled" value="true"/>
        
        <!-- 使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true"/>
        
        <!-- 允许JDBC支持自动生成主键 -->
        <setting name="useGeneratedKeys" value="false"/>
        
        <!-- 指定动态SQL生成的默认语言 -->
        <setting name="defaultScriptingLanguage" value="org.apache.ibatis.scripting.xmltags.XMLLanguageDriver"/>
        
        <!-- 当结果集中值为null的时候是否调用映射对象的setter方法 -->
        <setting name="callSettersOnNulls" value="false"/>
        
        <!-- 是否返回空行 -->
        <setting name="returnInstanceForEmptyRow" value="false"/>
        
        <!-- 指定MyBatis增加到日志名称的前缀 -->
        <setting name="logPrefix" value="mybatis."/>
        
        <!-- 指定MyBatis所用日志的具体实现 -->
        <setting name="logImpl" value="SLF4J"/>
        
        <!-- 使用实际的参数名称 -->
        <setting name="useActualParamName" value="true"/>
        
        <!-- 指定一个提供Configuration实例的类 -->
        <setting name="configurationFactory" value=""/>
    </settings>
    
    <!-- 类型别名 -->
    <typeAliases>
        <!-- 单个类型别名 -->
        <typeAlias type="com.example.entity.User" alias="User"/>
        <typeAlias type="com.example.entity.Role" alias="Role"/>
        <typeAlias type="com.example.entity.UserRole" alias="UserRole"/>
        <typeAlias type="com.example.entity.Permission" alias="Permission"/>
        <typeAlias type="com.example.entity.RolePermission" alias="RolePermission"/>
        
        <!-- 包扫描，自动为包下的类创建别名 -->
        <package name="com.example.entity"/>
        <package name="com.example.dto"/>
        <package name="com.example.vo"/>
    </typeAliases>
    
    <!-- 类型处理器 -->
    <typeHandlers>
        <!-- 自定义类型处理器 -->
        <typeHandler handler="com.example.handler.JsonTypeHandler"/>
        <typeHandler handler="com.example.handler.DateTypeHandler"/>
        <typeHandler handler="com.example.handler.EnumTypeHandler"/>
        
        <!-- 包扫描类型处理器 -->
        <package name="com.example.handler"/>
    </typeHandlers>
    
    <!-- 对象工厂 -->
    <objectFactory type="com.example.factory.CustomObjectFactory">
        <property name="someProperty" value="100"/>
    </objectFactory>
    
    <!-- 插件配置 -->
    <plugins>
        <!-- 分页插件 -->
        <plugin interceptor="com.github.pagehelper.PageInterceptor">
            <!-- 分页插件参数 -->
            <property name="helperDialect" value="mysql"/>
            <property name="reasonable" value="true"/>
            <property name="supportMethodsArguments" value="true"/>
            <property name="params" value="count=countSql"/>
            <property name="autoRuntimeDialect" value="true"/>
        </plugin>
        
        <!-- SQL执行时间监控插件 -->
        <plugin interceptor="com.example.plugin.SqlExecutionTimePlugin">
            <property name="slowSqlThreshold" value="1000"/>
            <property name="logSlowSql" value="true"/>
        </plugin>
        
        <!-- 数据权限插件 -->
        <plugin interceptor="com.example.plugin.DataPermissionPlugin">
            <property name="enable" value="true"/>
        </plugin>
        
        <!-- 自动填充插件 -->
        <plugin interceptor="com.example.plugin.AutoFillPlugin">
            <property name="createTimeField" value="createTime"/>
            <property name="updateTimeField" value="updateTime"/>
            <property name="createUserField" value="createUser"/>
            <property name="updateUserField" value="updateUser"/>
        </plugin>
    </plugins>
    
    <!-- 环境配置 -->
    <environments default="development">
        
        <!-- 开发环境 -->
        <environment id="development">
            <!-- 事务管理器 -->
            <transactionManager type="JDBC">
                <property name="closeConnection" value="false"/>
            </transactionManager>
            
            <!-- 数据源配置 -->
            <dataSource type="POOLED">
                <property name="driver" value="${database.driver}"/>
                <property name="url" value="${database.url}"/>
                <property name="username" value="${database.username}"/>
                <property name="password" value="${database.password}"/>
                
                <!-- 连接池配置 -->
                <property name="poolMaximumActiveConnections" value="20"/>
                <property name="poolMaximumIdleConnections" value="5"/>
                <property name="poolMaximumCheckoutTime" value="20000"/>
                <property name="poolTimeToWait" value="20000"/>
                <property name="poolMaximumLocalBadConnectionTolerance" value="3"/>
                <property name="poolPingEnabled" value="true"/>
                <property name="poolPingQuery" value="SELECT 1"/>
                <property name="poolPingConnectionsNotUsedFor" value="19000"/>
            </dataSource>
        </environment>
        
        <!-- 测试环境 -->
        <environment id="test">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="${database.driver}"/>
                <property name="url" value="${database.test.url}"/>
                <property name="username" value="${database.test.username}"/>
                <property name="password" value="${database.test.password}"/>
                
                <!-- 测试环境连接池配置 -->
                <property name="poolMaximumActiveConnections" value="10"/>
                <property name="poolMaximumIdleConnections" value="3"/>
                <property name="poolMaximumCheckoutTime" value="10000"/>
                <property name="poolTimeToWait" value="10000"/>
            </dataSource>
        </environment>
        
        <!-- 生产环境 -->
        <environment id="production">
            <transactionManager type="JDBC"/>
            <!-- 使用JNDI数据源 -->
            <dataSource type="JNDI">
                <property name="data_source" value="java:comp/env/jdbc/MyDataSource"/>
            </dataSource>
        </environment>
        
        <!-- 使用Druid数据源的环境 -->
        <environment id="druid">
            <transactionManager type="JDBC"/>
            <dataSource type="com.example.datasource.DruidDataSourceFactory">
                <property name="driverClassName" value="${database.driver}"/>
                <property name="url" value="${database.url}"/>
                <property name="username" value="${database.username}"/>
                <property name="password" value="${database.password}"/>
                
                <!-- Druid特有配置 -->
                <property name="initialSize" value="5"/>
                <property name="minIdle" value="5"/>
                <property name="maxActive" value="20"/>
                <property name="maxWait" value="60000"/>
                <property name="timeBetweenEvictionRunsMillis" value="60000"/>
                <property name="minEvictableIdleTimeMillis" value="300000"/>
                <property name="validationQuery" value="SELECT 1"/>
                <property name="testWhileIdle" value="true"/>
                <property name="testOnBorrow" value="false"/>
                <property name="testOnReturn" value="false"/>
                <property name="poolPreparedStatements" value="true"/>
                <property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>
                <property name="filters" value="stat,wall,slf4j"/>
                <property name="connectionProperties" value="druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000"/>
            </dataSource>
        </environment>
    </environments>
    
    <!-- 数据库厂商标识 -->
    <databaseIdProvider type="DB_VENDOR">
        <property name="MySQL" value="mysql"/>
        <property name="Oracle" value="oracle"/>
        <property name="PostgreSQL" value="postgresql"/>
        <property name="SQL Server" value="sqlserver"/>
        <property name="H2" value="h2"/>
    </databaseIdProvider>
    
    <!-- 映射器配置 -->
    <mappers>
        <!-- 单个映射文件 -->
        <mapper resource="mapper/UserMapper.xml"/>
        <mapper resource="mapper/RoleMapper.xml"/>
        <mapper resource="mapper/UserRoleMapper.xml"/>
        <mapper resource="mapper/PermissionMapper.xml"/>
        <mapper resource="mapper/RolePermissionMapper.xml"/>
        
        <!-- 使用类路径 -->
        <mapper class="com.example.mapper.UserMapper"/>
        <mapper class="com.example.mapper.RoleMapper"/>
        
        <!-- 使用URL -->
        <!-- <mapper url="file:///var/mappers/UserMapper.xml"/> -->
        
        <!-- 包扫描 -->
        <package name="com.example.mapper"/>
    </mappers>
    
</configuration>
