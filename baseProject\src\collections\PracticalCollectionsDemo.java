package collections;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实际应用场景演示
 * 展示集合在实际项目中的应用
 */
public class PracticalCollectionsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 集合实际应用演示 ===");
        
        // 1. 学生管理系统
        studentManagementDemo();
        
        // 2. 购物车系统
        shoppingCartDemo();
        
        // 3. 单词统计
        wordCountDemo();
        
        // 4. 数据分析
        dataAnalysisDemo();
    }
    
    /**
     * 学生管理系统演示
     */
    public static void studentManagementDemo() {
        System.out.println("\n--- 学生管理系统演示 ---");
        
        StudentManagementSystem sms = new StudentManagementSystem();
        
        // 添加学生
        sms.addStudent(new StudentInfo("S001", "张三", 20));
        sms.addStudent(new StudentInfo("S002", "李四", 21));
        sms.addStudent(new StudentInfo("S003", "王五", 19));
        sms.addStudent(new StudentInfo("S004", "赵六", 22));
        
        // 添加课程
        sms.addCourse("数学");
        sms.addCourse("英语");
        sms.addCourse("计算机");
        
        // 添加成绩
        sms.addGrade("S001", "数学", 85);
        sms.addGrade("S001", "英语", 78);
        sms.addGrade("S001", "计算机", 92);
        
        sms.addGrade("S002", "数学", 90);
        sms.addGrade("S002", "英语", 88);
        sms.addGrade("S002", "计算机", 85);
        
        sms.addGrade("S003", "数学", 75);
        sms.addGrade("S003", "英语", 82);
        sms.addGrade("S003", "计算机", 88);
        
        // 查询功能
        System.out.println("所有学生（按姓名排序）:");
        sms.getAllStudentsSorted().forEach(System.out::println);
        
        System.out.println("\n张三的平均分: " + sms.calculateAverageScore("S001"));
        
        System.out.println("\n数学课程排行榜:");
        sms.getCourseRanking("数学").forEach(student -> 
            System.out.println(student.getName() + " - " + student.getStudentId()));
    }
    
    /**
     * 购物车系统演示
     */
    public static void shoppingCartDemo() {
        System.out.println("\n--- 购物车系统演示 ---");
        
        ShoppingCart cart = new ShoppingCart();
        
        // 创建商品
        Product laptop = new Product("P001", "笔记本电脑", 5999.0, "电子产品");
        Product mouse = new Product("P002", "无线鼠标", 99.0, "电子产品");
        Product book = new Product("P003", "Java编程思想", 89.0, "图书");
        Product phone = new Product("P004", "智能手机", 3999.0, "电子产品");
        
        // 添加商品到购物车
        cart.addItem(laptop, 1);
        cart.addItem(mouse, 2);
        cart.addItem(book, 3);
        cart.addItem(phone, 1);
        
        System.out.println("购物车商品总数: " + cart.getTotalItems());
        System.out.println("购物车总价: ¥" + cart.getTotalPrice());
        
        // 按价格排序显示商品
        System.out.println("\n按价格排序的商品:");
        cart.getItemsSortedByPrice().forEach(entry -> 
            System.out.println(entry.getKey().getName() + " x" + entry.getValue() + 
                " - ¥" + entry.getKey().getPrice()));
        
        // 更新数量
        cart.updateQuantity(mouse, 1);
        System.out.println("\n更新鼠标数量后总价: ¥" + cart.getTotalPrice());
        
        // 移除商品
        cart.removeItem(book);
        System.out.println("移除图书后商品总数: " + cart.getTotalItems());
    }
    
    /**
     * 单词统计演示
     */
    public static void wordCountDemo() {
        System.out.println("\n--- 单词统计演示 ---");
        
        String text = "Java is a programming language. " +
                     "Java is object-oriented. " +
                     "Programming with Java is fun. " +
                     "Java programming language is powerful.";
        
        WordCounter counter = new WordCounter();
        Map<String, Integer> wordCount = counter.countWords(text);
        
        System.out.println("原文: " + text);
        System.out.println("\n单词统计结果:");
        
        // 按出现次数降序排序
        wordCount.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> 
                System.out.println(entry.getKey() + ": " + entry.getValue()));
        
        // 获取出现次数最多的单词
        String mostFrequent = counter.getMostFrequentWord(wordCount);
        System.out.println("\n出现最多的单词: " + mostFrequent);
        
        // 获取长度大于4的单词
        Set<String> longWords = counter.getWordsLongerThan(wordCount.keySet(), 4);
        System.out.println("长度大于4的单词: " + longWords);
    }
    
    /**
     * 数据分析演示
     */
    public static void dataAnalysisDemo() {
        System.out.println("\n--- 数据分析演示 ---");
        
        // 模拟销售数据
        List<SalesRecord> salesData = Arrays.asList(
            new SalesRecord("2023-01", "电子产品", 15000),
            new SalesRecord("2023-01", "服装", 8000),
            new SalesRecord("2023-01", "图书", 3000),
            new SalesRecord("2023-02", "电子产品", 18000),
            new SalesRecord("2023-02", "服装", 9500),
            new SalesRecord("2023-02", "图书", 3500),
            new SalesRecord("2023-03", "电子产品", 20000),
            new SalesRecord("2023-03", "服装", 11000),
            new SalesRecord("2023-03", "图书", 4000)
        );
        
        DataAnalyzer analyzer = new DataAnalyzer();
        
        // 按月份分组统计
        Map<String, Double> monthlyTotal = analyzer.getTotalByMonth(salesData);
        System.out.println("按月份统计总销售额:");
        monthlyTotal.forEach((month, total) -> 
            System.out.println(month + ": ¥" + total));
        
        // 按类别分组统计
        Map<String, Double> categoryTotal = analyzer.getTotalByCategory(salesData);
        System.out.println("\n按类别统计总销售额:");
        categoryTotal.forEach((category, total) -> 
            System.out.println(category + ": ¥" + total));
        
        // 找出销售额最高的月份
        String bestMonth = analyzer.getBestMonth(salesData);
        System.out.println("\n销售额最高的月份: " + bestMonth);
        
        // 计算平均销售额
        double averageSales = analyzer.getAverageSales(salesData);
        System.out.println("平均销售额: ¥" + averageSales);
        
        // 找出高于平均值的记录
        List<SalesRecord> aboveAverage = analyzer.getAboveAverageRecords(salesData);
        System.out.println("\n高于平均值的销售记录:");
        aboveAverage.forEach(System.out::println);
    }
}

/**
 * 学生类（用于实际应用演示）
 */
class StudentInfo {
    private String studentId;
    private String name;
    private int age;

    public StudentInfo(String studentId, String name, int age) {
        this.studentId = studentId;
        this.name = name;
        this.age = age;
    }

    // Getters
    public String getStudentId() { return studentId; }
    public String getName() { return name; }
    public int getAge() { return age; }

    @Override
    public String toString() {
        return String.format("Student{id='%s', name='%s', age=%d}", studentId, name, age);
    }
}

/**
 * 成绩类（用于实际应用演示）
 */
class GradeInfo {
    private String studentId;
    private String course;
    private double score;

    public GradeInfo(String studentId, String course, double score) {
        this.studentId = studentId;
        this.course = course;
        this.score = score;
    }

    // Getters
    public String getStudentId() { return studentId; }
    public String getCourse() { return course; }
    public double getScore() { return score; }
}

/**
 * 学生管理系统
 */
class StudentManagementSystem {
    private Map<String, StudentInfo> students = new HashMap<>();
    private Set<String> courses = new HashSet<>();
    private List<GradeInfo> grades = new ArrayList<>();

    public void addStudent(StudentInfo student) {
        students.put(student.getStudentId(), student);
    }

    public StudentInfo findStudent(String studentId) {
        return students.get(studentId);
    }

    public List<StudentInfo> getAllStudentsSorted() {
        return students.values().stream()
            .sorted(Comparator.comparing(StudentInfo::getName))
            .collect(Collectors.toList());
    }

    public void addCourse(String course) {
        courses.add(course);
    }

    public void addGrade(String studentId, String course, double score) {
        if (students.containsKey(studentId) && courses.contains(course)) {
            grades.add(new GradeInfo(studentId, course, score));
        }
    }

    public double calculateAverageScore(String studentId) {
        return grades.stream()
            .filter(grade -> grade.getStudentId().equals(studentId))
            .mapToDouble(GradeInfo::getScore)
            .average()
            .orElse(0.0);
    }

    public List<StudentInfo> getCourseRanking(String course) {
        Map<String, Double> courseScores = grades.stream()
            .filter(grade -> grade.getCourse().equals(course))
            .collect(Collectors.toMap(
                GradeInfo::getStudentId,
                GradeInfo::getScore,
                (existing, replacement) -> existing
            ));

        return courseScores.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .map(entry -> students.get(entry.getKey()))
            .collect(Collectors.toList());
    }
}

/**
 * 商品类
 */
class Product {
    private String id;
    private String name;
    private double price;
    private String category;
    
    public Product(String id, String name, double price, String category) {
        this.id = id;
        this.name = name;
        this.price = price;
        this.category = category;
    }
    
    // Getters
    public String getId() { return id; }
    public String getName() { return name; }
    public double getPrice() { return price; }
    public String getCategory() { return category; }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Product product = (Product) obj;
        return Objects.equals(id, product.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return String.format("Product{id='%s', name='%s', price=%.2f}", id, name, price);
    }
}

/**
 * 购物车类
 */
class ShoppingCart {
    private Map<Product, Integer> items = new LinkedHashMap<>();
    
    public void addItem(Product product, int quantity) {
        items.merge(product, quantity, Integer::sum);
    }
    
    public void removeItem(Product product) {
        items.remove(product);
    }
    
    public void updateQuantity(Product product, int quantity) {
        if (quantity <= 0) {
            removeItem(product);
        } else {
            items.put(product, quantity);
        }
    }
    
    public double getTotalPrice() {
        return items.entrySet().stream()
            .mapToDouble(entry -> entry.getKey().getPrice() * entry.getValue())
            .sum();
    }
    
    public int getTotalItems() {
        return items.values().stream().mapToInt(Integer::intValue).sum();
    }
    
    public List<Map.Entry<Product, Integer>> getItemsSortedByPrice() {
        return items.entrySet().stream()
            .sorted(Map.Entry.comparingByKey(Comparator.comparing(Product::getPrice)))
            .collect(Collectors.toList());
    }
    
    public boolean isEmpty() {
        return items.isEmpty();
    }
    
    public void clear() {
        items.clear();
    }
}

/**
 * 单词计数器
 */
class WordCounter {
    
    public Map<String, Integer> countWords(String text) {
        Map<String, Integer> wordCount = new HashMap<>();
        
        // 分割单词，转换为小写，过滤空字符串
        String[] words = text.toLowerCase()
            .replaceAll("[^a-zA-Z\\s]", "") // 移除标点符号
            .split("\\s+");
        
        for (String word : words) {
            if (!word.isEmpty()) {
                wordCount.merge(word, 1, Integer::sum);
            }
        }
        
        return wordCount;
    }
    
    public String getMostFrequentWord(Map<String, Integer> wordCount) {
        return wordCount.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("");
    }
    
    public Set<String> getWordsLongerThan(Set<String> words, int length) {
        return words.stream()
            .filter(word -> word.length() > length)
            .collect(Collectors.toSet());
    }
}

/**
 * 销售记录类
 */
class SalesRecord {
    private String month;
    private String category;
    private double amount;
    
    public SalesRecord(String month, String category, double amount) {
        this.month = month;
        this.category = category;
        this.amount = amount;
    }
    
    // Getters
    public String getMonth() { return month; }
    public String getCategory() { return category; }
    public double getAmount() { return amount; }
    
    @Override
    public String toString() {
        return String.format("SalesRecord{month='%s', category='%s', amount=%.2f}", 
                           month, category, amount);
    }
}

/**
 * 数据分析器
 */
class DataAnalyzer {
    
    public Map<String, Double> getTotalByMonth(List<SalesRecord> records) {
        return records.stream()
            .collect(Collectors.groupingBy(
                SalesRecord::getMonth,
                Collectors.summingDouble(SalesRecord::getAmount)
            ));
    }
    
    public Map<String, Double> getTotalByCategory(List<SalesRecord> records) {
        return records.stream()
            .collect(Collectors.groupingBy(
                SalesRecord::getCategory,
                Collectors.summingDouble(SalesRecord::getAmount)
            ));
    }
    
    public String getBestMonth(List<SalesRecord> records) {
        return getTotalByMonth(records).entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("");
    }
    
    public double getAverageSales(List<SalesRecord> records) {
        return records.stream()
            .mapToDouble(SalesRecord::getAmount)
            .average()
            .orElse(0.0);
    }
    
    public List<SalesRecord> getAboveAverageRecords(List<SalesRecord> records) {
        double average = getAverageSales(records);
        return records.stream()
            .filter(record -> record.getAmount() > average)
            .collect(Collectors.toList());
    }
}
