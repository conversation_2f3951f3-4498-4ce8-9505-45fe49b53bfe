# Spring Boot + Shiro 权限管理系统详解

## 📋 项目简介

这是一个基于Spring Boot + Apache Shiro + MyBatis实现的完整权限管理系统。项目采用经典的RBAC（基于角色的访问控制）模型，提供了用户认证、授权、会话管理、缓存等完整功能。

## 🚀 技术栈

- **Spring Boot 2.7.14**: 应用框架
- **Apache Shiro 1.12.0**: 安全框架
- **MyBatis 2.3.1**: ORM框架
- **MySQL 8.0**: 数据库
- **Druid 1.2.18**: 数据库连接池
- **EhCache**: 缓存框架
- **Thymeleaf**: 模板引擎
- **Hutool**: 工具类库
- **Lombok**: 代码简化

## 📦 项目结构

```
SpringBoot-Shiro实战项目/
├── src/
│   ├── main/
│   │   ├── java/com/example/
│   │   │   ├── config/
│   │   │   │   └── ShiroConfig.java           # Shiro配置类
│   │   │   ├── controller/
│   │   │   │   ├── AuthController.java        # 认证控制器
│   │   │   │   └── SysUserController.java     # 用户管理控制器
│   │   │   ├── entity/
│   │   │   │   ├── SysUser.java               # 用户实体
│   │   │   │   ├── SysRole.java               # 角色实体
│   │   │   │   └── SysPermission.java         # 权限实体
│   │   │   ├── mapper/
│   │   │   │   └── SysUserMapper.java         # 用户Mapper
│   │   │   ├── service/
│   │   │   │   ├── SysUserService.java        # 用户服务接口
│   │   │   │   └── impl/
│   │   │   │       └── SysUserServiceImpl.java # 用户服务实现
│   │   │   ├── shiro/
│   │   │   │   └── ShiroRealm.java            # 自定义Realm
│   │   │   ├── vo/
│   │   │   │   ├── ApiResponse.java           # API响应对象
│   │   │   │   ├── LoginRequest.java          # 登录请求对象
│   │   │   │   └── LoginResponse.java         # 登录响应对象
│   │   │   └── ShiroApplication.java          # 启动类
│   │   └── resources/
│   │       ├── mapper/
│   │       │   └── SysUserMapper.xml          # MyBatis映射文件
│   │       ├── ehcache.xml                    # EhCache配置
│   │       └── application.yml                # 应用配置
│   └── test/
│       └── java/com/example/
│           └── ShiroApplicationTest.java      # 测试类
├── sql/
│   └── shiro_schema.sql                       # 数据库表结构
├── pom.xml                                    # Maven配置
└── README.md                                  # 项目说明
```

## 🔧 Apache Shiro 详解

### 1. Shiro核心概念

#### Subject（主体）
- 代表当前用户，可以是人、第三方服务、定时任务等
- 通过SecurityUtils.getSubject()获取
- 提供认证、授权、会话管理等功能

#### SecurityManager（安全管理器）
- Shiro的核心，协调各个组件工作
- 管理所有Subject的安全操作
- 类似于Spring的DispatcherServlet

#### Realm（域）
- 数据源，负责获取安全数据（用户、角色、权限）
- 可以有多个Realm，支持多种数据源
- 本项目实现了自定义的ShiroRealm

### 2. Shiro架构图

```
Subject → SecurityManager → Realm
    ↓           ↓              ↓
  用户操作    安全管理      数据获取
    ↓           ↓              ↓
认证/授权   协调各组件    用户/角色/权限
```

### 3. 认证流程

```java
// 1. 获取Subject
Subject subject = SecurityUtils.getSubject();

// 2. 创建认证令牌
UsernamePasswordToken token = new UsernamePasswordToken(username, password);

// 3. 执行登录
subject.login(token);

// 4. Shiro调用Realm的doGetAuthenticationInfo方法进行认证
```

### 4. 授权流程

```java
// 角色检查
subject.hasRole("admin");

// 权限检查
subject.isPermitted("system:user:view");

// 注解方式
@RequiresPermissions("system:user:view")
public void viewUser() { ... }
```

## 🔐 权限控制详解

### 1. RBAC模型

```
用户(User) ←→ 角色(Role) ←→ 权限(Permission)
     ↓           ↓              ↓
   admin    →   admin    →   所有权限
  manager   →  manager   →  用户管理权限
   user     →   user     →  个人中心权限
```

### 2. 权限表达式

```java
// 角色权限
@RequiresRoles("admin")                    // 需要admin角色
@RequiresRoles({"admin", "manager"})       // 需要admin或manager角色

// 资源权限
@RequiresPermissions("system:user:view")   // 需要用户查看权限
@RequiresPermissions("system:user:add")    // 需要用户新增权限

// 逻辑表达式
@RequiresPermissions("system:user:view,system:user:edit") // AND关系
```

### 3. 自定义Realm实现

<augment_code_snippet path="SpringBoot-Shiro实战项目/src/main/java/com/example/shiro/ShiroRealm.java" mode="EXCERPT">
````java
@Component
public class ShiroRealm extends AuthorizingRealm {
    
    // 授权方法 - 获取用户的角色和权限
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        SysUser user = (SysUser) principals.getPrimaryPrincipal();
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        
        // 设置角色
        Set<String> roles = new HashSet<>();
        // 设置权限
        Set<String> permissions = new HashSet<>();
        
        return authorizationInfo;
    }
    
    // 认证方法 - 验证用户登录信息
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        UsernamePasswordToken upToken = (UsernamePasswordToken) token;
        String username = upToken.getUsername();
        
        // 查询用户信息
        SysUser user = userService.getUserByUsername(username);
        
        // 构建认证信息
        return new SimpleAuthenticationInfo(user, user.getPassword(), 
                ByteSource.Util.bytes(user.getCredentialsSalt()), getName());
    }
}
````
</augment_code_snippet>

## ⚙️ Shiro配置详解

### 1. 核心配置

<augment_code_snippet path="SpringBoot-Shiro实战项目/src/main/java/com/example/config/ShiroConfig.java" mode="EXCERPT">
````java
@Configuration
public class ShiroConfig {
    
    // 密码凭证匹配器
    @Bean
    public HashedCredentialsMatcher hashedCredentialsMatcher() {
        HashedCredentialsMatcher credentialsMatcher = new HashedCredentialsMatcher();
        credentialsMatcher.setHashAlgorithmName("MD5");  // 加密算法
        credentialsMatcher.setHashIterations(1);         // 加密次数
        return credentialsMatcher;
    }
    
    // 安全管理器
    @Bean
    public SecurityManager securityManager() {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(myShiroRealm());           // 设置realm
        securityManager.setCacheManager(ehCacheManager());  // 设置缓存
        securityManager.setSessionManager(sessionManager()); // 设置会话管理
        return securityManager;
    }
}
````
</augment_code_snippet>

### 2. 过滤器链配置

```java
// 过滤器链定义
Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();

// anon: 匿名访问
filterChainDefinitionMap.put("/login", "anon");
filterChainDefinitionMap.put("/api/auth/login", "anon");

// authc: 需要认证
filterChainDefinitionMap.put("/api/**", "authc");

// perms: 需要特定权限
filterChainDefinitionMap.put("/system/user/**", "authc,perms[system:user:view]");

// roles: 需要特定角色
filterChainDefinitionMap.put("/admin/**", "authc,roles[admin]");
```

### 3. 会话管理配置

```java
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    sessionManager.setGlobalSessionTimeout(1800000);  // 30分钟
    sessionManager.setDeleteInvalidSessions(true);    // 删除过期session
    sessionManager.setSessionIdCookieEnabled(true);   // 启用cookie
    return sessionManager;
}
```

## 💾 缓存配置

### 1. EhCache配置

<augment_code_snippet path="SpringBoot-Shiro实战项目/src/main/resources/ehcache.xml" mode="EXCERPT">
````xml
<ehcache>
    <!-- 认证缓存 -->
    <cache name="authenticationCache"
           maxElementsInMemory="2000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="1800"/>
    
    <!-- 授权缓存 -->
    <cache name="authorizationCache"
           maxElementsInMemory="2000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="1800"/>
</ehcache>
````
</augment_code_snippet>

### 2. 缓存管理

```java
// 清除用户缓存
public void clearUserCache(String username) {
    shiroRealm.clearCache(username);
}

// 清除所有缓存
public void clearAllCache() {
    shiroRealm.clearAllCache();
}
```

## 🔒 密码安全

### 1. 密码加密

```java
// 生成盐值
String salt = UUID.randomUUID().toString().replace("-", "").substring(0, 16);

// MD5加密
String encryptedPassword = DigestUtil.md5Hex(password + username + salt);

// 存储格式
user.setPassword(encryptedPassword);
user.setSalt(salt);
```

### 2. 密码验证

```java
// Shiro自动验证
HashedCredentialsMatcher matcher = new HashedCredentialsMatcher();
matcher.setHashAlgorithmName("MD5");
matcher.setHashIterations(1);

// 在Realm中返回认证信息
return new SimpleAuthenticationInfo(
    user,                                           // 主体
    user.getPassword(),                            // 密码
    ByteSource.Util.bytes(user.getCredentialsSalt()), // 盐值
    getName()                                      // Realm名称
);
```

## 🌐 API接口

### 认证接口

```bash
# 用户登录
POST /api/auth/login
{
  "username": "admin",
  "password": "123456",
  "rememberMe": true
}

# 用户登出
POST /api/auth/logout

# 获取当前用户信息
GET /api/auth/current

# 检查登录状态
GET /api/auth/check

# 修改密码
POST /api/auth/change-password
{
  "oldPassword": "123456",
  "newPassword": "newpassword"
}
```

### 用户管理接口

```bash
# 查询用户列表（需要权限）
GET /api/system/users

# 查询用户详情（需要权限）
GET /api/system/users/1

# 创建用户（需要权限）
POST /api/system/users
{
  "username": "newuser",
  "password": "123456",
  "email": "<EMAIL>",
  "realName": "新用户"
}

# 更新用户（需要权限）
PUT /api/system/users/1
{
  "username": "updateduser",
  "email": "<EMAIL>"
}

# 删除用户（需要权限）
DELETE /api/system/users/1
```

## 🚀 运行项目

### 1. 环境准备

- JDK 8+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库准备

```sql
-- 创建数据库
CREATE DATABASE shiro_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行sql/shiro_schema.sql文件
```

### 3. 配置文件

修改 `application.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    url: **************************************
    username: root
    password: 123456
```

### 4. 启动应用

```bash
# 编译项目
mvn clean compile

# 运行应用
mvn spring-boot:run

# 或者运行主类
java -cp target/classes com.example.ShiroApplication
```

### 5. 访问应用

- 应用地址: http://localhost:8080
- Druid监控: http://localhost:8080/druid (admin/123456)
- 健康检查: http://localhost:8080/actuator/health

## 👥 默认用户

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 所有权限 |
| manager | 123456 | 部门经理 | 用户管理、个人中心 |
| user | 123456 | 普通用户 | 个人中心 |

## 🧪 测试

```bash
# 运行所有测试
mvn test

# 运行指定测试
mvn test -Dtest=ShiroApplicationTest
```

## 📊 监控

### 1. Druid监控

访问 http://localhost:8080/druid 查看：
- SQL监控
- 连接池监控
- Web应用监控

### 2. Actuator监控

```bash
# 健康检查
curl http://localhost:8080/actuator/health

# 应用信息
curl http://localhost:8080/actuator/info

# 指标信息
curl http://localhost:8080/actuator/metrics
```

## 🎯 学习要点

### 1. Shiro核心概念
- Subject、SecurityManager、Realm的作用和关系
- 认证和授权的流程
- 会话管理机制

### 2. 权限控制
- RBAC模型的实现
- 权限注解的使用
- 过滤器链的配置

### 3. 安全最佳实践
- 密码加密存储
- 会话安全管理
- 缓存优化

### 4. 与Spring Boot集成
- 自动配置的使用
- Bean的配置和注入
- 异常处理

## 🔧 扩展功能

### 1. 验证码功能

```java
@GetMapping("/api/auth/captcha")
public void generateCaptcha(HttpServletResponse response) {
    // 生成验证码图片
}
```

### 2. 记住我功能

```java
// 已在ShiroConfig中配置
CookieRememberMeManager rememberMeManager = new CookieRememberMeManager();
```

### 3. 多Realm支持

```java
@Bean
public ModularRealmAuthenticator modularRealmAuthenticator() {
    ModularRealmAuthenticator authenticator = new ModularRealmAuthenticator();
    authenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());
    return authenticator;
}
```

## 🔍 常见问题

### 1. 认证失败
- 检查用户名和密码是否正确
- 检查用户状态是否为启用
- 检查密码加密方式是否一致

### 2. 权限不足
- 检查用户是否有对应的角色
- 检查角色是否有对应的权限
- 确认权限编码是否正确

### 3. 缓存问题
- 权限变更后需要清除缓存
- 检查EhCache配置是否正确
- 确认缓存名称是否匹配

## 📚 参考资料

- [Apache Shiro官方文档](https://shiro.apache.org/documentation.html)
- [Spring Boot官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)

---

**注意**: 这是一个学习示例项目，生产环境使用时请根据实际需求调整安全配置和功能实现。
