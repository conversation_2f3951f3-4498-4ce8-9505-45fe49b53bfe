package com.example.mybatisrbac.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Swagger 重定向控制器
 * 处理 Swagger UI 的访问路径
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Controller
public class SwaggerController {

    /**
     * 重定向到 Swagger UI
     *
     * @return 重定向到 Swagger UI
     */
    @GetMapping("/swagger")
    public String swagger() {
        return "redirect:/swagger-ui/index.html";
    }

    /**
     * 重定向到 Swagger UI (兼容旧路径)
     *
     * @return 重定向到 Swagger UI
     */
    @GetMapping("/swagger-ui")
    public String swaggerUi() {
        return "redirect:/swagger-ui/index.html";
    }

    /**
     * 重定向到 Swagger UI (处理根路径)
     *
     * @return 重定向到 Swagger UI
     */
    @GetMapping("/doc")
    public String doc() {
        return "redirect:/swagger-ui/index.html";
    }
}
