package com.example.mybatisdemo.common.enums;

/**
 * 统一响应状态码枚举
 */
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    PARAM_ERROR(422, "参数校验失败"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    BUSINESS_ERROR(1000, "业务处理失败"),
    DATA_NOT_FOUND(1001, "数据不存在"),
    DATA_ALREADY_EXISTS(1002, "数据已存在"),
    DATA_OPERATION_ERROR(1003, "数据操作失败"),
    
    // 用户相关错误 2xxx
    USER_NOT_FOUND(2001, "用户不存在"),
    USER_ALREADY_EXISTS(2002, "用户已存在"),
    USER_PASSWORD_ERROR(2003, "密码错误"),
    USER_ACCOUNT_DISABLED(2004, "账户已禁用"),
    USER_ACCOUNT_LOCKED(2005, "账户已锁定"),
    
    // 权限相关错误 3xxx
    PERMISSION_DENIED(3001, "权限不足"),
    TOKEN_INVALID(3002, "Token无效"),
    TOKEN_EXPIRED(3003, "Token已过期"),
    
    // 文件相关错误 4xxx
    FILE_NOT_FOUND(4001, "文件不存在"),
    FILE_UPLOAD_ERROR(4002, "文件上传失败"),
    FILE_TYPE_ERROR(4003, "文件类型不支持"),
    FILE_SIZE_ERROR(4004, "文件大小超出限制");
    
    private final int code;
    private final String message;
    
    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}
