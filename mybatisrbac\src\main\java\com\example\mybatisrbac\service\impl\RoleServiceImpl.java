package com.example.mybatisrbac.service.impl;

import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.common.ResultCode;
import com.example.mybatisrbac.dto.RoleQueryDTO;
import com.example.mybatisrbac.entity.Role;
import com.example.mybatisrbac.exception.BusinessException;
import com.example.mybatisrbac.mapper.RoleMapper;
import com.example.mybatisrbac.service.RoleService;
import com.example.mybatisrbac.util.BeanUtil;
import com.example.mybatisrbac.util.FieldMappingUtil;
//
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public PageResult<Role> getRoleList(RoleQueryDTO queryDTO) {
        try {
            // 查询总数
            Long total = roleMapper.countByCondition(queryDTO);

            if (total == 0) {
                // 根据查询条件判断返回不同的消息
                String message = hasQueryConditions(queryDTO) ?
                    "未找到符合条件的角色数据" : "暂无角色数据";
                return PageResult.success(message, List.of(),
                        queryDTO.getCurrent(), queryDTO.getSize(), 0L);
            }

            // 计算偏移量
            Long offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();

            // 处理排序字段映射
            String dbSortField = FieldMappingUtil.getRoleDbFieldName(queryDTO.getSortField());
            String sortOrder = "asc".equalsIgnoreCase(queryDTO.getSortOrder()) ? "ASC" : "DESC";

            // 分页查询数据
            List<Role> roles = roleMapper.selectByCondition(queryDTO, offset, queryDTO.getSize(),
                    dbSortField, sortOrder);

            return PageResult.success("角色列表查询成功", roles,
                    queryDTO.getCurrent(), queryDTO.getSize(), total);

        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "查询角色列表失败：" + e.getMessage());
        }
    }

    @Override
    public Role getRoleById(Long id) {
        try {
            Role role = roleMapper.selectById(id);
            if (role == null) {
                throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
            }
            return role;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "查询角色失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role createRole(Role role) {
        try {
            // 检查角色编码是否已存在
            if (roleMapper.checkRoleCodeExists(role.getRoleCode(), 0L) > 0) {
                throw new BusinessException(ResultCode.ROLE_ALREADY_EXISTS, "角色编码已存在");
            }

            // 检查角色名称是否已存在
            if (roleMapper.checkRoleNameExists(role.getRoleName(), 0L) > 0) {
                throw new BusinessException(ResultCode.ROLE_ALREADY_EXISTS, "角色名称已存在");
            }

            // 设置创建时间和更新时间
            role.setCreateTime(LocalDateTime.now());
            role.setUpdateTime(LocalDateTime.now());
            role.setCreateBy(1L); // 实际项目中从当前登录用户获取
            role.setUpdateBy(1L);

            // 插入角色
            int result = roleMapper.insert(role);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "创建角色失败");
            }

            return role;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "创建角色失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role updateRole(Long id, Role role) {
        try {
            // 检查角色是否存在
            Role existingRole = roleMapper.selectById(id);
            if (existingRole == null) {
                throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
            }

            // 检查角色编码是否已存在（排除当前角色）
            if (role.getRoleCode() != null &&
                roleMapper.checkRoleCodeExists(role.getRoleCode(), id) > 0) {
                throw new BusinessException(ResultCode.ROLE_ALREADY_EXISTS, "角色编码已存在");
            }

            // 检查角色名称是否已存在（排除当前角色）
            if (role.getRoleName() != null &&
                roleMapper.checkRoleNameExists(role.getRoleName(), id) > 0) {
                throw new BusinessException(ResultCode.ROLE_ALREADY_EXISTS, "角色名称已存在");
            }

            // 设置更新信息
            role.setId(id);
            role.setUpdateTime(LocalDateTime.now());
            role.setUpdateBy(1L); // 实际项目中从当前登录用户获取

            // 更新角色
            int result = roleMapper.updateById(role);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "更新角色失败");
            }

            // 返回更新后的角色信息
            Role updatedRole = roleMapper.selectById(id);
            return updatedRole;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "更新角色失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long id) {
        try {
            // 检查角色是否存在
            Role role = roleMapper.selectById(id);
            if (role == null) {
                throw new BusinessException(ResultCode.ROLE_NOT_FOUND);
            }

            // TODO: 检查角色是否被用户使用，如果被使用则不能删除
            // 这里需要查询用户角色关联表

            // 删除角色
            int result = roleMapper.deleteById(id);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "删除角色失败");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "删除角色失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteRoles(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "请选择要删除的角色");
            }

            // TODO: 检查角色是否被用户使用，如果被使用则不能删除

            // 批量删除角色
            int result = roleMapper.deleteByIds(ids);
            if (result != ids.size()) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "部分角色删除失败");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "批量删除角色失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否有查询条件
     */
    private boolean hasQueryConditions(RoleQueryDTO queryDTO) {
        return (queryDTO.getRoleName() != null && !queryDTO.getRoleName().trim().isEmpty()) ||
               (queryDTO.getRoleCode() != null && !queryDTO.getRoleCode().trim().isEmpty()) ||
               (queryDTO.getStatus() != null);
    }

}
