package examples;

/**
 * JDBC vs MyBatis 对比示例
 * 同一个功能的不同实现方式
 */

// ========== JDBC方式 ==========
class JdbcUserDao {
    
    // 查询用户 - JDBC方式（约30行代码）
    public User findUserById(Long id) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            String sql = "SELECT id, username, email, created_at FROM users WHERE id = ?";
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, id);
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                User user = new User();
                user.setId(rs.getLong("id"));
                user.setUsername(rs.getString("username"));
                user.setEmail(rs.getString("email"));
                user.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                return user;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源...
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}

// ========== MyBatis方式 ==========
@Mapper
interface MyBatisUserDao {
    
    // 查询用户 - MyBatis方式（1行代码）
    @Select("SELECT id, username, email, created_at FROM users WHERE id = #{id}")
    User findUserById(@Param("id") Long id);
}

// ========== 复杂查询对比 ==========

// JDBC方式 - 动态查询（复杂且容易出错）
class JdbcDynamicQuery {
    
    public List<User> findUsers(String username, String email, Date startDate, Date endDate) {
        StringBuilder sql = new StringBuilder("SELECT * FROM users WHERE 1=1");
        List<Object> params = new ArrayList<>();
        
        if (username != null && !username.isEmpty()) {
            sql.append(" AND username LIKE ?");
            params.add("%" + username + "%");
        }
        
        if (email != null && !email.isEmpty()) {
            sql.append(" AND email LIKE ?");
            params.add("%" + email + "%");
        }
        
        if (startDate != null) {
            sql.append(" AND created_at >= ?");
            params.add(startDate);
        }
        
        if (endDate != null) {
            sql.append(" AND created_at <= ?");
            params.add(endDate);
        }
        
        // 然后是大量的JDBC样板代码...
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        List<User> users = new ArrayList<>();
        
        try {
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            stmt = conn.prepareStatement(sql.toString());
            
            // 设置参数
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            rs = stmt.executeQuery();
            
            while (rs.next()) {
                // 手动映射结果...
                User user = new User();
                user.setId(rs.getLong("id"));
                user.setUsername(rs.getString("username"));
                user.setEmail(rs.getString("email"));
                user.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                users.add(user);
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源...
        }
        
        return users;
    }
}

// MyBatis方式 - 动态查询（简洁且安全）
@Mapper
interface MyBatisDynamicQuery {
    
    // XML配置方式
    List<User> findUsers(@Param("username") String username,
                        @Param("email") String email,
                        @Param("startDate") Date startDate,
                        @Param("endDate") Date endDate);
    
    /* 对应的XML配置：
    <select id="findUsers" resultMap="UserResultMap">
        SELECT * FROM users
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="startDate != null">
                AND created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND created_at <= #{endDate}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>
    */
}

// ========== 性能对比示例 ==========

@Component
public class PerformanceComparison {
    
    @Autowired
    private MyBatisUserDao myBatisUserDao;
    
    private JdbcUserDao jdbcUserDao = new JdbcUserDao();
    
    public void performanceTest() {
        long startTime, endTime;
        
        // JDBC性能测试
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            jdbcUserDao.findUserById(1L);
        }
        endTime = System.currentTimeMillis();
        System.out.println("JDBC 1000次查询耗时: " + (endTime - startTime) + "ms");
        
        // MyBatis性能测试
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            myBatisUserDao.findUserById(1L);
        }
        endTime = System.currentTimeMillis();
        System.out.println("MyBatis 1000次查询耗时: " + (endTime - startTime) + "ms");
    }
}
