# Spring容器与依赖注入原理详解

## 目录
1. [Spring容器概述](#spring容器概述)
2. [控制反转(IoC)原理](#控制反转ioc原理)
3. [依赖注入(DI)详解](#依赖注入di详解)
4. [Spring容器实现原理](#spring容器实现原理)
5. [Bean的生命周期](#bean的生命周期)
6. [依赖注入的实现方式](#依赖注入的实现方式)
7. [循环依赖问题](#循环依赖问题)
8. [实际应用示例](#实际应用示例)

## Spring容器概述

### 什么是Spring容器

Spring容器是Spring框架的核心，它负责创建、配置和管理应用程序中的对象（Bean）。容器通过读取配置信息，实例化对象，并建立对象之间的依赖关系。

### Spring容器的类型

```java
// 1. BeanFactory - 基础容器接口
BeanFactory factory = new XmlBeanFactory(new ClassPathResource("beans.xml"));
MyBean bean = (MyBean) factory.getBean("myBean");

// 2. ApplicationContext - 高级容器接口（推荐）
ApplicationContext context = new ClassPathXmlApplicationContext("beans.xml");
MyBean bean = context.getBean("myBean", MyBean.class);

// 3. 注解配置容器
ApplicationContext context = new AnnotationConfigApplicationContext(AppConfig.class);
```

### 容器的核心功能

1. **对象创建** - 根据配置创建Bean实例
2. **依赖注入** - 自动装配Bean之间的依赖关系
3. **生命周期管理** - 管理Bean的创建、初始化、销毁
4. **作用域管理** - 控制Bean的作用域（单例、原型等）
5. **AOP支持** - 提供面向切面编程支持

## 控制反转(IoC)原理

### 什么是控制反转

控制反转（Inversion of Control）是一种设计原则，它将对象的创建和依赖关系的管理从应用程序代码中移除，交给外部容器来处理。

### 传统方式 vs IoC方式

```java
// 传统方式：对象自己控制依赖的创建
public class TraditionalUserService {
    private UserRepository userRepository;
    private EmailService emailService;
    
    public TraditionalUserService() {
        // 硬编码依赖，紧耦合
        this.userRepository = new UserRepositoryImpl();
        this.emailService = new EmailServiceImpl();
    }
    
    public void createUser(String name, String email) {
        User user = new User(name, email);
        userRepository.save(user);
        emailService.sendWelcomeEmail(email);
    }
}

// IoC方式：容器控制依赖的创建和注入
@Service
public class IoCUserService {
    private final UserRepository userRepository;
    private final EmailService emailService;
    
    // 构造函数注入，依赖由容器提供
    public IoCUserService(UserRepository userRepository, EmailService emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }
    
    public void createUser(String name, String email) {
        User user = new User(name, email);
        userRepository.save(user);
        emailService.sendWelcomeEmail(email);
    }
}
```

### IoC的优势

1. **降低耦合度** - 对象不直接依赖具体实现
2. **提高可测试性** - 易于注入Mock对象
3. **增强可维护性** - 配置集中管理
4. **支持多态** - 可以轻松切换实现

### IoC的实现方式

```java
// 1. 依赖查找（Dependency Lookup）
public class ServiceLocatorExample {
    public void doSomething() {
        // 主动从容器中查找依赖
        UserService userService = ServiceLocator.getService(UserService.class);
        userService.createUser("Alice", "<EMAIL>");
    }
}

// 2. 依赖注入（Dependency Injection）- 推荐
@Service
public class DependencyInjectionExample {
    private final UserService userService;
    
    // 被动接收容器注入的依赖
    public DependencyInjectionExample(UserService userService) {
        this.userService = userService;
    }
    
    public void doSomething() {
        userService.createUser("Bob", "<EMAIL>");
    }
}
```

## 依赖注入(DI)详解

### 什么是依赖注入

依赖注入（Dependency Injection）是IoC的一种实现方式，它通过外部容器将依赖对象注入到目标对象中，而不是由目标对象自己创建依赖。

### 依赖注入的类型

#### 1. 构造函数注入（Constructor Injection）

```java
@Service
public class UserService {
    private final UserRepository userRepository;
    private final EmailService emailService;
    
    // 构造函数注入 - 推荐方式
    public UserService(UserRepository userRepository, EmailService emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }
    
    // 业务方法
    public User createUser(String name, String email) {
        User user = new User(name, email);
        User savedUser = userRepository.save(user);
        emailService.sendWelcomeEmail(email);
        return savedUser;
    }
}
```

**优点：**
- 保证依赖不可变（final字段）
- 保证依赖不为null
- 便于单元测试
- 避免循环依赖

#### 2. Setter注入（Setter Injection）

```java
@Service
public class UserService {
    private UserRepository userRepository;
    private EmailService emailService;
    
    // Setter注入
    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Autowired
    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }
    
    // 可选依赖的Setter注入
    @Autowired(required = false)
    public void setOptionalService(OptionalService optionalService) {
        this.optionalService = optionalService;
    }
}
```

**优点：**
- 支持可选依赖
- 支持重新配置
- 可以在对象创建后设置依赖

#### 3. 字段注入（Field Injection）

```java
@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private EmailService emailService;
    
    // 可选依赖
    @Autowired(required = false)
    private OptionalService optionalService;
}
```

**缺点：**
- 违反封装原则
- 难以进行单元测试
- 隐藏依赖关系
- 不支持final字段

### 依赖注入的配置方式

#### 1. XML配置

```xml
<!-- beans.xml -->
<beans xmlns="http://www.springframework.org/schema/beans">
    
    <!-- 定义Bean -->
    <bean id="userRepository" class="com.example.UserRepositoryImpl"/>
    <bean id="emailService" class="com.example.EmailServiceImpl"/>
    
    <!-- 构造函数注入 -->
    <bean id="userService" class="com.example.UserService">
        <constructor-arg ref="userRepository"/>
        <constructor-arg ref="emailService"/>
    </bean>
    
    <!-- Setter注入 -->
    <bean id="orderService" class="com.example.OrderService">
        <property name="userService" ref="userService"/>
        <property name="paymentService" ref="paymentService"/>
    </bean>
    
</beans>
```

#### 2. 注解配置

```java
// 配置类
@Configuration
@ComponentScan(basePackages = "com.example")
public class AppConfig {
    
    @Bean
    public UserRepository userRepository() {
        return new UserRepositoryImpl();
    }
    
    @Bean
    public EmailService emailService() {
        return new EmailServiceImpl();
    }
    
    @Bean
    public UserService userService(UserRepository userRepository, EmailService emailService) {
        return new UserService(userRepository, emailService);
    }
}

// 组件类
@Component
public class UserRepositoryImpl implements UserRepository {
    // 实现代码
}

@Service
public class UserService {
    private final UserRepository userRepository;
    
    @Autowired
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
}
```

#### 3. Java配置

```java
@Configuration
public class JavaConfig {
    
    @Bean
    @Scope("singleton")
    public UserRepository userRepository() {
        UserRepositoryImpl repository = new UserRepositoryImpl();
        repository.setConnectionPool(connectionPool());
        return repository;
    }
    
    @Bean
    @Scope("prototype")
    public EmailService emailService() {
        EmailServiceImpl service = new EmailServiceImpl();
        service.setSmtpServer("smtp.example.com");
        return service;
    }
    
    @Bean
    @Primary
    public UserService primaryUserService() {
        return new UserServiceImpl(userRepository(), emailService());
    }
    
    @Bean
    @Qualifier("backupUserService")
    public UserService backupUserService() {
        return new BackupUserServiceImpl(userRepository(), emailService());
    }
}
```

## Spring容器实现原理

### 容器启动流程

```java
public class ContainerStartupDemo {
    
    public static void demonstrateStartup() {
        // 1. 创建容器实例
        AnnotationConfigApplicationContext context = 
            new AnnotationConfigApplicationContext();
        
        // 2. 注册配置类
        context.register(AppConfig.class);
        
        // 3. 刷新容器（核心步骤）
        context.refresh();
        
        // 4. 获取Bean
        UserService userService = context.getBean(UserService.class);
        
        // 5. 关闭容器
        context.close();
    }
}
```

### 容器刷新过程（refresh方法）

```java
public void refresh() throws BeansException, IllegalStateException {
    synchronized (this.startupShutdownMonitor) {
        // 1. 准备刷新上下文
        prepareRefresh();
        
        // 2. 获取Bean工厂
        ConfigurableListableBeanFactory beanFactory = obtainFreshBeanFactory();
        
        // 3. 准备Bean工厂
        prepareBeanFactory(beanFactory);
        
        try {
            // 4. 后处理Bean工厂
            postProcessBeanFactory(beanFactory);
            
            // 5. 调用Bean工厂后处理器
            invokeBeanFactoryPostProcessors(beanFactory);
            
            // 6. 注册Bean后处理器
            registerBeanPostProcessors(beanFactory);
            
            // 7. 初始化消息源
            initMessageSource();
            
            // 8. 初始化事件多播器
            initApplicationEventMulticaster();
            
            // 9. 刷新特定上下文
            onRefresh();
            
            // 10. 注册监听器
            registerListeners();
            
            // 11. 完成Bean工厂初始化
            finishBeanFactoryInitialization(beanFactory);
            
            // 12. 完成刷新
            finishRefresh();
        }
        catch (BeansException ex) {
            // 销毁已创建的Bean
            destroyBeans();
            cancelRefresh(ex);
            throw ex;
        }
        finally {
            resetCommonCaches();
        }
    }
}
```

### Bean的创建过程

```java
public class BeanCreationProcess {
    
    // 简化的Bean创建流程
    public Object createBean(String beanName, BeanDefinition beanDefinition) {
        
        // 1. 实例化Bean
        Object bean = instantiateBean(beanDefinition);
        
        // 2. 属性填充（依赖注入）
        populateBean(bean, beanDefinition);
        
        // 3. 初始化Bean
        initializeBean(bean, beanName);
        
        return bean;
    }
    
    private Object instantiateBean(BeanDefinition beanDefinition) {
        // 通过反射创建实例
        Class<?> beanClass = beanDefinition.getBeanClass();
        Constructor<?> constructor = beanClass.getConstructor();
        return constructor.newInstance();
    }
    
    private void populateBean(Object bean, BeanDefinition beanDefinition) {
        // 注入依赖属性
        PropertyValues propertyValues = beanDefinition.getPropertyValues();
        for (PropertyValue pv : propertyValues.getPropertyValues()) {
            String propertyName = pv.getName();
            Object value = pv.getValue();
            
            // 通过反射设置属性值
            Field field = bean.getClass().getDeclaredField(propertyName);
            field.setAccessible(true);
            field.set(bean, value);
        }
    }
    
    private void initializeBean(Object bean, String beanName) {
        // 调用初始化方法
        if (bean instanceof InitializingBean) {
            ((InitializingBean) bean).afterPropertiesSet();
        }
        
        // 调用自定义初始化方法
        invokeCustomInitMethod(bean, beanName);
    }
}
```

## Bean的生命周期

### 完整的Bean生命周期

```java
@Component
public class LifecycleDemoBean implements BeanNameAware, BeanFactoryAware,
        ApplicationContextAware, InitializingBean, DisposableBean {

    private String beanName;
    private BeanFactory beanFactory;
    private ApplicationContext applicationContext;

    // 1. 构造函数
    public LifecycleDemoBean() {
        System.out.println("1. 构造函数调用");
    }

    // 2. 设置Bean名称
    @Override
    public void setBeanName(String name) {
        this.beanName = name;
        System.out.println("2. setBeanName: " + name);
    }

    // 3. 设置Bean工厂
    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
        System.out.println("3. setBeanFactory");
    }

    // 4. 设置应用上下文
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        System.out.println("4. setApplicationContext");
    }

    // 5. 前置处理器
    // BeanPostProcessor.postProcessBeforeInitialization()

    // 6. @PostConstruct注解方法
    @PostConstruct
    public void postConstruct() {
        System.out.println("6. @PostConstruct初始化");
    }

    // 7. InitializingBean接口方法
    @Override
    public void afterPropertiesSet() throws Exception {
        System.out.println("7. InitializingBean.afterPropertiesSet()");
    }

    // 8. 自定义初始化方法
    public void customInit() {
        System.out.println("8. 自定义初始化方法");
    }

    // 9. 后置处理器
    // BeanPostProcessor.postProcessAfterInitialization()

    // Bean可以正常使用了

    // 10. @PreDestroy注解方法
    @PreDestroy
    public void preDestroy() {
        System.out.println("10. @PreDestroy销毁前处理");
    }

    // 11. DisposableBean接口方法
    @Override
    public void destroy() throws Exception {
        System.out.println("11. DisposableBean.destroy()");
    }

    // 12. 自定义销毁方法
    public void customDestroy() {
        System.out.println("12. 自定义销毁方法");
    }
}

// 配置自定义初始化和销毁方法
@Configuration
public class LifecycleConfig {

    @Bean(initMethod = "customInit", destroyMethod = "customDestroy")
    public LifecycleDemoBean lifecycleDemoBean() {
        return new LifecycleDemoBean();
    }
}
```

### Bean后处理器

```java
@Component
public class CustomBeanPostProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName)
            throws BeansException {
        if (bean instanceof LifecycleDemoBean) {
            System.out.println("5. BeanPostProcessor.postProcessBeforeInitialization: " + beanName);
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName)
            throws BeansException {
        if (bean instanceof LifecycleDemoBean) {
            System.out.println("9. BeanPostProcessor.postProcessAfterInitialization: " + beanName);
        }
        return bean;
    }
}
```

### Bean作用域

```java
@Configuration
public class BeanScopeConfig {

    // 单例模式（默认）
    @Bean
    @Scope("singleton")
    public SingletonBean singletonBean() {
        return new SingletonBean();
    }

    // 原型模式
    @Bean
    @Scope("prototype")
    public PrototypeBean prototypeBean() {
        return new PrototypeBean();
    }

    // Web环境作用域
    @Bean
    @Scope("request")
    public RequestScopedBean requestScopedBean() {
        return new RequestScopedBean();
    }

    @Bean
    @Scope("session")
    public SessionScopedBean sessionScopedBean() {
        return new SessionScopedBean();
    }

    // 自定义作用域
    @Bean
    @Scope("custom")
    public CustomScopedBean customScopedBean() {
        return new CustomScopedBean();
    }
}

// 作用域演示
public class ScopeDemo {

    public static void demonstrateScope(ApplicationContext context) {
        // 单例Bean - 多次获取返回同一实例
        SingletonBean singleton1 = context.getBean(SingletonBean.class);
        SingletonBean singleton2 = context.getBean(SingletonBean.class);
        System.out.println("单例Bean相同: " + (singleton1 == singleton2)); // true

        // 原型Bean - 每次获取返回新实例
        PrototypeBean prototype1 = context.getBean(PrototypeBean.class);
        PrototypeBean prototype2 = context.getBean(PrototypeBean.class);
        System.out.println("原型Bean不同: " + (prototype1 != prototype2)); // true
    }
}
```

## 依赖注入的实现方式

### 自动装配类型

```java
public class AutowireDemo {

    // 1. 按类型自动装配（默认）
    @Autowired
    private UserRepository userRepository;

    // 2. 按名称自动装配
    @Autowired
    @Qualifier("primaryUserService")
    private UserService userService;

    // 3. 按主要Bean装配
    @Autowired
    private EmailService emailService; // 如果有@Primary注解的Bean会被优先选择

    // 4. 可选依赖
    @Autowired(required = false)
    private OptionalService optionalService;

    // 5. 集合注入
    @Autowired
    private List<MessageProcessor> messageProcessors; // 注入所有MessageProcessor类型的Bean

    @Autowired
    private Map<String, MessageProcessor> processorMap; // key为Bean名称，value为Bean实例

    // 6. 泛型依赖注入
    @Autowired
    private Repository<User> userRepository2; // 根据泛型类型匹配
}

// 配置多个同类型Bean
@Configuration
public class MultipleBeansConfig {

    @Bean
    @Primary
    public UserService primaryUserService() {
        return new PrimaryUserService();
    }

    @Bean
    @Qualifier("backupUserService")
    public UserService backupUserService() {
        return new BackupUserService();
    }

    @Bean
    public MessageProcessor emailProcessor() {
        return new EmailMessageProcessor();
    }

    @Bean
    public MessageProcessor smsProcessor() {
        return new SmsMessageProcessor();
    }
}
```

### 条件化Bean创建

```java
@Configuration
public class ConditionalConfig {

    // 基于属性条件
    @Bean
    @ConditionalOnProperty(name = "feature.email.enabled", havingValue = "true")
    public EmailService emailService() {
        return new EmailServiceImpl();
    }

    // 基于类存在条件
    @Bean
    @ConditionalOnClass(RedisTemplate.class)
    public CacheService redisCacheService() {
        return new RedisCacheService();
    }

    // 基于Bean存在条件
    @Bean
    @ConditionalOnBean(DataSource.class)
    public UserRepository userRepository(DataSource dataSource) {
        return new JdbcUserRepository(dataSource);
    }

    // 基于Bean不存在条件
    @Bean
    @ConditionalOnMissingBean(UserRepository.class)
    public UserRepository defaultUserRepository() {
        return new InMemoryUserRepository();
    }

    // 基于Profile条件
    @Bean
    @Profile("dev")
    public UserRepository devUserRepository() {
        return new MockUserRepository();
    }

    @Bean
    @Profile("prod")
    public UserRepository prodUserRepository() {
        return new DatabaseUserRepository();
    }
}
```

## 循环依赖问题

### 什么是循环依赖

```java
// 循环依赖示例
@Service
public class ServiceA {
    @Autowired
    private ServiceB serviceB; // ServiceA依赖ServiceB

    public void doSomething() {
        serviceB.process();
    }
}

@Service
public class ServiceB {
    @Autowired
    private ServiceA serviceA; // ServiceB依赖ServiceA，形成循环

    public void process() {
        serviceA.doSomething();
    }
}
```

### Spring解决循环依赖的机制

```java
public class CircularDependencyResolver {

    // 三级缓存解决循环依赖
    // 1. singletonObjects - 一级缓存，存放完全初始化好的Bean
    private final Map<String, Object> singletonObjects = new ConcurrentHashMap<>();

    // 2. earlySingletonObjects - 二级缓存，存放原始Bean对象（未填充属性）
    private final Map<String, Object> earlySingletonObjects = new HashMap<>();

    // 3. singletonFactories - 三级缓存，存放Bean工厂对象
    private final Map<String, ObjectFactory<?>> singletonFactories = new HashMap<>();

    // 正在创建的Bean名称集合
    private final Set<String> singletonsCurrentlyInCreation = Collections.newSetFromMap(new ConcurrentHashMap<>());

    public Object getSingleton(String beanName) {
        // 1. 从一级缓存获取
        Object singletonObject = singletonObjects.get(beanName);

        if (singletonObject == null && isSingletonCurrentlyInCreation(beanName)) {
            synchronized (this.singletonObjects) {
                // 2. 从二级缓存获取
                singletonObject = earlySingletonObjects.get(beanName);

                if (singletonObject == null) {
                    // 3. 从三级缓存获取
                    ObjectFactory<?> singletonFactory = singletonFactories.get(beanName);
                    if (singletonFactory != null) {
                        singletonObject = singletonFactory.getObject();
                        // 放入二级缓存
                        earlySingletonObjects.put(beanName, singletonObject);
                        // 从三级缓存移除
                        singletonFactories.remove(beanName);
                    }
                }
            }
        }

        return singletonObject;
    }

    protected void addSingletonFactory(String beanName, ObjectFactory<?> singletonFactory) {
        synchronized (this.singletonObjects) {
            if (!this.singletonObjects.containsKey(beanName)) {
                this.singletonFactories.put(beanName, singletonFactory);
                this.earlySingletonObjects.remove(beanName);
            }
        }
    }

    private boolean isSingletonCurrentlyInCreation(String beanName) {
        return singletonsCurrentlyInCreation.contains(beanName);
    }
}
```

### 循环依赖的解决方案

```java
// 1. 构造函数循环依赖 - 无法解决，需要重构
// 错误示例：
@Service
public class ServiceA {
    private final ServiceB serviceB;

    public ServiceA(ServiceB serviceB) { // 构造函数依赖
        this.serviceB = serviceB;
    }
}

@Service
public class ServiceB {
    private final ServiceA serviceA;

    public ServiceB(ServiceA serviceA) { // 构造函数依赖，形成循环
        this.serviceA = serviceA;
    }
}

// 解决方案1：使用@Lazy注解
@Service
public class ServiceA {
    private final ServiceB serviceB;

    public ServiceA(@Lazy ServiceB serviceB) { // 延迟初始化
        this.serviceB = serviceB;
    }
}

// 解决方案2：使用Setter注入
@Service
public class ServiceA {
    private ServiceB serviceB;

    @Autowired
    public void setServiceB(ServiceB serviceB) {
        this.serviceB = serviceB;
    }
}

// 解决方案3：使用@PostConstruct
@Service
public class ServiceA {
    @Autowired
    private ApplicationContext applicationContext;

    private ServiceB serviceB;

    @PostConstruct
    public void init() {
        this.serviceB = applicationContext.getBean(ServiceB.class);
    }
}

// 解决方案4：重构设计，引入第三个服务
@Service
public class ServiceA {
    @Autowired
    private CommonService commonService;

    public void doSomething() {
        commonService.processA();
    }
}

@Service
public class ServiceB {
    @Autowired
    private CommonService commonService;

    public void process() {
        commonService.processB();
    }
}

@Service
public class CommonService {
    public void processA() {
        // A的逻辑
    }

    public void processB() {
        // B的逻辑
    }
}
```

## 实际应用示例

### 完整的应用示例

```java
// 1. 实体类
public class User {
    private Long id;
    private String username;
    private String email;
    private LocalDateTime createdAt;

    // 构造函数、getter、setter省略
    public User(String username, String email) {
        this.username = username;
        this.email = email;
        this.createdAt = LocalDateTime.now();
    }

    // toString方法
    @Override
    public String toString() {
        return "User{id=" + id + ", username='" + username + "', email='" + email + "'}";
    }
}

// 2. 数据访问层
public interface UserRepository {
    User save(User user);
    User findById(Long id);
    List<User> findAll();
    void deleteById(Long id);
}

@Repository
public class InMemoryUserRepository implements UserRepository {
    private final Map<Long, User> users = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);

    @Override
    public User save(User user) {
        if (user.getId() == null) {
            user.setId(idGenerator.getAndIncrement());
        }
        users.put(user.getId(), user);
        System.out.println("保存用户到内存: " + user);
        return user;
    }

    @Override
    public User findById(Long id) {
        User user = users.get(id);
        System.out.println("从内存查找用户: " + id + " -> " + user);
        return user;
    }

    @Override
    public List<User> findAll() {
        List<User> userList = new ArrayList<>(users.values());
        System.out.println("查找所有用户，共" + userList.size() + "个");
        return userList;
    }

    @Override
    public void deleteById(Long id) {
        User removed = users.remove(id);
        System.out.println("删除用户: " + removed);
    }
}

// 3. 业务服务层
public interface EmailService {
    void sendWelcomeEmail(String email);
    void sendNotificationEmail(String email, String message);
}

@Service
public class EmailServiceImpl implements EmailService {

    @Override
    public void sendWelcomeEmail(String email) {
        System.out.println("发送欢迎邮件到: " + email);
        // 实际的邮件发送逻辑
    }

    @Override
    public void sendNotificationEmail(String email, String message) {
        System.out.println("发送通知邮件到: " + email + ", 内容: " + message);
    }
}

@Service
public class UserService {
    private final UserRepository userRepository;
    private final EmailService emailService;

    // 构造函数注入
    public UserService(UserRepository userRepository, EmailService emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
        System.out.println("UserService创建完成，依赖注入成功");
    }

    public User createUser(String username, String email) {
        // 创建用户
        User user = new User(username, email);
        User savedUser = userRepository.save(user);

        // 发送欢迎邮件
        emailService.sendWelcomeEmail(email);

        return savedUser;
    }

    public User getUserById(Long id) {
        return userRepository.findById(id);
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public void deleteUser(Long id) {
        User user = userRepository.findById(id);
        if (user != null) {
            userRepository.deleteById(id);
            emailService.sendNotificationEmail(user.getEmail(), "您的账户已被删除");
        }
    }
}

// 4. 配置类
@Configuration
@ComponentScan(basePackages = "com.example")
public class AppConfig {

    // 可以在这里定义额外的Bean配置
    @Bean
    @Profile("test")
    public UserRepository testUserRepository() {
        return new MockUserRepository();
    }

    @Bean
    @ConditionalOnProperty(name = "email.enabled", havingValue = "true", matchIfMissing = true)
    public EmailService emailService() {
        return new EmailServiceImpl();
    }

    @Bean
    @ConditionalOnProperty(name = "email.enabled", havingValue = "false")
    public EmailService mockEmailService() {
        return new MockEmailService();
    }
}

// 5. 应用启动类
public class SpringContainerDemo {

    public static void main(String[] args) {
        System.out.println("=== Spring容器与依赖注入演示 ===");

        // 创建Spring容器
        AnnotationConfigApplicationContext context =
            new AnnotationConfigApplicationContext(AppConfig.class);

        try {
            // 获取Bean
            UserService userService = context.getBean(UserService.class);

            // 使用服务
            System.out.println("\n--- 创建用户 ---");
            User user1 = userService.createUser("alice", "<EMAIL>");
            User user2 = userService.createUser("bob", "<EMAIL>");

            System.out.println("\n--- 查询用户 ---");
            User foundUser = userService.getUserById(1L);
            System.out.println("查找到用户: " + foundUser);

            System.out.println("\n--- 查询所有用户 ---");
            List<User> allUsers = userService.getAllUsers();
            allUsers.forEach(System.out::println);

            System.out.println("\n--- 删除用户 ---");
            userService.deleteUser(2L);

            // 验证容器中的Bean
            System.out.println("\n--- 容器信息 ---");
            String[] beanNames = context.getBeanDefinitionNames();
            System.out.println("容器中的Bean数量: " + beanNames.length);
            for (String beanName : beanNames) {
                if (!beanName.startsWith("org.springframework")) {
                    Object bean = context.getBean(beanName);
                    System.out.println("Bean: " + beanName + " -> " + bean.getClass().getSimpleName());
                }
            }

        } finally {
            // 关闭容器
            context.close();
        }
    }
}

// 6. Mock实现（用于测试）
public class MockUserRepository implements UserRepository {
    @Override
    public User save(User user) {
        System.out.println("Mock: 保存用户 " + user.getUsername());
        return user;
    }

    @Override
    public User findById(Long id) {
        System.out.println("Mock: 查找用户 " + id);
        return new User("mockUser" + id, "mock" + id + "@example.com");
    }

    @Override
    public List<User> findAll() {
        System.out.println("Mock: 查找所有用户");
        return Arrays.asList(
            new User("user1", "<EMAIL>"),
            new User("user2", "<EMAIL>")
        );
    }

    @Override
    public void deleteById(Long id) {
        System.out.println("Mock: 删除用户 " + id);
    }
}

public class MockEmailService implements EmailService {
    @Override
    public void sendWelcomeEmail(String email) {
        System.out.println("Mock: 发送欢迎邮件到 " + email);
    }

    @Override
    public void sendNotificationEmail(String email, String message) {
        System.out.println("Mock: 发送通知邮件到 " + email + ", 消息: " + message);
    }
}
```

### 单元测试示例

```java
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = AppConfig.class)
public class UserServiceTest {

    @Autowired
    private UserService userService;

    @MockBean
    private UserRepository userRepository;

    @MockBean
    private EmailService emailService;

    @Test
    public void testCreateUser() {
        // Given
        String username = "testUser";
        String email = "<EMAIL>";
        User expectedUser = new User(username, email);
        expectedUser.setId(1L);

        when(userRepository.save(any(User.class))).thenReturn(expectedUser);

        // When
        User actualUser = userService.createUser(username, email);

        // Then
        assertThat(actualUser).isNotNull();
        assertThat(actualUser.getUsername()).isEqualTo(username);
        assertThat(actualUser.getEmail()).isEqualTo(email);

        verify(userRepository).save(any(User.class));
        verify(emailService).sendWelcomeEmail(email);
    }

    @Test
    public void testGetUserById() {
        // Given
        Long userId = 1L;
        User expectedUser = new User("testUser", "<EMAIL>");
        expectedUser.setId(userId);

        when(userRepository.findById(userId)).thenReturn(expectedUser);

        // When
        User actualUser = userService.getUserById(userId);

        // Then
        assertThat(actualUser).isEqualTo(expectedUser);
        verify(userRepository).findById(userId);
    }
}
```

## 总结

### Spring容器核心概念

1. **IoC容器** - 负责管理对象的创建、配置和生命周期
2. **Bean** - 由Spring容器管理的对象
3. **依赖注入** - 容器自动装配对象之间的依赖关系
4. **配置元数据** - 告诉容器如何创建和配置Bean

### 依赖注入的优势

1. **降低耦合度** - 组件不直接依赖具体实现
2. **提高可测试性** - 易于注入Mock对象
3. **增强可维护性** - 配置集中管理
4. **支持多态** - 可以轻松切换实现
5. **延迟初始化** - 支持懒加载和按需创建

### 最佳实践

1. **优先使用构造函数注入**
   - 保证依赖不可变
   - 便于单元测试
   - 避免循环依赖

2. **合理使用Bean作用域**
   - 无状态服务使用单例
   - 有状态对象使用原型

3. **避免循环依赖**
   - 重构设计
   - 使用@Lazy注解
   - 使用事件机制解耦

4. **使用条件化配置**
   - 根据环境选择不同实现
   - 提高配置的灵活性

5. **合理使用Profile**
   - 区分不同环境的配置
   - 支持多环境部署

### 学习建议

1. **理解核心概念** - IoC、DI、Bean生命周期
2. **掌握配置方式** - 注解配置、Java配置
3. **实践最佳实践** - 构造函数注入、避免循环依赖
4. **深入源码学习** - 理解Spring容器的实现原理
5. **结合实际项目** - 在实际开发中应用所学知识

Spring的IoC容器和依赖注入机制是整个Spring框架的基础，理解这些核心概念对于掌握Spring框架至关重要。通过合理使用这些特性，可以构建出松耦合、易测试、易维护的应用程序。
```
```
