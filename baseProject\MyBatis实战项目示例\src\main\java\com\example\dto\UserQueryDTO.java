package com.example.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户查询条件DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名（模糊查询）
     */
    private String username;
    
    /**
     * 邮箱（模糊查询）
     */
    private String email;
    
    /**
     * 昵称（模糊查询）
     */
    private String nickname;
    
    /**
     * 手机号（模糊查询）
     */
    private String phone;
    
    /**
     * 性别: 0-未知, 1-男, 2-女
     */
    private Integer gender;
    
    /**
     * 最小年龄
     */
    @Min(value = 0, message = "最小年龄不能为负数")
    private Integer minAge;
    
    /**
     * 最大年龄
     */
    @Max(value = 150, message = "最大年龄不能超过150")
    private Integer maxAge;
    
    /**
     * 地址（模糊查询）
     */
    private String address;
    
    /**
     * 状态: 0-禁用, 1-启用, 2-锁定
     */
    private Integer status;
    
    /**
     * 状态列表（用于IN查询）
     */
    private List<Integer> statusList;
    
    /**
     * 邮箱是否验证: 0-未验证, 1-已验证
     */
    private Integer emailVerified;
    
    /**
     * 手机是否验证: 0-未验证, 1-已验证
     */
    private Integer phoneVerified;
    
    /**
     * 创建开始时间
     */
    private LocalDateTime createTimeStart;
    
    /**
     * 创建结束时间
     */
    private LocalDateTime createTimeEnd;
    
    /**
     * 更新开始时间
     */
    private LocalDateTime updateTimeStart;
    
    /**
     * 更新结束时间
     */
    private LocalDateTime updateTimeEnd;
    
    /**
     * 生日开始日期
     */
    private LocalDate birthdayStart;
    
    /**
     * 生日结束日期
     */
    private LocalDate birthdayEnd;
    
    /**
     * 最后登录开始时间
     */
    private LocalDateTime lastLoginTimeStart;
    
    /**
     * 最后登录结束时间
     */
    private LocalDateTime lastLoginTimeEnd;
    
    /**
     * 最小登录次数
     */
    @Min(value = 0, message = "最小登录次数不能为负数")
    private Integer minLoginCount;
    
    /**
     * 最大登录次数
     */
    private Integer maxLoginCount;
    
    /**
     * 创建人ID
     */
    private Long createUser;
    
    /**
     * 更新人ID
     */
    private Long updateUser;
    
    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    private Integer deleted;
    
    /**
     * 角色ID（用于查询拥有指定角色的用户）
     */
    private Long roleId;
    
    /**
     * 角色编码（用于查询拥有指定角色的用户）
     */
    private String roleCode;
    
    /**
     * 角色ID列表（用于查询拥有指定角色的用户）
     */
    private List<Long> roleIds;
    
    /**
     * 权限编码（用于查询拥有指定权限的用户）
     */
    private String permissionCode;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向: ASC, DESC
     */
    private String orderDirection;
    
    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize;
    
    /**
     * 偏移量（计算属性）
     */
    public Integer getOffset() {
        if (pageNum == null || pageSize == null) {
            return null;
        }
        return (pageNum - 1) * pageSize;
    }
    
    /**
     * 获取排序SQL片段
     */
    public String getOrderByClause() {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            return "create_time DESC";
        }
        
        String direction = "ASC";
        if ("DESC".equalsIgnoreCase(orderDirection)) {
            direction = "DESC";
        }
        
        // 防止SQL注入，只允许特定字段排序
        switch (orderBy.toLowerCase()) {
            case "id":
                return "id " + direction;
            case "username":
                return "username " + direction;
            case "email":
                return "email " + direction;
            case "nickname":
                return "nickname " + direction;
            case "age":
                return "age " + direction;
            case "status":
                return "status " + direction;
            case "createtime":
            case "create_time":
                return "create_time " + direction;
            case "updatetime":
            case "update_time":
                return "update_time " + direction;
            case "lastlogintime":
            case "last_login_time":
                return "last_login_time " + direction;
            case "logincount":
            case "login_count":
                return "login_count " + direction;
            default:
                return "create_time DESC";
        }
    }
    
    /**
     * 检查是否有查询条件
     */
    public boolean hasConditions() {
        return id != null ||
               (username != null && !username.trim().isEmpty()) ||
               (email != null && !email.trim().isEmpty()) ||
               (nickname != null && !nickname.trim().isEmpty()) ||
               (phone != null && !phone.trim().isEmpty()) ||
               gender != null ||
               minAge != null ||
               maxAge != null ||
               (address != null && !address.trim().isEmpty()) ||
               status != null ||
               (statusList != null && !statusList.isEmpty()) ||
               emailVerified != null ||
               phoneVerified != null ||
               createTimeStart != null ||
               createTimeEnd != null ||
               updateTimeStart != null ||
               updateTimeEnd != null ||
               birthdayStart != null ||
               birthdayEnd != null ||
               lastLoginTimeStart != null ||
               lastLoginTimeEnd != null ||
               minLoginCount != null ||
               maxLoginCount != null ||
               createUser != null ||
               updateUser != null ||
               deleted != null ||
               roleId != null ||
               (roleCode != null && !roleCode.trim().isEmpty()) ||
               (roleIds != null && !roleIds.isEmpty()) ||
               (permissionCode != null && !permissionCode.trim().isEmpty());
    }
    
    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (deleted == null) {
            deleted = 0; // 默认查询未删除的记录
        }
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        if (orderBy == null) {
            orderBy = "create_time";
        }
        if (orderDirection == null) {
            orderDirection = "DESC";
        }
    }
}
