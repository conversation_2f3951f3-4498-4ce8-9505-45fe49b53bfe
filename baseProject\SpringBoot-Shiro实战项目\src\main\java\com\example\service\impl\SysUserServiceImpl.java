package com.example.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import com.example.mapper.SysUserMapper;
import com.example.service.SysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 系统用户服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysUserServiceImpl implements SysUserService {
    
    private static final Logger logger = LoggerFactory.getLogger(SysUserServiceImpl.class);
    
    @Autowired
    private SysUserMapper userMapper;
    
    @Override
    @Transactional(readOnly = true)
    public SysUser getUserById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userMapper.selectById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public SysUser getUserByUsername(String username) {
        if (StrUtil.isBlank(username)) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        return userMapper.selectByUsername(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    public SysUser getUserByEmail(String email) {
        if (StrUtil.isBlank(email)) {
            throw new IllegalArgumentException("邮箱不能为空");
        }
        return userMapper.selectByEmail(email);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<SysUser> getAllUsers() {
        return userMapper.selectAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<SysUser> getUsersByStatus(Integer status) {
        return userMapper.selectByStatus(status);
    }
    
    @Override
    public boolean createUser(SysUser user) {
        if (user == null) {
            throw new IllegalArgumentException("用户信息不能为空");
        }
        
        // 验证用户信息
        validateUserForCreate(user);
        
        try {
            // 生成盐值
            String salt = generateSalt();
            user.setSalt(salt);
            
            // 加密密码
            if (StrUtil.isNotBlank(user.getPassword())) {
                String encryptedPassword = encryptPassword(user.getPassword(), user.getCredentialsSalt());
                user.setPassword(encryptedPassword);
            }
            
            // 设置创建时间
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            
            // 设置默认状态
            if (user.getStatus() == null) {
                user.setStatus(1);
            }
            
            int result = userMapper.insert(user);
            logger.info("用户创建成功: {}", user.getUsername());
            return result > 0;
        } catch (Exception e) {
            logger.error("创建用户失败: {}", user.getUsername(), e);
            throw new RuntimeException("用户创建失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean updateUser(SysUser user) {
        if (user == null || user.getId() == null) {
            throw new IllegalArgumentException("用户信息或用户ID不能为空");
        }
        
        // 验证用户信息
        validateUserForUpdate(user);
        
        try {
            // 设置更新时间
            user.setUpdateTime(LocalDateTime.now());
            
            int result = userMapper.update(user);
            logger.info("用户更新成功: {}", user.getUsername());
            return result > 0;
        } catch (Exception e) {
            logger.error("更新用户失败: {}", user.getUsername(), e);
            throw new RuntimeException("用户更新失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean deleteUser(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        try {
            // 先删除用户角色关联
            userMapper.deleteUserRoles(id);
            
            // 再删除用户
            int result = userMapper.deleteById(id);
            logger.info("用户删除成功: ID={}", id);
            return result > 0;
        } catch (Exception e) {
            logger.error("删除用户失败: ID={}", id, e);
            throw new RuntimeException("用户删除失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StrUtil.isBlank(oldPassword)) {
            throw new IllegalArgumentException("原密码不能为空");
        }
        if (StrUtil.isBlank(newPassword)) {
            throw new IllegalArgumentException("新密码不能为空");
        }
        
        try {
            // 查询用户
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 验证原密码
            String encryptedOldPassword = encryptPassword(oldPassword, user.getCredentialsSalt());
            if (!encryptedOldPassword.equals(user.getPassword())) {
                throw new RuntimeException("原密码错误");
            }
            
            // 生成新盐值
            String newSalt = generateSalt();
            
            // 加密新密码
            String encryptedNewPassword = encryptPassword(newPassword, user.getUsername() + newSalt);
            
            // 更新密码
            int result = userMapper.updatePassword(userId, encryptedNewPassword, newSalt, LocalDateTime.now(), userId);
            
            logger.info("用户密码修改成功: {}", user.getUsername());
            return result > 0;
        } catch (Exception e) {
            logger.error("修改用户密码失败: userId={}", userId, e);
            throw new RuntimeException("密码修改失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean resetPassword(Long userId, String newPassword) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StrUtil.isBlank(newPassword)) {
            throw new IllegalArgumentException("新密码不能为空");
        }
        
        try {
            // 查询用户
            SysUser user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 生成新盐值
            String newSalt = generateSalt();
            
            // 加密新密码
            String encryptedPassword = encryptPassword(newPassword, user.getUsername() + newSalt);
            
            // 更新密码
            int result = userMapper.updatePassword(userId, encryptedPassword, newSalt, LocalDateTime.now(), userId);
            
            logger.info("用户密码重置成功: userId={}", userId);
            return result > 0;
        } catch (Exception e) {
            logger.error("重置用户密码失败: userId={}", userId, e);
            throw new RuntimeException("密码重置失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean changeUserStatus(Long userId, Integer status) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (status == null || (status != 0 && status != 1)) {
            throw new IllegalArgumentException("用户状态值无效");
        }
        
        try {
            SysUser user = new SysUser();
            user.setId(userId);
            user.setStatus(status);
            user.setUpdateTime(LocalDateTime.now());
            
            int result = userMapper.update(user);
            logger.info("用户状态修改成功: userId={}, status={}", userId, status);
            return result > 0;
        } catch (Exception e) {
            logger.error("修改用户状态失败: userId={}, status={}", userId, status, e);
            throw new RuntimeException("用户状态修改失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameExists(String username) {
        if (StrUtil.isBlank(username)) {
            return false;
        }
        return userMapper.countByUsername(username) > 0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isEmailExists(String email) {
        if (StrUtil.isBlank(email)) {
            return false;
        }
        return userMapper.countByEmail(email) > 0;
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<SysRole> getUserRoles(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userMapper.selectUserRoles(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<SysPermission> getUserPermissions(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userMapper.selectUserPermissions(userId);
    }
    
    @Override
    @Transactional(readOnly = true)
    public SysUser getUserWithRolesAndPermissions(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userMapper.selectUserWithRolesAndPermissions(userId);
    }
    
    @Override
    public boolean assignRolesToUser(Long userId, List<Long> roleIds) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (roleIds == null || roleIds.isEmpty()) {
            throw new IllegalArgumentException("角色ID列表不能为空");
        }
        
        try {
            // 先删除用户的所有角色
            userMapper.deleteUserRoles(userId);
            
            // 再分配新角色
            LocalDateTime now = LocalDateTime.now();
            for (Long roleId : roleIds) {
                userMapper.insertUserRole(userId, roleId, now, userId);
            }
            
            logger.info("用户角色分配成功: userId={}, roleIds={}", userId, roleIds);
            return true;
        } catch (Exception e) {
            logger.error("分配用户角色失败: userId={}, roleIds={}", userId, roleIds, e);
            throw new RuntimeException("用户角色分配失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean removeUserRole(Long userId, Long roleId) {
        if (userId == null || roleId == null) {
            throw new IllegalArgumentException("用户ID和角色ID不能为空");
        }
        
        try {
            int result = userMapper.deleteUserRole(userId, roleId);
            logger.info("用户角色移除成功: userId={}, roleId={}", userId, roleId);
            return result > 0;
        } catch (Exception e) {
            logger.error("移除用户角色失败: userId={}, roleId={}", userId, roleId, e);
            throw new RuntimeException("用户角色移除失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean removeAllUserRoles(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        try {
            int result = userMapper.deleteUserRoles(userId);
            logger.info("用户所有角色移除成功: userId={}", userId);
            return result >= 0;
        } catch (Exception e) {
            logger.error("移除用户所有角色失败: userId={}", userId, e);
            throw new RuntimeException("用户角色移除失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean updateLastLogin(Long userId, String loginIp) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        try {
            int result = userMapper.updateLastLogin(userId, LocalDateTime.now(), loginIp);
            return result > 0;
        } catch (Exception e) {
            logger.error("更新用户最后登录信息失败: userId={}", userId, e);
            return false;
        }
    }
    
    @Override
    @Transactional(readOnly = true)
    public int getTotalUserCount() {
        return userMapper.countTotal();
    }
    
    @Override
    @Transactional(readOnly = true)
    public int getActiveUserCount() {
        return userMapper.selectByStatus(1).size();
    }
    
    /**
     * 验证用户信息（创建时）
     */
    private void validateUserForCreate(SysUser user) {
        if (StrUtil.isBlank(user.getUsername())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (StrUtil.isBlank(user.getPassword())) {
            throw new IllegalArgumentException("密码不能为空");
        }
        if (StrUtil.isNotBlank(user.getEmail()) && isEmailExists(user.getEmail())) {
            throw new IllegalArgumentException("邮箱已存在");
        }
        if (isUsernameExists(user.getUsername())) {
            throw new IllegalArgumentException("用户名已存在");
        }
    }
    
    /**
     * 验证用户信息（更新时）
     */
    private void validateUserForUpdate(SysUser user) {
        if (StrUtil.isBlank(user.getUsername())) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        // 更新时不验证用户名和邮箱重复，因为可能是自己的信息
    }
    
    /**
     * 生成盐值
     */
    private String generateSalt() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
    
    /**
     * 加密密码
     */
    private String encryptPassword(String password, String salt) {
        return DigestUtil.md5Hex(password + salt);
    }
}
