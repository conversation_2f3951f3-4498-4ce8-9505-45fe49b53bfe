package com.example.mybatisrbac.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI 3 配置类
 * 配置 Swagger 文档信息
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8082}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("MyBatis RBAC 系统 API")
                        .description("基于 Spring Boot + MyBatis + Druid 的 RBAC 权限管理系统\n\n" +
                                   "功能特性：\n" +
                                   "- 用户管理\n" +
                                   "- 角色管理\n" +
                                   "- 权限管理\n" +
                                   "- 数据库连接池监控\n" +
                                   "- API 文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("MyBatis RBAC Team")
                                .email("<EMAIL>")
                                .url("https://github.com/example/mybatisrbac"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("开发环境"),
                        new Server()
                                .url("https://api.example.com")
                                .description("生产环境")
                ));
    }


}
