# Java注解详解

## 目录
1. [注解简介](#注解简介)
2. [内置注解](#内置注解)
3. [元注解](#元注解)
4. [自定义注解](#自定义注解)
5. [注解处理器](#注解处理器)
6. [Spring注解](#spring注解)
7. [实际应用](#实际应用)
8. [最佳实践](#最佳实践)

## 注解简介

### 什么是注解
注解（Annotation）是Java 5引入的一种特殊的标记，它可以在编译、类加载、运行时被读取，并执行相应的处理。注解本身不会改变程序的执行，但可以被其他程序（如编译器、框架）读取并据此做出相应的处理。

### 注解的作用
- **编译检查** - 让编译器检查代码
- **代码生成** - 根据注解生成代码
- **运行时处理** - 在运行时通过反射读取注解信息

### 注解的语法
```java
@注解名(属性名=属性值, 属性名=属性值...)
public class MyClass {
    // 类体
}
```

## 内置注解

### 1. @Override
用于标记重写父类的方法，编译器会检查是否真的重写了父类方法。

```java
public class Animal {
    public void makeSound() {
        System.out.println("动物发出声音");
    }
}

public class Dog extends Animal {
    @Override
    public void makeSound() {
        System.out.println("汪汪汪");
    }
    
    // 编译错误：没有重写父类方法
    // @Override
    // public void makeSounds() {  // 方法名错误
    //     System.out.println("汪汪汪");
    // }
}
```

### 2. @Deprecated
标记已过时的方法、类或字段，编译器会发出警告。

```java
public class Calculator {
    
    /**
     * @deprecated 使用 {@link #add(int, int)} 替代
     */
    @Deprecated
    public int oldAdd(int a, int b) {
        return a + b;
    }
    
    public int add(int a, int b) {
        return a + b;
    }
}

// 使用时会有警告
Calculator calc = new Calculator();
int result = calc.oldAdd(1, 2); // 编译器警告
```

### 3. @SuppressWarnings
抑制编译器警告。

```java
public class WarningExample {
    
    @SuppressWarnings("unchecked")
    public void uncheckedCast() {
        List list = new ArrayList();
        List<String> stringList = (List<String>) list; // 抑制unchecked警告
    }
    
    @SuppressWarnings({"unused", "deprecation"})
    public void multipleWarnings() {
        String unusedVariable = "not used"; // 抑制unused警告
        Calculator calc = new Calculator();
        calc.oldAdd(1, 2); // 抑制deprecation警告
    }
    
    @SuppressWarnings("all")
    public void allWarnings() {
        // 抑制所有警告
    }
}
```

### 4. @SafeVarargs
用于抑制可变参数的堆污染警告。

```java
public class VarargsExample {
    
    @SafeVarargs
    public static <T> void printItems(T... items) {
        for (T item : items) {
            System.out.println(item);
        }
    }
    
    public static void main(String[] args) {
        printItems("Hello", "World", "Java");
        printItems(1, 2, 3, 4, 5);
    }
}
```

### 5. @FunctionalInterface
标记函数式接口，确保接口只有一个抽象方法。

```java
@FunctionalInterface
public interface Calculator {
    int calculate(int a, int b);
    
    // 可以有默认方法
    default void printResult(int result) {
        System.out.println("结果: " + result);
    }
    
    // 可以有静态方法
    static void info() {
        System.out.println("这是一个计算器接口");
    }
    
    // 编译错误：函数式接口只能有一个抽象方法
    // int subtract(int a, int b);
}

// 使用Lambda表达式
Calculator add = (a, b) -> a + b;
Calculator multiply = (a, b) -> a * b;
```

## 元注解

元注解是用来注解其他注解的注解，Java提供了四个标准的元注解。

### 1. @Target
指定注解可以应用的目标。

```java
import java.lang.annotation.ElementType;
import java.lang.annotation.Target;

// 只能用于方法
@Target(ElementType.METHOD)
public @interface MethodOnly {
    String value();
}

// 可以用于类和方法
@Target({ElementType.TYPE, ElementType.METHOD})
public @interface TypeAndMethod {
    String value();
}

// 所有ElementType值
@Target({
    ElementType.TYPE,           // 类、接口、枚举
    ElementType.FIELD,          // 字段
    ElementType.METHOD,         // 方法
    ElementType.PARAMETER,      // 参数
    ElementType.CONSTRUCTOR,    // 构造函数
    ElementType.LOCAL_VARIABLE, // 局部变量
    ElementType.ANNOTATION_TYPE,// 注解类型
    ElementType.PACKAGE,        // 包
    ElementType.TYPE_PARAMETER, // 类型参数
    ElementType.TYPE_USE        // 类型使用
})
public @interface AllTargets {
    String value();
}
```

### 2. @Retention
指定注解的保留策略。

```java
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

// 源码期保留，编译后丢弃
@Retention(RetentionPolicy.SOURCE)
public @interface SourceOnly {
    String value();
}

// 编译期保留，运行时丢弃（默认）
@Retention(RetentionPolicy.CLASS)
public @interface ClassOnly {
    String value();
}

// 运行时保留，可以通过反射读取
@Retention(RetentionPolicy.RUNTIME)
public @interface RuntimeKeep {
    String value();
}
```

### 3. @Documented
指定注解是否包含在JavaDoc中。

```java
import java.lang.annotation.Documented;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface DocumentedAnnotation {
    String author();
    String version() default "1.0";
}

/**
 * 示例类
 * 这个注解会出现在JavaDoc中
 */
@DocumentedAnnotation(author = "Developer", version = "2.0")
public class ExampleClass {
    // 类体
}
```

### 4. @Inherited
指定注解是否可以被子类继承。

```java
import java.lang.annotation.Inherited;

@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface InheritedAnnotation {
    String value();
}

@InheritedAnnotation("父类注解")
public class ParentClass {
    // 父类
}

// 子类会继承父类的@InheritedAnnotation注解
public class ChildClass extends ParentClass {
    // 子类
}
```

## 自定义注解

### 基本语法
```java
public @interface 注解名 {
    数据类型 属性名() default 默认值;
}
```

### 注解属性类型
注解的属性类型只能是以下几种：
- 基本数据类型
- String
- Class
- enum
- 注解类型
- 以上类型的数组

### 实例：自定义验证注解

```java
import java.lang.annotation.*;

/**
 * 自定义验证注解
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Validate {
    
    /**
     * 验证类型
     */
    ValidationType type() default ValidationType.NOT_NULL;
    
    /**
     * 最小长度
     */
    int minLength() default 0;
    
    /**
     * 最大长度
     */
    int maxLength() default Integer.MAX_VALUE;
    
    /**
     * 正则表达式
     */
    String pattern() default "";
    
    /**
     * 错误消息
     */
    String message() default "验证失败";
    
    /**
     * 是否必填
     */
    boolean required() default true;
}

/**
 * 验证类型枚举
 */
enum ValidationType {
    NOT_NULL,       // 非空
    EMAIL,          // 邮箱
    PHONE,          // 手机号
    ID_CARD,        // 身份证
    CUSTOM          // 自定义正则
}
```

### 使用自定义注解

```java
public class User {
    
    @Validate(type = ValidationType.NOT_NULL, message = "用户名不能为空")
    private String username;
    
    @Validate(type = ValidationType.EMAIL, message = "邮箱格式不正确")
    private String email;
    
    @Validate(type = ValidationType.PHONE, message = "手机号格式不正确")
    private String phone;
    
    @Validate(
        type = ValidationType.CUSTOM,
        pattern = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$",
        message = "密码必须包含字母和数字，长度6-20位"
    )
    private String password;
    
    @Validate(
        minLength = 2,
        maxLength = 50,
        message = "真实姓名长度必须在2-50之间"
    )
    private String realName;
    
    // 构造函数、getter和setter
    public User() {}
    
    public User(String username, String email, String phone, String password, String realName) {
        this.username = username;
        this.email = email;
        this.phone = phone;
        this.password = password;
        this.realName = realName;
    }
    
    // getters and setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getRealName() { return realName; }
    public void setRealName(String realName) { this.realName = realName; }
}
```

## 注解处理器

### 反射读取注解
通过反射API可以在运行时读取注解信息。

```java
import java.lang.reflect.Field;
import java.util.regex.Pattern;

/**
 * 注解验证器
 */
public class AnnotationValidator {

    /**
     * 验证对象
     */
    public static ValidationResult validate(Object obj) {
        ValidationResult result = new ValidationResult();
        Class<?> clazz = obj.getClass();

        // 获取所有字段
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // 检查字段是否有@Validate注解
            if (field.isAnnotationPresent(Validate.class)) {
                Validate validate = field.getAnnotation(Validate.class);

                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);

                    // 执行验证
                    String error = validateField(field.getName(), value, validate);
                    if (error != null) {
                        result.addError(field.getName(), error);
                    }

                } catch (IllegalAccessException e) {
                    result.addError(field.getName(), "无法访问字段");
                }
            }
        }

        return result;
    }

    /**
     * 验证单个字段
     */
    private static String validateField(String fieldName, Object value, Validate validate) {
        String stringValue = value == null ? null : value.toString();

        // 必填验证
        if (validate.required() && (value == null || stringValue.trim().isEmpty())) {
            return validate.message();
        }

        // 如果值为空且不是必填，跳过其他验证
        if (value == null || stringValue.trim().isEmpty()) {
            return null;
        }

        // 长度验证
        if (stringValue.length() < validate.minLength() ||
            stringValue.length() > validate.maxLength()) {
            return validate.message();
        }

        // 类型验证
        switch (validate.type()) {
            case EMAIL:
                if (!isValidEmail(stringValue)) {
                    return validate.message();
                }
                break;
            case PHONE:
                if (!isValidPhone(stringValue)) {
                    return validate.message();
                }
                break;
            case ID_CARD:
                if (!isValidIdCard(stringValue)) {
                    return validate.message();
                }
                break;
            case CUSTOM:
                if (!validate.pattern().isEmpty() && !Pattern.matches(validate.pattern(), stringValue)) {
                    return validate.message();
                }
                break;
        }

        return null;
    }

    // 验证邮箱
    private static boolean isValidEmail(String email) {
        String emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(emailPattern, email);
    }

    // 验证手机号
    private static boolean isValidPhone(String phone) {
        String phonePattern = "^1[3-9]\\d{9}$";
        return Pattern.matches(phonePattern, phone);
    }

    // 验证身份证
    private static boolean isValidIdCard(String idCard) {
        String idCardPattern = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return Pattern.matches(idCardPattern, idCard);
    }
}

/**
 * 验证结果类
 */
class ValidationResult {
    private boolean valid = true;
    private java.util.Map<String, String> errors = new java.util.HashMap<>();

    public void addError(String field, String message) {
        this.valid = false;
        this.errors.put(field, message);
    }

    public boolean isValid() {
        return valid;
    }

    public java.util.Map<String, String> getErrors() {
        return errors;
    }

    public String getErrorMessage() {
        if (valid) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (java.util.Map.Entry<String, String> entry : errors.entrySet()) {
            sb.append(entry.getKey()).append(": ").append(entry.getValue()).append("; ");
        }
        return sb.toString();
    }
}
```

### 使用注解验证器

```java
public class ValidationExample {

    public static void main(String[] args) {
        // 创建用户对象
        User user1 = new User("john", "invalid-email", "12345", "123", "J");
        User user2 = new User("alice", "<EMAIL>", "13800138000", "password123", "Alice Smith");

        // 验证用户1
        System.out.println("验证用户1:");
        ValidationResult result1 = AnnotationValidator.validate(user1);
        if (result1.isValid()) {
            System.out.println("验证通过");
        } else {
            System.out.println("验证失败: " + result1.getErrorMessage());
            result1.getErrors().forEach((field, error) ->
                System.out.println("  " + field + ": " + error));
        }

        // 验证用户2
        System.out.println("\n验证用户2:");
        ValidationResult result2 = AnnotationValidator.validate(user2);
        if (result2.isValid()) {
            System.out.println("验证通过");
        } else {
            System.out.println("验证失败: " + result2.getErrorMessage());
        }
    }
}
```

### 编译时注解处理器
编译时注解处理器可以在编译期间处理注解，生成代码或进行检查。

```java
import javax.annotation.processing.*;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.*;
import javax.tools.Diagnostic;
import java.util.Set;

/**
 * 编译时注解处理器示例
 */
@SupportedAnnotationTypes("com.example.Validate")
@SupportedSourceVersion(SourceVersion.RELEASE_8)
public class ValidateProcessor extends AbstractProcessor {

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        for (TypeElement annotation : annotations) {
            Set<? extends Element> annotatedElements = roundEnv.getElementsAnnotatedWith(annotation);

            for (Element element : annotatedElements) {
                if (element.getKind() == ElementKind.FIELD) {
                    processField((VariableElement) element);
                }
            }
        }
        return true;
    }

    private void processField(VariableElement field) {
        Validate validate = field.getAnnotation(Validate.class);
        if (validate != null) {
            // 在编译时进行检查
            if (validate.minLength() > validate.maxLength()) {
                processingEnv.getMessager().printMessage(
                    Diagnostic.Kind.ERROR,
                    "最小长度不能大于最大长度",
                    field
                );
            }
        }
    }
}
```

## Spring注解

Spring框架大量使用注解来简化配置和开发，以下是常用的Spring注解。

### 1. 核心注解

#### @Component系列
```java
// 通用组件注解
@Component
public class GenericComponent {
    // 组件逻辑
}

// 服务层注解
@Service
public class UserService {
    public void saveUser(User user) {
        // 业务逻辑
    }
}

// 数据访问层注解
@Repository
public class UserRepository {
    public User findById(Long id) {
        // 数据访问逻辑
        return null;
    }
}

// 控制器注解
@Controller
public class UserController {

    @RequestMapping("/users")
    public String listUsers() {
        return "users";
    }
}

// REST控制器注解（@Controller + @ResponseBody）
@RestController
public class UserRestController {

    @GetMapping("/api/users")
    public List<User> getUsers() {
        return Arrays.asList();
    }
}
```

#### @Configuration和@Bean
```java
@Configuration
public class AppConfig {

    @Bean
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("********************************");
        dataSource.setUsername("root");
        dataSource.setPassword("password");
        return dataSource;
    }

    @Bean
    @Primary  // 主要的Bean
    public UserService primaryUserService() {
        return new UserServiceImpl();
    }

    @Bean
    @Qualifier("secondaryUserService")  // 指定Bean名称
    public UserService secondaryUserService() {
        return new AnotherUserServiceImpl();
    }

    @Bean
    @Scope("prototype")  // 原型作用域
    public PrototypeBean prototypeBean() {
        return new PrototypeBean();
    }
}
```

### 2. 依赖注入注解

```java
@Service
public class UserService {

    // 字段注入
    @Autowired
    private UserRepository userRepository;

    // 可选注入
    @Autowired(required = false)
    private OptionalService optionalService;

    // 指定Bean名称注入
    @Autowired
    @Qualifier("secondaryUserService")
    private UserService anotherUserService;

    // 构造函数注入（推荐）
    private final EmailService emailService;

    @Autowired
    public UserService(EmailService emailService) {
        this.emailService = emailService;
    }

    // Setter注入
    private SmsService smsService;

    @Autowired
    public void setSmsService(SmsService smsService) {
        this.smsService = smsService;
    }

    // 注入配置值
    @Value("${app.name}")
    private String appName;

    @Value("${app.version:1.0}")  // 默认值
    private String appVersion;

    // 注入环境变量
    @Value("${JAVA_HOME}")
    private String javaHome;

    // SpEL表达式
    @Value("#{systemProperties['user.name']}")
    private String userName;
}
```

### 3. Web注解

```java
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")  // 跨域支持
public class UserController {

    @Autowired
    private UserService userService;

    // GET请求
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        // 处理逻辑
        return ResponseEntity.ok(Arrays.asList());
    }

    // 路径变量
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        User user = userService.findById(id);
        return ResponseEntity.ok(user);
    }

    // POST请求，请求体
    @PostMapping
    public ResponseEntity<User> createUser(@Valid @RequestBody UserDTO userDTO) {
        User user = userService.create(userDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(user);
    }

    // PUT请求
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UserDTO userDTO) {
        User user = userService.update(id, userDTO);
        return ResponseEntity.ok(user);
    }

    // DELETE请求
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.delete(id);
        return ResponseEntity.noContent().build();
    }

    // 请求头
    @GetMapping("/profile")
    public ResponseEntity<User> getUserProfile(
            @RequestHeader("Authorization") String token) {
        // 处理逻辑
        return ResponseEntity.ok(new User());
    }

    // Cookie
    @GetMapping("/preferences")
    public ResponseEntity<String> getUserPreferences(
            @CookieValue("sessionId") String sessionId) {
        // 处理逻辑
        return ResponseEntity.ok("preferences");
    }
}
```

### 4. 数据访问注解

```java
// JPA Repository
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    // 查询方法
    List<User> findByUsername(String username);

    List<User> findByAgeGreaterThan(Integer age);

    // 自定义查询
    @Query("SELECT u FROM User u WHERE u.email = ?1")
    Optional<User> findByEmail(String email);

    // 原生SQL查询
    @Query(value = "SELECT * FROM users WHERE status = ?1", nativeQuery = true)
    List<User> findByStatus(String status);

    // 更新操作
    @Modifying
    @Query("UPDATE User u SET u.status = ?2 WHERE u.id = ?1")
    int updateUserStatus(Long id, String status);
}

// 事务注解
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    // 只读事务
    @Transactional(readOnly = true)
    public User findById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    // 指定事务传播行为
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createUserInNewTransaction(User user) {
        userRepository.save(user);
    }

    // 指定回滚条件
    @Transactional(rollbackFor = Exception.class)
    public void createUserWithRollback(User user) throws Exception {
        userRepository.save(user);
        if (user.getUsername().equals("error")) {
            throw new Exception("测试回滚");
        }
    }
}
```

### 5. 缓存注解

```java
@Service
public class UserService {

    // 缓存结果
    @Cacheable(value = "users", key = "#id")
    public User findById(Long id) {
        // 模拟数据库查询
        System.out.println("从数据库查询用户: " + id);
        return new User();
    }

    // 缓存结果，条件缓存
    @Cacheable(value = "users", key = "#username", condition = "#username.length() > 3")
    public User findByUsername(String username) {
        System.out.println("从数据库查询用户: " + username);
        return new User();
    }

    // 更新缓存
    @CachePut(value = "users", key = "#user.id")
    public User updateUser(User user) {
        System.out.println("更新用户: " + user.getId());
        return user;
    }

    // 清除缓存
    @CacheEvict(value = "users", key = "#id")
    public void deleteUser(Long id) {
        System.out.println("删除用户: " + id);
    }

    // 清除所有缓存
    @CacheEvict(value = "users", allEntries = true)
    public void clearAllUsers() {
        System.out.println("清除所有用户缓存");
    }
}
```

### 6. 安全注解

```java
@RestController
@RequestMapping("/api/admin")
public class AdminController {

    // 需要认证
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/dashboard")
    public String dashboard() {
        return "管理员仪表板";
    }

    // 需要特定角色
    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("/users")
    public List<User> getAllUsers() {
        return Arrays.asList();
    }

    // 需要特定权限
    @PreAuthorize("hasAuthority('USER_READ')")
    @GetMapping("/users/{id}")
    public User getUser(@PathVariable Long id) {
        return new User();
    }

    // 复杂权限表达式
    @PreAuthorize("hasRole('ADMIN') or (hasRole('USER') and #id == authentication.principal.id)")
    @GetMapping("/users/{id}/profile")
    public User getUserProfile(@PathVariable Long id) {
        return new User();
    }

    // 方法执行后检查
    @PostAuthorize("returnObject.username == authentication.name")
    @GetMapping("/profile")
    public User getCurrentUserProfile() {
        return new User();
    }
}
```

## 实际应用

### 1. 日志注解
创建一个自动记录方法执行日志的注解。

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 操作描述
     */
    String value() default "";

    /**
     * 日志级别
     */
    LogLevel level() default LogLevel.INFO;

    /**
     * 是否记录参数
     */
    boolean logArgs() default true;

    /**
     * 是否记录返回值
     */
    boolean logResult() default true;

    /**
     * 是否记录执行时间
     */
    boolean logTime() default true;
}

enum LogLevel {
    DEBUG, INFO, WARN, ERROR
}
```

### 2. AOP日志处理器
```java
@Aspect
@Component
public class LogAspect {

    private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);

    @Around("@annotation(log)")
    public Object around(ProceedingJoinPoint joinPoint, Log log) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();

        long startTime = System.currentTimeMillis();

        // 记录方法开始
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("执行方法: ").append(className).append(".").append(methodName);

        if (!log.value().isEmpty()) {
            logMessage.append(" - ").append(log.value());
        }

        // 记录参数
        if (log.logArgs()) {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0) {
                logMessage.append(" - 参数: ").append(Arrays.toString(args));
            }
        }

        logWithLevel(log.level(), logMessage.toString());

        try {
            // 执行方法
            Object result = joinPoint.proceed();

            // 记录返回值
            if (log.logResult() && result != null) {
                logWithLevel(log.level(), "方法返回: " + result);
            }

            // 记录执行时间
            if (log.logTime()) {
                long endTime = System.currentTimeMillis();
                logWithLevel(log.level(), "方法执行时间: " + (endTime - startTime) + "ms");
            }

            return result;

        } catch (Throwable throwable) {
            logWithLevel(LogLevel.ERROR, "方法执行异常: " + throwable.getMessage());
            throw throwable;
        }
    }

    private void logWithLevel(LogLevel level, String message) {
        switch (level) {
            case DEBUG:
                logger.debug(message);
                break;
            case INFO:
                logger.info(message);
                break;
            case WARN:
                logger.warn(message);
                break;
            case ERROR:
                logger.error(message);
                break;
        }
    }
}
```

### 3. 使用日志注解
```java
@Service
public class UserService {

    @Log("查询用户信息")
    public User findById(Long id) {
        // 模拟数据库查询
        return new User();
    }

    @Log(value = "创建用户", level = LogLevel.INFO, logArgs = true, logResult = true)
    public User createUser(UserDTO userDTO) {
        // 创建用户逻辑
        User user = new User();
        user.setUsername(userDTO.getUsername());
        return user;
    }

    @Log(value = "删除用户", level = LogLevel.WARN, logTime = true)
    public void deleteUser(Long id) {
        // 删除用户逻辑
        System.out.println("删除用户: " + id);
    }
}
```

### 4. 权限控制注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {

    /**
     * 需要的权限
     */
    String[] value();

    /**
     * 权限关系（AND/OR）
     */
    PermissionType type() default PermissionType.AND;

    /**
     * 错误消息
     */
    String message() default "权限不足";
}

enum PermissionType {
    AND, OR
}
```

### 5. 权限检查处理器
```java
@Aspect
@Component
public class PermissionAspect {

    @Autowired
    private PermissionService permissionService;

    @Before("@annotation(requirePermission)")
    public void checkPermission(JoinPoint joinPoint, RequirePermission requirePermission) {
        // 获取当前用户
        String currentUser = getCurrentUser();

        String[] requiredPermissions = requirePermission.value();
        PermissionType type = requirePermission.type();

        boolean hasPermission = false;

        if (type == PermissionType.AND) {
            // 需要所有权限
            hasPermission = permissionService.hasAllPermissions(currentUser, requiredPermissions);
        } else {
            // 需要任一权限
            hasPermission = permissionService.hasAnyPermission(currentUser, requiredPermissions);
        }

        if (!hasPermission) {
            throw new PermissionDeniedException(requirePermission.message());
        }
    }

    private String getCurrentUser() {
        // 从SecurityContext或Session中获取当前用户
        return "currentUser";
    }
}

class PermissionDeniedException extends RuntimeException {
    public PermissionDeniedException(String message) {
        super(message);
    }
}
```

### 6. 缓存注解应用
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Cache {

    /**
     * 缓存名称
     */
    String name();

    /**
     * 缓存键
     */
    String key() default "";

    /**
     * 过期时间（秒）
     */
    int expireTime() default 300;

    /**
     * 缓存条件
     */
    String condition() default "";
}

@Service
public class ProductService {

    @Cache(name = "products", key = "#id", expireTime = 600)
    public Product findById(Long id) {
        // 从数据库查询产品
        return new Product();
    }

    @Cache(name = "products", key = "#category", condition = "#category != null")
    public List<Product> findByCategory(String category) {
        // 从数据库查询产品列表
        return Arrays.asList();
    }
}
```

## 最佳实践

### 1. 注解设计原则

#### 单一职责原则
```java
// ❌ 不好的设计：一个注解做太多事情
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface BadAnnotation {
    boolean cache() default false;
    boolean log() default false;
    boolean validate() default false;
    String[] permissions() default {};
}

// ✅ 好的设计：每个注解职责单一
@Cacheable
@Log
@RequirePermission("USER_READ")
public User getUser(Long id) {
    return userService.findById(id);
}
```

#### 合理的默认值
```java
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Validate {
    String message() default "验证失败";  // 提供默认消息
    boolean required() default true;     // 合理的默认值
    int minLength() default 0;          // 最小值作为默认
    int maxLength() default Integer.MAX_VALUE; // 最大值作为默认
}
```

### 2. 注解使用建议

#### 优先使用标准注解
```java
// ✅ 使用标准注解
@Override
@Deprecated
@SuppressWarnings("unchecked")

// ✅ 使用框架提供的注解
@Autowired
@Service
@Transactional
```

#### 注解组合
```java
// 创建组合注解
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Service
@Transactional
@Validated
public @interface BusinessService {
    String value() default "";
}

// 使用组合注解
@BusinessService
public class UserService {
    // 自动具有@Service、@Transactional、@Validated的功能
}
```

#### 避免过度使用
```java
// ❌ 过度使用注解
@Component
@Service  // 重复了
@Scope("singleton")  // 默认就是singleton
@Lazy(false)  // 默认就是false
public class UserService {
}

// ✅ 简洁明了
@Service
public class UserService {
}
```

### 3. 性能考虑

#### 缓存注解信息
```java
public class AnnotationCache {
    private static final Map<Class<?>, Map<String, Annotation[]>> cache =
        new ConcurrentHashMap<>();

    public static Annotation[] getMethodAnnotations(Class<?> clazz, String methodName) {
        return cache.computeIfAbsent(clazz, k -> new ConcurrentHashMap<>())
                   .computeIfAbsent(methodName, k -> {
                       try {
                           Method method = clazz.getMethod(methodName);
                           return method.getAnnotations();
                       } catch (NoSuchMethodException e) {
                           return new Annotation[0];
                       }
                   });
    }
}
```

#### 避免频繁反射
```java
// ❌ 每次都使用反射
public void processObject(Object obj) {
    Class<?> clazz = obj.getClass();
    Field[] fields = clazz.getDeclaredFields(); // 每次都获取
    // 处理字段...
}

// ✅ 缓存反射信息
private static final Map<Class<?>, Field[]> fieldCache = new ConcurrentHashMap<>();

public void processObject(Object obj) {
    Class<?> clazz = obj.getClass();
    Field[] fields = fieldCache.computeIfAbsent(clazz, Class::getDeclaredFields);
    // 处理字段...
}
```

### 4. 文档和维护

#### 完善的文档
```java
/**
 * 用户验证注解
 *
 * <p>用于验证用户输入数据的格式和内容</p>
 *
 * <h3>使用示例：</h3>
 * <pre>
 * {@code
 * @Validate(type = ValidationType.EMAIL, message = "邮箱格式不正确")
 * private String email;
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0
 * @see ValidationType
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Validate {
    // 注解定义...
}
```

#### 版本兼容性
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ApiVersion {

    /**
     * API版本号
     * @since 1.0
     */
    String value();

    /**
     * 是否已废弃
     * @since 1.1
     */
    boolean deprecated() default false;

    /**
     * 废弃说明
     * @since 1.1
     */
    String deprecatedMessage() default "";
}
```

Java注解是现代Java开发中不可或缺的特性，特别是在Spring Boot等框架中更是核心机制。掌握注解的使用和原理，能够大大提高开发效率和代码质量。记住要合理使用注解，避免过度设计，保持代码的简洁和可维护性。
