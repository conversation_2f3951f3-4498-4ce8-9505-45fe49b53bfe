# PageHelper通用分页使用指南

## 概述

本项目使用PageHelper实现通用分页功能，提供了一套完整的分页解决方案，包括：

- **BasePageQuery**: 分页查询基类
- **PageResult**: 通用分页结果类
- **PageUtils**: 分页工具类

## 核心组件

### 1. BasePageQuery - 分页查询基类

所有需要分页的查询DTO都应该继承此类：

```java
@Data
public class BasePageQuery {
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;        // 页码，从1开始
    
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 500, message = "每页大小不能超过500")
    private Integer pageSize = 10;      // 每页大小
    
    private String orderBy;             // 排序字符串
    private Boolean count = true;       // 是否进行count查询
    private Boolean reasonable = true;  // 分页合理化
    private Boolean pageSizeZero = false; // 支持pageSize=0
}
```

### 2. PageResult - 通用分页结果类

```java
@Data
public class PageResult<T> {
    private List<T> records;           // 数据列表
    private Long total;                // 总记录数
    private Integer pageNum;           // 当前页码
    private Integer pageSize;          // 每页大小
    private Integer pages;             // 总页数
    private Boolean hasNextPage;       // 是否有下一页
    private Boolean hasPreviousPage;   // 是否有上一页
    private Boolean isFirstPage;       // 是否为第一页
    private Boolean isLastPage;        // 是否为最后一页
    private int[] navigatepageNums;    // 导航页码数组
}
```

### 3. PageUtils - 分页工具类

提供便捷的分页查询方法：

```java
// 执行分页查询
PageResult<T> doSelectPage(BasePageQuery pageQuery, Supplier<List<T>> queryFunction)

// 执行分页查询（带数据转换）
PageResult<R> doSelectPage(BasePageQuery pageQuery, Supplier<List<T>> queryFunction, Function<List<T>, List<R>> convertFunction)

// 构建排序字符串
String buildOrderBy(String sortField, String sortDirection)
```

## 使用步骤

### 1. 创建查询DTO

继承BasePageQuery并添加业务查询字段：

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleQueryDTO extends BasePageQuery {
    
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;
    
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    private String roleCode;
    
    private Integer status;
    private String keyword;
    private String sortField = "createTime";
    private String sortDirection = "desc";
    
    // 初始化排序参数
    public void initOrderBy() {
        String orderBy = PageUtils.buildOrderBy(sortField, sortDirection);
        if (StringUtils.hasText(orderBy)) {
            this.setOrderBy(orderBy);
        }
    }
}
```

### 2. 更新Mapper接口

移除手动分页相关方法，只保留条件查询：

```java
@Mapper
public interface RoleMapper {
    // 根据条件查询（PageHelper自动处理分页）
    List<Role> selectByCondition(RoleQueryDTO queryDTO);
    
    // 其他CRUD方法...
}
```

### 3. 更新XML映射文件

移除LIMIT和COUNT查询，PageHelper会自动处理：

```xml
<!-- 根据条件查询角色（PageHelper自动处理分页） -->
<select id="selectByCondition" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM role
    <include refid="Query_Condition"/>
</select>
```

### 4. 更新Service实现

使用PageUtils进行分页查询：

```java
@Override
@Transactional(readOnly = true)
public PageResult<Role> getRolesByPage(RoleQueryDTO queryDTO) {
    if (queryDTO == null) {
        throw new BusinessException(ResultCode.PARAM_ERROR, "查询参数不能为空");
    }
    
    // 验证和初始化分页参数
    PageUtils.validatePageQuery(queryDTO);
    queryDTO.initOrderBy();
    
    // 使用PageHelper进行分页查询
    return PageUtils.doSelectPage(queryDTO, () -> roleMapper.selectByCondition(queryDTO));
}
```

### 5. Controller保持不变

```java
@GetMapping
public Result<PageResult<Role>> getRoles(@Valid RoleQueryDTO queryDTO) {
    PageResult<Role> pageResult = roleService.getRolesByPage(queryDTO);
    return Result.success(pageResult);
}
```

## 配置说明

### application.yml配置

```yaml
pagehelper:
  helper-dialect: mysql          # 数据库方言
  reasonable: true              # 分页合理化
  support-methods-arguments: true # 支持通过Mapper接口参数来传递分页参数
  params: count=countSql        # 为了支持startPage(Object params)方法
  page-size-zero: false         # pageSize=0时不进行分页
  default-count: true           # 默认进行count查询
```

## 使用示例

### 基础分页查询

```bash
GET /api/roles?pageNum=1&pageSize=10
```

### 条件查询 + 分页

```bash
GET /api/roles?roleName=管理员&status=1&pageNum=1&pageSize=10
```

### 关键字搜索 + 分页

```bash
GET /api/roles?keyword=管理&pageNum=1&pageSize=10
```

### 排序 + 分页

```bash
GET /api/roles?sortField=createTime&sortDirection=desc&pageNum=1&pageSize=10
```

### 复合查询

```bash
GET /api/roles?keyword=管理&status=1&sortField=createTime&sortDirection=desc&pageNum=1&pageSize=10
```

## 响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "roleName": "系统管理员",
        "roleCode": "ADMIN",
        "status": 1,
        "createTime": "2025-07-22 10:00:00"
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "isFirstPage": true,
    "isLastPage": false,
    "navigatepageNums": [1, 2, 3, 4, 5]
  },
  "timestamp": "2025-07-22 10:00:00"
}
```

## 高级用法

### 1. 数据转换

当需要将查询结果转换为DTO时：

```java
public PageResult<RoleVO> getRoleVOsByPage(RoleQueryDTO queryDTO) {
    PageUtils.validatePageQuery(queryDTO);
    queryDTO.initOrderBy();
    
    return PageUtils.doSelectPage(
        queryDTO, 
        () -> roleMapper.selectByCondition(queryDTO),
        roles -> roles.stream()
                     .map(this::convertToVO)
                     .collect(Collectors.toList())
    );
}
```

### 2. 不分页查询

当pageSize设置为0时，返回所有数据：

```java
// 获取所有角色，不分页
RoleQueryDTO queryDTO = new RoleQueryDTO();
queryDTO.setPageSize(0);
PageResult<Role> result = roleService.getRolesByPage(queryDTO);
```

### 3. 自定义排序

```java
// 多字段排序
queryDTO.setOrderBy("sort_order ASC, create_time DESC");

// 或者使用工具方法
String orderBy = PageUtils.buildOrderBy("createTime", "desc");
queryDTO.setOrderBy(orderBy);
```

## 优势对比

### 使用PageHelper前

```java
// 需要手动处理分页
public PageResult<Role> getRolesByPage(RoleQueryDTO queryDTO) {
    int offset = (queryDTO.getPage() - 1) * queryDTO.getSize();
    List<Role> roles = roleMapper.selectByPage(offset, queryDTO.getSize(), queryDTO);
    Long total = roleMapper.countByCondition(queryDTO);
    return PageResult.of(roles, total, queryDTO.getPage(), queryDTO.getSize());
}
```

### 使用PageHelper后

```java
// 自动处理分页，代码简洁
public PageResult<Role> getRolesByPage(RoleQueryDTO queryDTO) {
    PageUtils.validatePageQuery(queryDTO);
    queryDTO.initOrderBy();
    return PageUtils.doSelectPage(queryDTO, () -> roleMapper.selectByCondition(queryDTO));
}
```

## 注意事项

1. **线程安全**: PageHelper使用ThreadLocal，确保线程安全
2. **及时清理**: PageUtils会自动清理分页参数
3. **SQL注入防护**: 排序字段会进行安全检查
4. **性能优化**: 合理设置pageSize上限，避免大数据量查询
5. **参数校验**: 使用Bean Validation进行参数校验

## 扩展其他实体

只需要3步即可为任何实体添加分页功能：

1. **创建QueryDTO继承BasePageQuery**
2. **Mapper添加selectByCondition方法**
3. **Service使用PageUtils.doSelectPage**

这样就实现了真正的通用分页功能！
