package proxy;

import java.lang.reflect.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Java动态代理演示程序
 * 展示JDK动态代理的各种用法和应用场景
 */
public class ProxyDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java动态代理演示 ===");
        
        try {
            // 1. 基本代理演示
            demonstrateBasicProxy();
            
            // 2. 日志代理演示
            demonstrateLoggingProxy();
            
            // 3. 性能监控代理演示
            demonstratePerformanceProxy();
            
            // 4. 缓存代理演示
            demonstrateCachingProxy();
            
            // 5. 事务代理演示
            demonstrateTransactionProxy();
            
            // 6. 代理链演示
            demonstrateProxyChain();
            
        } catch (Exception e) {
            System.err.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 基本代理演示
     */
    public static void demonstrateBasicProxy() {
        System.out.println("\n--- 基本代理演示 ---");
        
        // 创建目标对象
        UserService userService = new UserServiceImpl();
        
        // 创建代理对象
        UserService proxy = (UserService) Proxy.newProxyInstance(
            userService.getClass().getClassLoader(),
            userService.getClass().getInterfaces(),
            new BasicInvocationHandler(userService)
        );
        
        // 使用代理对象
        proxy.createUser("Alice");
        String user = proxy.getUserById(1L);
        System.out.println("获取到的用户: " + user);
        proxy.updateUser(1L, "Alice Updated");
        proxy.deleteUser(1L);
        
        // 检查代理类型
        System.out.println("是否为代理类: " + Proxy.isProxyClass(proxy.getClass()));
        System.out.println("代理类名: " + proxy.getClass().getName());
    }
    
    /**
     * 日志代理演示
     */
    public static void demonstrateLoggingProxy() {
        System.out.println("\n--- 日志代理演示 ---");
        
        UserService userService = new UserServiceImpl();
        UserService proxy = ProxyFactory.createLoggingProxy(userService);
        
        proxy.createUser("Bob");
        proxy.getUserById(2L);
        proxy.updateUser(2L, "Bob Updated");
    }
    
    /**
     * 性能监控代理演示
     */
    public static void demonstratePerformanceProxy() {
        System.out.println("\n--- 性能监控代理演示 ---");
        
        UserService userService = new UserServiceImpl();
        UserService proxy = ProxyFactory.createPerformanceProxy(userService);
        
        proxy.createUser("Charlie");
        proxy.getUserById(3L);
        
        // 模拟耗时操作
        try {
            Thread.sleep(100);
            proxy.updateUser(3L, "Charlie Updated");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 缓存代理演示
     */
    public static void demonstrateCachingProxy() {
        System.out.println("\n--- 缓存代理演示 ---");
        
        UserService userService = new UserServiceImpl();
        UserService proxy = ProxyFactory.createCachingProxy(userService);
        
        // 第一次调用，会执行实际方法
        System.out.println("第一次调用:");
        String user1 = proxy.getUserById(4L);
        System.out.println("结果: " + user1);
        
        // 第二次调用，会从缓存返回
        System.out.println("\n第二次调用:");
        String user2 = proxy.getUserById(4L);
        System.out.println("结果: " + user2);
        
        // 不同参数的调用
        System.out.println("\n不同参数调用:");
        String user3 = proxy.getUserById(5L);
        System.out.println("结果: " + user3);
    }
    
    /**
     * 事务代理演示
     */
    public static void demonstrateTransactionProxy() {
        System.out.println("\n--- 事务代理演示 ---");
        
        UserService userService = new UserServiceImpl();
        UserService proxy = ProxyFactory.createTransactionProxy(userService);
        
        try {
            proxy.createUser("David");
            proxy.updateUser(6L, "David Updated");
            
            // 模拟异常情况
            proxy.deleteUser(0L); // 这会抛出异常
        } catch (Exception e) {
            System.out.println("捕获到异常: " + e.getMessage());
        }
    }
    
    /**
     * 代理链演示
     */
    public static void demonstrateProxyChain() {
        System.out.println("\n--- 代理链演示 ---");
        
        UserService userService = new UserServiceImpl();
        
        // 创建多层代理
        UserService proxy = ProxyFactory.createCompositeProxy(userService,
            new LoggingHandler(),
            new PerformanceHandler(),
            new CachingHandler()
        );
        
        proxy.createUser("Eve");
        proxy.getUserById(7L);
        proxy.getUserById(7L); // 第二次调用，测试缓存
    }
}



/**
 * 基本调用处理器
 */
class BasicInvocationHandler implements InvocationHandler {
    private final Object target;
    
    public BasicInvocationHandler(Object target) {
        this.target = target;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        System.out.println("代理调用方法: " + method.getName());
        
        try {
            Object result = method.invoke(target, args);
            System.out.println("方法执行成功");
            return result;
        } catch (InvocationTargetException e) {
            System.out.println("方法执行异常: " + e.getCause().getMessage());
            throw e.getCause();
        }
    }
}

/**
 * 日志调用处理器
 */
class LoggingInvocationHandler implements InvocationHandler {
    private final Object target;
    
    public LoggingInvocationHandler(Object target) {
        this.target = target;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        System.out.println("=== 日志开始 ===");
        System.out.println("调用方法: " + method.getName());
        System.out.println("参数: " + Arrays.toString(args));
        System.out.println("时间: " + new Date());
        
        try {
            Object result = method.invoke(target, args);
            System.out.println("返回值: " + result);
            System.out.println("=== 日志结束 ===\n");
            return result;
        } catch (InvocationTargetException e) {
            System.out.println("异常: " + e.getCause().getMessage());
            System.out.println("=== 日志结束 ===\n");
            throw e.getCause();
        }
    }
}

/**
 * 性能监控调用处理器
 */
class PerformanceInvocationHandler implements InvocationHandler {
    private final Object target;
    
    public PerformanceInvocationHandler(Object target) {
        this.target = target;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        long startTime = System.nanoTime();
        
        try {
            Object result = method.invoke(target, args);
            
            long endTime = System.nanoTime();
            long duration = endTime - startTime;
            
            System.out.printf("方法 %s 执行时间: %.2f ms%n", 
                            method.getName(), duration / 1_000_000.0);
            
            return result;
        } catch (InvocationTargetException e) {
            throw e.getCause();
        }
    }
}

/**
 * 缓存调用处理器
 */
class CachingInvocationHandler implements InvocationHandler {
    private final Object target;
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    
    public CachingInvocationHandler(Object target) {
        this.target = target;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // 只缓存getter方法
        if (method.getName().startsWith("get") && args != null && args.length > 0) {
            String cacheKey = method.getName() + "_" + Arrays.toString(args);
            
            Object cachedResult = cache.get(cacheKey);
            if (cachedResult != null) {
                System.out.println("从缓存返回: " + cacheKey);
                return cachedResult;
            }
            
            try {
                Object result = method.invoke(target, args);
                cache.put(cacheKey, result);
                System.out.println("缓存结果: " + cacheKey);
                return result;
            } catch (InvocationTargetException e) {
                throw e.getCause();
            }
        } else {
            // 非getter方法直接调用
            try {
                return method.invoke(target, args);
            } catch (InvocationTargetException e) {
                throw e.getCause();
            }
        }
    }
}

/**
 * 事务调用处理器
 */
class TransactionInvocationHandler implements InvocationHandler {
    private final Object target;
    
    public TransactionInvocationHandler(Object target) {
        this.target = target;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        System.out.println("开始事务");
        
        try {
            Object result = method.invoke(target, args);
            System.out.println("提交事务");
            return result;
        } catch (InvocationTargetException e) {
            System.out.println("回滚事务");
            throw e.getCause();
        } catch (Exception e) {
            System.out.println("回滚事务");
            throw e;
        }
    }
}

/**
 * 代理处理器接口
 */
interface ProxyHandler {
    Object handle(Object target, Method method, Object[] args, InvocationChain chain) throws Throwable;
}

/**
 * 调用链接口
 */
interface InvocationChain {
    Object proceed() throws Throwable;
}

/**
 * 组合调用处理器
 */
class CompositeInvocationHandler implements InvocationHandler {
    private final Object target;
    private final List<ProxyHandler> handlers;
    
    public CompositeInvocationHandler(Object target, List<ProxyHandler> handlers) {
        this.target = target;
        this.handlers = handlers;
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        return new DefaultInvocationChain(target, method, args, handlers, 0).proceed();
    }
}

/**
 * 默认调用链实现
 */
class DefaultInvocationChain implements InvocationChain {
    private final Object target;
    private final Method method;
    private final Object[] args;
    private final List<ProxyHandler> handlers;
    private final int index;
    
    public DefaultInvocationChain(Object target, Method method, Object[] args,
                                List<ProxyHandler> handlers, int index) {
        this.target = target;
        this.method = method;
        this.args = args;
        this.handlers = handlers;
        this.index = index;
    }
    
    @Override
    public Object proceed() throws Throwable {
        if (index < handlers.size()) {
            ProxyHandler handler = handlers.get(index);
            InvocationChain nextChain = new DefaultInvocationChain(
                target, method, args, handlers, index + 1);
            return handler.handle(target, method, args, nextChain);
        } else {
            try {
                return method.invoke(target, args);
            } catch (InvocationTargetException e) {
                throw e.getCause();
            }
        }
    }
}

// 具体的处理器实现
class LoggingHandler implements ProxyHandler {
    @Override
    public Object handle(Object target, Method method, Object[] args, InvocationChain chain) throws Throwable {
        System.out.println("日志: 调用方法 " + method.getName());
        Object result = chain.proceed();
        System.out.println("日志: 方法返回 " + result);
        return result;
    }
}

class PerformanceHandler implements ProxyHandler {
    @Override
    public Object handle(Object target, Method method, Object[] args, InvocationChain chain) throws Throwable {
        long start = System.nanoTime();
        Object result = chain.proceed();
        long duration = System.nanoTime() - start;
        System.out.printf("性能: 方法 %s 执行时间 %.2f ms%n", 
                        method.getName(), duration / 1_000_000.0);
        return result;
    }
}

class CachingHandler implements ProxyHandler {
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    
    @Override
    public Object handle(Object target, Method method, Object[] args, InvocationChain chain) throws Throwable {
        if (method.getName().startsWith("get") && args != null && args.length > 0) {
            String key = method.getName() + "_" + Arrays.toString(args);
            Object cached = cache.get(key);
            if (cached != null) {
                System.out.println("缓存: 命中 " + key);
                return cached;
            }
            
            Object result = chain.proceed();
            cache.put(key, result);
            System.out.println("缓存: 存储 " + key);
            return result;
        } else {
            return chain.proceed();
        }
    }
}
