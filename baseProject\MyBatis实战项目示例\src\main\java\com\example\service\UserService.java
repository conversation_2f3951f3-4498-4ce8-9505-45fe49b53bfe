package com.example.service;

import com.example.entity.User;
import com.example.dto.UserQueryDTO;
import com.example.vo.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface UserService {
    
    // ===== 基本CRUD操作 =====
    
    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    User getUserByEmail(String email);
    
    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    User getUserByPhone(String phone);
    
    /**
     * 查询所有用户
     * 
     * @return 用户列表
     */
    List<User> getAllUsers();
    
    /**
     * 根据条件查询用户列表
     * 
     * @param query 查询条件
     * @return 用户列表
     */
    List<User> getUsersByCondition(UserQueryDTO query);
    
    /**
     * 分页查询用户列表
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<User> getUsersByPage(UserQueryDTO query);
    
    /**
     * 保存用户
     * 
     * @param user 用户信息
     * @return 保存结果
     */
    boolean saveUser(User user);
    
    /**
     * 批量保存用户
     * 
     * @param users 用户列表
     * @return 保存结果
     */
    boolean batchSaveUsers(List<User> users);
    
    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @return 更新结果
     */
    boolean updateUser(User user);
    
    /**
     * 选择性更新用户信息
     * 
     * @param user 用户信息
     * @return 更新结果
     */
    boolean updateUserSelective(User user);
    
    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 删除结果
     */
    boolean deleteUser(Long id);
    
    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     * @return 删除结果
     */
    boolean batchDeleteUsers(List<Long> ids);
    
    /**
     * 逻辑删除用户
     * 
     * @param id 用户ID
     * @param operatorId 操作人ID
     * @return 删除结果
     */
    boolean logicDeleteUser(Long id, Long operatorId);
    
    // ===== 关联查询 =====
    
    /**
     * 查询用户及其角色信息
     * 
     * @param id 用户ID
     * @return 用户信息（包含角色）
     */
    User getUserWithRoles(Long id);
    
    /**
     * 查询用户及其权限信息
     * 
     * @param id 用户ID
     * @return 用户信息（包含权限）
     */
    User getUserWithPermissions(Long id);
    
    /**
     * 查询用户完整信息（包含角色和权限）
     * 
     * @param id 用户ID
     * @return 用户完整信息
     */
    User getUserWithRolesAndPermissions(Long id);
    
    /**
     * 查询所有用户及其角色信息
     * 
     * @return 用户列表（包含角色）
     */
    List<User> getUsersWithRoles();
    
    /**
     * 根据角色ID查询用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> getUsersByRoleId(Long roleId);
    
    /**
     * 根据角色编码查询用户列表
     * 
     * @param roleCode 角色编码
     * @return 用户列表
     */
    List<User> getUsersByRoleCode(String roleCode);
    
    /**
     * 根据权限编码查询用户列表
     * 
     * @param permissionCode 权限编码
     * @return 用户列表
     */
    List<User> getUsersByPermissionCode(String permissionCode);
    
    // ===== 统计查询 =====
    
    /**
     * 统计用户总数
     * 
     * @return 用户总数
     */
    int getUserCount();
    
    /**
     * 根据条件统计用户数量
     * 
     * @param query 查询条件
     * @return 用户数量
     */
    int getUserCountByCondition(UserQueryDTO query);
    
    /**
     * 根据状态统计用户数量
     * 
     * @param status 状态
     * @return 用户数量
     */
    int getUserCountByStatus(Integer status);
    
    /**
     * 统计今日新增用户数量
     * 
     * @return 今日新增用户数量
     */
    int getTodayNewUserCount();
    
    /**
     * 获取用户统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getUserStatistics();
    
    /**
     * 获取用户年龄分布统计
     * 
     * @return 年龄分布统计
     */
    List<Map<String, Object>> getAgeDistribution();
    
    /**
     * 获取用户性别分布统计
     * 
     * @return 性别分布统计
     */
    List<Map<String, Object>> getGenderDistribution();
    
    /**
     * 获取用户注册趋势统计
     * 
     * @param days 统计天数
     * @return 注册趋势统计
     */
    List<Map<String, Object>> getRegistrationTrend(int days);
    
    // ===== 业务操作 =====
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);
    
    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeId);
    
    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeId);
    
    /**
     * 更新用户状态
     * 
     * @param id 用户ID
     * @param status 状态
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateUserStatus(Long id, Integer status, Long operatorId);
    
    /**
     * 更新用户密码
     * 
     * @param id 用户ID
     * @param newPassword 新密码
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateUserPassword(Long id, String newPassword, Long operatorId);
    
    /**
     * 重置用户密码
     * 
     * @param id 用户ID
     * @param operatorId 操作人ID
     * @return 新密码
     */
    String resetUserPassword(Long id, Long operatorId);
    
    /**
     * 验证用户邮箱
     * 
     * @param id 用户ID
     * @param operatorId 操作人ID
     * @return 验证结果
     */
    boolean verifyUserEmail(Long id, Long operatorId);
    
    /**
     * 验证用户手机
     * 
     * @param id 用户ID
     * @param operatorId 操作人ID
     * @return 验证结果
     */
    boolean verifyUserPhone(Long id, Long operatorId);
    
    /**
     * 用户登录
     * 
     * @param username 用户名
     * @param password 密码
     * @param loginIp 登录IP
     * @return 用户信息
     */
    User login(String username, String password, String loginIp);
    
    /**
     * 用户登出
     * 
     * @param userId 用户ID
     * @return 登出结果
     */
    boolean logout(Long userId);
}
