server:
  port: 8081

spring:
  application:
    name: redis-demo
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: # 如果有密码请填写
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: 2000ms

# 日志配置
logging:
  level:
    com.example.redis: DEBUG
    org.springframework.data.redis: DEBUG
