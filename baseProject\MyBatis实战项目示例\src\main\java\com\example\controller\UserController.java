package com.example.controller;

import com.example.entity.User;
import com.example.dto.UserQueryDTO;
import com.example.vo.PageResult;
import com.example.service.UserService;
import com.example.util.ResponseUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/users")
@Validated
@CrossOrigin(origins = "*")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserService userService;
    
    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable @NotNull Long id) {
        try {
            User user = userService.getUserById(id);
            if (user != null) {
                return ResponseUtil.success("查询成功", user);
            } else {
                return ResponseUtil.error("用户不存在");
            }
        } catch (Exception e) {
            logger.error("查询用户失败: ID={}", id, e);
            return ResponseUtil.error("查询用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户及其角色信息
     * 
     * @param id 用户ID
     * @return 用户信息（包含角色）
     */
    @GetMapping("/{id}/roles")
    public ResponseEntity<Map<String, Object>> getUserWithRoles(@PathVariable @NotNull Long id) {
        try {
            User user = userService.getUserWithRoles(id);
            if (user != null) {
                return ResponseUtil.success("查询成功", user);
            } else {
                return ResponseUtil.error("用户不存在");
            }
        } catch (Exception e) {
            logger.error("查询用户角色失败: ID={}", id, e);
            return ResponseUtil.error("查询用户角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户及其权限信息
     * 
     * @param id 用户ID
     * @return 用户信息（包含权限）
     */
    @GetMapping("/{id}/permissions")
    public ResponseEntity<Map<String, Object>> getUserWithPermissions(@PathVariable @NotNull Long id) {
        try {
            User user = userService.getUserWithPermissions(id);
            if (user != null) {
                return ResponseUtil.success("查询成功", user);
            } else {
                return ResponseUtil.error("用户不存在");
            }
        } catch (Exception e) {
            logger.error("查询用户权限失败: ID={}", id, e);
            return ResponseUtil.error("查询用户权限失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户完整信息
     * 
     * @param id 用户ID
     * @return 用户完整信息
     */
    @GetMapping("/{id}/full")
    public ResponseEntity<Map<String, Object>> getUserWithRolesAndPermissions(@PathVariable @NotNull Long id) {
        try {
            User user = userService.getUserWithRolesAndPermissions(id);
            if (user != null) {
                return ResponseUtil.success("查询成功", user);
            } else {
                return ResponseUtil.error("用户不存在");
            }
        } catch (Exception e) {
            logger.error("查询用户完整信息失败: ID={}", id, e);
            return ResponseUtil.error("查询用户完整信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询用户列表
     * 
     * @param query 查询条件
     * @return 用户列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getUsersByPage(@Valid UserQueryDTO query) {
        try {
            PageResult<User> result = userService.getUsersByPage(query);
            return ResponseUtil.success("查询成功", result);
        } catch (Exception e) {
            logger.error("分页查询用户失败", e);
            return ResponseUtil.error("查询用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据条件查询用户列表
     * 
     * @param query 查询条件
     * @return 用户列表
     */
    @PostMapping("/search")
    public ResponseEntity<Map<String, Object>> getUsersByCondition(@RequestBody @Valid UserQueryDTO query) {
        try {
            List<User> users = userService.getUsersByCondition(query);
            return ResponseUtil.success("查询成功", users);
        } catch (Exception e) {
            logger.error("条件查询用户失败", e);
            return ResponseUtil.error("查询用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @param request HTTP请求
     * @return 创建结果
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createUser(@RequestBody @Valid User user, HttpServletRequest request) {
        try {
            // 设置创建人（这里简化处理，实际应该从认证信息中获取）
            user.setCreateUser(1L);
            user.setUpdateUser(1L);
            
            boolean result = userService.saveUser(user);
            if (result) {
                logger.info("用户创建成功: {}, IP: {}", user.getUsername(), getClientIp(request));
                return ResponseUtil.success("用户创建成功", user);
            } else {
                return ResponseUtil.error("用户创建失败");
            }
        } catch (Exception e) {
            logger.error("用户创建失败", e);
            return ResponseUtil.error("用户创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量创建用户
     * 
     * @param users 用户列表
     * @param request HTTP请求
     * @return 创建结果
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchCreateUsers(@RequestBody @Valid List<User> users, HttpServletRequest request) {
        try {
            // 设置创建人
            users.forEach(user -> {
                user.setCreateUser(1L);
                user.setUpdateUser(1L);
            });
            
            boolean result = userService.batchSaveUsers(users);
            if (result) {
                logger.info("批量创建用户成功，数量: {}, IP: {}", users.size(), getClientIp(request));
                return ResponseUtil.success("批量创建用户成功", users.size());
            } else {
                return ResponseUtil.error("批量创建用户失败");
            }
        } catch (Exception e) {
            logger.error("批量创建用户失败", e);
            return ResponseUtil.error("批量创建用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户信息
     * 
     * @param id 用户ID
     * @param user 用户信息
     * @param request HTTP请求
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateUser(@PathVariable @NotNull Long id, 
                                                          @RequestBody @Valid User user, 
                                                          HttpServletRequest request) {
        try {
            user.setId(id);
            user.setUpdateUser(1L); // 实际应该从认证信息中获取
            
            boolean result = userService.updateUser(user);
            if (result) {
                logger.info("用户更新成功: ID={}, IP: {}", id, getClientIp(request));
                return ResponseUtil.success("用户更新成功");
            } else {
                return ResponseUtil.error("用户更新失败");
            }
        } catch (Exception e) {
            logger.error("用户更新失败: ID={}", id, e);
            return ResponseUtil.error("用户更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 选择性更新用户信息
     * 
     * @param id 用户ID
     * @param user 用户信息
     * @param request HTTP请求
     * @return 更新结果
     */
    @PatchMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateUserSelective(@PathVariable @NotNull Long id, 
                                                                   @RequestBody User user, 
                                                                   HttpServletRequest request) {
        try {
            user.setId(id);
            user.setUpdateUser(1L); // 实际应该从认证信息中获取
            
            boolean result = userService.updateUserSelective(user);
            if (result) {
                logger.info("用户选择性更新成功: ID={}, IP: {}", id, getClientIp(request));
                return ResponseUtil.success("用户更新成功");
            } else {
                return ResponseUtil.error("用户更新失败");
            }
        } catch (Exception e) {
            logger.error("用户选择性更新失败: ID={}", id, e);
            return ResponseUtil.error("用户更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @param request HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteUser(@PathVariable @NotNull Long id, HttpServletRequest request) {
        try {
            boolean result = userService.logicDeleteUser(id, 1L); // 使用逻辑删除
            if (result) {
                logger.info("用户删除成功: ID={}, IP: {}", id, getClientIp(request));
                return ResponseUtil.success("用户删除成功");
            } else {
                return ResponseUtil.error("用户删除失败");
            }
        } catch (Exception e) {
            logger.error("用户删除失败: ID={}", id, e);
            return ResponseUtil.error("用户删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     * @param request HTTP请求
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchDeleteUsers(@RequestBody List<Long> ids, HttpServletRequest request) {
        try {
            boolean result = userService.batchDeleteUsers(ids);
            if (result) {
                logger.info("批量删除用户成功，数量: {}, IP: {}", ids.size(), getClientIp(request));
                return ResponseUtil.success("批量删除用户成功", ids.size());
            } else {
                return ResponseUtil.error("批量删除用户失败");
            }
        } catch (Exception e) {
            logger.error("批量删除用户失败", e);
            return ResponseUtil.error("批量删除用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户状态
     * 
     * @param id 用户ID
     * @param status 状态
     * @param request HTTP请求
     * @return 更新结果
     */
    @PutMapping("/{id}/status/{status}")
    public ResponseEntity<Map<String, Object>> updateUserStatus(@PathVariable @NotNull Long id, 
                                                                @PathVariable @NotNull Integer status, 
                                                                HttpServletRequest request) {
        try {
            boolean result = userService.updateUserStatus(id, status, 1L);
            if (result) {
                logger.info("用户状态更新成功: ID={}, 状态={}, IP: {}", id, status, getClientIp(request));
                return ResponseUtil.success("用户状态更新成功");
            } else {
                return ResponseUtil.error("用户状态更新失败");
            }
        } catch (Exception e) {
            logger.error("用户状态更新失败: ID={}", id, e);
            return ResponseUtil.error("用户状态更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置用户密码
     * 
     * @param id 用户ID
     * @param request HTTP请求
     * @return 重置结果
     */
    @PutMapping("/{id}/password/reset")
    public ResponseEntity<Map<String, Object>> resetUserPassword(@PathVariable @NotNull Long id, HttpServletRequest request) {
        try {
            String newPassword = userService.resetUserPassword(id, 1L);
            logger.info("用户密码重置成功: ID={}, IP: {}", id, getClientIp(request));
            return ResponseUtil.success("密码重置成功", Map.of("newPassword", newPassword));
        } catch (Exception e) {
            logger.error("用户密码重置失败: ID={}", id, e);
            return ResponseUtil.error("密码重置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getUserStatistics() {
        try {
            Map<String, Object> statistics = userService.getUserStatistics();
            return ResponseUtil.success("查询成功", statistics);
        } catch (Exception e) {
            logger.error("获取用户统计信息失败", e);
            return ResponseUtil.error("获取统计信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户年龄分布
     * 
     * @return 年龄分布
     */
    @GetMapping("/statistics/age")
    public ResponseEntity<Map<String, Object>> getAgeDistribution() {
        try {
            List<Map<String, Object>> distribution = userService.getAgeDistribution();
            return ResponseUtil.success("查询成功", distribution);
        } catch (Exception e) {
            logger.error("获取用户年龄分布失败", e);
            return ResponseUtil.error("获取年龄分布失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户性别分布
     * 
     * @return 性别分布
     */
    @GetMapping("/statistics/gender")
    public ResponseEntity<Map<String, Object>> getGenderDistribution() {
        try {
            List<Map<String, Object>> distribution = userService.getGenderDistribution();
            return ResponseUtil.success("查询成功", distribution);
        } catch (Exception e) {
            logger.error("获取用户性别分布失败", e);
            return ResponseUtil.error("获取性别分布失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户注册趋势
     * 
     * @param days 统计天数
     * @return 注册趋势
     */
    @GetMapping("/statistics/trend")
    public ResponseEntity<Map<String, Object>> getRegistrationTrend(@RequestParam(defaultValue = "30") Integer days) {
        try {
            List<Map<String, Object>> trend = userService.getRegistrationTrend(days);
            return ResponseUtil.success("查询成功", trend);
        } catch (Exception e) {
            logger.error("获取用户注册趋势失败", e);
            return ResponseUtil.error("获取注册趋势失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 检查结果
     */
    @GetMapping("/check/username")
    public ResponseEntity<Map<String, Object>> checkUsername(@RequestParam String username, 
                                                             @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = userService.isUsernameExists(username, excludeId);
            return ResponseUtil.success("检查完成", Map.of("exists", exists));
        } catch (Exception e) {
            logger.error("检查用户名失败", e);
            return ResponseUtil.error("检查用户名失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 检查结果
     */
    @GetMapping("/check/email")
    public ResponseEntity<Map<String, Object>> checkEmail(@RequestParam String email, 
                                                          @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = userService.isEmailExists(email, excludeId);
            return ResponseUtil.success("检查完成", Map.of("exists", exists));
        } catch (Exception e) {
            logger.error("检查邮箱失败", e);
            return ResponseUtil.error("检查邮箱失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 检查结果
     */
    @GetMapping("/check/phone")
    public ResponseEntity<Map<String, Object>> checkPhone(@RequestParam String phone, 
                                                          @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = userService.isPhoneExists(phone, excludeId);
            return ResponseUtil.success("检查完成", Map.of("exists", exists));
        } catch (Exception e) {
            logger.error("检查手机号失败", e);
            return ResponseUtil.error("检查手机号失败: " + e.getMessage());
        }
    }
    
    // ===== 私有辅助方法 =====
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
