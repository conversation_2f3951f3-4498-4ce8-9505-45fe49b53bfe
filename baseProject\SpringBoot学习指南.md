# Spring Boot学习指南

## 项目概述

这是一个完整的Spring Boot学习项目，包含了Spring Boot开发的核心概念、最佳实践和实际应用示例。通过这个项目，您可以系统地学习Spring Boot框架的各个方面。

## 项目结构

```
springboot-demo/
├── pom.xml                                    # Maven配置文件
├── src/
│   ├── main/
│   │   ├── java/com/example/demo/
│   │   │   ├── Application.java               # 主启动类
│   │   │   ├── config/                        # 配置类
│   │   │   │   ├── SecurityConfig.java       # 安全配置
│   │   │   │   ├── WebConfig.java            # Web配置
│   │   │   │   ├── CacheConfig.java          # 缓存配置
│   │   │   │   └── SwaggerConfig.java        # API文档配置
│   │   │   ├── controller/                    # 控制器层
│   │   │   │   ├── UserController.java       # 用户控制器
│   │   │   │   ├── AuthController.java       # 认证控制器
│   │   │   │   └── HomeController.java       # 首页控制器
│   │   │   ├── service/                       # 服务层
│   │   │   │   ├── UserService.java          # 用户服务
│   │   │   │   ├── AuthService.java          # 认证服务
│   │   │   │   └── impl/                      # 服务实现
│   │   │   ├── repository/                    # 数据访问层
│   │   │   │   ├── UserRepository.java       # 用户仓库
│   │   │   │   └── RoleRepository.java       # 角色仓库
│   │   │   ├── entity/                        # 实体类
│   │   │   │   ├── User.java                 # 用户实体
│   │   │   │   ├── Role.java                 # 角色实体
│   │   │   │   └── Order.java                # 订单实体
│   │   │   ├── dto/                          # 数据传输对象
│   │   │   │   ├── UserDTO.java              # 用户DTO
│   │   │   │   └── LoginRequest.java         # 登录请求DTO
│   │   │   ├── exception/                     # 异常处理
│   │   │   │   ├── GlobalExceptionHandler.java
│   │   │   │   └── BusinessException.java
│   │   │   ├── security/                      # 安全相关
│   │   │   │   ├── JwtTokenProvider.java     # JWT工具类
│   │   │   │   └── UserDetailsServiceImpl.java
│   │   │   └── util/                         # 工具类
│   │   │       ├── ApiResponse.java          # API响应封装
│   │   │       └── DateUtil.java             # 日期工具
│   │   └── resources/
│   │       ├── application.yml                # 主配置文件
│   │       ├── application-dev.yml            # 开发环境配置
│   │       ├── application-prod.yml           # 生产环境配置
│   │       ├── application-test.yml           # 测试环境配置
│   │       ├── static/                        # 静态资源
│   │       ├── templates/                     # 模板文件
│   │       └── db/migration/                  # 数据库迁移脚本
│   └── test/
│       └── java/com/example/demo/
│           ├── ApplicationTests.java          # 应用测试
│           ├── controller/                    # 控制器测试
│           ├── service/                       # 服务测试
│           └── repository/                    # 仓库测试
├── docker/
│   ├── Dockerfile                            # Docker镜像构建文件
│   ├── docker-compose.yml                   # Docker编排文件
│   └── docker-compose.dev.yml               # 开发环境Docker编排
└── docs/                                     # 文档目录
    ├── API.md                               # API文档
    ├── DEPLOYMENT.md                        # 部署文档
    └── TROUBLESHOOTING.md                   # 故障排除
```

## 技术栈

### 核心框架
- **Spring Boot 2.7.14** - 主框架
- **Spring Web** - Web开发
- **Spring Data JPA** - 数据访问
- **Spring Security** - 安全控制
- **Spring Cache** - 缓存支持

### 数据库
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存数据库
- **H2** - 测试数据库

### 工具库
- **JWT** - 身份认证
- **Validation** - 数据验证
- **Jackson** - JSON处理
- **Swagger** - API文档
- **Actuator** - 监控管理

### 测试框架
- **JUnit 5** - 单元测试
- **Mockito** - 模拟测试
- **TestContainers** - 集成测试
- **Spring Boot Test** - Spring测试

## 快速开始

### 环境要求
- JDK 8 或更高版本
- Maven 3.6+ 或 Gradle 6.8+
- MySQL 8.0
- Redis (可选)

### 1. 克隆项目
```bash
git clone https://github.com/your-username/springboot-demo.git
cd springboot-demo
```

### 2. 配置数据库
```sql
-- 创建数据库
CREATE DATABASE springboot_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'demo_user'@'localhost' IDENTIFIED BY 'demo_password';
GRANT ALL PRIVILEGES ON springboot_demo.* TO 'demo_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 修改配置
编辑 `src/main/resources/application.yml` 文件，修改数据库连接信息：
```yaml
spring:
  datasource:
    url: *******************************************
    username: your_username
    password: your_password
```

### 4. 运行应用
```bash
# 使用Maven
mvn spring-boot:run

# 或者先编译再运行
mvn clean package
java -jar target/springboot-demo-0.0.1-SNAPSHOT.jar

# 使用Gradle
./gradlew bootRun
```

### 5. 访问应用
- **应用首页**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health
- **应用信息**: http://localhost:8080/actuator/info

## 核心功能

### 1. 用户管理
- 用户注册、登录、注销
- 用户信息CRUD操作
- 用户角色权限管理
- 用户状态管理

### 2. 安全控制
- JWT身份认证
- 基于角色的访问控制
- 密码加密存储
- 安全配置

### 3. 数据访问
- JPA实体映射
- 自定义查询方法
- 分页和排序
- 事务管理

### 4. 缓存支持
- Redis缓存集成
- 缓存注解使用
- 缓存策略配置

### 5. 异常处理
- 全局异常处理器
- 自定义业务异常
- 统一错误响应格式

### 6. 数据验证
- 请求参数验证
- 自定义验证注解
- 验证错误处理

### 7. 监控管理
- Actuator端点
- 健康检查
- 应用指标
- 环境信息

## API接口

### 认证接口
```
POST /api/auth/login     # 用户登录
POST /api/auth/register  # 用户注册
POST /api/auth/logout    # 用户注销
POST /api/auth/refresh   # 刷新令牌
```

### 用户接口
```
GET    /api/users           # 获取用户列表（分页）
GET    /api/users/{id}      # 获取用户详情
POST   /api/users           # 创建用户
PUT    /api/users/{id}      # 更新用户
DELETE /api/users/{id}      # 删除用户
GET    /api/users/search    # 搜索用户
GET    /api/users/stats     # 用户统计
DELETE /api/users/batch     # 批量删除
```

### 管理接口
```
GET /actuator/health        # 健康检查
GET /actuator/info          # 应用信息
GET /actuator/metrics       # 应用指标
GET /actuator/env           # 环境变量
```

## 学习路径

### 第一阶段：基础入门
1. **Spring Boot简介** - 了解框架特点和优势
2. **项目创建** - 使用Spring Initializr创建项目
3. **基本配置** - 学习application.yml配置
4. **Hello World** - 创建第一个REST接口

### 第二阶段：Web开发
1. **控制器开发** - @RestController、@RequestMapping
2. **参数绑定** - @PathVariable、@RequestParam、@RequestBody
3. **数据验证** - @Valid、自定义验证注解
4. **异常处理** - @ControllerAdvice、全局异常处理

### 第三阶段：数据访问
1. **JPA基础** - 实体映射、基本CRUD
2. **Repository接口** - JpaRepository、自定义查询
3. **关系映射** - @OneToMany、@ManyToMany
4. **事务管理** - @Transactional

### 第四阶段：安全控制
1. **Spring Security** - 安全配置、认证授权
2. **JWT集成** - 令牌生成、验证、刷新
3. **权限控制** - 基于角色的访问控制
4. **密码安全** - 密码加密、安全策略

### 第五阶段：高级特性
1. **缓存支持** - Redis集成、缓存注解
2. **异步处理** - @Async、线程池配置
3. **定时任务** - @Scheduled、任务调度
4. **监控管理** - Actuator、健康检查

### 第六阶段：测试部署
1. **单元测试** - JUnit 5、Mockito
2. **集成测试** - @SpringBootTest、TestContainers
3. **Docker部署** - 容器化、编排
4. **生产部署** - 配置管理、监控告警

## 最佳实践

### 1. 项目结构
- 按功能模块组织代码
- 遵循分层架构原则
- 使用统一的命名规范

### 2. 配置管理
- 使用YAML格式配置文件
- 区分不同环境配置
- 敏感信息使用环境变量

### 3. 代码质量
- 编写单元测试和集成测试
- 使用代码检查工具
- 遵循编码规范

### 4. 安全考虑
- 实施安全认证和授权
- 对敏感数据进行加密
- 定期更新依赖版本

### 5. 性能优化
- 合理使用缓存
- 优化数据库查询
- 配置连接池参数

## 常见问题

### 1. 启动失败
- 检查端口是否被占用
- 验证数据库连接配置
- 查看启动日志错误信息

### 2. 数据库连接问题
- 确认数据库服务已启动
- 检查连接URL、用户名、密码
- 验证数据库驱动版本

### 3. 权限问题
- 检查JWT令牌是否有效
- 验证用户角色权限
- 确认安全配置正确

## 扩展学习

### 推荐资源
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [Spring Boot参考指南](https://docs.spring.io/spring-boot/docs/current/reference/html/)
- [Spring Boot示例项目](https://github.com/spring-projects/spring-boot/tree/main/spring-boot-samples)

### 进阶主题
- Spring Cloud微服务
- Spring Boot Admin监控
- Spring Boot + Kubernetes
- Spring Boot性能调优

## 贡献指南

欢迎提交Issue和Pull Request来改进这个学习项目！

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
