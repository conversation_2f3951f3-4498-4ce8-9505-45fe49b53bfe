import annotations.*;

/**
 * Java注解测试运行器
 * 用于演示和测试各种注解功能
 */
public class AnnotationTestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    Java注解演示程序");
        System.out.println("========================================");
        
        try {
            // 1. 基础注解演示
            runDemo("基础注解演示", () -> AnnotationDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 2. 自定义注解演示
            runDemo("自定义注解演示", () -> customAnnotationDemo());
            
            waitBetweenDemos();
            
            // 3. 实体注解演示
            runDemo("实体注解演示", () -> entityAnnotationDemo());
            
            waitBetweenDemos();
            
            // 4. 方法注解演示
            runDemo("方法注解演示", () -> methodAnnotationDemo());
            
        } catch (Exception e) {
            System.out.println("演示程序出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           演示程序结束");
        System.out.println("=".repeat(50));
        
        showSummary();
    }
    
    /**
     * 运行演示
     */
    private static void runDemo(String demoName, Runnable demo) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           " + demoName);
        System.out.println("=".repeat(50));
        
        try {
            demo.run();
        } catch (Exception e) {
            System.out.println(demoName + "出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示之间的等待
     */
    private static void waitBetweenDemos() {
        try {
            System.out.println("\n等待2秒后继续下一个演示...");
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 自定义注解演示
     */
    private static void customAnnotationDemo() {
        System.out.println("=== 自定义注解演示 ===");
        
        // 创建用户对象进行验证
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setPhone("13800138000");
        user.setPassword("password123");
        user.setRealName("测试用户");
        
        System.out.println("创建用户: " + user.getUsername());
        System.out.println("邮箱: " + user.getEmail());
        System.out.println("手机: " + user.getPhone());
        
        // 模拟方法调用（实际需要AOP支持）
        System.out.println("\n模拟方法调用:");
        user.getUserInfo(1L);
        user.updateUserInfo("<EMAIL>", "13900139000");
        user.sendWelcomeEmail();
    }
    
    /**
     * 实体注解演示
     */
    private static void entityAnnotationDemo() {
        System.out.println("=== 实体注解演示 ===");
        
        // 创建产品对象
        Product product = new Product();
        product.setId(1L);
        product.setName("iPhone 15 Pro");
        product.setPrice(8999.0);
        product.setCategory("电子产品");
        product.setDescription("最新款iPhone，配备A17 Pro芯片");
        product.setStock(100);
        
        System.out.println("创建产品: " + product.getName());
        System.out.println("价格: ¥" + product.getPrice());
        System.out.println("类别: " + product.getCategory());
        System.out.println("库存: " + product.getStock());
        
        // 模拟业务操作
        System.out.println("\n模拟业务操作:");
        product.getProductDetails(1L);
        product.updateStock(95);
        product.syncToExternalSystem();
    }
    
    /**
     * 方法注解演示
     */
    private static void methodAnnotationDemo() {
        System.out.println("=== 方法注解演示 ===");
        
        // 演示不同类型的方法注解
        MethodAnnotationExample example = new MethodAnnotationExample();
        
        System.out.println("1. 日志注解演示:");
        example.loggedMethod("参数1", "参数2");
        
        System.out.println("\n2. 缓存注解演示:");
        example.cachedMethod("key1");
        example.cachedMethod("key1"); // 第二次调用应该从缓存获取
        
        System.out.println("\n3. 权限注解演示:");
        try {
            example.secureMethod();
        } catch (Exception e) {
            System.out.println("权限检查: " + e.getMessage());
        }
        
        System.out.println("\n4. 限流注解演示:");
        for (int i = 0; i < 3; i++) {
            example.rateLimitedMethod();
        }
        
        System.out.println("\n5. 重试注解演示:");
        example.retryableMethod();
        
        System.out.println("\n6. 异步注解演示:");
        example.asyncMethod();
        
        System.out.println("\n7. API版本注解演示:");
        example.oldApiMethod();
        example.newApiMethod();
    }
    
    /**
     * 显示总结信息
     */
    private static void showSummary() {
        System.out.println("\n演示内容总结:");
        
        System.out.println("\n1. 内置注解:");
        System.out.println("   - @Override: 重写方法标记");
        System.out.println("   - @Deprecated: 过时方法标记");
        System.out.println("   - @SuppressWarnings: 抑制警告");
        System.out.println("   - @FunctionalInterface: 函数式接口标记");
        System.out.println("   - @SafeVarargs: 安全可变参数");
        
        System.out.println("\n2. 元注解:");
        System.out.println("   - @Target: 指定注解目标");
        System.out.println("   - @Retention: 指定保留策略");
        System.out.println("   - @Documented: 包含在JavaDoc中");
        System.out.println("   - @Inherited: 可被子类继承");
        
        System.out.println("\n3. 自定义注解:");
        System.out.println("   - @Validate: 数据验证注解");
        System.out.println("   - @Entity: 实体类标记");
        System.out.println("   - @Column: 数据库列映射");
        System.out.println("   - @Log: 日志记录注解");
        System.out.println("   - @Cache: 缓存注解");
        System.out.println("   - @RequirePermission: 权限控制注解");
        System.out.println("   - @RateLimit: 限流注解");
        System.out.println("   - @Retry: 重试注解");
        System.out.println("   - @Async: 异步执行注解");
        System.out.println("   - @ApiVersion: API版本控制注解");
        System.out.println("   - @Scheduled: 定时任务注解");
        
        System.out.println("\n4. 注解处理:");
        System.out.println("   - 反射读取注解信息");
        System.out.println("   - 编译时注解处理器");
        System.out.println("   - 运行时注解处理器");
        System.out.println("   - AOP切面处理注解");
        
        System.out.println("\n5. 应用场景:");
        System.out.println("   - 数据验证和格式检查");
        System.out.println("   - ORM映射和数据库操作");
        System.out.println("   - 日志记录和监控");
        System.out.println("   - 缓存管理");
        System.out.println("   - 权限控制和安全");
        System.out.println("   - API版本管理");
        System.out.println("   - 性能优化（限流、重试）");
        System.out.println("   - 异步处理和定时任务");
        
        System.out.println("\n6. 最佳实践:");
        System.out.println("   - 单一职责原则");
        System.out.println("   - 合理的默认值");
        System.out.println("   - 优先使用标准注解");
        System.out.println("   - 注解组合使用");
        System.out.println("   - 避免过度使用");
        System.out.println("   - 性能考虑（缓存反射信息）");
        System.out.println("   - 完善的文档说明");
        System.out.println("   - 版本兼容性考虑");
        
        System.out.println("\n注意事项:");
        System.out.println("- 注解本身不会改变程序执行逻辑");
        System.out.println("- 需要配合注解处理器或框架使用");
        System.out.println("- 反射操作会影响性能，需要合理使用");
        System.out.println("- 注解信息的保留策略很重要");
        System.out.println("- 在Spring Boot等框架中注解是核心机制");
    }
}

/**
 * 方法注解演示类
 */
class MethodAnnotationExample {
    
    // 模拟日志注解处理
    public String loggedMethod(String param1, String param2) {
        System.out.println("执行带日志的方法，参数: " + param1 + ", " + param2);
        return "result";
    }
    
    // 模拟缓存注解处理
    public String cachedMethod(String key) {
        System.out.println("执行带缓存的方法，键: " + key);
        return "cached_result_" + key;
    }
    
    // 模拟权限注解处理
    public void secureMethod() {
        System.out.println("执行需要权限的方法");
        // 模拟权限检查失败
        throw new RuntimeException("权限不足");
    }
    
    // 模拟限流注解处理
    public void rateLimitedMethod() {
        System.out.println("执行限流方法");
    }
    
    // 模拟重试注解处理
    public void retryableMethod() {
        System.out.println("执行可重试的方法");
        // 模拟偶尔失败
        if (Math.random() < 0.3) {
            System.out.println("方法执行失败，将重试");
        } else {
            System.out.println("方法执行成功");
        }
    }
    
    // 模拟异步注解处理
    public void asyncMethod() {
        System.out.println("执行异步方法");
        // 模拟异步执行
        new Thread(() -> {
            try {
                Thread.sleep(1000);
                System.out.println("异步任务完成");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    // 模拟API版本注解处理
    public String oldApiMethod() {
        System.out.println("调用旧版API（已废弃）");
        return "old_result";
    }
    
    public String newApiMethod() {
        System.out.println("调用新版API");
        return "new_result";
    }
}
