/**
 * 这个程序展示Java中的接口和抽象类
 */
public class InterfaceAndAbstract {
    public static void main(String[] args) {
        // 使用实现了接口的类
        Circle circle = new Circle(5.0);
        Rectangle rectangle = new Rectangle(4.0, 6.0);
        
        // 计算并显示面积
        System.out.println("圆的面积: " + circle.calculateArea());
        System.out.println("矩形的面积: " + rectangle.calculateArea());
        
        // 调用接口默认方法
        circle.printShape();
        rectangle.printShape();
        
        // 使用抽象类
        Dog dog = new Dog("旺财");
        Cat cat = new Cat("咪咪");
        
        // 调用抽象方法和具体方法
        dog.makeSound();
        dog.sleep();
        
        cat.makeSound();
        cat.sleep();
    }
}

// 定义一个接口
interface Shape {
    // 抽象方法（接口中的方法默认是public abstract）
    double calculateArea();
    
    // Java 8+ 支持接口中的默认方法
    default void printShape() {
        System.out.println("这是一个" + getShapeName());
    }
    
    // 接口中的抽象方法
    String getShapeName();
}

// 实现接口的类
class Circle implements Shape {
    private double radius;
    
    public Circle(double radius) {
        this.radius = radius;
    }
    
    @Override
    public double calculateArea() {
        return Math.PI * radius * radius;
    }
    
    @Override
    public String getShapeName() {
        return "圆形";
    }
}

// 另一个实现接口的类
class Rectangle implements Shape {
    private double width;
    private double height;
    
    public Rectangle(double width, double height) {
        this.width = width;
        this.height = height;
    }
    
    @Override
    public double calculateArea() {
        return width * height;
    }
    
    @Override
    public String getShapeName() {
        return "矩形";
    }
}

// 定义一个抽象类
abstract class Animal {
    protected String name;
    
    public Animal(String name) {
        this.name = name;
    }
    
    // 抽象方法（必须由子类实现）
    public abstract void makeSound();
    
    // 具体方法（可以被子类继承）
    public void sleep() {
        System.out.println(name + "正在睡觉...");
    }
}

// 继承抽象类的具体类
class Dog extends Animal {
    public Dog(String name) {
        super(name);
    }
    
    @Override
    public void makeSound() {
        System.out.println(name + "汪汪叫!");
    }
}

// 另一个继承抽象类的具体类
class Cat extends Animal {
    public Cat(String name) {
        super(name);
    }
    
    @Override
    public void makeSound() {
        System.out.println(name + "喵喵叫!");
    }
} 