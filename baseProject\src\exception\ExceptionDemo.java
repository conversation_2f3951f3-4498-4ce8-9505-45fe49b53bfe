package exception;

import java.io.*;

/**
 * 异常处理示例类
 * 演示Java中各种异常处理机制
 */
public class ExceptionDemo {
    
    public static void main(String[] args) {
        ExceptionDemo demo = new ExceptionDemo();
        
        System.out.println("=== 基本异常处理 ===");
        demo.basicExceptionHandling();
        
        System.out.println("\n=== 多重异常处理 ===");
        demo.multipleExceptionHandling();
        
        System.out.println("\n=== try-with-resources ===");
        demo.tryWithResourcesDemo();
        
        System.out.println("\n=== 自定义异常 ===");
        demo.customExceptionDemo();
    }
    
    /**
     * 基本异常处理示例
     */
    public void basicExceptionHandling() {
        try {
            int result = 10 / 0; // 会抛出ArithmeticException
            System.out.println("结果: " + result);
        } catch (ArithmeticException e) {
            System.out.println("捕获到算术异常: " + e.getMessage());
        } finally {
            System.out.println("finally块总是执行");
        }
    }
    
    /**
     * 多重异常处理示例
     */
    public void multipleExceptionHandling() {
        String[] array = {"1", "2", "abc", "4"};
        
        for (int i = 0; i <= array.length; i++) {
            try {
                int num = Integer.parseInt(array[i]);
                int result = 100 / num;
                System.out.println("array[" + i + "] = " + num + ", 100/" + num + " = " + result);
            } catch (ArrayIndexOutOfBoundsException e) {
                System.out.println("数组越界异常: 索引 " + i + " 超出数组范围");
            } catch (NumberFormatException e) {
                System.out.println("数字格式异常: '" + array[i] + "' 不是有效数字");
            } catch (ArithmeticException e) {
                System.out.println("算术异常: 除零错误");
            } catch (Exception e) {
                System.out.println("其他异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * try-with-resources示例
     */
    public void tryWithResourcesDemo() {
        // 创建测试文件
        String fileName = "test_exception.txt";
        createTestFile(fileName);
        
        // 使用try-with-resources读取文件
        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
            String line;
            System.out.println("文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
        } catch (FileNotFoundException e) {
            System.out.println("文件未找到: " + e.getMessage());
        } catch (IOException e) {
            System.out.println("IO异常: " + e.getMessage());
        }
        // 资源会自动关闭
    }
    
    /**
     * 自定义异常示例
     */
    public void customExceptionDemo() {
        BankAccount account = new BankAccount("12345", 1000.0);
        
        try {
            account.withdraw(500.0);  // 正常提取
            account.withdraw(600.0);  // 余额不足，会抛出自定义异常
        } catch (InsufficientFundsException e) {
            System.out.println("提取失败: " + e.getMessage());
            System.out.println("当前余额: " + e.getCurrentBalance());
            System.out.println("尝试提取: " + e.getAttemptedAmount());
        }
        
        try {
            new BankAccount("", 1000.0); // 无效账户号，会抛出运行时异常
        } catch (InvalidAccountException e) {
            System.out.println("账户创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建测试文件
     */
    private void createTestFile(String fileName) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(fileName))) {
            writer.println("这是测试文件的第一行");
            writer.println("这是测试文件的第二行");
            writer.println("这是测试文件的第三行");
        } catch (IOException e) {
            System.out.println("创建测试文件失败: " + e.getMessage());
        }
    }
}

/**
 * 自定义检查异常 - 余额不足异常
 */
class InsufficientFundsException extends Exception {
    private double currentBalance;
    private double attemptedAmount;
    
    public InsufficientFundsException(double currentBalance, double attemptedAmount) {
        super("余额不足: 当前余额 " + currentBalance + ", 尝试提取 " + attemptedAmount);
        this.currentBalance = currentBalance;
        this.attemptedAmount = attemptedAmount;
    }
    
    public double getCurrentBalance() {
        return currentBalance;
    }
    
    public double getAttemptedAmount() {
        return attemptedAmount;
    }
}

/**
 * 自定义运行时异常 - 无效账户异常
 */
class InvalidAccountException extends RuntimeException {
    public InvalidAccountException(String message) {
        super(message);
    }
    
    public InvalidAccountException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 银行账户类
 */
class BankAccount {
    private String accountNumber;
    private double balance;
    
    public BankAccount(String accountNumber, double initialBalance) {
        if (accountNumber == null || accountNumber.trim().isEmpty()) {
            throw new InvalidAccountException("账户号码不能为空");
        }
        if (initialBalance < 0) {
            throw new InvalidAccountException("初始余额不能为负数: " + initialBalance);
        }
        
        this.accountNumber = accountNumber;
        this.balance = initialBalance;
        System.out.println("账户创建成功: " + accountNumber + ", 初始余额: " + balance);
    }
    
    public void withdraw(double amount) throws InsufficientFundsException {
        if (amount <= 0) {
            throw new IllegalArgumentException("提取金额必须大于0: " + amount);
        }
        
        if (amount > balance) {
            throw new InsufficientFundsException(balance, amount);
        }
        
        balance -= amount;
        System.out.println("成功提取: " + amount + ", 余额: " + balance);
    }
    
    public void deposit(double amount) {
        if (amount <= 0) {
            throw new IllegalArgumentException("存款金额必须大于0: " + amount);
        }
        
        balance += amount;
        System.out.println("成功存入: " + amount + ", 余额: " + balance);
    }
    
    public double getBalance() {
        return balance;
    }
    
    public String getAccountNumber() {
        return accountNumber;
    }
}
