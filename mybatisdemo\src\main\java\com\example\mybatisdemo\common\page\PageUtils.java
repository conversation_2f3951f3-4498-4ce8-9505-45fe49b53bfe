package com.example.mybatisdemo.common.page;

import com.example.mybatisdemo.dto.PageResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.function.Supplier;

/**
 * 分页工具类
 * 提供通用的分页查询方法
 */
public class PageUtils {
    
    /**
     * 开始分页
     * @param pageQuery 分页查询参数
     */
    public static void startPage(BasePageQuery pageQuery) {
        if (pageQuery == null) {
            return;
        }
        
        PageHelper.startPage(
            pageQuery.getPageNum(), 
            pageQuery.getPageSize(),
            pageQuery.getCount(),
            pageQuery.getReasonable(),
            pageQuery.getPageSizeZero()
        );
        
        // 设置排序
        if (StringUtils.hasText(pageQuery.getOrderBy())) {
            PageHelper.orderBy(pageQuery.getOrderBy());
        }
    }
    
    /**
     * 执行分页查询
     * @param pageQuery 分页查询参数
     * @param queryFunction 查询函数
     * @return 分页结果
     */
    public static <T> PageResult<T> doSelectPage(BasePageQuery pageQuery, Supplier<List<T>> queryFunction) {
        startPage(pageQuery);
        List<T> list = queryFunction.get();
        return PageResult.of(new PageInfo<>(list));
    }
    
    /**
     * 执行分页查询（带数据转换）
     * @param pageQuery 分页查询参数
     * @param queryFunction 查询函数
     * @param convertFunction 数据转换函数
     * @return 分页结果
     */
    public static <T, R> PageResult<R> doSelectPage(
            BasePageQuery pageQuery, 
            Supplier<List<T>> queryFunction,
            java.util.function.Function<List<T>, List<R>> convertFunction) {
        
        startPage(pageQuery);
        List<T> list = queryFunction.get();
        PageInfo<T> pageInfo = new PageInfo<>(list);
        List<R> convertedList = convertFunction.apply(list);
        return PageResult.of(pageInfo, convertedList);
    }
    
    /**
     * 清理分页参数
     */
    public static void clearPage() {
        PageHelper.clearPage();
    }
    
    /**
     * 构建排序字符串
     * @param sortField 排序字段
     * @param sortDirection 排序方向
     * @return 排序字符串
     */
    public static String buildOrderBy(String sortField, String sortDirection) {
        if (!StringUtils.hasText(sortField)) {
            return null;
        }
        
        // 防止SQL注入，只允许字母、数字、下划线
        if (!sortField.matches("^[a-zA-Z0-9_]+$")) {
            return null;
        }
        
        StringBuilder orderBy = new StringBuilder();
        
        // 转换驼峰命名为下划线命名
        String dbField = camelToUnderscore(sortField);
        orderBy.append(dbField);
        
        if (StringUtils.hasText(sortDirection)) {
            if ("asc".equalsIgnoreCase(sortDirection) || "desc".equalsIgnoreCase(sortDirection)) {
                orderBy.append(" ").append(sortDirection.toUpperCase());
            }
        }
        
        return orderBy.toString();
    }
    
    /**
     * 驼峰命名转下划线命名
     */
    private static String camelToUnderscore(String camelCase) {
        if (!StringUtils.hasText(camelCase)) {
            return camelCase;
        }
        
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }
    
    /**
     * 验证分页参数
     */
    public static void validatePageQuery(BasePageQuery pageQuery) {
        if (pageQuery == null) {
            return;
        }
        
        if (pageQuery.getPageNum() == null || pageQuery.getPageNum() < 1) {
            pageQuery.setPageNum(1);
        }
        
        if (pageQuery.getPageSize() == null || pageQuery.getPageSize() < 1) {
            pageQuery.setPageSize(10);
        }
        
        if (pageQuery.getPageSize() > 500) {
            pageQuery.setPageSize(500);
        }
    }
}
