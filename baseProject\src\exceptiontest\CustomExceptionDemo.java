package exceptiontest;

class InsufficientFundsException extends Exception {

    private double amount;

    public InsufficientFundsException(double amount) {
        super("余额不足，尝试提取: " + amount);
        this.amount = amount;
    }

    public double getAmount() {
        return amount;
    }
}
class InvalidAccountException extends RuntimeException {
    public InvalidAccountException(String message) {
        super(message);
    }
    public InvalidAccountException(String message, Throwable cause) {
        super(message, cause);
    }
}
class BankAccount {
    private String accountNumber;
    private double balance;

    public BankAccount(String accountNumber, double initialBalance) {
        if (accountNumber == null || accountNumber.trim().isEmpty()) {
            throw new InvalidAccountException("账户号码不能为空");
        }
        if (initialBalance < 0) {
            throw new InvalidAccountException("初始余额不能为负数: " + initialBalance);
        }

        this.accountNumber = accountNumber;
    }
    public void withdraw(double amount) throws InsufficientFundsException {
        if (amount <= 0) {
            throw new IllegalArgumentException("提取金额必须大于0: " + amount);
        }

        if (amount > balance) {
            throw new InsufficientFundsException(amount);
        }

        balance -= amount;
        System.out.println("成功提取: " + amount + ", 余额: " + balance);
    }
    public double getBalance() {
        return balance;
    }
}
public class CustomExceptionDemo {
     public  static void main(String[] args) {
         try {
             BankAccount account = new BankAccount("12345", 1000.0);
             account.withdraw(1500.0); // 会抛出InsufficientFundsException
         } catch (InsufficientFundsException e) {
             System.out.println("自定义异常: " + e.getMessage());
             System.out.println("尝试提取金额: " + e.getAmount());
         } catch (InvalidAccountException e) {
             System.out.println("账户异常: " + e.getMessage());
         }
     }

}
