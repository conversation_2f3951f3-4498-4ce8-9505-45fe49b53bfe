package thread;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Random;

/**
 * 实际应用场景演示
 * 包括生产者消费者、线程安全单例、并发集合等
 */
public class PracticalExamplesDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 实际应用场景演示 ===");
        
        // 1. 生产者消费者模式
        producerConsumerDemo();
        
        // 2. 线程安全单例模式
        singletonDemo();
        
        // 3. 并发集合演示
        concurrentCollectionDemo();
        
        // 4. 任务分解并行处理
        parallelTaskDemo();
    }
    
    /**
     * 生产者消费者模式演示
     */
    public static void producerConsumerDemo() {
        System.out.println("\n--- 生产者消费者模式演示 ---");
        
        // 使用BlockingQueue实现生产者消费者
        BlockingQueue<String> buffer = new LinkedBlockingQueue<>(5);
        
        // 生产者
        class Producer implements Runnable {
            private final String name;
            private final Random random = new Random();
            
            public Producer(String name) {
                this.name = name;
            }
            
            @Override
            public void run() {
                try {
                    for (int i = 1; i <= 5; i++) {
                        String product = name + "-产品" + i;
                        buffer.put(product); // 阻塞式添加
                        System.out.println("📦 " + name + " 生产: " + product + 
                            " (缓冲区大小: " + buffer.size() + ")");
                        Thread.sleep(500 + random.nextInt(1000)); // 随机生产时间
                    }
                    System.out.println("✅ " + name + " 生产完成");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        
        // 消费者
        class Consumer implements Runnable {
            private final String name;
            private final Random random = new Random();
            
            public Consumer(String name) {
                this.name = name;
            }
            
            @Override
            public void run() {
                try {
                    while (true) {
                        String product = buffer.take(); // 阻塞式获取
                        System.out.println("🛒 " + name + " 消费: " + product + 
                            " (缓冲区大小: " + buffer.size() + ")");
                        Thread.sleep(800 + random.nextInt(1200)); // 随机消费时间
                    }
                } catch (InterruptedException e) {
                    System.out.println(name + " 停止消费");
                    Thread.currentThread().interrupt();
                }
            }
        }
        
        // 启动生产者和消费者
        Thread producer1 = new Thread(new Producer("生产者A"), "生产者A");
        Thread producer2 = new Thread(new Producer("生产者B"), "生产者B");
        Thread consumer1 = new Thread(new Consumer("消费者X"), "消费者X");
        Thread consumer2 = new Thread(new Consumer("消费者Y"), "消费者Y");
        
        producer1.start();
        producer2.start();
        consumer1.start();
        consumer2.start();
        
        try {
            // 等待生产者完成
            producer1.join();
            producer2.join();
            
            // 让消费者消费完剩余产品
            Thread.sleep(3000);
            
            // 中断消费者
            consumer1.interrupt();
            consumer2.interrupt();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 线程安全单例模式演示
     */
    public static void singletonDemo() {
        System.out.println("\n--- 线程安全单例模式演示 ---");
        
        // 测试单例模式的线程安全性
        CountDownLatch latch = new CountDownLatch(5);
        
        for (int i = 1; i <= 5; i++) {
            final int threadId = i;
            new Thread(() -> {
                ThreadSafeSingleton instance1 = ThreadSafeSingleton.getInstance();
                ThreadSafeSingleton instance2 = ThreadSafeSingleton.getInstanceByInnerClass();
                
                System.out.println("线程" + threadId + " 获取实例: " + 
                    instance1.hashCode() + ", " + instance2.hashCode());
                
                latch.countDown();
            }, "单例测试线程-" + i).start();
        }
        
        try {
            latch.await();
            System.out.println("单例模式测试完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 并发集合演示
     */
    public static void concurrentCollectionDemo() {
        System.out.println("\n--- 并发集合演示 ---");
        
        // ConcurrentHashMap演示
        ConcurrentHashMap<String, Integer> concurrentMap = new ConcurrentHashMap<>();
        CountDownLatch latch = new CountDownLatch(3);
        
        // 多线程操作ConcurrentHashMap
        for (int i = 1; i <= 3; i++) {
            final int threadId = i;
            new Thread(() -> {
                try {
                    for (int j = 1; j <= 3; j++) {
                        String key = "key" + (threadId * 10 + j);
                        Integer value = threadId * 10 + j;
                        
                        concurrentMap.put(key, value);
                        System.out.println("线程" + threadId + " 添加: " + key + " = " + value);
                        
                        // 原子性操作
                        concurrentMap.compute(key, (k, v) -> v == null ? 1 : v + 1);
                        System.out.println("线程" + threadId + " 计算后: " + key + " = " + 
                            concurrentMap.get(key));
                        
                        Thread.sleep(100);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            }, "ConcurrentMap线程-" + i).start();
        }
        
        try {
            latch.await();
            System.out.println("ConcurrentHashMap最终结果: " + concurrentMap);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // CopyOnWriteArrayList演示
        System.out.println("\nCopyOnWriteArrayList演示:");
        CopyOnWriteArrayList<String> copyOnWriteList = new CopyOnWriteArrayList<>();
        
        // 写线程
        Thread writer = new Thread(() -> {
            for (int i = 1; i <= 5; i++) {
                copyOnWriteList.add("元素" + i);
                System.out.println("写入: 元素" + i);
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }, "写线程");
        
        // 读线程
        Thread reader = new Thread(() -> {
            for (int i = 0; i < 10; i++) {
                System.out.println("读取列表: " + copyOnWriteList);
                try {
                    Thread.sleep(300);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }, "读线程");
        
        writer.start();
        reader.start();
        
        try {
            writer.join();
            reader.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 任务分解并行处理演示
     */
    public static void parallelTaskDemo() {
        System.out.println("\n--- 任务分解并行处理演示 ---");
        
        // 模拟大数据处理任务
        int[] data = new int[1000];
        Random random = new Random();
        for (int i = 0; i < data.length; i++) {
            data[i] = random.nextInt(100);
        }
        
        System.out.println("数据大小: " + data.length);
        
        // 串行处理
        long startTime = System.currentTimeMillis();
        long serialSum = serialSum(data);
        long serialTime = System.currentTimeMillis() - startTime;
        System.out.println("串行处理结果: " + serialSum + ", 耗时: " + serialTime + "ms");
        
        // 并行处理
        startTime = System.currentTimeMillis();
        long parallelSum = parallelSum(data);
        long parallelTime = System.currentTimeMillis() - startTime;
        System.out.println("并行处理结果: " + parallelSum + ", 耗时: " + parallelTime + "ms");
        
        System.out.println("性能提升: " + (serialTime > 0 ? (double)serialTime / parallelTime : "N/A"));
    }
    
    /**
     * 串行求和
     */
    private static long serialSum(int[] data) {
        long sum = 0;
        for (int value : data) {
            sum += value;
            // 模拟计算复杂度
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return sum;
    }
    
    /**
     * 并行求和
     */
    private static long parallelSum(int[] data) {
        int threadCount = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        int chunkSize = data.length / threadCount;
        Future<Long>[] futures = new Future[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int start = i * chunkSize;
            final int end = (i == threadCount - 1) ? data.length : (i + 1) * chunkSize;
            
            futures[i] = executor.submit(() -> {
                long sum = 0;
                for (int j = start; j < end; j++) {
                    sum += data[j];
                    // 模拟计算复杂度
                    try {
                        Thread.sleep(1);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                return sum;
            });
        }
        
        long totalSum = 0;
        try {
            for (Future<Long> future : futures) {
                totalSum += future.get();
            }
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
        
        return totalSum;
    }
}

/**
 * 线程安全的单例模式
 */
class ThreadSafeSingleton {
    // 使用volatile确保可见性
    private static volatile ThreadSafeSingleton instance;
    
    private ThreadSafeSingleton() {
        // 私有构造函数
        System.out.println("创建ThreadSafeSingleton实例: " + this.hashCode());
    }
    
    // 双重检查锁定
    public static ThreadSafeSingleton getInstance() {
        if (instance == null) {
            synchronized (ThreadSafeSingleton.class) {
                if (instance == null) {
                    instance = new ThreadSafeSingleton();
                }
            }
        }
        return instance;
    }
    
    // 静态内部类方式（推荐）
    private static class SingletonHolder {
        private static final ThreadSafeSingleton INSTANCE = new ThreadSafeSingleton();
    }
    
    public static ThreadSafeSingleton getInstanceByInnerClass() {
        return SingletonHolder.INSTANCE;
    }
}

/**
 * 线程安全的计数器
 */
class ThreadSafeCounter {
    private final AtomicInteger count = new AtomicInteger(0);
    
    public int increment() {
        return count.incrementAndGet();
    }
    
    public int decrement() {
        return count.decrementAndGet();
    }
    
    public int get() {
        return count.get();
    }
    
    public boolean compareAndSet(int expected, int update) {
        return count.compareAndSet(expected, update);
    }
}
