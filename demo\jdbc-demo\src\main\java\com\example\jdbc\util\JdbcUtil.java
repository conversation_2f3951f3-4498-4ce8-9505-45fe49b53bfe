package com.example.jdbc.util;

import com.example.jdbc.config.DatabaseConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.Properties;

/**
 * JDBC工具类
 * 提供数据库连接和基础操作方法
 */
@Component
public class JdbcUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(JdbcUtil.class);
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    /**
     * 获取数据库连接 - 基础方式
     */
    public Connection getConnection() throws SQLException {
        DatabaseConfig.DatabaseInfo dbInfo = databaseConfig.getCurrentDatabaseInfo();
        
        logger.info("正在连接数据库: {}", dbInfo.getUrl());
        
        try {
            // 加载驱动（可选，新版本JDBC会自动加载）
            Class.forName(dbInfo.getDriver());
            
            // 获取连接
            Connection connection = DriverManager.getConnection(
                dbInfo.getUrl(), 
                dbInfo.getUsername(), 
                dbInfo.getPassword()
            );
            
            logger.info("数据库连接成功!");
            return connection;
            
        } catch (ClassNotFoundException e) {
            logger.error("数据库驱动未找到: {}", dbInfo.getDriver(), e);
            throw new SQLException("数据库驱动未找到", e);
        } catch (SQLException e) {
            logger.error("数据库连接失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 获取数据库连接 - 使用Properties
     */
    public Connection getConnectionWithProperties() throws SQLException {
        DatabaseConfig.DatabaseInfo dbInfo = databaseConfig.getCurrentDatabaseInfo();
        
        Properties props = new Properties();
        props.setProperty("user", dbInfo.getUsername());
        props.setProperty("password", dbInfo.getPassword());
        
        // 添加额外的连接属性
        if (dbInfo.getUrl().contains("mysql")) {
            props.setProperty("useSSL", "false");
            props.setProperty("serverTimezone", "UTC");
            props.setProperty("characterEncoding", "utf8");
            props.setProperty("allowPublicKeyRetrieval", "true");
        }
        
        try {
            Connection connection = DriverManager.getConnection(dbInfo.getUrl(), props);
            logger.info("使用Properties方式连接数据库成功!");
            return connection;
        } catch (SQLException e) {
            logger.error("使用Properties方式连接数据库失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        Connection conn = null;
        try {
            conn = getConnection();
            
            // 执行简单查询测试连接
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT 1");
            
            if (rs.next()) {
                logger.info("数据库连接测试成功!");
                return true;
            }
            
        } catch (SQLException e) {
            logger.error("数据库连接测试失败: {}", e.getMessage(), e);
            return false;
        } finally {
            closeConnection(conn);
        }
        return false;
    }
    
    /**
     * 获取数据库元信息
     */
    public void printDatabaseInfo() {
        Connection conn = null;
        try {
            conn = getConnection();
            DatabaseMetaData metaData = conn.getMetaData();
            
            logger.info("=== 数据库信息 ===");
            logger.info("数据库产品名称: {}", metaData.getDatabaseProductName());
            logger.info("数据库产品版本: {}", metaData.getDatabaseProductVersion());
            logger.info("驱动名称: {}", metaData.getDriverName());
            logger.info("驱动版本: {}", metaData.getDriverVersion());
            logger.info("JDBC版本: {}.{}", metaData.getJDBCMajorVersion(), metaData.getJDBCMinorVersion());
            logger.info("连接URL: {}", metaData.getURL());
            logger.info("用户名: {}", metaData.getUserName());
            logger.info("是否只读: {}", metaData.isReadOnly());
            logger.info("支持事务: {}", metaData.supportsTransactions());
            logger.info("================");
            
        } catch (SQLException e) {
            logger.error("获取数据库信息失败: {}", e.getMessage(), e);
        } finally {
            closeConnection(conn);
        }
    }
    
    /**
     * 关闭数据库连接
     */
    public void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
                logger.debug("数据库连接已关闭");
            } catch (SQLException e) {
                logger.error("关闭数据库连接失败: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 关闭Statement
     */
    public void closeStatement(Statement stmt) {
        if (stmt != null) {
            try {
                stmt.close();
                logger.debug("Statement已关闭");
            } catch (SQLException e) {
                logger.error("关闭Statement失败: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 关闭ResultSet
     */
    public void closeResultSet(ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
                logger.debug("ResultSet已关闭");
            } catch (SQLException e) {
                logger.error("关闭ResultSet失败: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 关闭所有资源
     */
    public void closeAll(Connection conn, Statement stmt, ResultSet rs) {
        closeResultSet(rs);
        closeStatement(stmt);
        closeConnection(conn);
    }
    
    /**
     * 执行DDL语句（创建表、索引等）
     */
    public boolean executeDDL(String sql) {
        Connection conn = null;
        Statement stmt = null;
        
        try {
            conn = getConnection();
            stmt = conn.createStatement();
            
            logger.info("执行DDL语句: {}", sql);
            stmt.execute(sql);
            logger.info("DDL语句执行成功");
            return true;
            
        } catch (SQLException e) {
            logger.error("DDL语句执行失败: {}", e.getMessage(), e);
            return false;
        } finally {
            closeAll(conn, stmt, null);
        }
    }
}
