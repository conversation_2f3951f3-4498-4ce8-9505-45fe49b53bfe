<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    
    <!-- 开启二级缓存 -->
    <cache eviction="LRU" flushInterval="60000" size="512" readOnly="false"/>
    
    <!-- 用户基本结果映射 -->
    <resultMap id="UserResultMap" type="User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="email" column="email"/>
        <result property="password" column="password"/>
        <result property="salt" column="salt"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="phone" column="phone"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="age" column="age"/>
        <result property="address" column="address"/>
        <result property="bio" column="bio"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="loginCount" column="login_count"/>
        <result property="status" column="status"/>
        <result property="emailVerified" column="email_verified"/>
        <result property="phoneVerified" column="phone_verified"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="version" column="version"/>
        <result property="deleted" column="deleted"/>
    </resultMap>
    
    <!-- 用户和角色关联结果映射 -->
    <resultMap id="UserWithRolesResultMap" type="User" extends="UserResultMap">
        <collection property="roles" ofType="Role">
            <id property="id" column="role_id"/>
            <result property="roleName" column="role_name"/>
            <result property="roleCode" column="role_code"/>
            <result property="roleDesc" column="role_desc"/>
            <result property="sortOrder" column="role_sort_order"/>
            <result property="status" column="role_status"/>
            <result property="createTime" column="role_create_time"/>
            <result property="updateTime" column="role_update_time"/>
        </collection>
    </resultMap>
    
    <!-- 用户和权限关联结果映射 -->
    <resultMap id="UserWithPermissionsResultMap" type="User" extends="UserResultMap">
        <collection property="permissions" ofType="Permission">
            <id property="id" column="permission_id"/>
            <result property="permissionName" column="permission_name"/>
            <result property="permissionCode" column="permission_code"/>
            <result property="permissionDesc" column="permission_desc"/>
            <result property="resourceType" column="resource_type"/>
            <result property="parentId" column="parent_id"/>
            <result property="path" column="path"/>
            <result property="url" column="url"/>
            <result property="method" column="method"/>
            <result property="icon" column="icon"/>
            <result property="sortOrder" column="permission_sort_order"/>
            <result property="level" column="level"/>
            <result property="leaf" column="leaf"/>
            <result property="status" column="permission_status"/>
        </collection>
    </resultMap>
    
    <!-- 用户、角色和权限完整关联结果映射 -->
    <resultMap id="UserWithRolesAndPermissionsResultMap" type="User" extends="UserResultMap">
        <collection property="roles" ofType="Role">
            <id property="id" column="role_id"/>
            <result property="roleName" column="role_name"/>
            <result property="roleCode" column="role_code"/>
            <result property="roleDesc" column="role_desc"/>
            <result property="sortOrder" column="role_sort_order"/>
            <result property="status" column="role_status"/>
            <collection property="permissions" ofType="Permission">
                <id property="id" column="permission_id"/>
                <result property="permissionName" column="permission_name"/>
                <result property="permissionCode" column="permission_code"/>
                <result property="permissionDesc" column="permission_desc"/>
                <result property="resourceType" column="resource_type"/>
                <result property="status" column="permission_status"/>
            </collection>
        </collection>
    </resultMap>
    
    <!-- SQL片段：基本字段列表 -->
    <sql id="Base_Column_List">
        id, username, email, password, salt, nickname, avatar, phone, gender, birthday, age,
        address, bio, last_login_time, last_login_ip, login_count, status, email_verified,
        phone_verified, create_time, update_time, create_user, update_user, version, deleted
    </sql>
    
    <!-- SQL片段：查询条件 -->
    <sql id="Base_Where_Clause">
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="nickname != null and nickname != ''">
                AND nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="gender != null">
                AND gender = #{gender}
            </if>
            <if test="minAge != null">
                AND age >= #{minAge}
            </if>
            <if test="maxAge != null">
                AND age <= #{maxAge}
            </if>
            <if test="address != null and address != ''">
                AND address LIKE CONCAT('%', #{address}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="statusList != null and statusList.size() > 0">
                AND status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="emailVerified != null">
                AND email_verified = #{emailVerified}
            </if>
            <if test="phoneVerified != null">
                AND phone_verified = #{phoneVerified}
            </if>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time <= #{createTimeEnd}
            </if>
            <if test="updateTimeStart != null">
                AND update_time >= #{updateTimeStart}
            </if>
            <if test="updateTimeEnd != null">
                AND update_time <= #{updateTimeEnd}
            </if>
            <if test="birthdayStart != null">
                AND birthday >= #{birthdayStart}
            </if>
            <if test="birthdayEnd != null">
                AND birthday <= #{birthdayEnd}
            </if>
            <if test="lastLoginTimeStart != null">
                AND last_login_time >= #{lastLoginTimeStart}
            </if>
            <if test="lastLoginTimeEnd != null">
                AND last_login_time <= #{lastLoginTimeEnd}
            </if>
            <if test="minLoginCount != null">
                AND login_count >= #{minLoginCount}
            </if>
            <if test="maxLoginCount != null">
                AND login_count <= #{maxLoginCount}
            </if>
            <if test="createUser != null">
                AND create_user = #{createUser}
            </if>
            <if test="updateUser != null">
                AND update_user = #{updateUser}
            </if>
            <if test="deleted != null">
                AND deleted = #{deleted}
            </if>
        </where>
    </sql>
    
    <!-- 根据ID查询用户 -->
    <select id="selectById" parameterType="long" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE id = #{id} AND deleted = 0
    </select>
    
    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="string" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE username = #{username} AND deleted = 0
    </select>
    
    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" parameterType="string" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE email = #{email} AND deleted = 0
    </select>
    
    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" parameterType="string" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE phone = #{phone} AND deleted = 0
    </select>
    
    <!-- 查询所有用户 -->
    <select id="selectAll" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE deleted = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据条件查询用户列表 -->
    <select id="selectByCondition" parameterType="UserQueryDTO" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        <include refid="Base_Where_Clause"/>
        <if test="orderByClause != null and orderByClause != ''">
            ORDER BY ${orderByClause}
        </if>
        <if test="orderByClause == null or orderByClause == ''">
            ORDER BY create_time DESC
        </if>
    </select>
    
    <!-- 分页查询用户列表 -->
    <select id="selectByPage" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE deleted = 0
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (
            username, email, password, salt, nickname, avatar, phone, gender, birthday, age,
            address, bio, status, email_verified, phone_verified, create_time, update_time,
            create_user, update_user, version, deleted
        ) VALUES (
            #{username}, #{email}, #{password}, #{salt}, #{nickname}, #{avatar}, #{phone},
            #{gender}, #{birthday}, #{age}, #{address}, #{bio}, #{status}, #{emailVerified},
            #{phoneVerified}, #{createTime}, #{updateTime}, #{createUser}, #{updateUser},
            #{version}, #{deleted}
        )
    </insert>

    <!-- 批量插入用户 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO user (
            username, email, password, salt, nickname, avatar, phone, gender, birthday, age,
            address, bio, status, email_verified, phone_verified, create_time, update_time,
            create_user, update_user, version, deleted
        ) VALUES
        <foreach collection="users" item="user" separator=",">
            (#{user.username}, #{user.email}, #{user.password}, #{user.salt}, #{user.nickname},
             #{user.avatar}, #{user.phone}, #{user.gender}, #{user.birthday}, #{user.age},
             #{user.address}, #{user.bio}, #{user.status}, #{user.emailVerified},
             #{user.phoneVerified}, #{user.createTime}, #{user.updateTime}, #{user.createUser},
             #{user.updateUser}, #{user.version}, #{user.deleted})
        </foreach>
    </insert>

    <!-- 更新用户信息 -->
    <update id="update" parameterType="User">
        UPDATE user
        SET username = #{username},
            email = #{email},
            password = #{password},
            salt = #{salt},
            nickname = #{nickname},
            avatar = #{avatar},
            phone = #{phone},
            gender = #{gender},
            birthday = #{birthday},
            age = #{age},
            address = #{address},
            bio = #{bio},
            status = #{status},
            email_verified = #{emailVerified},
            phone_verified = #{phoneVerified},
            update_time = #{updateTime},
            update_user = #{updateUser},
            version = version + 1
        WHERE id = #{id} AND version = #{version} AND deleted = 0
    </update>

    <!-- 选择性更新用户信息 -->
    <update id="updateSelective" parameterType="User">
        UPDATE user
        <set>
            <if test="username != null and username != ''">
                username = #{username},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="salt != null and salt != ''">
                salt = #{salt},
            </if>
            <if test="nickname != null">
                nickname = #{nickname},
            </if>
            <if test="avatar != null">
                avatar = #{avatar},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="gender != null">
                gender = #{gender},
            </if>
            <if test="birthday != null">
                birthday = #{birthday},
            </if>
            <if test="age != null">
                age = #{age},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="bio != null">
                bio = #{bio},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="emailVerified != null">
                email_verified = #{emailVerified},
            </if>
            <if test="phoneVerified != null">
                phone_verified = #{phoneVerified},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime},
            </if>
            <if test="lastLoginIp != null">
                last_login_ip = #{lastLoginIp},
            </if>
            <if test="loginCount != null">
                login_count = #{loginCount},
            </if>
            update_time = #{updateTime},
            update_user = #{updateUser},
            version = version + 1
        </set>
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 根据ID删除用户 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 批量删除用户 -->
    <delete id="batchDelete" parameterType="list">
        DELETE FROM user WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 逻辑删除用户 -->
    <update id="logicDelete">
        UPDATE user
        SET deleted = 1,
            update_time = NOW(),
            update_user = #{updateUser},
            version = version + 1
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 查询用户及其角色信息 -->
    <select id="selectUserWithRoles" parameterType="long" resultMap="UserWithRolesResultMap">
        SELECT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender, u.birthday,
               u.age, u.address, u.bio, u.last_login_time, u.last_login_ip, u.login_count,
               u.status, u.email_verified, u.phone_verified, u.create_time, u.update_time,
               u.create_user, u.update_user, u.version, u.deleted,
               r.id as role_id, r.role_name, r.role_code, r.role_desc,
               r.sort_order as role_sort_order, r.status as role_status,
               r.create_time as role_create_time, r.update_time as role_update_time
        FROM user u
        LEFT JOIN user_role ur ON u.id = ur.user_id
        LEFT JOIN role r ON ur.role_id = r.id AND r.deleted = 0 AND r.status = 1
        WHERE u.id = #{id} AND u.deleted = 0
    </select>

    <!-- 查询用户及其权限信息 -->
    <select id="selectUserWithPermissions" parameterType="long" resultMap="UserWithPermissionsResultMap">
        SELECT DISTINCT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender,
               u.birthday, u.age, u.address, u.bio, u.last_login_time, u.last_login_ip,
               u.login_count, u.status, u.email_verified, u.phone_verified, u.create_time,
               u.update_time, u.create_user, u.update_user, u.version, u.deleted,
               p.id as permission_id, p.permission_name, p.permission_code, p.permission_desc,
               p.resource_type, p.parent_id, p.path, p.url, p.method, p.icon,
               p.sort_order as permission_sort_order, p.level, p.leaf, p.status as permission_status
        FROM user u
        LEFT JOIN user_role ur ON u.id = ur.user_id
        LEFT JOIN role r ON ur.role_id = r.id AND r.deleted = 0 AND r.status = 1
        LEFT JOIN role_permission rp ON r.id = rp.role_id
        LEFT JOIN permission p ON rp.permission_id = p.id AND p.deleted = 0 AND p.status = 1
        WHERE u.id = #{id} AND u.deleted = 0
    </select>

    <!-- 查询用户完整信息（包含角色和权限） -->
    <select id="selectUserWithRolesAndPermissions" parameterType="long" resultMap="UserWithRolesAndPermissionsResultMap">
        SELECT DISTINCT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender,
               u.birthday, u.age, u.address, u.bio, u.last_login_time, u.last_login_ip,
               u.login_count, u.status, u.email_verified, u.phone_verified, u.create_time,
               u.update_time, u.create_user, u.update_user, u.version, u.deleted,
               r.id as role_id, r.role_name, r.role_code, r.role_desc,
               r.sort_order as role_sort_order, r.status as role_status,
               p.id as permission_id, p.permission_name, p.permission_code, p.permission_desc,
               p.resource_type, p.status as permission_status
        FROM user u
        LEFT JOIN user_role ur ON u.id = ur.user_id
        LEFT JOIN role r ON ur.role_id = r.id AND r.deleted = 0 AND r.status = 1
        LEFT JOIN role_permission rp ON r.id = rp.role_id
        LEFT JOIN permission p ON rp.permission_id = p.id AND p.deleted = 0 AND p.status = 1
        WHERE u.id = #{id} AND u.deleted = 0
        ORDER BY r.sort_order, p.sort_order
    </select>

    <!-- 查询所有用户及其角色信息 -->
    <select id="selectUsersWithRoles" resultMap="UserWithRolesResultMap">
        SELECT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender, u.birthday,
               u.age, u.address, u.bio, u.last_login_time, u.last_login_ip, u.login_count,
               u.status, u.email_verified, u.phone_verified, u.create_time, u.update_time,
               u.create_user, u.update_user, u.version, u.deleted,
               r.id as role_id, r.role_name, r.role_code, r.role_desc,
               r.sort_order as role_sort_order, r.status as role_status,
               r.create_time as role_create_time, r.update_time as role_update_time
        FROM user u
        LEFT JOIN user_role ur ON u.id = ur.user_id
        LEFT JOIN role r ON ur.role_id = r.id AND r.deleted = 0 AND r.status = 1
        WHERE u.deleted = 0
        ORDER BY u.create_time DESC, r.sort_order
    </select>

    <!-- 根据角色ID查询用户列表 -->
    <select id="selectUsersByRoleId" parameterType="long" resultMap="UserResultMap">
        SELECT DISTINCT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender,
               u.birthday, u.age, u.address, u.bio, u.last_login_time, u.last_login_ip,
               u.login_count, u.status, u.email_verified, u.phone_verified, u.create_time,
               u.update_time, u.create_user, u.update_user, u.version, u.deleted
        FROM user u
        INNER JOIN user_role ur ON u.id = ur.user_id
        INNER JOIN role r ON ur.role_id = r.id
        WHERE r.id = #{roleId} AND u.deleted = 0 AND r.deleted = 0 AND r.status = 1
        ORDER BY u.create_time DESC
    </select>

    <!-- 根据角色编码查询用户列表 -->
    <select id="selectUsersByRoleCode" parameterType="string" resultMap="UserResultMap">
        SELECT DISTINCT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender,
               u.birthday, u.age, u.address, u.bio, u.last_login_time, u.last_login_ip,
               u.login_count, u.status, u.email_verified, u.phone_verified, u.create_time,
               u.update_time, u.create_user, u.update_user, u.version, u.deleted
        FROM user u
        INNER JOIN user_role ur ON u.id = ur.user_id
        INNER JOIN role r ON ur.role_id = r.id
        WHERE r.role_code = #{roleCode} AND u.deleted = 0 AND r.deleted = 0 AND r.status = 1
        ORDER BY u.create_time DESC
    </select>

    <!-- 根据权限编码查询用户列表 -->
    <select id="selectUsersByPermissionCode" parameterType="string" resultMap="UserResultMap">
        SELECT DISTINCT u.id, u.username, u.email, u.nickname, u.avatar, u.phone, u.gender,
               u.birthday, u.age, u.address, u.bio, u.last_login_time, u.last_login_ip,
               u.login_count, u.status, u.email_verified, u.phone_verified, u.create_time,
               u.update_time, u.create_user, u.update_user, u.version, u.deleted
        FROM user u
        INNER JOIN user_role ur ON u.id = ur.user_id
        INNER JOIN role r ON ur.role_id = r.id
        INNER JOIN role_permission rp ON r.id = rp.role_id
        INNER JOIN permission p ON rp.permission_id = p.id
        WHERE p.permission_code = #{permissionCode} AND u.deleted = 0 AND r.deleted = 0
              AND r.status = 1 AND p.deleted = 0 AND p.status = 1
        ORDER BY u.create_time DESC
    </select>

    <!-- 统计用户总数 -->
    <select id="count" resultType="int">
        SELECT COUNT(*) FROM user WHERE deleted = 0
    </select>

    <!-- 根据条件统计用户数量 -->
    <select id="countByCondition" parameterType="UserQueryDTO" resultType="int">
        SELECT COUNT(*)
        FROM user
        <include refid="Base_Where_Clause"/>
    </select>

    <!-- 根据状态统计用户数量 -->
    <select id="countByStatus" parameterType="int" resultType="int">
        SELECT COUNT(*) FROM user WHERE status = #{status} AND deleted = 0
    </select>

    <!-- 统计今日新增用户数量 -->
    <select id="countTodayNew" resultType="int">
        SELECT COUNT(*)
        FROM user
        WHERE DATE(create_time) = CURDATE() AND deleted = 0
    </select>

    <!-- 统计指定日期范围内的用户数量 -->
    <select id="countByDateRange" resultType="int">
        SELECT COUNT(*)
        FROM user
        WHERE create_time >= #{startDate} AND create_time <= #{endDate} AND deleted = 0
    </select>

    <!-- 获取用户统计信息 -->
    <select id="getUserStatistics" resultType="map">
        SELECT
            COUNT(*) as totalUsers,
            COUNT(CASE WHEN status = 1 THEN 1 END) as activeUsers,
            COUNT(CASE WHEN status = 0 THEN 1 END) as disabledUsers,
            COUNT(CASE WHEN status = 2 THEN 1 END) as lockedUsers,
            COUNT(CASE WHEN email_verified = 1 THEN 1 END) as emailVerifiedUsers,
            COUNT(CASE WHEN phone_verified = 1 THEN 1 END) as phoneVerifiedUsers,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayNewUsers,
            COUNT(CASE WHEN DATE(create_time) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as weekNewUsers,
            COUNT(CASE WHEN DATE(create_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as monthNewUsers,
            AVG(age) as averageAge,
            MIN(create_time) as earliestUser,
            MAX(create_time) as latestUser,
            MAX(last_login_time) as lastLoginTime
        FROM user
        WHERE deleted = 0
    </select>

    <!-- 获取用户年龄分布统计 -->
    <select id="getAgeDistribution" resultType="map">
        SELECT
            CASE
                WHEN age IS NULL THEN '未知'
                WHEN age < 18 THEN '18岁以下'
                WHEN age BETWEEN 18 AND 25 THEN '18-25岁'
                WHEN age BETWEEN 26 AND 35 THEN '26-35岁'
                WHEN age BETWEEN 36 AND 45 THEN '36-45岁'
                WHEN age BETWEEN 46 AND 55 THEN '46-55岁'
                WHEN age > 55 THEN '55岁以上'
            END as ageGroup,
            COUNT(*) as count
        FROM user
        WHERE deleted = 0
        GROUP BY
            CASE
                WHEN age IS NULL THEN '未知'
                WHEN age < 18 THEN '18岁以下'
                WHEN age BETWEEN 18 AND 25 THEN '18-25岁'
                WHEN age BETWEEN 26 AND 35 THEN '26-35岁'
                WHEN age BETWEEN 36 AND 45 THEN '36-45岁'
                WHEN age BETWEEN 46 AND 55 THEN '46-55岁'
                WHEN age > 55 THEN '55岁以上'
            END
        ORDER BY count DESC
    </select>

    <!-- 获取用户性别分布统计 -->
    <select id="getGenderDistribution" resultType="map">
        SELECT
            CASE
                WHEN gender = 0 THEN '未知'
                WHEN gender = 1 THEN '男'
                WHEN gender = 2 THEN '女'
                ELSE '未知'
            END as gender,
            COUNT(*) as count
        FROM user
        WHERE deleted = 0
        GROUP BY gender
        ORDER BY count DESC
    </select>

    <!-- 获取用户注册趋势统计 -->
    <select id="getRegistrationTrend" parameterType="int" resultType="map">
        SELECT
            DATE(create_time) as date,
            COUNT(*) as count
        FROM user
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) AND deleted = 0
        GROUP BY DATE(create_time)
        ORDER BY date DESC
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user
        WHERE username = #{username} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user
        WHERE email = #{email} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user
        WHERE phone = #{phone} AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 更新用户状态 -->
    <update id="updateStatus">
        UPDATE user
        SET status = #{status},
            update_time = NOW(),
            update_user = #{updateUser},
            version = version + 1
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE user
        SET password = #{password},
            salt = #{salt},
            update_time = NOW(),
            update_user = #{updateUser},
            version = version + 1
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLastLogin">
        UPDATE user
        SET last_login_time = #{loginTime},
            last_login_ip = #{loginIp},
            login_count = IFNULL(login_count, 0) + 1,
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 增加用户登录次数 -->
    <update id="incrementLoginCount">
        UPDATE user
        SET login_count = IFNULL(login_count, 0) + 1,
            update_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 验证用户邮箱 -->
    <update id="verifyEmail">
        UPDATE user
        SET email_verified = 1,
            update_time = NOW(),
            update_user = #{updateUser},
            version = version + 1
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 验证用户手机 -->
    <update id="verifyPhone">
        UPDATE user
        SET phone_verified = 1,
            update_time = NOW(),
            update_user = #{updateUser},
            version = version + 1
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper>
