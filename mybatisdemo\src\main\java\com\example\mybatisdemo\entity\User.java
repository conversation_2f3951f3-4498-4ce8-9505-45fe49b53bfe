package com.example.mybatisdemo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 用户实体类
 *
 * @Data - 自动生成getter、setter、toString、equals、hashCode方法
 * @NoArgsConstructor - 生成无参构造函数
 * @AllArgsConstructor - 生成全参构造函数
 * @Builder - 生成建造者模式代码，支持链式调用
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @ApiModel(description = "用户实体")
public class User implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名 - 必填，长度3-50个字符
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    /**
     * 年龄 - 可选，范围1-150
     */
    @Min(value = 1, message = "年龄不能小于1岁")
    @Max(value = 150, message = "年龄不能大于150岁")
    private Integer age;

    /**
     * 邮箱 - 可选，但格式必须正确
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 密码 - 必填，长度6-100个字符
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

//    @Schema(description = "密码盐值")
    private String salt;

    /**
     * 昵称 - 可选，长度1-50个字符
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 头像URL - 可选
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatar;

    /**
     * 手机号 - 可选，但格式必须正确
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 性别 - 可选，0-女，1-男
     */
    @Min(value = 0, message = "性别值不正确")
    @Max(value = 1, message = "性别值不正确")
    private Integer gender;

//    @Schema(description = "生日", example = "1990-01-01")
    private LocalDate birthday;

//    @Schema(description = "地址")
    private String address;

//    @Schema(description = "个人简介")
    private String bio;

//    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

//    @Schema(description = "最后登录IP")
    private String lastLoginIp;

//    @Schema(description = "登录次数", example = "10")
    private Integer loginCount;

//    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

//    @Schema(description = "邮箱是否验证：0-未验证，1-已验证", example = "1")
    private Integer emailVerified;

//    @Schema(description = "手机是否验证：0-未验证，1-已验证", example = "1")
    private Integer phoneVerified;

//    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

//    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    private Long createUser;
    private Long updateUser;
    private Integer version;
    private Integer deleted;

}
