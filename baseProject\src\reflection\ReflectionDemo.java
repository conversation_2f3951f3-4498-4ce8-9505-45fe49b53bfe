package reflection;

import java.lang.annotation.*;
import java.lang.reflect.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Java反射演示程序
 * 展示反射的各种用法和应用场景
 */
public class ReflectionDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java反射演示 ===");
        
        try {
            // 1. Class对象演示
            demonstrateClassObject();
            
            // 2. 字段反射演示
            demonstrateFieldReflection();
            
            // 3. 方法反射演示
            demonstrateMethodReflection();
            
            // 4. 构造函数反射演示
            demonstrateConstructorReflection();
            
            // 5. 注解反射演示
            demonstrateAnnotationReflection();
            
            // 6. 实际应用演示
            demonstratePracticalApplications();
            
        } catch (Exception e) {
            System.err.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Class对象演示
     */
    public static void demonstrateClassObject() throws Exception {
        System.out.println("\n--- Class对象演示 ---");
        
        // 获取Class对象的三种方式
        Class<String> clazz1 = String.class;
        Class<?> clazz2 = "Hello".getClass();
        Class<?> clazz3 = Class.forName("java.lang.String");
        
        System.out.println("三种方式获取的Class对象是否相同: " + (clazz1 == clazz2 && clazz2 == clazz3));
        
        // 类信息
        System.out.println("类名: " + clazz1.getName());
        System.out.println("简单类名: " + clazz1.getSimpleName());
        System.out.println("包名: " + clazz1.getPackage().getName());
        System.out.println("父类: " + clazz1.getSuperclass().getName());
        
        // 接口信息
        Class<?>[] interfaces = clazz1.getInterfaces();
        System.out.println("实现的接口数量: " + interfaces.length);
        for (Class<?> intf : interfaces) {
            System.out.println("  接口: " + intf.getName());
        }
        
        // 修饰符
        int modifiers = clazz1.getModifiers();
        System.out.println("是否为public: " + Modifier.isPublic(modifiers));
        System.out.println("是否为final: " + Modifier.isFinal(modifiers));
        
        // 数组类型
        int[] intArray = new int[5];
        Class<?> arrayClass = intArray.getClass();
        System.out.println("数组类名: " + arrayClass.getName());
        System.out.println("是否为数组: " + arrayClass.isArray());
        System.out.println("组件类型: " + arrayClass.getComponentType());
    }
    
    /**
     * 字段反射演示
     */
    public static void demonstrateFieldReflection() throws Exception {
        System.out.println("\n--- 字段反射演示 ---");
        
        Student student = new Student("Alice", 20, "<EMAIL>");
        Class<?> clazz = student.getClass();
        
        // 获取所有字段
        Field[] fields = clazz.getDeclaredFields();
        System.out.println("字段数量: " + fields.length);
        
        for (Field field : fields) {
            System.out.println("字段名: " + field.getName());
            System.out.println("字段类型: " + field.getType().getName());
            System.out.println("修饰符: " + Modifier.toString(field.getModifiers()));
            
            // 访问字段值
            field.setAccessible(true);
            Object value = field.get(student);
            System.out.println("字段值: " + value);
            System.out.println("---");
        }
        
        // 修改字段值
        Field nameField = clazz.getDeclaredField("name");
        nameField.setAccessible(true);
        nameField.set(student, "Bob");
        System.out.println("修改后的学生信息: " + student);
        
        // 静态字段
        Field countField = clazz.getDeclaredField("count");
        countField.setAccessible(true);
        System.out.println("学生总数: " + countField.get(null));
    }
    
    /**
     * 方法反射演示
     */
    public static void demonstrateMethodReflection() throws Exception {
        System.out.println("\n--- 方法反射演示 ---");
        
        Calculator calculator = new Calculator();
        Class<?> clazz = calculator.getClass();
        
        // 获取所有方法
        Method[] methods = clazz.getDeclaredMethods();
        System.out.println("方法数量: " + methods.length);
        
        for (Method method : methods) {
            System.out.println("方法名: " + method.getName());
            System.out.println("返回类型: " + method.getReturnType().getName());
            System.out.println("参数类型: " + Arrays.toString(method.getParameterTypes()));
            System.out.println("---");
        }
        
        // 调用public方法
        Method addMethod = clazz.getMethod("add", int.class, int.class);
        Object result = addMethod.invoke(calculator, 10, 5);
        System.out.println("10 + 5 = " + result);
        
        // 调用private方法
        Method subtractMethod = clazz.getDeclaredMethod("subtract", int.class, int.class);
        subtractMethod.setAccessible(true);
        Object subtractResult = subtractMethod.invoke(calculator, 10, 5);
        System.out.println("10 - 5 = " + subtractResult);
        
        // 调用静态方法
        Method multiplyMethod = clazz.getMethod("multiply", int.class, int.class);
        Object multiplyResult = multiplyMethod.invoke(null, 10, 5);
        System.out.println("10 * 5 = " + multiplyResult);
    }
    
    /**
     * 构造函数反射演示
     */
    public static void demonstrateConstructorReflection() throws Exception {
        System.out.println("\n--- 构造函数反射演示 ---");
        
        Class<?> clazz = Student.class;
        
        // 获取所有构造函数
        Constructor<?>[] constructors = clazz.getConstructors();
        System.out.println("构造函数数量: " + constructors.length);
        
        for (Constructor<?> constructor : constructors) {
            System.out.println("构造函数参数: " + Arrays.toString(constructor.getParameterTypes()));
        }
        
        // 使用默认构造函数
        Constructor<?> defaultConstructor = clazz.getConstructor();
        Object student1 = defaultConstructor.newInstance();
        System.out.println("默认构造函数创建: " + student1);
        
        // 使用参数构造函数
        Constructor<?> paramConstructor = clazz.getConstructor(String.class, int.class, String.class);
        Object student2 = paramConstructor.newInstance("Charlie", 22, "<EMAIL>");
        System.out.println("参数构造函数创建: " + student2);
    }
    
    /**
     * 注解反射演示
     */
    public static void demonstrateAnnotationReflection() throws Exception {
        System.out.println("\n--- 注解反射演示 ---");
        
        Class<?> clazz = AnnotatedStudent.class;
        
        // 类注解
        if (clazz.isAnnotationPresent(Entity.class)) {
            Entity entity = clazz.getAnnotation(Entity.class);
            System.out.println("实体名称: " + entity.name());
            System.out.println("表名: " + entity.tableName());
        }
        
        // 字段注解
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            System.out.println("字段: " + field.getName());
            
            if (field.isAnnotationPresent(Column.class)) {
                Column column = field.getAnnotation(Column.class);
                System.out.println("  列名: " + column.name());
                System.out.println("  是否可空: " + column.nullable());
            }
            
            if (field.isAnnotationPresent(Required.class)) {
                Required required = field.getAnnotation(Required.class);
                System.out.println("  必填字段: " + required.message());
            }
        }
        
        // 方法注解
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (method.isAnnotationPresent(Transactional.class)) {
                Transactional tx = method.getAnnotation(Transactional.class);
                System.out.println("事务方法: " + method.getName());
                System.out.println("  只读: " + tx.readOnly());
                System.out.println("  超时: " + tx.timeout());
            }
        }
    }
    
    /**
     * 实际应用演示
     */
    public static void demonstratePracticalApplications() throws Exception {
        System.out.println("\n--- 实际应用演示 ---");
        
        // 1. 对象属性复制
        Student source = new Student("David", 25, "<EMAIL>");
        Student target = new Student();
        
        copyProperties(source, target);
        System.out.println("属性复制结果: " + target);
        
        // 2. 简单的依赖注入
        SimpleContainer container = new SimpleContainer();
        container.register(EmailServiceDemo.class, new EmailServiceDemoImpl());
        
        NotificationService notificationService = new NotificationServiceImpl();
        container.inject(notificationService);
        
        notificationService.sendNotification("Hello World!");
        
        // 3. 对象序列化为Map
        Map<String, Object> studentMap = objectToMap(source);
        System.out.println("对象转Map: " + studentMap);
        
        // 4. 从Map创建对象
        Student fromMap = mapToObject(studentMap, Student.class);
        System.out.println("Map转对象: " + fromMap);
    }
    
    /**
     * 对象属性复制
     */
    public static void copyProperties(Object source, Object target) throws Exception {
        Class<?> sourceClass = source.getClass();
        Class<?> targetClass = target.getClass();
        
        Field[] sourceFields = sourceClass.getDeclaredFields();
        Field[] targetFields = targetClass.getDeclaredFields();
        
        Map<String, Field> targetFieldMap = new HashMap<>();
        for (Field field : targetFields) {
            targetFieldMap.put(field.getName(), field);
        }
        
        for (Field sourceField : sourceFields) {
            Field targetField = targetFieldMap.get(sourceField.getName());
            
            if (targetField != null && 
                targetField.getType().isAssignableFrom(sourceField.getType())) {
                
                sourceField.setAccessible(true);
                targetField.setAccessible(true);
                
                Object value = sourceField.get(source);
                targetField.set(target, value);
            }
        }
    }
    
    /**
     * 对象转Map
     */
    public static Map<String, Object> objectToMap(Object obj) throws Exception {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value != null) {
                map.put(field.getName(), value);
            }
        }
        
        return map;
    }
    
    /**
     * Map转对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) throws Exception {
        T instance = clazz.newInstance();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            Object value = map.get(field.getName());
            if (value != null && field.getType().isAssignableFrom(value.getClass())) {
                field.setAccessible(true);
                field.set(instance, value);
            }
        }
        
        return instance;
    }
}

// 简单的依赖注入容器
class SimpleContainer {
    private final Map<Class<?>, Object> instances = new ConcurrentHashMap<>();
    
    public <T> void register(Class<T> type, T instance) {
        instances.put(type, instance);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getInstance(Class<T> type) {
        return (T) instances.get(type);
    }
    
    public void inject(Object target) throws Exception {
        Class<?> clazz = target.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(InjectDep.class)) {
                Object dependency = getInstance(field.getType());
                if (dependency != null) {
                    field.setAccessible(true);
                    field.set(target, dependency);
                }
            }
        }
    }
}

// 依赖注入注解
@interface InjectDep {}

// 示例服务接口和实现
interface EmailServiceDemo {
    void sendEmail(String message);
}

class EmailServiceDemoImpl implements EmailServiceDemo {
    @Override
    public void sendEmail(String message) {
        System.out.println("发送邮件: " + message);
    }
}

interface NotificationService {
    void sendNotification(String message);
}

class NotificationServiceImpl implements NotificationService {

    @InjectDep
    private EmailServiceDemo emailService;

    @Override
    public void sendNotification(String message) {
        System.out.println("发送通知: " + message);
        if (emailService != null) {
            emailService.sendEmail(message);
        }
    }
}
