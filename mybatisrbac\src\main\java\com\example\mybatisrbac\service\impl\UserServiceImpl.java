package com.example.mybatisrbac.service.impl;

import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.common.ResultCode;
import com.example.mybatisrbac.dto.UserQueryDTO;
import com.example.mybatisrbac.entity.Role;
import com.example.mybatisrbac.entity.User;
import com.example.mybatisrbac.exception.BusinessException;
import com.example.mybatisrbac.mapper.UserMapper;
import com.example.mybatisrbac.service.UserService;
import com.example.mybatisrbac.util.FieldMappingUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 用户服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public PageResult<User> getUserList(UserQueryDTO queryDTO) {
        try {
            // 查询总数
            Long total = userMapper.countByCondition(queryDTO);
            
            if (total == 0) {
                String message = hasQueryConditions(queryDTO) ? 
                    "未找到符合条件的用户数据" : "暂无用户数据";
                return PageResult.success(message, List.of(), 
                        queryDTO.getCurrent(), queryDTO.getSize(), 0L);
            }
            
            // 计算偏移量
            Long offset = (queryDTO.getCurrent() - 1) * queryDTO.getSize();
            
            // 处理排序字段映射
            String dbSortField = FieldMappingUtil.getUserDbFieldName(queryDTO.getSortField());
            String sortOrder = "asc".equalsIgnoreCase(queryDTO.getSortOrder()) ? "ASC" : "DESC";
            
            // 分页查询数据
            List<User> users = userMapper.selectByCondition(queryDTO, offset, queryDTO.getSize(), 
                    dbSortField, sortOrder);
            
            return PageResult.success("用户列表查询成功", users, 
                    queryDTO.getCurrent(), queryDTO.getSize(), total);
                    
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "查询用户列表失败：" + e.getMessage());
        }
    }

    @Override
    public User getUserById(Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }
            return user;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "查询用户失败：" + e.getMessage());
        }
    }

    @Override
    public User getUserByUsername(String username) {
        try {
            return userMapper.selectByUsername(username);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "查询用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        try {
            // 检查用户名是否已存在
            if (userMapper.checkUsernameExists(user.getUsername(), 0L) > 0) {
                throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "用户名已存在");
            }
            
            // 检查邮箱是否已存在
            if (user.getEmail() != null && 
                userMapper.checkEmailExists(user.getEmail(), 0L) > 0) {
                throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "邮箱已存在");
            }
            
            // 设置创建时间和更新时间
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            user.setCreateBy(1L); // 实际项目中从当前登录用户获取
            user.setUpdateBy(1L);
            
            // 插入用户
            int result = userMapper.insert(user);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "创建用户失败");
            }
            
            return user;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "创建用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(Long id, User user) {
        try {
            // 检查用户是否存在
            User existingUser = userMapper.selectById(id);
            if (existingUser == null) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }
            
            // 检查用户名是否已存在（排除当前用户）
            if (user.getUsername() != null && 
                userMapper.checkUsernameExists(user.getUsername(), id) > 0) {
                throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "用户名已存在");
            }
            
            // 检查邮箱是否已存在（排除当前用户）
            if (user.getEmail() != null && 
                userMapper.checkEmailExists(user.getEmail(), id) > 0) {
                throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "邮箱已存在");
            }
            
            // 设置更新信息
            user.setId(id);
            user.setUpdateTime(new Date());
            user.setUpdateBy(1L); // 实际项目中从当前登录用户获取
            
            // 更新用户
            int result = userMapper.updateById(user);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "更新用户失败");
            }
            
            // 返回更新后的用户信息
            User updatedUser = userMapper.selectById(id);
            return updatedUser;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "更新用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(id);
            if (user == null) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }
            
            // 删除用户
            int result = userMapper.deleteById(id);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "删除用户失败");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "删除用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteUsers(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "请选择要删除的用户");
            }
            
            // 批量删除用户
            int result = userMapper.deleteByIds(ids);
            if (result != ids.size()) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "部分用户删除失败");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "批量删除用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(Long id, String newPassword) {
        try {
            User user = new User();
            user.setId(id);
            user.setPassword(newPassword); // 实际项目中需要加密
            user.setUpdateTime(new Date());
            user.setUpdateBy(1L);
            
            int result = userMapper.updateById(user);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "重置密码失败");
            }
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "重置密码失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long id, Integer status) {
        try {
            User user = new User();
            user.setId(id);
            user.setStatus(status);
            user.setUpdateTime(new Date());
            user.setUpdateBy(1L);
            
            int result = userMapper.updateById(user);
            if (result <= 0) {
                throw new BusinessException(ResultCode.DATABASE_ERROR, "更新用户状态失败");
            }
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "更新用户状态失败：" + e.getMessage());
        }
    }

    @Override
    public List<Role> getUserRoles(Long userId) {
        try {
            return userMapper.selectUserRoles(userId);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "查询用户角色失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignUserRoles(Long userId, List<Long> roleIds) {
        // TODO: 实现用户角色分配逻辑
        // 1. 删除用户现有角色
        // 2. 插入新的用户角色关联
        throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "功能暂未实现");
    }

    /**
     * 检查是否有查询条件
     */
    private boolean hasQueryConditions(UserQueryDTO queryDTO) {
        return (queryDTO.getUsername() != null && !queryDTO.getUsername().trim().isEmpty()) ||
               (queryDTO.getEmail() != null && !queryDTO.getEmail().trim().isEmpty()) ||
               (queryDTO.getPhone() != null && !queryDTO.getPhone().trim().isEmpty()) ||
               (queryDTO.getRealName() != null && !queryDTO.getRealName().trim().isEmpty()) ||
               (queryDTO.getStatus() != null);
    }
}
