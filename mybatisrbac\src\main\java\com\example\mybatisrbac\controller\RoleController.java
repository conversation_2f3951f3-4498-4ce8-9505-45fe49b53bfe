package com.example.mybatisrbac.controller;

import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.common.Result;
import com.example.mybatisrbac.dto.RoleCreateDTO;
import com.example.mybatisrbac.dto.RoleQueryDTO;
import com.example.mybatisrbac.dto.RoleUpdateDTO;
import com.example.mybatisrbac.entity.Role;
import com.example.mybatisrbac.service.RoleService;
import com.example.mybatisrbac.util.BeanUtil;
import com.example.mybatisrbac.util.ResponseUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import org.springframework.validation.annotation.Validated;
import com.example.mybatisrbac.validation.ValidStatus;
import com.example.mybatisrbac.validation.ValidSortField;
import java.util.List;

/**
 * 角色控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Tag(name = "角色管理", description = "角色相关的增删改查操作")
@RestController
@RequestMapping("/api/roles")
@Validated
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 分页查询角色列表
     */
    @Operation(summary = "分页查询角色", description = "根据条件分页查询角色列表")
    @GetMapping
    public PageResult<Role> getRoleList(
            @Parameter(description = "当前页码，必须大于0", example = "1")
            @RequestParam(defaultValue = "1")
            @Min(value = 1, message = "当前页码必须大于0")
            @Max(value = 10000, message = "当前页码不能超过10000")
            Long current,

            @Parameter(description = "每页大小，范围1-100", example = "10")
            @RequestParam(defaultValue = "10")
            @Min(value = 1, message = "每页大小必须大于0")
            @Max(value = 100, message = "每页大小不能超过100")
            Long size,

            @Parameter(description = "角色名称关键字，长度不超过50")
            @RequestParam(required = false)
            @Size(max = 50, message = "角色名称关键字长度不能超过50")
            String roleName,

            @Parameter(description = "角色编码关键字，长度不超过50")
            @RequestParam(required = false)
            @Size(max = 50, message = "角色编码关键字长度不能超过50")
            @Pattern(regexp = "^[A-Z_]*$", message = "角色编码只能包含大写字母和下划线")
            String roleCode,

            @Parameter(description = "角色状态：0-禁用，1-启用")
            @RequestParam(required = false)
            @ValidStatus
            Integer status,

            @Parameter(description = "排序字段，可选值：id,roleName,roleCode,status,createTime,updateTime")
            @RequestParam(defaultValue = "createTime")
            @ValidSortField(entityType = ValidSortField.EntityType.ROLE)
            String sortField,

            @Parameter(description = "排序方向：asc-升序，desc-降序")
            @RequestParam(defaultValue = "desc")
            @Pattern(regexp = "^(asc|desc)$", message = "排序方向只能为asc或desc")
            String sortOrder) {

        // 构建查询条件
        RoleQueryDTO queryDTO = new RoleQueryDTO();
        queryDTO.setCurrent(current);
        queryDTO.setSize(size);
        queryDTO.setRoleName(roleName);
        queryDTO.setRoleCode(roleCode);
        queryDTO.setStatus(status);
        queryDTO.setSortField(sortField);
        queryDTO.setSortOrder(sortOrder);

        return roleService.getRoleList(queryDTO);
    }

    /**
     * 根据ID查询角色
     */
    @Operation(summary = "查询角色详情", description = "根据角色ID查询角色详细信息")
    @GetMapping("/{id}")
    public Result<Role> getRoleById(
            @Parameter(description = "角色ID", example = "1")
            @PathVariable Long id) {

        Role role = roleService.getRoleById(id);
        return ResponseUtil.success("角色查询成功", role);
    }

    /**
     * 创建角色
     */
    @Operation(summary = "创建角色", description = "创建新角色")
    @PostMapping
    public Result<Role> createRole(@Valid @RequestBody RoleCreateDTO createDTO) {

        // 转换 DTO 为 Entity
        Role role = BeanUtil.createDTOToRole(createDTO);

        // 创建角色
        Role createdRole = roleService.createRole(role);

        return ResponseUtil.success("角色创建成功", createdRole);
    }

    /**
     * 更新角色
     */
    @Operation(summary = "更新角色", description = "更新角色信息")
    @PutMapping("/{id}")
    public Result<Role> updateRole(
            @Parameter(description = "角色ID", example = "1")
            @PathVariable Long id,
            @Valid @RequestBody RoleUpdateDTO updateDTO) {

        // 先查询现有角色
        Role existingRole = roleService.getRoleById(id);
        if (existingRole == null) {
            return ResponseUtil.error("角色不存在");
        }

        // 转换为 Entity
        Role role = new Role();
        role.setId(id);
        BeanUtil.updateDTOToRole(updateDTO, role);

        // 更新角色
        Role updatedRole = roleService.updateRole(id, role);

        return ResponseUtil.success("角色更新成功", updatedRole);
    }

    /**
     * 删除角色
     */
    @Operation(summary = "删除角色", description = "根据角色ID删除角色")
    @DeleteMapping("/{id}")
    public Result<Void> deleteRole(
            @Parameter(description = "角色ID", example = "1")
            @PathVariable Long id) {

        roleService.deleteRole(id);
        return ResponseUtil.success("角色删除成功");
    }

    /**
     * 批量删除角色
     */
    @Operation(summary = "批量删除角色", description = "根据角色ID列表批量删除角色")
    @DeleteMapping
    public Result<Void> batchDeleteRoles(@RequestBody List<Long> ids) {

        if (ids == null || ids.isEmpty()) {
            return ResponseUtil.error("请选择要删除的角色");
        }

        roleService.batchDeleteRoles(ids);
        return ResponseUtil.success("批量删除成功，共删除 " + ids.size() + " 个角色");
    }

    /**
     * 测试数据库连接和数据
     */
    @Operation(summary = "测试角色数据", description = "测试数据库连接和角色数据")
    @GetMapping("/test")
    public Result<String> testRoleData() {
        try {
            // 简单查询总数
            RoleQueryDTO queryDTO = new RoleQueryDTO();
            queryDTO.setCurrent(1L);
            queryDTO.setSize(10L);

            PageResult<Role> result = roleService.getRoleList(queryDTO);

            return ResponseUtil.success("数据库连接正常",
                    "总记录数: " + result.getData().getTotal() +
                    ", 当前页记录数: " + result.getData().getRecords().size());
        } catch (Exception e) {
            return ResponseUtil.error("数据库连接失败: " + e.getMessage());
        }
    }
}
