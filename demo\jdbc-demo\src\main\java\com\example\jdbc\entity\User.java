package com.example.jdbc.entity;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户实体类
 */
public class User {
    
    private Long id;
    private String username;
    private String email;
    private String phone;
    private Integer age;
    private String address;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 无参构造函数
    public User() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // 带参构造函数
    public User(String username, String email) {
        this();
        this.username = username;
        this.email = email;
    }
    
    public User(String username, String email, String phone, Integer age) {
        this(username, email);
        this.phone = phone;
        this.age = age;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // equals和hashCode方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return Objects.equals(id, user.id) &&
               Objects.equals(username, user.username) &&
               Objects.equals(email, user.email);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, username, email);
    }
    
    // toString方法
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", age=" + age +
                ", address='" + address + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
    
    /**
     * 创建用户的建造者模式
     */
    public static class Builder {
        private User user;
        
        public Builder() {
            this.user = new User();
        }
        
        public Builder username(String username) {
            user.setUsername(username);
            return this;
        }
        
        public Builder email(String email) {
            user.setEmail(email);
            return this;
        }
        
        public Builder phone(String phone) {
            user.setPhone(phone);
            return this;
        }
        
        public Builder age(Integer age) {
            user.setAge(age);
            return this;
        }
        
        public Builder address(String address) {
            user.setAddress(address);
            return this;
        }
        
        public User build() {
            return user;
        }
    }
    
    public static Builder builder() {
        return new Builder();
    }
}
