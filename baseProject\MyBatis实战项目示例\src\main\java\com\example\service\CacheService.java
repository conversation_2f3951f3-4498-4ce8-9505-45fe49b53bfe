package com.example.service;

import com.example.entity.User;
import com.example.vo.UserDetailVO;
import com.example.vo.UserStatsVO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;

import java.util.List;
import java.util.Map;

/**
 * 缓存服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface CacheService {
    
    /**
     * 获取用户详情（包含角色和权限）
     */
    UserDetailVO getUserDetail(Long userId);
    
    /**
     * 获取用户角色列表
     */
    List<String> getUserRoles(Long userId);
    
    /**
     * 获取用户权限列表
     */
    List<String> getUserPermissions(Long userId);
    
    /**
     * 获取用户统计信息
     */
    UserStatsVO getUserStats();
    
    /**
     * 获取活跃用户列表
     */
    List<User> getActiveUsers();
    
    /**
     * 获取系统配置
     */
    String getSystemConfig(String key);
    
    /**
     * 更新系统配置
     */
    void updateSystemConfig(String key, String value);
    
    /**
     * 批量获取用户信息
     */
    Map<Long, User> batchGetUsers(List<Long> userIds);
    
    /**
     * 预热用户缓存
     */
    void warmupUserCache(List<Long> userIds);
    
    /**
     * 清除用户相关的所有缓存
     */
    void evictUserCache(Long userId);
    
    /**
     * 清除所有缓存
     */
    void evictAllCache();
    
    /**
     * 获取缓存统计信息
     */
    Map<String, Object> getCacheStatistics();
}
