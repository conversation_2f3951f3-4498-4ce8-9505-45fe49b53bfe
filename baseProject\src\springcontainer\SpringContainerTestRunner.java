package springcontainer;

import java.util.Scanner;

/**
 * Spring容器原理测试运行器
 * 综合演示Spring容器、IoC、依赖注入的核心概念
 */
public class SpringContainerTestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    Spring容器与依赖注入原理演示");
        System.out.println("========================================");
        
        try {
            // 1. Spring容器概述
            demonstrateSpringContainer();
            
            // 2. 控制反转(IoC)原理
            demonstrateIoC();
            
            // 3. 依赖注入(DI)详解
            demonstrateDependencyInjection();
            
            // 4. Bean生命周期
            demonstrateBeanLifecycle();
            
            // 5. 循环依赖问题
            demonstrateCircularDependency();
            
            // 6. 实际应用示例
            demonstratePracticalExample();
            
            System.out.println("\n========================================");
            System.out.println("           演示完成");
            System.out.println("========================================");
            
            showSummary();
            
        } catch (Exception e) {
            System.out.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Spring容器概述演示
     */
    private static void demonstrateSpringContainer() {
        System.out.println("\n=== 1. Spring容器概述 ===");
        
        System.out.println("Spring容器的核心功能:");
        System.out.println("✓ 对象创建 - 根据配置创建Bean实例");
        System.out.println("✓ 依赖注入 - 自动装配Bean之间的依赖关系");
        System.out.println("✓ 生命周期管理 - 管理Bean的创建、初始化、销毁");
        System.out.println("✓ 作用域管理 - 控制Bean的作用域（单例、原型等）");
        System.out.println("✓ AOP支持 - 提供面向切面编程支持");
        
        System.out.println("\nSpring容器类型:");
        System.out.println("• BeanFactory - 基础容器接口，提供基本的IoC功能");
        System.out.println("• ApplicationContext - 高级容器接口，提供更多企业级功能");
        System.out.println("  - ClassPathXmlApplicationContext - 从类路径加载XML配置");
        System.out.println("  - AnnotationConfigApplicationContext - 基于注解的配置");
        System.out.println("  - WebApplicationContext - Web环境的应用上下文");
        
        System.out.println("\n容器启动流程:");
        System.out.println("1. 创建容器实例");
        System.out.println("2. 加载配置信息");
        System.out.println("3. 解析Bean定义");
        System.out.println("4. 实例化Bean");
        System.out.println("5. 依赖注入");
        System.out.println("6. 初始化Bean");
        System.out.println("7. 容器就绪");
    }
    
    /**
     * 控制反转(IoC)原理演示
     */
    private static void demonstrateIoC() {
        System.out.println("\n=== 2. 控制反转(IoC)原理 ===");
        
        System.out.println("什么是控制反转?");
        System.out.println("控制反转是一种设计原则，将对象的创建和依赖关系的管理");
        System.out.println("从应用程序代码中移除，交给外部容器来处理。");
        
        System.out.println("\n传统方式 vs IoC方式:");
        
        System.out.println("\n【传统方式】- 对象自己控制依赖:");
        System.out.println("public class UserService {");
        System.out.println("    private UserRepository repo = new UserRepositoryImpl(); // 硬编码");
        System.out.println("    private EmailService email = new EmailServiceImpl();   // 紧耦合");
        System.out.println("}");
        
        System.out.println("\n【IoC方式】- 容器控制依赖:");
        System.out.println("@Service");
        System.out.println("public class UserService {");
        System.out.println("    private final UserRepository repo;");
        System.out.println("    private final EmailService email;");
        System.out.println("    ");
        System.out.println("    public UserService(UserRepository repo, EmailService email) {");
        System.out.println("        this.repo = repo;     // 依赖由容器注入");
        System.out.println("        this.email = email;   // 松耦合");
        System.out.println("    }");
        System.out.println("}");
        
        System.out.println("\nIoC的优势:");
        System.out.println("✓ 降低耦合度 - 对象不直接依赖具体实现");
        System.out.println("✓ 提高可测试性 - 易于注入Mock对象");
        System.out.println("✓ 增强可维护性 - 配置集中管理");
        System.out.println("✓ 支持多态 - 可以轻松切换实现");
        
        System.out.println("\nIoC的实现方式:");
        System.out.println("• 依赖查找 (Dependency Lookup) - 主动从容器查找");
        System.out.println("• 依赖注入 (Dependency Injection) - 被动接收注入 ⭐推荐");
    }
    
    /**
     * 依赖注入(DI)详解演示
     */
    private static void demonstrateDependencyInjection() {
        System.out.println("\n=== 3. 依赖注入(DI)详解 ===");
        
        System.out.println("依赖注入的三种方式:");
        
        System.out.println("\n【1. 构造函数注入】⭐推荐:");
        System.out.println("@Service");
        System.out.println("public class UserService {");
        System.out.println("    private final UserRepository userRepository;");
        System.out.println("    ");
        System.out.println("    public UserService(UserRepository userRepository) {");
        System.out.println("        this.userRepository = userRepository;");
        System.out.println("    }");
        System.out.println("}");
        System.out.println("优点: 保证依赖不可变、不为null、便于测试");
        
        System.out.println("\n【2. Setter注入】:");
        System.out.println("@Service");
        System.out.println("public class UserService {");
        System.out.println("    private UserRepository userRepository;");
        System.out.println("    ");
        System.out.println("    @Autowired");
        System.out.println("    public void setUserRepository(UserRepository userRepository) {");
        System.out.println("        this.userRepository = userRepository;");
        System.out.println("    }");
        System.out.println("}");
        System.out.println("优点: 支持可选依赖、支持重新配置");
        
        System.out.println("\n【3. 字段注入】❌不推荐:");
        System.out.println("@Service");
        System.out.println("public class UserService {");
        System.out.println("    @Autowired");
        System.out.println("    private UserRepository userRepository;");
        System.out.println("}");
        System.out.println("缺点: 违反封装、难以测试、隐藏依赖");
        
        System.out.println("\n自动装配类型:");
        System.out.println("• 按类型装配 - @Autowired (默认)");
        System.out.println("• 按名称装配 - @Qualifier");
        System.out.println("• 按主要Bean装配 - @Primary");
        System.out.println("• 可选依赖 - @Autowired(required = false)");
        System.out.println("• 集合注入 - List<T>, Map<String, T>");
    }
    
    /**
     * Bean生命周期演示
     */
    private static void demonstrateBeanLifecycle() {
        System.out.println("\n=== 4. Bean生命周期 ===");
        
        System.out.println("完整的Bean生命周期:");
        System.out.println("1. 🏗️  实例化 - 调用构造函数");
        System.out.println("2. 🔧  属性注入 - 设置Bean属性");
        System.out.println("3. 📝  设置Bean名称 - BeanNameAware.setBeanName()");
        System.out.println("4. 🏭  设置Bean工厂 - BeanFactoryAware.setBeanFactory()");
        System.out.println("5. 🌍  设置应用上下文 - ApplicationContextAware.setApplicationContext()");
        System.out.println("6. ⚡  前置处理 - BeanPostProcessor.postProcessBeforeInitialization()");
        System.out.println("7. 🚀  初始化方法 - @PostConstruct");
        System.out.println("8. 🔄  初始化接口 - InitializingBean.afterPropertiesSet()");
        System.out.println("9. 🎯  自定义初始化 - init-method");
        System.out.println("10. ⚡ 后置处理 - BeanPostProcessor.postProcessAfterInitialization()");
        System.out.println("11. ✅ Bean就绪 - 可以正常使用");
        System.out.println("12. 🧹 销毁前处理 - @PreDestroy");
        System.out.println("13. 💥 销毁接口 - DisposableBean.destroy()");
        System.out.println("14. 🗑️  自定义销毁 - destroy-method");
        
        System.out.println("\nBean作用域:");
        System.out.println("• singleton - 单例模式（默认）");
        System.out.println("• prototype - 原型模式，每次获取创建新实例");
        System.out.println("• request - Web环境，每个HTTP请求一个实例");
        System.out.println("• session - Web环境，每个HTTP会话一个实例");
        System.out.println("• application - Web环境，每个ServletContext一个实例");
    }
    
    /**
     * 循环依赖问题演示
     */
    private static void demonstrateCircularDependency() {
        System.out.println("\n=== 5. 循环依赖问题 ===");
        
        System.out.println("什么是循环依赖?");
        System.out.println("两个或多个Bean相互依赖，形成闭环。");
        
        System.out.println("\n循环依赖示例:");
        System.out.println("@Service");
        System.out.println("public class ServiceA {");
        System.out.println("    @Autowired");
        System.out.println("    private ServiceB serviceB; // A依赖B");
        System.out.println("}");
        System.out.println("");
        System.out.println("@Service");
        System.out.println("public class ServiceB {");
        System.out.println("    @Autowired");
        System.out.println("    private ServiceA serviceA; // B依赖A，形成循环");
        System.out.println("}");
        
        System.out.println("\nSpring解决循环依赖的机制 - 三级缓存:");
        System.out.println("• 一级缓存 (singletonObjects) - 完全初始化的Bean");
        System.out.println("• 二级缓存 (earlySingletonObjects) - 早期Bean对象");
        System.out.println("• 三级缓存 (singletonFactories) - Bean工厂对象");
        
        System.out.println("\n解决方案:");
        System.out.println("1. 使用@Lazy注解 - 延迟初始化");
        System.out.println("2. 使用Setter注入 - 替代构造函数注入");
        System.out.println("3. 使用@PostConstruct - 初始化后设置依赖");
        System.out.println("4. 重构设计 - 引入第三个服务解耦");
        
        System.out.println("\n注意: 构造函数循环依赖无法解决，必须重构!");
    }
    
    /**
     * 实际应用示例演示
     */
    private static void demonstratePracticalExample() {
        System.out.println("\n=== 6. 实际应用示例 ===");
        
        System.out.println("典型的三层架构:");
        System.out.println("┌─────────────────┐");
        System.out.println("│   Controller    │ ← Web层，处理HTTP请求");
        System.out.println("│   @Controller   │");
        System.out.println("└─────────────────┘");
        System.out.println("         ↓");
        System.out.println("┌─────────────────┐");
        System.out.println("│    Service      │ ← 业务层，处理业务逻辑");
        System.out.println("│   @Service      │");
        System.out.println("└─────────────────┘");
        System.out.println("         ↓");
        System.out.println("┌─────────────────┐");
        System.out.println("│   Repository    │ ← 数据层，处理数据访问");
        System.out.println("│  @Repository    │");
        System.out.println("└─────────────────┘");
        
        System.out.println("\n依赖注入流程:");
        System.out.println("1. Spring容器启动");
        System.out.println("2. 扫描@Component、@Service、@Repository等注解");
        System.out.println("3. 创建Bean定义");
        System.out.println("4. 实例化Bean");
        System.out.println("5. 注入依赖关系");
        System.out.println("6. 初始化Bean");
        System.out.println("7. 应用就绪");
        
        System.out.println("\n配置方式对比:");
        System.out.println("• XML配置 - 传统方式，配置繁琐");
        System.out.println("• 注解配置 - 现代方式，简洁直观 ⭐推荐");
        System.out.println("• Java配置 - 类型安全，便于重构");
        System.out.println("• 混合配置 - 灵活组合多种方式");
    }
    
    /**
     * 显示总结信息
     */
    private static void showSummary() {
        System.out.println("\nSpring容器与依赖注入总结:");
        
        System.out.println("\n🎯 核心概念:");
        System.out.println("   • IoC容器 - 管理对象的创建和生命周期");
        System.out.println("   • 控制反转 - 将控制权交给容器");
        System.out.println("   • 依赖注入 - 容器自动装配依赖关系");
        System.out.println("   • Bean - 由Spring管理的对象");
        
        System.out.println("\n✨ 核心优势:");
        System.out.println("   • 降低耦合度 - 组件间松耦合");
        System.out.println("   • 提高可测试性 - 易于单元测试");
        System.out.println("   • 增强可维护性 - 配置集中管理");
        System.out.println("   • 支持AOP - 横切关注点分离");
        
        System.out.println("\n🛠️ 最佳实践:");
        System.out.println("   • 优先使用构造函数注入");
        System.out.println("   • 合理使用Bean作用域");
        System.out.println("   • 避免循环依赖");
        System.out.println("   • 使用条件化配置");
        System.out.println("   • 合理使用Profile");
        
        System.out.println("\n📚 学习建议:");
        System.out.println("   1. 理解IoC和DI的核心概念");
        System.out.println("   2. 掌握Bean的生命周期");
        System.out.println("   3. 熟练使用各种注解");
        System.out.println("   4. 深入理解Spring源码");
        System.out.println("   5. 在实际项目中应用");
        
        System.out.println("\n🚀 进阶方向:");
        System.out.println("   • Spring AOP - 面向切面编程");
        System.out.println("   • Spring Boot - 自动配置和起步依赖");
        System.out.println("   • Spring Cloud - 微服务架构");
        System.out.println("   • Spring Security - 安全框架");
        System.out.println("   • Spring Data - 数据访问抽象");
        
        System.out.println("\n💡 实际应用:");
        System.out.println("   • 企业级应用开发");
        System.out.println("   • 微服务架构");
        System.out.println("   • Web应用开发");
        System.out.println("   • RESTful API开发");
        System.out.println("   • 分布式系统开发");
        
        System.out.println("\n要深入学习Spring，建议:");
        System.out.println("1. 阅读Spring官方文档");
        System.out.println("2. 研究Spring源码实现");
        System.out.println("3. 实践各种应用场景");
        System.out.println("4. 关注Spring生态发展");
    }
}
