package com.example.mybatisrbac.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "角色实体")
public class Role implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID", example = "1")
    private Long id;

    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 20, message = "角色名称长度必须在2到20之间")
    @Schema(description = "角色名称", example = "管理员", required = true)
    private String roleName;

    @NotBlank(message = "角色编码不能为空")
    @Size(min = 2, max = 20, message = "角色编码长度必须在2到20之间")
    @Pattern(regexp ="^[a-zA-Z_]+$", message = "角色编码只能包含字母、数字和下划线" )
    @Schema(description = "角色编码", example = "ADMIN", required = true)
    private String roleCode;

    @Size(max = 200,message = "角色描述长度不能超过200")
    @Schema(description = "角色描述", example = "系统管理员角色")
    private String description;

    @NotNull(message = "角色状态不能为空")
    @Max(value = 1,message = "角色状态只能为0或1")
    @Min(value = 0,message = "角色状态只能为0或1")
    @Schema(description = "角色状态：0-禁用，1-启用", example = "1", required = true)
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人ID")
    private Long createBy;

    @Schema(description = "更新人ID")
    private Long updateBy;


}
