package com.example.config;

import com.example.shiro.ShiroRealm;
import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.cache.ehcache.EhCacheManager;
import org.apache.shiro.codec.Base64;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Shiro配置类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class ShiroConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(ShiroConfig.class);
    
    @Autowired
    private ShiroRealm shiroRealm;
    
    /**
     * 密码凭证匹配器
     */
    @Bean
    public HashedCredentialsMatcher hashedCredentialsMatcher() {
        HashedCredentialsMatcher credentialsMatcher = new HashedCredentialsMatcher();
        // 设置加密算法
        credentialsMatcher.setHashAlgorithmName("MD5");
        // 设置加密次数
        credentialsMatcher.setHashIterations(1);
        // 设置存储凭证十六进制编码
        credentialsMatcher.setStoredCredentialsHexEncoded(true);
        
        logger.info("密码凭证匹配器配置完成");
        return credentialsMatcher;
    }
    
    /**
     * 自定义Realm配置
     */
    @Bean
    public ShiroRealm myShiroRealm() {
        ShiroRealm realm = new ShiroRealm();
        // 设置密码凭证匹配器
        realm.setCredentialsMatcher(hashedCredentialsMatcher());
        // 启用缓存
        realm.setCachingEnabled(true);
        // 启用认证缓存
        realm.setAuthenticationCachingEnabled(true);
        realm.setAuthenticationCacheName("authenticationCache");
        // 启用授权缓存
        realm.setAuthorizationCachingEnabled(true);
        realm.setAuthorizationCacheName("authorizationCache");
        
        logger.info("自定义Realm配置完成");
        return realm;
    }
    
    /**
     * EhCache缓存管理器
     */
    @Bean
    public EhCacheManager ehCacheManager() {
        EhCacheManager cacheManager = new EhCacheManager();
        cacheManager.setCacheManagerConfigFile("classpath:ehcache.xml");
        
        logger.info("EhCache缓存管理器配置完成");
        return cacheManager;
    }
    
    /**
     * 会话管理器
     */
    @Bean
    public SessionManager sessionManager() {
        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        // 设置session过期时间（毫秒）
        sessionManager.setGlobalSessionTimeout(1800000); // 30分钟
        // 删除过期的session
        sessionManager.setDeleteInvalidSessions(true);
        // 设置sessionId
        sessionManager.setSessionIdCookieEnabled(true);
        sessionManager.setSessionIdCookie(sessionIdCookie());
        // 设置sessionId的URL重写
        sessionManager.setSessionIdUrlRewritingEnabled(false);
        
        logger.info("会话管理器配置完成");
        return sessionManager;
    }
    
    /**
     * SessionId Cookie配置
     */
    @Bean
    public SimpleCookie sessionIdCookie() {
        SimpleCookie cookie = new SimpleCookie("JSESSIONID");
        cookie.setHttpOnly(true);
        cookie.setMaxAge(-1); // 浏览器关闭时失效
        cookie.setPath("/");
        
        return cookie;
    }
    
    /**
     * 记住我管理器
     */
    @Bean
    public CookieRememberMeManager rememberMeManager() {
        CookieRememberMeManager cookieRememberMeManager = new CookieRememberMeManager();
        cookieRememberMeManager.setCookie(rememberMeCookie());
        // 设置加密密钥
        cookieRememberMeManager.setCipherKey(Base64.decode("4AvVhmFLUs0KTA3Kprsdag=="));
        
        logger.info("记住我管理器配置完成");
        return cookieRememberMeManager;
    }
    
    /**
     * 记住我Cookie配置
     */
    @Bean
    public SimpleCookie rememberMeCookie() {
        SimpleCookie cookie = new SimpleCookie("rememberMe");
        cookie.setHttpOnly(true);
        cookie.setMaxAge(2592000); // 30天
        cookie.setPath("/");
        
        return cookie;
    }
    
    /**
     * 安全管理器
     */
    @Bean
    public SecurityManager securityManager() {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        // 设置realm
        securityManager.setRealm(myShiroRealm());
        // 设置缓存管理器
        securityManager.setCacheManager(ehCacheManager());
        // 设置会话管理器
        securityManager.setSessionManager(sessionManager());
        // 设置记住我管理器
        securityManager.setRememberMeManager(rememberMeManager());
        
        logger.info("安全管理器配置完成");
        return securityManager;
    }
    
    /**
     * Shiro过滤器工厂
     */
    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        
        // 设置登录页面
        shiroFilterFactoryBean.setLoginUrl("/login");
        // 设置登录成功后跳转页面
        shiroFilterFactoryBean.setSuccessUrl("/index");
        // 设置未授权页面
        shiroFilterFactoryBean.setUnauthorizedUrl("/unauthorized");
        
        // 设置过滤器链
        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
        
        // 静态资源不拦截
        filterChainDefinitionMap.put("/css/**", "anon");
        filterChainDefinitionMap.put("/js/**", "anon");
        filterChainDefinitionMap.put("/images/**", "anon");
        filterChainDefinitionMap.put("/fonts/**", "anon");
        filterChainDefinitionMap.put("/favicon.ico", "anon");
        
        // 公开接口不拦截
        filterChainDefinitionMap.put("/api/public/**", "anon");
        filterChainDefinitionMap.put("/actuator/health", "anon");
        
        // 登录相关不拦截
        filterChainDefinitionMap.put("/login", "anon");
        filterChainDefinitionMap.put("/api/auth/login", "anon");
        filterChainDefinitionMap.put("/api/auth/logout", "anon");
        filterChainDefinitionMap.put("/api/auth/captcha", "anon");
        
        // 需要记住我或认证
        filterChainDefinitionMap.put("/", "user");
        filterChainDefinitionMap.put("/index", "user");
        
        // 需要认证
        filterChainDefinitionMap.put("/api/**", "authc");
        filterChainDefinitionMap.put("/system/**", "authc");
        
        // 需要特定权限
        filterChainDefinitionMap.put("/system/user/**", "authc,perms[system:user:view]");
        filterChainDefinitionMap.put("/system/role/**", "authc,perms[system:role:view]");
        filterChainDefinitionMap.put("/system/permission/**", "authc,perms[system:permission:view]");
        
        // 其他所有路径都需要认证
        filterChainDefinitionMap.put("/**", "authc");
        
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        
        logger.info("Shiro过滤器工厂配置完成");
        return shiroFilterFactoryBean;
    }
    
    /**
     * 开启Shiro注解支持
     */
    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor = new AuthorizationAttributeSourceAdvisor();
        authorizationAttributeSourceAdvisor.setSecurityManager(securityManager);
        
        logger.info("Shiro注解支持开启");
        return authorizationAttributeSourceAdvisor;
    }
}
