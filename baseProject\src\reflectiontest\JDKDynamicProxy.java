package reflectiontest;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;

public class JDKDynamicProxy {
    public static void main(String[] args) {
        UserService userService = new UserServiceImpl();
        UserService proxy = (UserService) java.lang.reflect.Proxy.newProxyInstance(
                userService.getClass().getClassLoader(),
                userService.getClass().getInterfaces(),
                new LoggingInvocationHandler(userService)
        );
        proxy.createUser("Alice");
        proxy.getUserById(1L);
        proxy.updateUser(1L, "Alice Updated");
        proxy.deleteUser(1L);
    }

}

interface UserService {
    void createUser(String name);

    String getUserById(Long id);

    void updateUser(Long id, String name);

    void deleteUser(Long id);
}

class UserServiceImpl implements UserService {

    @Override
    public void createUser(String name) {
        System.out.println("创建用户：" + name);
    }

    @Override
    public String getUserById(Long id) {
        System.out.println("获取用户：" + id);
        return "user"+id;
    }

    @Override
    public void updateUser(Long id, String name) {
        System.out.println("更新用户："+id+"-》"+name);
    }

    @Override
    public void deleteUser(Long id) {
        System.out.println("删除用户："+id);
    }
}
//处理器
class LoggingInvocationHandler implements InvocationHandler {
    private final Object target;

    public LoggingInvocationHandler(Object target) {
        this.target = target;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        long startTime = System.currentTimeMillis();

        System.out.println("=== 方法调用开始 ===");
        System.out.println("方法名: " + method.getName());
        System.out.println("参数: " + Arrays.toString(args));
        // 调用目标方法
        try {
            Object result = method.invoke(target, args);
            long endTime = System.currentTimeMillis();
            System.out.println("执行时间: " + (endTime - startTime) + "ms");
            System.out.println("返回值: " + result);
            System.out.println("=== 方法调用结束 ===\n");
            return result;
        }
         catch (InvocationTargetException e) {
            System.out.println("方法执行异常: " + e.getCause().getMessage());
            throw e.getCause();
        }
    }
}