package com.example.redis;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class RedisDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(RedisDemoApplication.class, args);
        System.out.println("=================================");
        System.out.println("Redis Demo 模块启动成功！");
        System.out.println("访问地址: http://localhost:8081");
        System.out.println("Redis缓存功能已就绪");
        System.out.println("=================================");
    }
}
