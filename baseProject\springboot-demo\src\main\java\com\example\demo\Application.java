package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Spring Boot 主启动类
 * 
 * @SpringBootApplication 是一个组合注解，包含：
 * - @Configuration: 标识这是一个配置类
 * - @EnableAutoConfiguration: 启用自动配置
 * - @ComponentScan: 启用组件扫描
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableConfigurationProperties  // 启用配置属性
@EnableJpaAuditing             // 启用JPA审计功能
@EnableTransactionManagement   // 启用事务管理
@EnableCaching                 // 启用缓存
@EnableAsync                   // 启用异步处理
@EnableScheduling              // 启用定时任务
public class Application {
    
    public static void main(String[] args) {
        // 启动Spring Boot应用
        SpringApplication.run(Application.class, args);
        
        System.out.println("========================================");
        System.out.println("    Spring Boot Demo 启动成功！");
        System.out.println("    访问地址: http://localhost:8080");
        System.out.println("    API文档: http://localhost:8080/swagger-ui.html");
        System.out.println("    健康检查: http://localhost:8080/actuator/health");
        System.out.println("========================================");
    }
}
