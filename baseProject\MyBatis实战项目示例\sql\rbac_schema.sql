-- RBAC系统数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS rbac_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE rbac_demo;

-- 1. 用户表
CREATE TABLE sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(200) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_login_time DATETIME COMMENT '最后登录时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 角色表
CREATE TABLE sys_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 3. 权限表
CREATE TABLE sys_permission (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    resource_type VARCHAR(20) DEFAULT 'MENU' COMMENT '资源类型：MENU-菜单，BUTTON-按钮，API-接口',
    url VARCHAR(200) COMMENT '资源URL',
    method VARCHAR(10) COMMENT 'HTTP方法',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    icon VARCHAR(50) COMMENT '图标',
    description VARCHAR(200) COMMENT '权限描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 4. 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 5. 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES sys_permission(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 插入初始数据

-- 插入默认用户（密码都是123456，已加密）
INSERT INTO sys_user (username, password, email, real_name, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '系统管理员', 1),
('manager', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '部门经理', 1),
('user', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '普通用户', 1);

-- 插入默认角色
INSERT INTO sys_role (role_name, role_code, description) VALUES
('超级管理员', 'ROLE_ADMIN', '系统超级管理员，拥有所有权限'),
('部门经理', 'ROLE_MANAGER', '部门经理，拥有部门管理权限'),
('普通用户', 'ROLE_USER', '普通用户，拥有基本权限');

-- 插入默认权限
INSERT INTO sys_permission (permission_name, permission_code, resource_type, url, method, parent_id, sort_order, description) VALUES
-- 系统管理
('系统管理', 'system', 'MENU', '/system', 'GET', 0, 1, '系统管理菜单'),
('用户管理', 'system:user', 'MENU', '/system/user', 'GET', 1, 1, '用户管理菜单'),
('角色管理', 'system:role', 'MENU', '/system/role', 'GET', 1, 2, '角色管理菜单'),
('权限管理', 'system:permission', 'MENU', '/system/permission', 'GET', 1, 3, '权限管理菜单'),

-- 用户管理权限
('用户查询', 'system:user:list', 'API', '/api/users', 'GET', 2, 1, '查询用户列表'),
('用户详情', 'system:user:detail', 'API', '/api/users/*', 'GET', 2, 2, '查询用户详情'),
('用户新增', 'system:user:add', 'API', '/api/users', 'POST', 2, 3, '新增用户'),
('用户修改', 'system:user:edit', 'API', '/api/users/*', 'PUT', 2, 4, '修改用户'),
('用户删除', 'system:user:delete', 'API', '/api/users/*', 'DELETE', 2, 5, '删除用户'),

-- 角色管理权限
('角色查询', 'system:role:list', 'API', '/api/roles', 'GET', 3, 1, '查询角色列表'),
('角色详情', 'system:role:detail', 'API', '/api/roles/*', 'GET', 3, 2, '查询角色详情'),
('角色新增', 'system:role:add', 'API', '/api/roles', 'POST', 3, 3, '新增角色'),
('角色修改', 'system:role:edit', 'API', '/api/roles/*', 'PUT', 3, 4, '修改角色'),
('角色删除', 'system:role:delete', 'API', '/api/roles/*', 'DELETE', 3, 5, '删除角色'),

-- 权限管理权限
('权限查询', 'system:permission:list', 'API', '/api/permissions', 'GET', 4, 1, '查询权限列表'),
('权限详情', 'system:permission:detail', 'API', '/api/permissions/*', 'GET', 4, 2, '查询权限详情'),
('权限新增', 'system:permission:add', 'API', '/api/permissions', 'POST', 4, 3, '新增权限'),
('权限修改', 'system:permission:edit', 'API', '/api/permissions/*', 'PUT', 4, 4, '修改权限'),
('权限删除', 'system:permission:delete', 'API', '/api/permissions/*', 'DELETE', 4, 5, '删除权限'),

-- 个人中心
('个人中心', 'profile', 'MENU', '/profile', 'GET', 0, 2, '个人中心菜单'),
('个人信息', 'profile:info', 'API', '/api/profile', 'GET', 18, 1, '查看个人信息'),
('修改个人信息', 'profile:edit', 'API', '/api/profile', 'PUT', 18, 2, '修改个人信息'),
('修改密码', 'profile:password', 'API', '/api/profile/password', 'PUT', 18, 3, '修改密码');

-- 分配用户角色
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1), -- admin -> ROLE_ADMIN
(2, 2), -- manager -> ROLE_MANAGER  
(3, 3); -- user -> ROLE_USER

-- 分配角色权限
-- 超级管理员拥有所有权限
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 1, id FROM sys_permission;

-- 部门经理拥有用户管理和个人中心权限
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(2, 2), (2, 5), (2, 6), (2, 7), (2, 8), -- 用户管理相关
(2, 18), (2, 19), (2, 20), (2, 21); -- 个人中心相关

-- 普通用户只有个人中心权限
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(3, 18), (3, 19), (3, 20), (3, 21); -- 个人中心相关

-- 创建索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_email ON sys_user(email);
CREATE INDEX idx_user_status ON sys_user(status);
CREATE INDEX idx_role_code ON sys_role(role_code);
CREATE INDEX idx_permission_code ON sys_permission(permission_code);
CREATE INDEX idx_permission_parent ON sys_permission(parent_id);
CREATE INDEX idx_user_role_user ON sys_user_role(user_id);
CREATE INDEX idx_user_role_role ON sys_user_role(role_id);
CREATE INDEX idx_role_permission_role ON sys_role_permission(role_id);
CREATE INDEX idx_role_permission_permission ON sys_role_permission(permission_id);
