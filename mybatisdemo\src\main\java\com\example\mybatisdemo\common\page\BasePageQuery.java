package com.example.mybatisdemo.common.page;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 分页查询基类
 * 所有需要分页的查询DTO都可以继承此类
 */
@Data
public class BasePageQuery {
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 500, message = "每页大小不能超过500")
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 是否进行count查询
     */
    private Boolean count = true;
    
    /**
     * 分页合理化，当页码超出范围时自动调整
     */
    private Boolean reasonable = true;
    
    /**
     * 当设置为true时，如果pageSize设置为0（或RowBounds的limit=0），就不执行分页
     */
    private Boolean pageSizeZero = false;
}
