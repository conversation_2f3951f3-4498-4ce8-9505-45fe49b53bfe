# 角色查询接口使用指南

## 接口设计理念

**统一查询接口**: `GET /api/roles`

采用单一接口支持多种查询方式的设计，避免接口冗余，提供更灵活的查询能力。

## 支持的查询方式

### 1. 基础分页查询

**场景**: 获取所有角色的分页列表

```bash
GET /api/roles?page=1&size=10
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "roleName": "系统管理员",
        "roleCode": "ADMIN",
        "roleDesc": "系统管理员角色",
        "status": 1,
        "createTime": "2025-07-22 10:00:00"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 10,
    "totalPages": 5,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

### 2. 条件筛选查询

**场景**: 按具体字段筛选角色

```bash
# 查询启用状态的角色
GET /api/roles?status=1&page=1&size=10

# 查询特定名称的角色
GET /api/roles?roleName=管理员&page=1&size=10

# 查询特定编码的角色
GET /api/roles?roleCode=ADMIN&page=1&size=10

# 多条件组合查询
GET /api/roles?status=1&roleName=管理&page=1&size=10
```

### 3. 关键字搜索

**场景**: 模糊搜索角色名称、编码、描述

```bash
# 搜索包含"管理"的角色
GET /api/roles?keyword=管理&page=1&size=10

# 搜索结果同时按状态筛选
GET /api/roles?keyword=管理&status=1&page=1&size=10
```

### 4. 排序查询

**场景**: 按指定字段排序

```bash
# 按创建时间降序
GET /api/roles?sortField=createTime&sortDirection=desc&page=1&size=10

# 按角色名称升序
GET /api/roles?sortField=roleName&sortDirection=asc&page=1&size=10

# 按状态排序，同时筛选
GET /api/roles?status=1&sortField=sortOrder&sortDirection=asc&page=1&size=10
```

### 5. 复合查询

**场景**: 组合多种查询条件

```bash
# 搜索启用状态的管理类角色，按创建时间排序
GET /api/roles?keyword=管理&status=1&sortField=createTime&sortDirection=desc&page=1&size=10

# 查询特定名称的角色，按排序号排序
GET /api/roles?roleName=管理员&sortField=sortOrder&sortDirection=asc&page=1&size=10
```

## 查询参数说明

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `page` | Integer | 否 | 1 | 页码，从1开始 |
| `size` | Integer | 否 | 10 | 每页大小，最大100 |
| `roleName` | String | 否 | - | 角色名称（模糊匹配） |
| `roleCode` | String | 否 | - | 角色编码（模糊匹配） |
| `status` | Integer | 否 | - | 状态：0-禁用，1-启用 |
| `keyword` | String | 否 | - | 关键字搜索（搜索名称、编码、描述） |
| `sortField` | String | 否 | createTime | 排序字段 |
| `sortDirection` | String | 否 | desc | 排序方向：asc/desc |

### 支持的排序字段

- `roleName` - 角色名称
- `roleCode` - 角色编码  
- `status` - 状态
- `sortOrder` - 排序号
- `createTime` - 创建时间
- `updateTime` - 更新时间

## 查询逻辑说明

### 1. 参数优先级

当同时提供多个查询参数时，系统会同时应用所有条件（AND逻辑）：

```bash
# 这个查询会同时满足以下条件：
# - 角色名称包含"管理"
# - 状态为启用(1)
# - 按创建时间降序排列
GET /api/roles?roleName=管理&status=1&sortField=createTime&sortDirection=desc
```

### 2. 关键字搜索范围

`keyword` 参数会在以下字段中搜索：
- 角色名称 (`role_name`)
- 角色编码 (`role_code`) 
- 角色描述 (`role_desc`)

### 3. 模糊匹配规则

- `roleName` 和 `roleCode` 使用 `LIKE '%value%'` 进行模糊匹配
- `keyword` 在多个字段中使用 `OR` 逻辑进行模糊匹配

## 实际使用场景

### 场景1：管理后台角色列表页

```bash
# 默认查询：获取第一页数据，按创建时间降序
GET /api/roles?page=1&size=10&sortField=createTime&sortDirection=desc
```

### 场景2：角色搜索功能

```bash
# 用户在搜索框输入"管理"
GET /api/roles?keyword=管理&page=1&size=10
```

### 场景3：状态筛选

```bash
# 只显示启用的角色
GET /api/roles?status=1&page=1&size=10
```

### 场景4：下拉选择框

```bash
# 获取所有启用的角色，不分页
GET /api/roles/all
```

### 场景5：高级搜索

```bash
# 搜索启用状态的管理类角色，按排序号排序
GET /api/roles?keyword=管理&status=1&sortField=sortOrder&sortDirection=asc&page=1&size=10
```

## 与原设计的对比

### 原设计（有问题）：
```bash
# 分页查询
GET /api/roles?page=1&size=10&status=1

# 搜索接口  
GET /api/roles/search?keyword=管理&page=1&size=10
```

**问题**：
1. 接口功能重叠
2. 无法同时使用条件筛选和关键字搜索
3. 搜索接口参数有限

### 新设计（优化后）：
```bash
# 统一接口，支持所有查询方式
GET /api/roles?keyword=管理&status=1&page=1&size=10
```

**优势**：
1. 单一接口，功能强大
2. 支持任意参数组合
3. 接口简洁，易于维护
4. 前端调用更灵活

## 前端调用示例

### JavaScript/TypeScript

```javascript
// 基础查询
const getRoles = async (params = {}) => {
  const queryParams = new URLSearchParams({
    page: params.page || 1,
    size: params.size || 10,
    ...params
  });
  
  const response = await fetch(`/api/roles?${queryParams}`);
  return response.json();
};

// 使用示例
getRoles({ keyword: '管理', status: 1, page: 1, size: 10 });
getRoles({ roleName: '管理员', sortField: 'createTime', sortDirection: 'desc' });
```

### Vue.js组合式API

```javascript
const searchRoles = async () => {
  const params = {
    page: currentPage.value,
    size: pageSize.value,
    keyword: searchKeyword.value,
    status: selectedStatus.value,
    sortField: sortField.value,
    sortDirection: sortDirection.value
  };
  
  // 过滤空值
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, v]) => v !== null && v !== undefined && v !== '')
  );
  
  const result = await getRoles(filteredParams);
  roles.value = result.data.records;
  total.value = result.data.total;
};
```

这样设计的接口更加灵活和强大，避免了功能重复，提供了更好的用户体验。
