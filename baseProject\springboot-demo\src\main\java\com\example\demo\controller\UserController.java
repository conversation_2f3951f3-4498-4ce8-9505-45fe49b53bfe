package com.example.demo.controller;

import com.example.demo.dto.UserDTO;
import com.example.demo.entity.User;
import com.example.demo.service.UserService;
import com.example.demo.util.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Optional;

/**
 * 用户控制器
 * 提供用户相关的RESTful API
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取所有用户（分页）
     * 
     * @param page 页码，从0开始
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return 用户分页数据
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Page<User>>> getAllUsers(
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        logger.info("获取用户列表 - page: {}, size: {}, sortBy: {}, sortDir: {}", 
                   page, size, sortBy, sortDir);
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                   Sort.by(sortBy).descending() : 
                   Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<User> users = userService.findAll(pageable);
        
        return ResponseEntity.ok(ApiResponse.success(users, "获取用户列表成功"));
    }
    
    /**
     * 根据ID获取用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<User>> getUserById(@PathVariable @Min(1) Long id) {
        logger.info("获取用户详情 - id: {}", id);
        
        Optional<User> user = userService.findById(id);
        if (user.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success(user.get(), "获取用户详情成功"));
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("USER_NOT_FOUND", "用户不存在"));
        }
    }
    
    /**
     * 创建用户
     * 
     * @param userDTO 用户数据传输对象
     * @return 创建的用户信息
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<User>> createUser(@Valid @RequestBody UserDTO userDTO) {
        logger.info("创建用户 - username: {}", userDTO.getUsername());
        
        try {
            User user = userService.create(userDTO);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success(user, "用户创建成功"));
        } catch (Exception e) {
            logger.error("创建用户失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("CREATE_FAILED", e.getMessage()));
        }
    }
    
    /**
     * 更新用户
     * 
     * @param id 用户ID
     * @param userDTO 用户数据传输对象
     * @return 更新后的用户信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userService.isOwner(#id, authentication.name)")
    public ResponseEntity<ApiResponse<User>> updateUser(
            @PathVariable @Min(1) Long id, 
            @Valid @RequestBody UserDTO userDTO) {
        
        logger.info("更新用户 - id: {}, username: {}", id, userDTO.getUsername());
        
        try {
            Optional<User> updatedUser = userService.update(id, userDTO);
            if (updatedUser.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success(updatedUser.get(), "用户更新成功"));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error("USER_NOT_FOUND", "用户不存在"));
            }
        } catch (Exception e) {
            logger.error("更新用户失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("UPDATE_FAILED", e.getMessage()));
        }
    }
    
    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable @Min(1) Long id) {
        logger.info("删除用户 - id: {}", id);
        
        boolean deleted = userService.delete(id);
        if (deleted) {
            return ResponseEntity.ok(ApiResponse.success(null, "用户删除成功"));
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("USER_NOT_FOUND", "用户不存在"));
        }
    }
    
    /**
     * 搜索用户
     * 
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<User>>> searchUsers(
            @RequestParam String keyword) {
        
        logger.info("搜索用户 - keyword: {}", keyword);
        
        List<User> users = userService.searchByKeyword(keyword);
        return ResponseEntity.ok(ApiResponse.success(users, "搜索用户成功"));
    }
    
    /**
     * 获取用户统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserStats>> getUserStats() {
        logger.info("获取用户统计信息");
        
        UserStats stats = userService.getUserStats();
        return ResponseEntity.ok(ApiResponse.success(stats, "获取统计信息成功"));
    }
    
    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Integer>> batchDeleteUsers(@RequestBody List<Long> ids) {
        logger.info("批量删除用户 - ids: {}", ids);
        
        int deletedCount = userService.batchDelete(ids);
        return ResponseEntity.ok(ApiResponse.success(deletedCount, 
                String.format("成功删除%d个用户", deletedCount)));
    }
    
    /**
     * 用户统计信息内部类
     */
    public static class UserStats {
        private long totalUsers;
        private long activeUsers;
        private long inactiveUsers;
        private long suspendedUsers;
        
        public UserStats(long totalUsers, long activeUsers, long inactiveUsers, long suspendedUsers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.inactiveUsers = inactiveUsers;
            this.suspendedUsers = suspendedUsers;
        }
        
        // Getters and Setters
        public long getTotalUsers() { return totalUsers; }
        public void setTotalUsers(long totalUsers) { this.totalUsers = totalUsers; }
        
        public long getActiveUsers() { return activeUsers; }
        public void setActiveUsers(long activeUsers) { this.activeUsers = activeUsers; }
        
        public long getInactiveUsers() { return inactiveUsers; }
        public void setInactiveUsers(long inactiveUsers) { this.inactiveUsers = inactiveUsers; }
        
        public long getSuspendedUsers() { return suspendedUsers; }
        public void setSuspendedUsers(long suspendedUsers) { this.suspendedUsers = suspendedUsers; }
    }
}
