<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.SysPermissionMapper">

    <!-- 权限结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.SysPermission">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
        <result column="permission_code" property="permissionCode" jdbcType="VARCHAR"/>
        <result column="resource_type" property="resourceType" jdbcType="VARCHAR"/>
        <result column="url" property="url" jdbcType="VARCHAR"/>
        <result column="method" property="method" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 权限树结果映射 -->
    <resultMap id="PermissionTreeMap" type="com.example.entity.SysPermission" extends="BaseResultMap">
        <collection property="children" ofType="com.example.entity.SysPermission" 
                   column="id" select="selectByParentId"/>
    </resultMap>

    <!-- 查询权限树（递归查询） -->
    <select id="selectPermissionTree" resultMap="PermissionTreeMap">
        SELECT * FROM sys_permission 
        WHERE parent_id = 0 AND status = 1
        ORDER BY sort_order
    </select>

    <!-- 根据用户ID查询权限树 -->
    <select id="selectUserPermissionTree" parameterType="long" resultMap="PermissionTreeMap">
        SELECT DISTINCT p.* FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId} AND p.parent_id = 0 AND p.status = 1
        ORDER BY p.sort_order
    </select>

    <!-- 根据角色ID查询权限树 -->
    <select id="selectRolePermissionTree" parameterType="long" resultMap="PermissionTreeMap">
        SELECT p.* FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId} AND p.parent_id = 0 AND p.status = 1
        ORDER BY p.sort_order
    </select>

    <!-- 分页查询权限 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT * FROM sys_permission
        <where>
            <if test="permissionName != null and permissionName != ''">
                AND permission_name LIKE CONCAT('%', #{permissionName}, '%')
            </if>
            <if test="permissionCode != null and permissionCode != ''">
                AND permission_code LIKE CONCAT('%', #{permissionCode}, '%')
            </if>
            <if test="resourceType != null and resourceType != ''">
                AND resource_type = #{resourceType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY parent_id, sort_order
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据条件统计权限数 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM sys_permission
        <where>
            <if test="permissionName != null and permissionName != ''">
                AND permission_name LIKE CONCAT('%', #{permissionName}, '%')
            </if>
            <if test="permissionCode != null and permissionCode != ''">
                AND permission_code LIKE CONCAT('%', #{permissionCode}, '%')
            </if>
            <if test="resourceType != null and resourceType != ''">
                AND resource_type = #{resourceType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>
