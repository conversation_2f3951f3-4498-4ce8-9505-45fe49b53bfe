# Docker Compose 配置文件
# 用于本地开发和测试环境

version: '3.8'

services:
  # Spring Boot 应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: springboot-demo-app
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=springboot_demo
      - DB_USERNAME=demo_user
      - DB_PASSWORD=demo_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JAVA_OPTS=-Xms256m -Xmx512m
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - springboot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: springboot-demo-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=springboot_demo
      - MYSQL_USER=demo_user
      - MYSQL_PASSWORD=demo_password
      - MYSQL_ROOT_HOST=%
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    networks:
      - springboot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:6.2-alpine
    container_name: springboot-demo-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - springboot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server /usr/local/etc/redis/redis.conf

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: springboot-demo-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - springboot-network
    restart: unless-stopped

  # Prometheus 监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: springboot-demo-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - springboot-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 可视化（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: springboot-demo-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - springboot-network
    restart: unless-stopped

# 网络配置
networks:
  springboot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
