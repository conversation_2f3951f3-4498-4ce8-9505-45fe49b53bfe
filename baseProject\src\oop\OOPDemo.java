package oop;

/**
 * 面向对象编程演示程序
 * 展示封装、继承、多态和抽象的实际应用
 */
public class OOPDemo {
    public static void main(String[] args) {
        System.out.println("=== Java面向对象编程演示 ===\n");
        
        // 1. 创建教师对象
        System.out.println("1. 创建教师对象");
        Teacher teacher1 = new Teacher("张教授", 45, "T001", "计算机科学系", "教授");
        Teacher teacher2 = new Teacher("李老师", 35, "T002", "数学系", "讲师");
        
        teacher1.displayInfo();
        System.out.println();
        teacher2.displayInfo();
        System.out.println();
        
        // 2. 创建学生对象
        System.out.println("2. 创建学生对象");
        Student student1 = new Student("王小明", 20, "S001", "计算机科学");
        Student student2 = new Student("李小红", 19, "S002", "软件工程");
        Student student3 = new Student("张小强", 21, "S003", "计算机科学");
        
        student1.displayInfo();
        System.out.println();
        
        // 3. 创建课程对象
        System.out.println("3. 创建课程对象");
        Course javaCourse = new Course("CS101", "Java程序设计", 3, 30, 
                                     "学习Java编程语言基础知识和面向对象编程");
        Course mathCourse = new Course("MATH201", "高等数学", 4, 50, 
                                     "学习微积分、线性代数等数学知识");
        
        javaCourse.displayCourseInfo();
        System.out.println();
        
        // 4. 演示继承和多态
        System.out.println("4. 演示继承和多态");
        Person[] people = {teacher1, teacher2, student1, student2, student3};
        
        for (Person person : people) {
            person.greet();  // 多态：不同类型的对象调用不同的greet方法
        }
        System.out.println();
        
        // 5. 演示封装 - 教师分配课程
        System.out.println("5. 演示封装 - 教师分配课程");
        teacher1.assignCourse(javaCourse);
        teacher2.assignCourse(mathCourse);
        System.out.println();
        
        // 6. 演示对象间的关联 - 学生选课
        System.out.println("6. 演示对象间的关联 - 学生选课");
        student1.enrollCourse(javaCourse);
        student2.enrollCourse(javaCourse);
        student3.enrollCourse(javaCourse);
        
        student1.enrollCourse(mathCourse);
        student2.enrollCourse(mathCourse);
        System.out.println();
        
        // 7. 显示更新后的信息
        System.out.println("7. 显示更新后的信息");
        javaCourse.displayCourseInfo();
        System.out.println();
        
        teacher1.displayInfo();
        System.out.println();
        
        student1.displayInfo();
        System.out.println();
        
        // 8. 演示方法调用
        System.out.println("8. 演示方法调用");
        javaCourse.startClass();
        System.out.println();
        
        teacher1.teach();
        student1.study();
        System.out.println();
        
        // 9. 演示评分功能
        System.out.println("9. 演示评分功能");
        teacher1.gradeStudent(student1, "Java程序设计", 95);
        teacher1.gradeStudent(student2, "Java程序设计", 88);
        teacher2.gradeStudent(student1, "高等数学", 92);
        System.out.println();
        
        // 10. 显示最终状态
        System.out.println("10. 显示最终状态");
        student1.displayInfo();
        System.out.println();
        
        // 11. 演示退课功能
        System.out.println("11. 演示退课功能");
        student3.dropCourse(mathCourse);
        student3.enrollCourse(mathCourse);
        System.out.println();
        
        // 12. 演示toString方法
        System.out.println("12. 演示toString方法");
        System.out.println("教师信息：" + teacher1);
        System.out.println("学生信息：" + student1);
        System.out.println("课程信息：" + javaCourse);
        System.out.println();
        
        // 13. 演示静态方法调用
        System.out.println("13. 演示课程统计");
        displayCourseStatistics(javaCourse);
        displayCourseStatistics(mathCourse);
        
        System.out.println("\n=== 演示结束 ===");
    }
    
    /**
     * 静态方法 - 显示课程统计信息
     * @param course 课程对象
     */
    public static void displayCourseStatistics(Course course) {
        System.out.println("课程：" + course.getCourseName());
        System.out.println("  - 当前学生数：" + course.getEnrolledStudents().size());
        System.out.println("  - 剩余容量：" + course.getRemainingCapacity());
        System.out.println("  - 是否已满：" + (course.isFull() ? "是" : "否"));
        System.out.println("  - 授课教师：" + 
                         (course.getTeacher() != null ? course.getTeacher().getName() : "未分配"));
    }
}
