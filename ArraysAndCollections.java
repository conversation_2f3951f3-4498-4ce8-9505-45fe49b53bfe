import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 这个程序展示Java中的数组和集合类
 */
public class ArraysAndCollections {
    public static void main(String[] args) {
        // 1. 数组
        System.out.println("===== 数组示例 =====");
        
        // 声明和初始化数组
        int[] numbers = {10, 20, 30, 40, 50};
        
        // 访问数组元素
        System.out.println("第三个元素是: " + numbers[2]);  // 注意：索引从0开始
        
        // 修改数组元素
        numbers[1] = 25;
        
        // 遍历数组
        System.out.println("数组元素:");
        for (int i = 0; i < numbers.length; i++) {
            System.out.println("索引 " + i + ": " + numbers[i]);
        }
        
        // 2. ArrayList (动态数组)
        System.out.println("\n===== ArrayList示例 =====");
        
        // 创建ArrayList
        List<String> fruits = new ArrayList<>();
        
        // 添加元素
        fruits.add("苹果");
        fruits.add("香蕉");
        fruits.add("橙子");
        
        // 访问元素
        System.out.println("第二个水果是: " + fruits.get(1));
        
        // 修改元素
        fruits.set(0, "红苹果");
        
        // 删除元素
        fruits.remove(2);  // 删除索引为2的元素
        
        // 遍历ArrayList
        System.out.println("水果列表:");
        for (String fruit : fruits) {
            System.out.println(fruit);
        }
        
        // 3. HashSet (集合，不允许重复元素)
        System.out.println("\n===== HashSet示例 =====");
        
        // 创建HashSet
        Set<String> uniqueNames = new HashSet<>();
        
        // 添加元素
        uniqueNames.add("张三");
        uniqueNames.add("李四");
        uniqueNames.add("王五");
        uniqueNames.add("张三");  // 重复元素，将被忽略
        
        // 检查元素是否存在
        System.out.println("是否包含'李四': " + uniqueNames.contains("李四"));
        
        // 删除元素
        uniqueNames.remove("王五");
        
        // 遍历HashSet
        System.out.println("唯一名称:");
        for (String name : uniqueNames) {
            System.out.println(name);
        }
        
        // 4. HashMap (键值对映射)
        System.out.println("\n===== HashMap示例 =====");
        
        // 创建HashMap
        Map<String, Integer> studentScores = new HashMap<>();
        
        // 添加键值对
        studentScores.put("张三", 95);
        studentScores.put("李四", 88);
        studentScores.put("王五", 76);
        
        // 访问值
        System.out.println("张三的分数: " + studentScores.get("张三"));
        
        // 修改值
        studentScores.put("李四", 90);  // 更新已存在的键的值
        
        // 检查键是否存在
        System.out.println("是否包含赵六: " + studentScores.containsKey("赵六"));
        
        // 遍历HashMap
        System.out.println("学生成绩:");
        for (Map.Entry<String, Integer> entry : studentScores.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
    }
} 