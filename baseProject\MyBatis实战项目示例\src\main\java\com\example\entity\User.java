package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;
    
    /**
     * 邮箱地址
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    /**
     * 密码(加密后)
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 255, message = "密码长度必须在6-255个字符之间")
    private String password;
    
    /**
     * 密码盐值
     */
    private String salt;
    
    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;
    
    /**
     * 头像URL
     */
    @Size(max = 255, message = "头像URL长度不能超过255个字符")
    private String avatar;
    
    /**
     * 手机号码
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phone;
    
    /**
     * 性别: 0-未知, 1-男, 2-女
     */
    @Min(value = 0, message = "性别值不正确")
    @Max(value = 2, message = "性别值不正确")
    private Integer gender;
    
    /**
     * 生日
     */
    private java.time.LocalDate birthday;
    
    /**
     * 年龄
     */
    @Min(value = 0, message = "年龄不能为负数")
    @Max(value = 150, message = "年龄不能超过150")
    private Integer age;
    
    /**
     * 地址
     */
    @Size(max = 255, message = "地址长度不能超过255个字符")
    private String address;
    
    /**
     * 个人简介
     */
    @Size(max = 1000, message = "个人简介长度不能超过1000个字符")
    private String bio;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 登录次数
     */
    @Min(value = 0, message = "登录次数不能为负数")
    private Integer loginCount;
    
    /**
     * 状态: 0-禁用, 1-启用, 2-锁定
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 2, message = "状态值不正确")
    private Integer status;
    
    /**
     * 邮箱是否验证: 0-未验证, 1-已验证
     */
    private Integer emailVerified;
    
    /**
     * 手机是否验证: 0-未验证, 1-已验证
     */
    private Integer phoneVerified;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人ID
     */
    private Long createUser;
    
    /**
     * 更新人ID
     */
    private Long updateUser;
    
    /**
     * 版本号(乐观锁)
     */
    private Integer version;
    
    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    private Integer deleted;
    
    // ===== 关联属性 =====
    
    /**
     * 用户拥有的角色列表
     */
    private List<Role> roles;
    
    /**
     * 用户权限列表
     */
    private List<Permission> permissions;
    
    // ===== 业务方法 =====
    
    /**
     * 检查用户是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    /**
     * 检查用户是否被锁定
     */
    public boolean isLocked() {
        return status != null && status == 2;
    }
    
    /**
     * 检查邮箱是否已验证
     */
    public boolean isEmailVerified() {
        return emailVerified != null && emailVerified == 1;
    }
    
    /**
     * 检查手机是否已验证
     */
    public boolean isPhoneVerified() {
        return phoneVerified != null && phoneVerified == 1;
    }
    
    /**
     * 检查用户是否有指定角色
     */
    public boolean hasRole(String roleCode) {
        if (roles == null || roles.isEmpty()) {
            return false;
        }
        return roles.stream().anyMatch(role -> roleCode.equals(role.getRoleCode()));
    }
    
    /**
     * 检查用户是否有指定权限
     */
    public boolean hasPermission(String permissionCode) {
        if (permissions == null || permissions.isEmpty()) {
            return false;
        }
        return permissions.stream().anyMatch(permission -> 
            permissionCode.equals(permission.getPermissionCode()));
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0: return "禁用";
            case 1: return "启用";
            case 2: return "锁定";
            default: return "未知";
        }
    }
    
    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        if (gender == null) {
            return "未知";
        }
        switch (gender) {
            case 0: return "未知";
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }
    
    /**
     * 脱敏显示手机号
     */
    public String getMaskedPhone() {
        if (phone == null || phone.length() < 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
    
    /**
     * 脱敏显示邮箱
     */
    public String getMaskedEmail() {
        if (email == null || !email.contains("@")) {
            return email;
        }
        String[] parts = email.split("@");
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 2) {
            return username + "***@" + domain;
        }
        return username.substring(0, 2) + "***@" + domain;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + getMaskedEmail() + '\'' +
                ", nickname='" + nickname + '\'' +
                ", phone='" + getMaskedPhone() + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}
