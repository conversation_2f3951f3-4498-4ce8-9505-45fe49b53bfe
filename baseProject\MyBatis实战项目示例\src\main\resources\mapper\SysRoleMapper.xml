<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.SysRoleMapper">

    <!-- 角色结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.SysRole">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 角色权限结果映射 -->
    <resultMap id="RoleWithPermissionsMap" type="com.example.entity.SysRole" extends="BaseResultMap">
        <collection property="permissions" ofType="com.example.entity.SysPermission">
            <id column="permission_id" property="id" jdbcType="BIGINT"/>
            <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
            <result column="permission_code" property="permissionCode" jdbcType="VARCHAR"/>
            <result column="resource_type" property="resourceType" jdbcType="VARCHAR"/>
            <result column="url" property="url" jdbcType="VARCHAR"/>
            <result column="method" property="method" jdbcType="VARCHAR"/>
            <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
            <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
            <result column="icon" property="icon" jdbcType="VARCHAR"/>
            <result column="permission_description" property="description" jdbcType="VARCHAR"/>
            <result column="permission_status" property="status" jdbcType="TINYINT"/>
        </collection>
    </resultMap>

    <!-- 查询角色详细信息（包含权限） -->
    <select id="selectRoleWithPermissions" parameterType="long" resultMap="RoleWithPermissionsMap">
        SELECT 
            r.*,
            p.id as permission_id,
            p.permission_name,
            p.permission_code,
            p.resource_type,
            p.url,
            p.method,
            p.parent_id,
            p.sort_order,
            p.icon,
            p.description as permission_description,
            p.status as permission_status
        FROM sys_role r
        LEFT JOIN sys_role_permission rp ON r.id = rp.role_id
        LEFT JOIN sys_permission p ON rp.permission_id = p.id AND p.status = 1
        WHERE r.id = #{roleId}
        ORDER BY p.parent_id, p.sort_order
    </select>

    <!-- 分页查询角色 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT * FROM sys_role
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null and roleCode != ''">
                AND role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据条件统计角色数 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM sys_role
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null and roleCode != ''">
                AND role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>
