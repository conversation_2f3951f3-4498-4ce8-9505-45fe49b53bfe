package thread;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 并发工具类演示
 * 包括CountDownLatch、CyclicBarrier、Semaphore等
 */
public class ConcurrentUtilsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 并发工具类演示 ===");
        
        // 1. CountDownLatch演示
        countDownLatchDemo();
        
        // 2. CyclicBarrier演示
        cyclicBarrierDemo();
        
        // 3. Semaphore演示
        semaphoreDemo();
        
        // 4. 原子类演示
        atomicDemo();
        
        // 5. 线程通信演示
        threadCommunicationDemo();
    }
    
    /**
     * CountDownLatch演示 - 等待多个线程完成
     */
    public static void countDownLatchDemo() {
        System.out.println("\n--- CountDownLatch演示 ---");
        
        int workerCount = 3;
        CountDownLatch latch = new CountDownLatch(workerCount);
        
        System.out.println("主线程启动" + workerCount + "个工作线程");
        
        // 启动工作线程
        for (int i = 1; i <= workerCount; i++) {
            final int workerId = i;
            new Thread(() -> {
                try {
                    System.out.println("工作者" + workerId + " 开始工作");
                    
                    // 模拟不同的工作时间
                    Thread.sleep(1000 + workerId * 500);
                    
                    System.out.println("工作者" + workerId + " 完成工作");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown(); // 计数器减1
                    System.out.println("工作者" + workerId + " 报告完成，剩余: " + latch.getCount());
                }
            }, "工作者-" + i).start();
        }
        
        try {
            System.out.println("主线程等待所有工作者完成...");
            latch.await(); // 等待计数器归零
            System.out.println("所有工作者完成，主线程继续执行");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * CyclicBarrier演示 - 多个线程相互等待
     */
    public static void cyclicBarrierDemo() {
        System.out.println("\n--- CyclicBarrier演示 ---");
        
        int playerCount = 3;
        
        // 创建屏障，当所有玩家准备就绪时执行回调
        CyclicBarrier barrier = new CyclicBarrier(playerCount, () -> {
            System.out.println("🎮 所有玩家准备就绪，游戏开始！");
        });
        
        System.out.println("等待" + playerCount + "个玩家准备...");
        
        // 启动玩家线程
        for (int i = 1; i <= playerCount; i++) {
            final int playerId = i;
            new Thread(() -> {
                try {
                    System.out.println("玩家" + playerId + " 正在准备...");
                    
                    // 模拟准备时间
                    Thread.sleep(1000 + playerId * 300);
                    
                    System.out.println("玩家" + playerId + " 准备完成，等待其他玩家");
                    
                    barrier.await(); // 等待所有玩家准备完成
                    
                    System.out.println("玩家" + playerId + " 开始游戏！");
                    
                    // 模拟游戏时间
                    Thread.sleep(2000);
                    System.out.println("玩家" + playerId + " 游戏结束");
                    
                } catch (InterruptedException | BrokenBarrierException e) {
                    System.out.println("玩家" + playerId + " 游戏被中断");
                }
            }, "玩家-" + i).start();
        }
        
        // 等待所有玩家完成
        try {
            Thread.sleep(8000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Semaphore演示 - 控制资源访问数量
     */
    public static void semaphoreDemo() {
        System.out.println("\n--- Semaphore演示 ---");
        
        // 停车场有3个车位
        Semaphore parkingLot = new Semaphore(3);
        System.out.println("停车场开放，共有3个车位");
        
        // 5辆车尝试停车
        for (int i = 1; i <= 5; i++) {
            final int carId = i;
            new Thread(() -> {
                try {
                    System.out.println("🚗 车辆" + carId + " 到达停车场，尝试停车");
                    System.out.println("车辆" + carId + " 等待车位... (可用车位: " + 
                        parkingLot.availablePermits() + ")");
                    
                    parkingLot.acquire(); // 获取许可证（车位）
                    
                    System.out.println("✅ 车辆" + carId + " 成功停车 (剩余车位: " + 
                        parkingLot.availablePermits() + ")");
                    
                    // 模拟停车时间
                    Thread.sleep(2000 + carId * 500);
                    
                    System.out.println("🚗 车辆" + carId + " 离开停车场");
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    parkingLot.release(); // 释放许可证（车位）
                    System.out.println("车辆" + carId + " 释放车位 (可用车位: " + 
                        parkingLot.availablePermits() + ")");
                }
            }, "车辆-" + i).start();
        }
        
        // 等待所有车辆完成
        try {
            Thread.sleep(15000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 原子类演示
     */
    public static void atomicDemo() {
        System.out.println("\n--- 原子类演示 ---");
        
        AtomicInteger atomicCounter = new AtomicInteger(0);
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        System.out.println("启动" + threadCount + "个线程进行原子操作");
        
        // 启动多个线程进行原子操作
        for (int i = 1; i <= threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < 3; j++) {
                    // 原子性递增
                    int newValue = atomicCounter.incrementAndGet();
                    System.out.println("线程" + threadId + " 递增后值: " + newValue);
                    
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                
                // CAS操作演示
                int expectedValue = atomicCounter.get();
                int newValue = expectedValue + 10;
                boolean success = atomicCounter.compareAndSet(expectedValue, newValue);
                System.out.println("线程" + threadId + " CAS操作" + 
                    (success ? "成功" : "失败") + ", 当前值: " + atomicCounter.get());
                
                latch.countDown();
            }, "原子线程-" + i).start();
        }
        
        try {
            latch.await();
            System.out.println("所有原子操作完成，最终值: " + atomicCounter.get());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 线程通信演示 - wait/notify和Condition
     */
    public static void threadCommunicationDemo() {
        System.out.println("\n--- 线程通信演示 ---");
        
        // 使用wait/notify的生产者消费者
        waitNotifyDemo();
        
        // 使用Condition的生产者消费者
        conditionDemo();
    }
    
    /**
     * wait/notify机制演示
     */
    private static void waitNotifyDemo() {
        System.out.println("\n使用wait/notify机制:");
        
        class WaitNotifyExample {
            private final Object lock = new Object();
            private boolean condition = false;
            
            public void waitingMethod() {
                synchronized (lock) {
                    while (!condition) {
                        try {
                            System.out.println(Thread.currentThread().getName() + " 开始等待条件");
                            lock.wait(); // 等待条件满足
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            return;
                        }
                    }
                    System.out.println(Thread.currentThread().getName() + " 条件满足，继续执行");
                }
            }
            
            public void notifyingMethod() {
                synchronized (lock) {
                    condition = true;
                    System.out.println(Thread.currentThread().getName() + " 设置条件为true");
                    lock.notifyAll(); // 唤醒所有等待的线程
                }
            }
        }
        
        WaitNotifyExample example = new WaitNotifyExample();
        
        // 启动等待线程
        Thread waiter1 = new Thread(example::waitingMethod, "等待者1");
        Thread waiter2 = new Thread(example::waitingMethod, "等待者2");
        
        waiter1.start();
        waiter2.start();
        
        // 延迟后通知
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                example.notifyingMethod();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "通知者").start();
        
        try {
            waiter1.join();
            waiter2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Condition机制演示
     */
    private static void conditionDemo() {
        System.out.println("\n使用Condition机制:");
        
        class ConditionExample {
            private final Lock lock = new ReentrantLock();
            private final Condition condition = lock.newCondition();
            private boolean ready = false;
            
            public void waitForCondition() {
                lock.lock();
                try {
                    while (!ready) {
                        System.out.println(Thread.currentThread().getName() + " 等待条件");
                        condition.await(); // 等待条件
                    }
                    System.out.println(Thread.currentThread().getName() + " 条件满足");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    lock.unlock();
                }
            }
            
            public void signalCondition() {
                lock.lock();
                try {
                    ready = true;
                    System.out.println(Thread.currentThread().getName() + " 发送信号");
                    condition.signalAll(); // 唤醒所有等待的线程
                } finally {
                    lock.unlock();
                }
            }
        }
        
        ConditionExample example = new ConditionExample();
        
        // 启动等待线程
        Thread waiter1 = new Thread(example::waitForCondition, "Condition等待者1");
        Thread waiter2 = new Thread(example::waitForCondition, "Condition等待者2");
        
        waiter1.start();
        waiter2.start();
        
        // 延迟后发送信号
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                example.signalCondition();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "Condition信号者").start();
        
        try {
            waiter1.join();
            waiter2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
