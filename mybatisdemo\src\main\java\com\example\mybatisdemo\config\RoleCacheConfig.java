package com.example.mybatisdemo.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 角色缓存配置
 * 
 * 缓存策略：
 * - roleCache: 角色基础信息缓存，TTL 30分钟
 * - roleTreeCache: 角色树结构缓存，TTL 1小时
 * - rolePermissionCache: 角色权限缓存，TTL 15分钟
 * - roleStatisticsCache: 角色统计缓存，TTL 5分钟
 */
@Configuration
@EnableCaching
public class RoleCacheConfig {
    
    /**
     * 默认缓存管理器
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // 设置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "roleCache",           // 角色基础信息
            "roleTreeCache",       // 角色树结构
            "rolePermissionCache", // 角色权限
            "roleStatisticsCache", // 角色统计
            "roleQueryCache"       // 角色查询结果
        ));
        
        return cacheManager;
    }
    
    /**
     * 缓存键生成器
     */
    @Bean
    public org.springframework.cache.interceptor.KeyGenerator roleKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder key = new StringBuilder();
            key.append(target.getClass().getSimpleName()).append(":");
            key.append(method.getName()).append(":");
            
            for (Object param : params) {
                if (param != null) {
                    key.append(param.toString()).append(":");
                }
            }
            
            return key.toString();
        };
    }
}
