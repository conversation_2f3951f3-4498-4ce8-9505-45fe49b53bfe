# Spring Boot 参数校验详解文档

## 1. 概述

Spring Boot 提供了强大的参数校验功能，基于 JSR-303/JSR-380 Bean Validation 规范，可以在多个层面进行数据校验，确保数据的完整性和有效性。

## 2. 依赖配置

### 2.1 Maven依赖

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
</dependency>
```

### 2.2 自动配置

Spring Boot 会自动配置校验器，无需额外配置。

## 3. 常用校验注解

### 3.1 基础校验注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@NotNull` | 不能为null | `@NotNull(message = "ID不能为空")` |
| `@NotEmpty` | 不能为null且长度>0 | `@NotEmpty(message = "列表不能为空")` |
| `@NotBlank` | 不能为null且去除空格后长度>0 | `@NotBlank(message = "用户名不能为空")` |
| `@Null` | 必须为null | `@Null(message = "ID必须为空")` |

### 3.2 数值校验注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Min` | 最小值 | `@Min(value = 1, message = "年龄不能小于1")` |
| `@Max` | 最大值 | `@Max(value = 150, message = "年龄不能大于150")` |
| `@DecimalMin` | 最小值(支持小数) | `@DecimalMin(value = "0.0", message = "价格不能为负")` |
| `@DecimalMax` | 最大值(支持小数) | `@DecimalMax(value = "999.99", message = "价格过高")` |
| `@Positive` | 必须为正数 | `@Positive(message = "ID必须为正数")` |
| `@PositiveOrZero` | 必须为正数或0 | `@PositiveOrZero(message = "数量不能为负")` |
| `@Negative` | 必须为负数 | `@Negative(message = "必须为负数")` |
| `@NegativeOrZero` | 必须为负数或0 | `@NegativeOrZero(message = "必须为负数或0")` |

### 3.3 长度和大小校验

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Size` | 字符串、集合、数组的大小 | `@Size(min = 3, max = 50, message = "长度在3-50之间")` |
| `@Length` | 字符串长度(Hibernate扩展) | `@Length(min = 6, max = 20, message = "密码长度6-20位")` |

### 3.4 格式校验注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Email` | 邮箱格式 | `@Email(message = "邮箱格式不正确")` |
| `@Pattern` | 正则表达式 | `@Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")` |
| `@URL` | URL格式 | `@URL(message = "URL格式不正确")` |

### 3.5 时间校验注解

| 注解 | 作用 | 示例 |
|------|------|------|
| `@Past` | 必须是过去时间 | `@Past(message = "生日不能是未来日期")` |
| `@PastOrPresent` | 必须是过去或现在时间 | `@PastOrPresent(message = "时间不能是未来")` |
| `@Future` | 必须是未来时间 | `@Future(message = "截止时间必须是未来")` |
| `@FutureOrPresent` | 必须是未来或现在时间 | `@FutureOrPresent(message = "开始时间不能是过去")` |

## 4. 实际应用示例

### 4.1 用户创建DTO

```java
@Data
public class UserCreateDTO {
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,}$", 
             message = "密码必须包含至少一个大写字母、一个小写字母和一个数字")
    private String password;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @Min(value = 1, message = "年龄不能小于1岁")
    @Max(value = 150, message = "年龄不能大于150岁")
    private Integer age;
    
    @Past(message = "生日不能是未来日期")
    private LocalDate birthday;
}
```

### 4.2 Controller中使用校验

```java
@RestController
@RequestMapping("/api/users")
@Validated  // 启用方法级别校验
public class UserController {
    
    // 请求体校验
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody UserCreateDTO userCreateDTO) {
        // 业务逻辑
        return Result.success(user);
    }
    
    // 路径参数校验
    @GetMapping("/{id}")
    public Result<User> getUserById(
            @PathVariable 
            @NotNull(message = "用户ID不能为空") 
            @Positive(message = "用户ID必须为正数") 
            Long id) {
        // 业务逻辑
        return Result.success(user);
    }
    
    // 查询参数校验
    @GetMapping("/search")
    public Result<List<User>> searchUsers(
            @RequestParam 
            @NotBlank(message = "关键字不能为空")
            @Size(min = 2, max = 50, message = "关键字长度在2-50之间")
            String keyword) {
        // 业务逻辑
        return Result.success(users);
    }
}
```

## 5. 自定义校验注解

### 5.1 创建自定义注解

```java
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PhoneNumberValidator.class)
@Documented
public @interface PhoneNumber {
    
    String message() default "手机号格式不正确";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    boolean allowEmpty() default true;
}
```

### 5.2 实现校验器

```java
public class PhoneNumberValidator implements ConstraintValidator<PhoneNumber, String> {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private boolean allowEmpty;
    
    @Override
    public void initialize(PhoneNumber constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (allowEmpty && !StringUtils.hasText(value)) {
            return true;
        }
        
        if (!allowEmpty && !StringUtils.hasText(value)) {
            return false;
        }
        
        return PHONE_PATTERN.matcher(value).matches();
    }
}
```

### 5.3 使用自定义注解

```java
public class UserDTO {
    @PhoneNumber(allowEmpty = false, message = "请输入正确的手机号")
    private String phone;
}
```

## 6. 校验分组

### 6.1 定义校验分组

```java
public interface CreateGroup {}
public interface UpdateGroup {}
```

### 6.2 在字段上指定分组

```java
@Data
public class UserDTO {
    
    @Null(groups = CreateGroup.class, message = "创建时ID必须为空")
    @NotNull(groups = UpdateGroup.class, message = "更新时ID不能为空")
    private Long id;
    
    @NotBlank(groups = {CreateGroup.class, UpdateGroup.class}, message = "用户名不能为空")
    private String username;
    
    @NotBlank(groups = CreateGroup.class, message = "创建时密码不能为空")
    private String password;
}
```

### 6.3 在Controller中使用分组

```java
@PostMapping
public Result<User> createUser(@Validated(CreateGroup.class) @RequestBody UserDTO userDTO) {
    // 创建逻辑
}

@PutMapping("/{id}")
public Result<User> updateUser(@Validated(UpdateGroup.class) @RequestBody UserDTO userDTO) {
    // 更新逻辑
}
```

## 7. 嵌套对象校验

### 7.1 嵌套对象

```java
@Data
public class OrderDTO {

    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Valid  // 重要：嵌套对象需要@Valid注解
    @NotNull(message = "用户信息不能为空")
    private UserDTO user;

    @Valid  // 校验集合中的每个元素
    @NotEmpty(message = "订单项不能为空")
    private List<OrderItemDTO> items;
}

@Data
public class OrderItemDTO {
    @NotBlank(message = "商品名称不能为空")
    private String productName;

    @Positive(message = "数量必须大于0")
    private Integer quantity;

    @DecimalMin(value = "0.01", message = "价格必须大于0")
    private BigDecimal price;
}
```

## 8. 全局异常处理

### 8.1 处理校验异常

```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理请求体校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            errorMsg.append(fieldError.getField())
                   .append(": ")
                   .append(fieldError.getDefaultMessage())
                   .append("; ");
        }
        return Result.error(ResultCode.PARAM_ERROR.getCode(), errorMsg.toString());
    }

    /**
     * 处理方法参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleConstraintViolationException(ConstraintViolationException e) {
        StringBuilder errorMsg = new StringBuilder();
        for (ConstraintViolation<?> violation : e.getConstraintViolations()) {
            errorMsg.append(violation.getPropertyPath())
                   .append(": ")
                   .append(violation.getMessage())
                   .append("; ");
        }
        return Result.error(ResultCode.PARAM_ERROR.getCode(), errorMsg.toString());
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Object> handleBindException(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        StringBuilder errorMsg = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            errorMsg.append(fieldError.getField())
                   .append(": ")
                   .append(fieldError.getDefaultMessage())
                   .append("; ");
        }
        return Result.error(ResultCode.PARAM_ERROR.getCode(), errorMsg.toString());
    }
}
```

## 9. 校验配置

### 9.1 自定义校验器配置

```java
@Configuration
public class ValidationConfig {

    @Bean
    public Validator validator() {
        ValidatorFactory factory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .failFast(true)  // 快速失败模式
                .buildValidatorFactory();
        return factory.getValidator();
    }

    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        MethodValidationPostProcessor processor = new MethodValidationPostProcessor();
        processor.setValidator(validator());
        return processor;
    }
}
```

## 10. 常见正则表达式

### 10.1 常用正则表达式模式

```java
public class ValidationPatterns {

    // 手机号
    public static final String PHONE = "^1[3-9]\\d{9}$";

    // 身份证号
    public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

    // 用户名（字母数字下划线，3-20位）
    public static final String USERNAME = "^[a-zA-Z0-9_]{3,20}$";

    // 强密码（至少包含大小写字母、数字，8-20位）
    public static final String STRONG_PASSWORD = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,20}$";

    // 中文姓名
    public static final String CHINESE_NAME = "^[\\u4e00-\\u9fa5]{2,10}$";

    // IP地址
    public static final String IP_ADDRESS = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";

    // 邮政编码
    public static final String POSTAL_CODE = "^[1-9]\\d{5}$";
}
```

## 11. 最佳实践

### 11.1 设计原则

1. **分层校验**: 在Controller层进行格式校验，在Service层进行业务校验
2. **使用DTO**: 为不同操作创建专门的DTO类
3. **合理分组**: 使用校验分组处理不同场景
4. **自定义注解**: 对于复杂业务规则，创建自定义校验注解
5. **国际化**: 支持多语言错误消息

### 11.2 性能优化

1. **快速失败**: 配置failFast模式，遇到第一个错误就停止
2. **缓存校验器**: 重用Validator实例
3. **合理使用正则**: 避免复杂的正则表达式影响性能

### 11.3 错误处理

1. **统一格式**: 使用统一的错误响应格式
2. **详细信息**: 提供具体的字段错误信息
3. **用户友好**: 错误消息要对用户友好

## 12. 测试示例

### 12.1 单元测试

```java
@SpringBootTest
class UserValidationTest {

    @Autowired
    private Validator validator;

    @Test
    void testUserCreateDTOValidation() {
        UserCreateDTO dto = new UserCreateDTO();
        dto.setUsername("ab");  // 长度不够
        dto.setPassword("123");  // 密码太简单
        dto.setEmail("invalid-email");  // 邮箱格式错误

        Set<ConstraintViolation<UserCreateDTO>> violations = validator.validate(dto);

        assertThat(violations).hasSize(3);
        assertThat(violations).extracting(ConstraintViolation::getMessage)
                .contains("用户名长度必须在3-50个字符之间",
                         "密码必须包含至少一个大写字母、一个小写字母和一个数字",
                         "邮箱格式不正确");
    }
}
```

### 12.2 集成测试

```java
@SpringBootTest
@AutoConfigureTestDatabase
@Transactional
class UserControllerValidationTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testCreateUserWithInvalidData() throws Exception {
        String invalidUserJson = """
            {
                "username": "ab",
                "password": "123",
                "email": "invalid-email"
            }
            """;

        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidUserJson))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(422))
                .andExpect(jsonPath("$.message").value(containsString("用户名长度必须在3-50个字符之间")));
    }
}
```

## 13. 总结

Spring Boot的参数校验功能强大且灵活，通过合理使用各种校验注解和自定义校验器，可以有效保证数据的完整性和有效性。关键要点：

1. **选择合适的注解**: 根据数据类型和业务需求选择合适的校验注解
2. **分层设计**: 在不同层次进行不同类型的校验
3. **统一处理**: 使用全局异常处理器统一处理校验异常
4. **用户体验**: 提供清晰、友好的错误提示
5. **性能考虑**: 合理配置校验器，避免性能问题

通过遵循这些最佳实践，可以构建出健壮、用户友好的API接口。
