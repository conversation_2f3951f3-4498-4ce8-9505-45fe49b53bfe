# Swagger API文档使用说明

## 概述

本项目集成了Swagger（OpenAPI 3.0）来自动生成API文档，提供了友好的Web界面来查看和测试API接口。

## 访问地址

启动应用后，可以通过以下地址访问API文档：

- **Swagger UI界面**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/v3/api-docs
- **OpenAPI YAML**: http://localhost:8080/v3/api-docs.yaml

## 功能特性

### 1. 自动生成API文档
- 根据Controller注解自动生成接口文档
- 支持请求参数、响应结果的详细说明
- 实时更新，无需手动维护

### 2. 在线测试功能
- 直接在浏览器中测试API接口
- 支持各种HTTP方法（GET、POST、PUT、DELETE等）
- 自动填充示例数据

### 3. 接口分组管理
- 按Controller分组显示接口
- 支持标签排序和操作排序
- 清晰的接口层次结构

## 使用的注解

### 1. 类级别注解

#### @Tag
用于Controller类，定义接口分组：

```java
@Tag(name = "用户管理", description = "用户相关的API接口")
@RestController
public class UserController {
    // ...
}
```

### 2. 方法级别注解

#### @Operation
用于接口方法，定义操作说明：

```java
@Operation(summary = "根据ID查询用户", description = "通过用户ID获取用户详细信息")
@GetMapping("/{id}")
public Result<User> getUserById(@PathVariable Long id) {
    // ...
}
```

#### @Parameter
用于方法参数，定义参数说明：

```java
public Result<User> getUserById(
    @Parameter(description = "用户ID", required = true, example = "1") 
    @PathVariable Long id) {
    // ...
}
```

### 3. 实体类注解

#### @Schema
用于实体类和字段，定义数据模型：

```java
@Schema(description = "用户实体")
public class User {
    
    @Schema(description = "用户ID", example = "1")
    private Long id;
    
    @Schema(description = "用户名", example = "admin", required = true)
    private String username;
    
    // ...
}
```

## API接口分组

### 1. 用户管理 (User Management)
- 用户查询、创建、更新、删除
- 用户状态管理
- 用户统计信息

### 2. 演示接口 (Demo APIs)
- 统一响应格式演示
- 异常处理演示
- 参数校验演示

## 主要API接口

### 用户管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/users` | 查询所有用户 |
| GET | `/api/users/{id}` | 根据ID查询用户 |
| GET | `/api/users/username/{username}` | 根据用户名查询用户 |
| GET | `/api/users/page` | 分页查询用户 |
| GET | `/api/users/search` | 搜索用户 |
| POST | `/api/users` | 创建用户 |
| PUT | `/api/users/{id}` | 更新用户信息 |
| DELETE | `/api/users/{id}` | 删除用户 |
| GET | `/api/users/statistics` | 获取用户统计信息 |

### 演示接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/demo/success` | 成功响应示例 |
| GET | `/api/demo/business-error` | 业务异常示例 |
| GET | `/api/demo/system-error` | 系统异常示例 |
| POST | `/api/demo/validate-error` | 参数校验异常示例 |

## 使用步骤

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 访问Swagger UI
打开浏览器，访问：http://localhost:8080/swagger-ui.html

### 3. 浏览API文档
- 查看接口分组和详细说明
- 了解请求参数和响应格式
- 查看示例数据

### 4. 测试API接口
1. 选择要测试的接口
2. 点击"Try it out"按钮
3. 填写必要的参数
4. 点击"Execute"执行请求
5. 查看响应结果

## 响应格式

所有API接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2025-07-21 12:00:00"
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1000+ | 业务错误码 |

## 测试示例

### 1. 查询所有用户
```
GET /api/users
```

### 2. 根据ID查询用户
```
GET /api/users/1
```

### 3. 创建用户
```
POST /api/users
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "status": 1
}
```

### 4. 分页查询用户
```
GET /api/users/page?page=1&size=10
```

## 配置说明

### application.yml配置

```yaml
springdoc:
  api-docs:
    path: /v3/api-docs              # API文档JSON路径
  swagger-ui:
    path: /swagger-ui.html          # Swagger UI访问路径
    tags-sorter: alpha              # 标签排序方式
    operations-sorter: alpha        # 操作排序方式
  packages-to-scan: com.example.mybatisdemo.controller  # 扫描的包路径
```

### OpenAPI配置类

```java
@Configuration
public class OpenApiConfig {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("MyBatis Demo API")
                        .description("Spring Boot + MyBatis + MySQL 示例项目API文档")
                        .version("1.0.0"))
                .servers(List.of(
                        new Server().url("http://localhost:8080").description("开发环境")
                ));
    }
}
```

## 注意事项

1. **开发环境使用**: Swagger主要用于开发和测试环境，生产环境建议关闭
2. **安全考虑**: 如果需要在生产环境使用，建议添加访问控制
3. **性能影响**: Swagger会增加少量的启动时间和内存使用
4. **版本兼容**: 使用springdoc-openapi适配Spring Boot 3.x

## 扩展功能

### 1. 添加认证支持
可以配置JWT或其他认证方式的文档说明

### 2. 自定义主题
可以自定义Swagger UI的样式和主题

### 3. 导出文档
支持导出PDF、HTML等格式的API文档

### 4. Mock数据
可以配置Mock服务器来模拟API响应
