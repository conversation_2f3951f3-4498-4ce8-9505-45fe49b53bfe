package com.example.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.security.Key;
import java.util.Date;

/**
 * JWT令牌提供者
 * 负责JWT令牌的生成、验证和解析
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class JwtTokenProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);
    
    /**
     * JWT密钥
     */
    @Value("${app.jwt.secret:mySecretKey}")
    private String jwtSecret;
    
    /**
     * JWT过期时间（毫秒）
     */
    @Value("${app.jwt.expiration:86400000}")
    private long jwtExpirationInMs;
    
    /**
     * 签名密钥
     */
    private Key key;
    
    /**
     * 初始化签名密钥
     */
    @PostConstruct
    public void init() {
        // 使用HMAC-SHA算法生成密钥
        this.key = Keys.hmacShaKeyFor(jwtSecret.getBytes());
        logger.info("JWT密钥初始化完成");
    }
    
    /**
     * 根据认证信息生成JWT令牌
     * 
     * @param authentication Spring Security认证对象
     * @return JWT令牌字符串
     */
    public String generateToken(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);
        
        String token = Jwts.builder()
                .setSubject(userPrincipal.getId().toString())  // 设置主题为用户ID
                .setIssuedAt(new Date())                       // 设置签发时间
                .setExpiration(expiryDate)                     // 设置过期时间
                .claim("username", userPrincipal.getUsername()) // 添加用户名声明
                .claim("email", userPrincipal.getEmail())       // 添加邮箱声明
                .signWith(key, SignatureAlgorithm.HS512)       // 使用HS512算法签名
                .compact();
        
        logger.debug("为用户 {} 生成JWT令牌", userPrincipal.getUsername());
        return token;
    }
    
    /**
     * 根据用户ID生成JWT令牌
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param email 邮箱
     * @return JWT令牌字符串
     */
    public String generateTokenFromUserId(Long userId, String username, String email) {
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);
        
        String token = Jwts.builder()
                .setSubject(userId.toString())
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .claim("username", username)
                .claim("email", email)
                .signWith(key, SignatureAlgorithm.HS512)
                .compact();
        
        logger.debug("为用户ID {} 生成JWT令牌", userId);
        return token;
    }
    
    /**
     * 从JWT令牌中获取用户ID
     * 
     * @param token JWT令牌
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
        
        return Long.parseLong(claims.getSubject());
    }
    
    /**
     * 从JWT令牌中获取用户名
     * 
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
        
        return claims.get("username", String.class);
    }
    
    /**
     * 从JWT令牌中获取邮箱
     * 
     * @param token JWT令牌
     * @return 邮箱
     */
    public String getEmailFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
        
        return claims.get("email", String.class);
    }
    
    /**
     * 获取JWT令牌的过期时间
     * 
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
        
        return claims.getExpiration();
    }
    
    /**
     * 验证JWT令牌是否有效
     * 
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token);
            return true;
        } catch (SecurityException ex) {
            logger.error("JWT令牌签名无效: {}", ex.getMessage());
        } catch (MalformedJwtException ex) {
            logger.error("JWT令牌格式无效: {}", ex.getMessage());
        } catch (ExpiredJwtException ex) {
            logger.error("JWT令牌已过期: {}", ex.getMessage());
        } catch (UnsupportedJwtException ex) {
            logger.error("JWT令牌不支持: {}", ex.getMessage());
        } catch (IllegalArgumentException ex) {
            logger.error("JWT令牌参数为空: {}", ex.getMessage());
        }
        return false;
    }
    
    /**
     * 检查JWT令牌是否过期
     * 
     * @param token JWT令牌
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            logger.error("检查JWT令牌过期时间失败: {}", e.getMessage());
            return true;
        }
    }
    
    /**
     * 刷新JWT令牌
     * 
     * @param token 原JWT令牌
     * @return 新的JWT令牌
     */
    public String refreshToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);
            
            return Jwts.builder()
                    .setClaims(claims)
                    .setIssuedAt(new Date())
                    .setExpiration(expiryDate)
                    .signWith(key, SignatureAlgorithm.HS512)
                    .compact();
        } catch (Exception e) {
            logger.error("刷新JWT令牌失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取JWT过期时间（秒）
     * 
     * @return 过期时间（秒）
     */
    public long getJwtExpirationInSeconds() {
        return jwtExpirationInMs / 1000;
    }
}
