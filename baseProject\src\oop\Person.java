package oop;

/**
 * Person基类 - 演示抽象类的使用
 * 这是一个抽象类，不能被直接实例化
 */
public abstract class Person {
    // 受保护的属性，子类可以访问
    protected String name;
    protected int age;
    protected String id;
    
    /**
     * 构造方法
     * @param name 姓名
     * @param age 年龄
     * @param id 身份证号或学号/工号
     */
    public Person(String name, int age, String id) {
        this.name = name;
        this.age = age;
        this.id = id;
    }
    
    /**
     * 抽象方法 - 显示个人信息
     * 子类必须实现这个方法
     */
    public abstract void displayInfo();
    
    /**
     * 具体方法 - 通用的问候方法
     */
    public void greet() {
        System.out.println("你好，我是" + name);
    }
    
    // Getter和Setter方法
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        if (age > 0 && age < 150) {
            this.age = age;
        } else {
            System.out.println("年龄输入无效");
        }
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        return "Person{name='" + name + "', age=" + age + ", id='" + id + "'}";
    }
    
    /**
     * 重写equals方法
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Person person = (Person) obj;
        return id != null ? id.equals(person.id) : person.id == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
