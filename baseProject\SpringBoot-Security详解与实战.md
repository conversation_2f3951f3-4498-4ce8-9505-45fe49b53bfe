# Spring Boot Security 详解与实战

## 📚 目录

1. [Spring Security 概述](#spring-security-概述)
2. [核心概念](#核心概念)
3. [快速开始](#快速开始)
4. [认证机制](#认证机制)
5. [授权控制](#授权控制)
6. [JWT集成](#jwt集成)
7. [OAuth2集成](#oauth2集成)
8. [安全配置](#安全配置)
9. [实战案例](#实战案例)
10. [最佳实践](#最佳实践)

---

## Spring Security 概述

### 什么是 Spring Security

Spring Security 是一个功能强大且高度可定制的身份验证和访问控制框架，是保护基于Spring的应用程序的事实标准。

### 核心功能

- **认证 (Authentication)**: 验证用户身份
- **授权 (Authorization)**: 控制用户访问权限
- **攻击防护**: 防止CSRF、会话固定等攻击
- **密码加密**: 提供多种密码加密算法
- **会话管理**: 管理用户会话生命周期

### 主要优势

1. **全面的安全解决方案**
2. **高度可定制和扩展**
3. **与Spring生态系统无缝集成**
4. **支持多种认证方式**
5. **提供丰富的安全特性**

---

## 核心概念

### 1. 认证 (Authentication)

认证是验证用户身份的过程：

```java
// 认证对象
public interface Authentication extends Principal, Serializable {
    Collection<? extends GrantedAuthority> getAuthorities();
    Object getCredentials();
    Object getDetails();
    Object getPrincipal();
    boolean isAuthenticated();
    void setAuthenticated(boolean isAuthenticated);
}
```

### 2. 授权 (Authorization)

授权是确定用户是否有权限访问特定资源的过程：

```java
// 权限对象
public interface GrantedAuthority extends Serializable {
    String getAuthority();
}

// 简单权限实现
public class SimpleGrantedAuthority implements GrantedAuthority {
    private final String role;
    
    public SimpleGrantedAuthority(String role) {
        this.role = role;
    }
    
    @Override
    public String getAuthority() {
        return this.role;
    }
}
```

### 3. 用户详情 (UserDetails)

用户详情接口定义了用户的核心信息：

```java
public interface UserDetails extends Serializable {
    Collection<? extends GrantedAuthority> getAuthorities();
    String getPassword();
    String getUsername();
    boolean isAccountNonExpired();
    boolean isAccountNonLocked();
    boolean isCredentialsNonExpired();
    boolean isEnabled();
}
```

### 4. 安全上下文 (SecurityContext)

安全上下文持有当前认证的用户信息：

```java
// 获取当前认证用户
Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
String username = authentication.getName();
Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
```

---

## 快速开始

### 1. 添加依赖

```xml
<dependencies>
    <!-- Spring Boot Security Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    
    <!-- Spring Boot Web Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- Spring Boot Data JPA -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    
    <!-- MySQL Driver -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <scope>runtime</scope>
    </dependency>
    
    <!-- JWT -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>0.11.5</version>
    </dependency>
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-impl</artifactId>
        <version>0.11.5</version>
        <scope>runtime</scope>
    </dependency>
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-jackson</artifactId>
        <version>0.11.5</version>
        <scope>runtime</scope>
    </dependency>
</dependencies>
```

### 2. 基本配置

```yaml
# application.yml
spring:
  datasource:
    url: *****************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# 自定义配置
app:
  security:
    jwt:
      secret: mySecretKey
      expiration: 86400000 # 24小时
    password:
      strength: 10
```

### 3. 实体类定义

#### 用户实体

```java
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false, length = 50)
    private String username;
    
    @Column(unique = true, nullable = false, length = 100)
    private String email;
    
    @Column(nullable = false)
    private String password;
    
    @Column(name = "first_name", length = 50)
    private String firstName;
    
    @Column(name = "last_name", length = 50)
    private String lastName;
    
    @Column(nullable = false)
    private Boolean enabled = true;
    
    @Column(name = "account_non_expired")
    private Boolean accountNonExpired = true;
    
    @Column(name = "account_non_locked")
    private Boolean accountNonLocked = true;
    
    @Column(name = "credentials_non_expired")
    private Boolean credentialsNonExpired = true;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();
}
```

#### 角色实体

```java
@Entity
@Table(name = "roles")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Role {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(unique = true, nullable = false)
    private RoleName name;
    
    @Column(length = 200)
    private String description;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @ManyToMany(mappedBy = "roles")
    private Set<User> users = new HashSet<>();
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
}

// 角色枚举
public enum RoleName {
    ROLE_USER,
    ROLE_ADMIN,
    ROLE_MODERATOR
}
```

#### 权限实体

```java
@Entity
@Table(name = "permissions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Permission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false, length = 100)
    private String name;
    
    @Column(length = 200)
    private String description;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @ManyToMany(mappedBy = "permissions")
    private Set<Role> roles = new HashSet<>();
}
```

### 4. Repository 接口

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    Boolean existsByUsername(String username);
    
    Boolean existsByEmail(String email);
    
    @Query("SELECT u FROM User u JOIN FETCH u.roles r JOIN FETCH r.permissions WHERE u.username = :username")
    Optional<User> findByUsernameWithRolesAndPermissions(@Param("username") String username);
}

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    Optional<Role> findByName(RoleName roleName);
    
    @Query("SELECT r FROM Role r JOIN FETCH r.permissions WHERE r.name = :name")
    Optional<Role> findByNameWithPermissions(@Param("name") RoleName name);
}

@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {
    
    Optional<Permission> findByName(String name);
    
    List<Permission> findByNameIn(List<String> names);
}
```

### 5. UserDetails 实现

```java
public class UserPrincipal implements UserDetails {
    
    private Long id;
    private String username;
    private String email;
    private String password;
    private Collection<? extends GrantedAuthority> authorities;
    private boolean enabled;
    private boolean accountNonExpired;
    private boolean accountNonLocked;
    private boolean credentialsNonExpired;
    
    public UserPrincipal(Long id, String username, String email, String password,
                        Collection<? extends GrantedAuthority> authorities,
                        boolean enabled, boolean accountNonExpired,
                        boolean accountNonLocked, boolean credentialsNonExpired) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.password = password;
        this.authorities = authorities;
        this.enabled = enabled;
        this.accountNonExpired = accountNonExpired;
        this.accountNonLocked = accountNonLocked;
        this.credentialsNonExpired = credentialsNonExpired;
    }
    
    public static UserPrincipal create(User user) {
        List<GrantedAuthority> authorities = user.getRoles().stream()
            .flatMap(role -> {
                List<GrantedAuthority> roleAuthorities = new ArrayList<>();
                // 添加角色权限
                roleAuthorities.add(new SimpleGrantedAuthority(role.getName().name()));
                // 添加具体权限
                role.getPermissions().forEach(permission -> 
                    roleAuthorities.add(new SimpleGrantedAuthority(permission.getName())));
                return roleAuthorities.stream();
            })
            .collect(Collectors.toList());
        
        return new UserPrincipal(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getPassword(),
            authorities,
            user.getEnabled(),
            user.getAccountNonExpired(),
            user.getAccountNonLocked(),
            user.getCredentialsNonExpired()
        );
    }
    
    // Getters
    public Long getId() { return id; }
    public String getEmail() { return email; }
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserPrincipal that = (UserPrincipal) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
```

---

## 认证机制

### 1. UserDetailsService 实现

```java
@Service
@Transactional(readOnly = true)
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsernameWithRolesAndPermissions(username)
            .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + username));

        return UserPrincipal.create(user);
    }

    @Transactional(readOnly = true)
    public UserDetails loadUserById(Long id) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + id));

        return UserPrincipal.create(user);
    }
}
```

### 2. 密码编码器配置

```java
@Configuration
public class PasswordConfig {

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }

    @Bean
    public DelegatingPasswordEncoder delegatingPasswordEncoder() {
        Map<String, PasswordEncoder> encoders = new HashMap<>();
        encoders.put("bcrypt", new BCryptPasswordEncoder());
        encoders.put("pbkdf2", new Pbkdf2PasswordEncoder());
        encoders.put("scrypt", new SCryptPasswordEncoder());
        encoders.put("argon2", new Argon2PasswordEncoder());

        return new DelegatingPasswordEncoder("bcrypt", encoders);
    }
}
```

### 3. 认证提供者

```java
@Component
public class CustomAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        UserDetails userDetails = userDetailsService.loadUserByUsername(username);

        if (passwordEncoder.matches(password, userDetails.getPassword())) {
            return new UsernamePasswordAuthenticationToken(
                userDetails, password, userDetails.getAuthorities());
        } else {
            throw new BadCredentialsException("密码错误");
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }
}
```

### 4. JWT 工具类

```java
@Component
public class JwtTokenProvider {

    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);

    @Value("${app.security.jwt.secret}")
    private String jwtSecret;

    @Value("${app.security.jwt.expiration}")
    private int jwtExpirationInMs;

    private Key getSigningKey() {
        byte[] keyBytes = Decoders.BASE64.decode(jwtSecret);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String generateToken(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);

        return Jwts.builder()
                .setSubject(Long.toString(userPrincipal.getId()))
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    public String generateTokenFromUsername(String username) {
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);

        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    public Long getUserIdFromJWT(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        return Long.parseLong(claims.getSubject());
    }

    public String getUsernameFromJWT(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        return claims.getSubject();
    }

    public boolean validateToken(String authToken) {
        try {
            Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(authToken);
            return true;
        } catch (SecurityException ex) {
            logger.error("Invalid JWT signature");
        } catch (MalformedJwtException ex) {
            logger.error("Invalid JWT token");
        } catch (ExpiredJwtException ex) {
            logger.error("Expired JWT token");
        } catch (UnsupportedJwtException ex) {
            logger.error("Unsupported JWT token");
        } catch (IllegalArgumentException ex) {
            logger.error("JWT claims string is empty");
        }
        return false;
    }
}
```

### 5. JWT 认证过滤器

```java
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private CustomUserDetailsService customUserDetailsService;

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {
        try {
            String jwt = getJwtFromRequest(request);

            if (StringUtils.hasText(jwt) && tokenProvider.validateToken(jwt)) {
                Long userId = tokenProvider.getUserIdFromJWT(jwt);

                UserDetails userDetails = customUserDetailsService.loadUserById(userId);
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        } catch (Exception ex) {
            logger.error("Could not set user authentication in security context", ex);
        }

        filterChain.doFilter(request, response);
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

### 6. 认证入口点

```java
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationEntryPoint.class);

    @Override
    public void commence(HttpServletRequest httpServletRequest,
                        HttpServletResponse httpServletResponse,
                        AuthenticationException ex) throws IOException, ServletException {
        logger.error("Responding with unauthorized error. Message - {}", ex.getMessage());

        httpServletResponse.setContentType("application/json;charset=UTF-8");
        httpServletResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        Map<String, Object> response = new HashMap<>();
        response.put("error", "Unauthorized");
        response.put("message", "您需要登录才能访问此资源");
        response.put("timestamp", new Date());
        response.put("status", HttpServletResponse.SC_UNAUTHORIZED);
        response.put("path", httpServletRequest.getRequestURI());

        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(httpServletResponse.getOutputStream(), response);
    }
}
```

---

## 授权控制

### 1. 方法级安全

```java
@Configuration
@EnableGlobalMethodSecurity(
    securedEnabled = true,
    jsr250Enabled = true,
    prePostEnabled = true
)
public class MethodSecurityConfig extends GlobalMethodSecurityConfiguration {

    @Override
    protected MethodSecurityExpressionHandler createExpressionHandler() {
        DefaultMethodSecurityExpressionHandler expressionHandler =
            new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setPermissionEvaluator(new CustomPermissionEvaluator());
        return expressionHandler;
    }
}
```

### 2. 自定义权限评估器

```java
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        return hasPrivilege(authentication, permission.toString());
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        return hasPrivilege(authentication, permission.toString());
    }

    private boolean hasPrivilege(Authentication authentication, String permission) {
        return authentication.getAuthorities().stream()
            .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(permission));
    }
}
```

### 3. 权限注解使用示例

```java
@RestController
@RequestMapping("/api/users")
@PreAuthorize("hasRole('ADMIN')")
public class UserController {

    @GetMapping
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<List<User>> getAllUsers() {
        // 只有拥有USER_READ权限的用户才能访问
        return ResponseEntity.ok(userService.getAllUsers());
    }

    @PostMapping
    @PreAuthorize("hasAuthority('USER_CREATE')")
    public ResponseEntity<User> createUser(@Valid @RequestBody CreateUserRequest request) {
        // 只有拥有USER_CREATE权限的用户才能访问
        return ResponseEntity.ok(userService.createUser(request));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_UPDATE') or #id == authentication.principal.id")
    public ResponseEntity<User> updateUser(@PathVariable Long id,
                                          @Valid @RequestBody UpdateUserRequest request) {
        // 拥有USER_UPDATE权限或者是用户本人才能更新
        return ResponseEntity.ok(userService.updateUser(id, request));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_DELETE')")
    @PostAuthorize("returnObject.username != authentication.name")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        // 拥有USER_DELETE权限但不能删除自己
        userService.deleteUser(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{id}")
    @PostAuthorize("hasAuthority('USER_READ') or returnObject.id == authentication.principal.id")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        // 拥有USER_READ权限或者查看自己的信息
        return ResponseEntity.ok(userService.getUserById(id));
    }
}
```

---

## 安全配置

### 1. 主要安全配置类

```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private CustomUserDetailsService customUserDetailsService;

    @Autowired
    private JwtAuthenticationEntryPoint unauthorizedHandler;

    @Autowired
    private JwtAccessDeniedHandler accessDeniedHandler;

    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter() {
        return new JwtAuthenticationFilter();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(customUserDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
            .exceptionHandling()
                .authenticationEntryPoint(unauthorizedHandler)
                .accessDeniedHandler(accessDeniedHandler)
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeHttpRequests(authz -> authz
                // 公开端点
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()

                // 管理员端点
                .requestMatchers("/api/admin/**").hasRole("ADMIN")

                // 用户端点
                .requestMatchers(HttpMethod.GET, "/api/users/me").hasAnyRole("USER", "ADMIN")
                .requestMatchers(HttpMethod.PUT, "/api/users/me").hasAnyRole("USER", "ADMIN")

                // 其他所有请求需要认证
                .anyRequest().authenticated()
            );

        http.authenticationProvider(authenticationProvider());
        http.addFilterBefore(jwtAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
```

### 2. 访问拒绝处理器

```java
@Component
public class JwtAccessDeniedHandler implements AccessDeniedHandler {

    private static final Logger logger = LoggerFactory.getLogger(JwtAccessDeniedHandler.class);

    @Override
    public void handle(HttpServletRequest request,
                      HttpServletResponse response,
                      AccessDeniedException accessDeniedException) throws IOException, ServletException {

        logger.error("Access denied error. Message - {}", accessDeniedException.getMessage());

        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("error", "Access Denied");
        responseBody.put("message", "您没有权限访问此资源");
        responseBody.put("timestamp", new Date());
        responseBody.put("status", HttpServletResponse.SC_FORBIDDEN);
        responseBody.put("path", request.getRequestURI());

        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getOutputStream(), responseBody);
    }
}
```

---

## 实战案例

### 1. 认证控制器

```java
@RestController
@RequestMapping("/api/auth")
@Validated
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @PostMapping("/signin")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {

        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(
                loginRequest.getUsernameOrEmail(),
                loginRequest.getPassword()
            )
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);

        String jwt = tokenProvider.generateToken(authentication);
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        return ResponseEntity.ok(new JwtAuthenticationResponse(
            jwt,
            "Bearer",
            userPrincipal.getId(),
            userPrincipal.getUsername(),
            userPrincipal.getEmail(),
            userPrincipal.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.toList())
        ));
    }

    @PostMapping("/signup")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignUpRequest signUpRequest) {

        if (userService.existsByUsername(signUpRequest.getUsername())) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse(false, "用户名已存在!"));
        }

        if (userService.existsByEmail(signUpRequest.getEmail())) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse(false, "邮箱已被注册!"));
        }

        // 创建用户账户
        User user = User.builder()
            .username(signUpRequest.getUsername())
            .email(signUpRequest.getEmail())
            .password(passwordEncoder.encode(signUpRequest.getPassword()))
            .firstName(signUpRequest.getFirstName())
            .lastName(signUpRequest.getLastName())
            .build();

        User result = userService.createUser(user);

        return ResponseEntity.ok(new ApiResponse(true, "用户注册成功"));
    }

    @PostMapping("/refresh")
    public ResponseEntity<?> refreshToken(@Valid @RequestBody TokenRefreshRequest request) {
        String requestRefreshToken = request.getRefreshToken();

        return refreshTokenService.findByToken(requestRefreshToken)
            .map(refreshTokenService::verifyExpiration)
            .map(RefreshToken::getUser)
            .map(user -> {
                String token = tokenProvider.generateTokenFromUsername(user.getUsername());
                return ResponseEntity.ok(new TokenRefreshResponse(token, requestRefreshToken));
            })
            .orElseThrow(() -> new TokenRefreshException(requestRefreshToken, "刷新令牌不存在!"));
    }

    @PostMapping("/signout")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<?> logoutUser(@CurrentUser UserPrincipal currentUser) {
        refreshTokenService.deleteByUserId(currentUser.getId());
        return ResponseEntity.ok(new ApiResponse(true, "退出登录成功!"));
    }
}
```

### 2. 请求和响应对象

```java
// 登录请求
@Data
public class LoginRequest {

    @NotBlank(message = "用户名或邮箱不能为空")
    private String usernameOrEmail;

    @NotBlank(message = "密码不能为空")
    private String password;
}

// 注册请求
@Data
public class SignUpRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 15, message = "用户名长度必须在3-15之间")
    private String username;

    @NotBlank(message = "邮箱不能为空")
    @Size(max = 40, message = "邮箱长度不能超过40")
    @Email(message = "邮箱格式不正确")
    private String email;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    private String password;

    @Size(max = 40, message = "名字长度不能超过40")
    private String firstName;

    @Size(max = 40, message = "姓氏长度不能超过40")
    private String lastName;
}

// JWT认证响应
@Data
@AllArgsConstructor
public class JwtAuthenticationResponse {
    private String accessToken;
    private String tokenType;
    private Long id;
    private String username;
    private String email;
    private List<String> roles;
}

// 通用API响应
@Data
@AllArgsConstructor
public class ApiResponse {
    private Boolean success;
    private String message;
}

// 令牌刷新请求
@Data
public class TokenRefreshRequest {

    @NotBlank(message = "刷新令牌不能为空")
    private String refreshToken;
}

// 令牌刷新响应
@Data
@AllArgsConstructor
public class TokenRefreshResponse {
    private String accessToken;
    private String refreshToken;
}
```

### 3. 当前用户注解

```java
@Target({ElementType.PARAMETER, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@AuthenticationPrincipal
public @interface CurrentUser {
}
```

### 4. 用户服务实现

```java
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public User createUser(User user) {
        // 设置默认角色
        Role userRole = roleRepository.findByName(RoleName.ROLE_USER)
            .orElseThrow(() -> new AppException("用户角色未设置"));

        user.setRoles(Collections.singleton(userRole));

        return userRepository.save(user);
    }

    public User getUserById(Long id) {
        return userRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
    }

    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));
    }

    public User getUserByEmail(String email) {
        return userRepository.findByEmail(email)
            .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
    }

    public Boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    public Boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    public User updateUser(Long id, UpdateUserRequest request, UserPrincipal currentUser) {
        User user = getUserById(id);

        // 检查权限：只能更新自己的信息或者管理员可以更新任何人
        if (!user.getId().equals(currentUser.getId()) &&
            !currentUser.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN"))) {
            throw new AccessDeniedException("您只能更新自己的信息");
        }

        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setEmail(request.getEmail());

        return userRepository.save(user);
    }

    public void changePassword(Long userId, ChangePasswordRequest request, UserPrincipal currentUser) {
        User user = getUserById(userId);

        // 检查权限
        if (!user.getId().equals(currentUser.getId())) {
            throw new AccessDeniedException("您只能修改自己的密码");
        }

        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BadCredentialsException("旧密码不正确");
        }

        // 设置新密码
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        userRepository.save(user);
    }
}
```

---

## 最佳实践

### 1. 密码安全

```java
@Component
public class PasswordPolicy {

    private static final String PASSWORD_PATTERN =
        "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#&()–[{}]:;',?/*~$^+=<>]).{8,20}$";

    private static final Pattern pattern = Pattern.compile(PASSWORD_PATTERN);

    public boolean isValid(String password) {
        return pattern.matcher(password).matches();
    }

    public List<String> validatePassword(String password) {
        List<String> errors = new ArrayList<>();

        if (password == null || password.length() < 8) {
            errors.add("密码长度至少8位");
        }

        if (password != null && password.length() > 20) {
            errors.add("密码长度不能超过20位");
        }

        if (!password.matches(".*[0-9].*")) {
            errors.add("密码必须包含至少一个数字");
        }

        if (!password.matches(".*[a-z].*")) {
            errors.add("密码必须包含至少一个小写字母");
        }

        if (!password.matches(".*[A-Z].*")) {
            errors.add("密码必须包含至少一个大写字母");
        }

        if (!password.matches(".*[!@#&()–\\[{}\\]:;',?/*~$^+=<>].*")) {
            errors.add("密码必须包含至少一个特殊字符");
        }

        return errors;
    }
}
```

### 2. 安全审计

```java
@Component
@Slf4j
public class SecurityAuditService {

    @Autowired
    private AuditLogRepository auditLogRepository;

    @EventListener
    public void handleAuthenticationSuccess(AuthenticationSuccessEvent event) {
        String username = event.getAuthentication().getName();
        String details = event.getAuthentication().getDetails().toString();

        log.info("用户登录成功: {}", username);

        AuditLog auditLog = AuditLog.builder()
            .username(username)
            .action("LOGIN_SUCCESS")
            .details(details)
            .timestamp(LocalDateTime.now())
            .build();

        auditLogRepository.save(auditLog);
    }

    @EventListener
    public void handleAuthenticationFailure(AbstractAuthenticationFailureEvent event) {
        String username = event.getAuthentication().getName();
        String exception = event.getException().getMessage();

        log.warn("用户登录失败: {} - {}", username, exception);

        AuditLog auditLog = AuditLog.builder()
            .username(username)
            .action("LOGIN_FAILURE")
            .details(exception)
            .timestamp(LocalDateTime.now())
            .build();

        auditLogRepository.save(auditLog);
    }

    @EventListener
    public void handleAccessDenied(AuthorizationFailureEvent event) {
        String username = event.getAuthentication().getName();
        String resource = event.getSource().toString();

        log.warn("访问被拒绝: {} 尝试访问 {}", username, resource);

        AuditLog auditLog = AuditLog.builder()
            .username(username)
            .action("ACCESS_DENIED")
            .details(resource)
            .timestamp(LocalDateTime.now())
            .build();

        auditLogRepository.save(auditLog);
    }
}
```

### 3. 会话管理

```java
@Configuration
public class SessionConfig {

    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new HttpSessionEventPublisher();
    }

    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

    @EventListener
    public void sessionCreated(HttpSessionCreatedEvent event) {
        log.info("会话创建: {}", event.getSession().getId());
    }

    @EventListener
    public void sessionDestroyed(HttpSessionDestroyedEvent event) {
        log.info("会话销毁: {}", event.getSession().getId());
    }
}
```

### 4. 安全配置建议

```yaml
# application.yml
server:
  servlet:
    session:
      timeout: 30m
      cookie:
        http-only: true
        secure: true
        same-site: strict

spring:
  security:
    # 启用CSRF保护（如果不是纯API应用）
    csrf:
      enabled: false

    # 会话管理
    session:
      creation-policy: stateless

    # 密码编码
    password:
      strength: 12

# 自定义安全配置
app:
  security:
    jwt:
      secret: ${JWT_SECRET:myVerySecretKey}
      expiration: 86400000 # 24小时

    # 登录尝试限制
    login:
      max-attempts: 5
      lockout-duration: 15m

    # 密码策略
    password:
      min-length: 8
      max-length: 20
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special: true
```

### 5. 异常处理

```java
@ControllerAdvice
public class SecurityExceptionHandler {

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse> handleBadCredentials(BadCredentialsException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(new ApiResponse(false, "用户名或密码错误"));
    }

    @ExceptionHandler(UsernameNotFoundException.class)
    public ResponseEntity<ApiResponse> handleUsernameNotFound(UsernameNotFoundException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(new ApiResponse(false, "用户不存在"));
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<ApiResponse> handleAccessDenied(AccessDeniedException ex) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
            .body(new ApiResponse(false, "访问被拒绝"));
    }

    @ExceptionHandler(AccountExpiredException.class)
    public ResponseEntity<ApiResponse> handleAccountExpired(AccountExpiredException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(new ApiResponse(false, "账户已过期"));
    }

    @ExceptionHandler(CredentialsExpiredException.class)
    public ResponseEntity<ApiResponse> handleCredentialsExpired(CredentialsExpiredException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(new ApiResponse(false, "密码已过期"));
    }

    @ExceptionHandler(DisabledException.class)
    public ResponseEntity<ApiResponse> handleDisabled(DisabledException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(new ApiResponse(false, "账户已被禁用"));
    }

    @ExceptionHandler(LockedException.class)
    public ResponseEntity<ApiResponse> handleLocked(LockedException ex) {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
            .body(new ApiResponse(false, "账户已被锁定"));
    }
}
```

### 6. 测试示例

```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
public class SecurityIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Test
    public void testUserRegistration() {
        SignUpRequest signUpRequest = new SignUpRequest();
        signUpRequest.setUsername("testuser");
        signUpRequest.setEmail("<EMAIL>");
        signUpRequest.setPassword("Test123!");
        signUpRequest.setFirstName("Test");
        signUpRequest.setLastName("User");

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/auth/signup", signUpRequest, ApiResponse.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().getSuccess());
    }

    @Test
    public void testUserLogin() {
        // 先创建用户
        createTestUser();

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsernameOrEmail("testuser");
        loginRequest.setPassword("Test123!");

        ResponseEntity<JwtAuthenticationResponse> response = restTemplate.postForEntity(
            "/api/auth/signin", loginRequest, JwtAuthenticationResponse.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody().getAccessToken());
    }

    @Test
    public void testProtectedEndpointWithoutToken() {
        ResponseEntity<String> response = restTemplate.getForEntity(
            "/api/users/me", String.class);

        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    public void testProtectedEndpointWithToken() {
        String token = loginAndGetToken();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<User> response = restTemplate.exchange(
            "/api/users/me", HttpMethod.GET, entity, User.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    private void createTestUser() {
        User user = User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password(passwordEncoder.encode("Test123!"))
            .enabled(true)
            .accountNonExpired(true)
            .accountNonLocked(true)
            .credentialsNonExpired(true)
            .build();

        userRepository.save(user);
    }

    private String loginAndGetToken() {
        createTestUser();

        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsernameOrEmail("testuser");
        loginRequest.setPassword("Test123!");

        ResponseEntity<JwtAuthenticationResponse> response = restTemplate.postForEntity(
            "/api/auth/signin", loginRequest, JwtAuthenticationResponse.class);

        return response.getBody().getAccessToken();
    }
}
```

## 📝 总结

Spring Boot Security 提供了强大而灵活的安全解决方案：

### 核心特性
- **认证**: 支持多种认证方式（表单、JWT、OAuth2等）
- **授权**: 细粒度的权限控制
- **防护**: 内置多种安全防护机制
- **集成**: 与Spring生态系统无缝集成

### 最佳实践
1. **使用强密码策略**
2. **实施最小权限原则**
3. **启用安全审计**
4. **定期更新依赖**
5. **进行安全测试**

### 注意事项
- 生产环境中务必使用HTTPS
- 妥善保管JWT密钥
- 定期轮换密钥和令牌
- 监控异常登录行为
- 实施账户锁定策略

通过本文档的学习，您应该能够构建一个安全、可靠的Spring Boot应用程序。

---

## 完整示例项目

### 1. 项目结构

```
spring-boot-security-demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           ├── SecurityDemoApplication.java
│   │   │           ├── config/
│   │   │           │   ├── SecurityConfig.java
│   │   │           │   ├── JwtConfig.java
│   │   │           │   └── CorsConfig.java
│   │   │           ├── controller/
│   │   │           │   ├── AuthController.java
│   │   │           │   ├── UserController.java
│   │   │           │   └── AdminController.java
│   │   │           ├── service/
│   │   │           │   ├── UserService.java
│   │   │           │   ├── AuthService.java
│   │   │           │   └── impl/
│   │   │           ├── repository/
│   │   │           │   ├── UserRepository.java
│   │   │           │   ├── RoleRepository.java
│   │   │           │   └── PermissionRepository.java
│   │   │           ├── entity/
│   │   │           │   ├── User.java
│   │   │           │   ├── Role.java
│   │   │           │   └── Permission.java
│   │   │           ├── security/
│   │   │           │   ├── JwtTokenProvider.java
│   │   │           │   ├── JwtAuthenticationFilter.java
│   │   │           │   ├── JwtAuthenticationEntryPoint.java
│   │   │           │   ├── UserPrincipal.java
│   │   │           │   └── CustomUserDetailsService.java
│   │   │           ├── dto/
│   │   │           │   ├── LoginRequest.java
│   │   │           │   ├── SignUpRequest.java
│   │   │           │   └── JwtAuthenticationResponse.java
│   │   │           ├── exception/
│   │   │           │   ├── ResourceNotFoundException.java
│   │   │           │   └── BadRequestException.java
│   │   │           └── util/
│   │   │               └── ResponseUtil.java
│   │   └── resources/
│   │       ├── application.yml
│   │       ├── application-dev.yml
│   │       ├── application-prod.yml
│   │       └── data.sql
│   └── test/
│       └── java/
│           └── com/
│               └── example/
│                   ├── SecurityDemoApplicationTests.java
│                   ├── controller/
│                   └── service/
└── pom.xml
```

### 2. 主启动类

```java
@SpringBootApplication
public class SecurityDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(SecurityDemoApplication.class, args);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public ModelMapper modelMapper() {
        return new ModelMapper();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initializeData() {
        log.info("应用启动完成，开始初始化数据...");
        // 初始化默认用户和角色
    }
}
```

### 3. 完整的Controller示例

#### AuthController - 认证控制器

```java
@RestController
@RequestMapping("/api/auth")
@Validated
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserService userService;

    @Autowired
    private AuthService authService;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 用户登录
     */
    @PostMapping("/signin")
    public ResponseEntity<ApiResponse<JwtAuthenticationResponse>> authenticateUser(
            @Valid @RequestBody LoginRequest loginRequest,
            HttpServletRequest request) {

        try {
            // 记录登录尝试
            String clientIp = getClientIp(request);
            log.info("用户登录尝试: {} from IP: {}", loginRequest.getUsernameOrEmail(), clientIp);

            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsernameOrEmail(),
                    loginRequest.getPassword()
                )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            String jwt = tokenProvider.generateToken(authentication);
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 更新最后登录时间
            userService.updateLastLoginTime(userPrincipal.getId(), clientIp);

            JwtAuthenticationResponse response = JwtAuthenticationResponse.builder()
                .accessToken(jwt)
                .tokenType("Bearer")
                .expiresIn(tokenProvider.getJwtExpirationInMs())
                .user(UserInfo.builder()
                    .id(userPrincipal.getId())
                    .username(userPrincipal.getUsername())
                    .email(userPrincipal.getEmail())
                    .roles(userPrincipal.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .filter(auth -> auth.startsWith("ROLE_"))
                        .collect(Collectors.toList()))
                    .permissions(userPrincipal.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .filter(auth -> !auth.startsWith("ROLE_"))
                        .collect(Collectors.toList()))
                    .build())
                .build();

            log.info("用户登录成功: {}", userPrincipal.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登录成功", response));

        } catch (BadCredentialsException e) {
            log.warn("用户登录失败 - 密码错误: {}", loginRequest.getUsernameOrEmail());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("用户名或密码错误"));
        } catch (DisabledException e) {
            log.warn("用户登录失败 - 账户被禁用: {}", loginRequest.getUsernameOrEmail());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("账户已被禁用"));
        } catch (LockedException e) {
            log.warn("用户登录失败 - 账户被锁定: {}", loginRequest.getUsernameOrEmail());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(ApiResponse.error("账户已被锁定"));
        } catch (Exception e) {
            log.error("用户登录异常: {}", loginRequest.getUsernameOrEmail(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("登录失败，请稍后重试"));
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/signup")
    public ResponseEntity<ApiResponse<UserInfo>> registerUser(
            @Valid @RequestBody SignUpRequest signUpRequest,
            HttpServletRequest request) {

        try {
            String clientIp = getClientIp(request);
            log.info("用户注册尝试: {} from IP: {}", signUpRequest.getUsername(), clientIp);

            // 检查用户名是否已存在
            if (userService.existsByUsername(signUpRequest.getUsername())) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("用户名已存在"));
            }

            // 检查邮箱是否已存在
            if (userService.existsByEmail(signUpRequest.getEmail())) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("邮箱已被注册"));
            }

            // 创建用户
            User user = User.builder()
                .username(signUpRequest.getUsername())
                .email(signUpRequest.getEmail())
                .password(passwordEncoder.encode(signUpRequest.getPassword()))
                .firstName(signUpRequest.getFirstName())
                .lastName(signUpRequest.getLastName())
                .enabled(true)
                .accountNonExpired(true)
                .accountNonLocked(true)
                .credentialsNonExpired(true)
                .registrationIp(clientIp)
                .build();

            User savedUser = userService.createUser(user);

            UserInfo userInfo = UserInfo.builder()
                .id(savedUser.getId())
                .username(savedUser.getUsername())
                .email(savedUser.getEmail())
                .firstName(savedUser.getFirstName())
                .lastName(savedUser.getLastName())
                .build();

            log.info("用户注册成功: {}", savedUser.getUsername());
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("注册成功", userInfo));

        } catch (Exception e) {
            log.error("用户注册异常: {}", signUpRequest.getUsername(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("注册失败，请稍后重试"));
        }
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<TokenRefreshResponse>> refreshToken(
            @Valid @RequestBody TokenRefreshRequest request) {

        try {
            String requestRefreshToken = request.getRefreshToken();

            return authService.findByToken(requestRefreshToken)
                .map(authService::verifyExpiration)
                .map(RefreshToken::getUser)
                .map(user -> {
                    String token = tokenProvider.generateTokenFromUsername(user.getUsername());
                    TokenRefreshResponse response = new TokenRefreshResponse(token, requestRefreshToken);
                    return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", response));
                })
                .orElse(ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(ApiResponse.error("刷新令牌无效")));

        } catch (Exception e) {
            log.error("令牌刷新异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("令牌刷新失败"));
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/signout")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> logoutUser(@CurrentUser UserPrincipal currentUser) {

        try {
            authService.deleteRefreshTokenByUserId(currentUser.getId());
            log.info("用户登出成功: {}", currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
        } catch (Exception e) {
            log.error("用户登出异常: {}", currentUser.getUsername(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("登出失败"));
        }
    }

    /**
     * 忘记密码
     */
    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<Void>> forgotPassword(
            @Valid @RequestBody ForgotPasswordRequest request) {

        try {
            authService.sendPasswordResetEmail(request.getEmail());
            return ResponseEntity.ok(ApiResponse.success("密码重置邮件已发送"));
        } catch (ResourceNotFoundException e) {
            // 为了安全，即使邮箱不存在也返回成功
            return ResponseEntity.ok(ApiResponse.success("密码重置邮件已发送"));
        } catch (Exception e) {
            log.error("发送密码重置邮件异常: {}", request.getEmail(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("发送邮件失败，请稍后重试"));
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<Void>> resetPassword(
            @Valid @RequestBody ResetPasswordRequest request) {

        try {
            authService.resetPassword(request.getToken(), request.getNewPassword());
            return ResponseEntity.ok(ApiResponse.success("密码重置成功"));
        } catch (BadRequestException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("密码重置异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("密码重置失败"));
        }
    }

    /**
     * 验证令牌
     */
    @PostMapping("/validate-token")
    public ResponseEntity<ApiResponse<TokenValidationResponse>> validateToken(
            @Valid @RequestBody TokenValidationRequest request) {

        try {
            boolean isValid = tokenProvider.validateToken(request.getToken());

            TokenValidationResponse response = TokenValidationResponse.builder()
                .valid(isValid)
                .build();

            if (isValid) {
                Long userId = tokenProvider.getUserIdFromJWT(request.getToken());
                User user = userService.getUserById(userId);
                response.setUser(UserInfo.fromUser(user));
            }

            return ResponseEntity.ok(ApiResponse.success("令牌验证完成", response));

        } catch (Exception e) {
            log.error("令牌验证异常", e);
            return ResponseEntity.ok(ApiResponse.success("令牌验证完成",
                TokenValidationResponse.builder().valid(false).build()));
        }
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
```

#### UserController - 用户管理控制器

```java
@RestController
@RequestMapping("/api/users")
@Validated
@Slf4j
@PreAuthorize("hasRole('USER')")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private ModelMapper modelMapper;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<ApiResponse<UserProfile>> getCurrentUser(@CurrentUser UserPrincipal currentUser) {

        try {
            User user = userService.getUserById(currentUser.getId());
            UserProfile profile = modelMapper.map(user, UserProfile.class);

            return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", profile));
        } catch (Exception e) {
            log.error("获取当前用户信息异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户信息失败"));
        }
    }

    /**
     * 更新当前用户信息
     */
    @PutMapping("/me")
    public ResponseEntity<ApiResponse<UserProfile>> updateCurrentUser(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody UpdateUserRequest request) {

        try {
            User updatedUser = userService.updateUser(currentUser.getId(), request);
            UserProfile profile = modelMapper.map(updatedUser, UserProfile.class);

            log.info("用户信息更新成功: {}", currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户信息更新成功", profile));
        } catch (Exception e) {
            log.error("更新用户信息异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("更新用户信息失败"));
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/me/password")
    public ResponseEntity<ApiResponse<Void>> changePassword(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody ChangePasswordRequest request) {

        try {
            userService.changePassword(currentUser.getId(), request);
            log.info("用户密码修改成功: {}", currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("密码修改成功"));
        } catch (BadCredentialsException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error("原密码不正确"));
        } catch (Exception e) {
            log.error("修改密码异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("密码修改失败"));
        }
    }

    /**
     * 获取用户头像上传URL
     */
    @PostMapping("/me/avatar/upload-url")
    public ResponseEntity<ApiResponse<UploadUrlResponse>> getAvatarUploadUrl(
            @CurrentUser UserPrincipal currentUser) {

        try {
            UploadUrlResponse response = userService.generateAvatarUploadUrl(currentUser.getId());
            return ResponseEntity.ok(ApiResponse.success("获取上传URL成功", response));
        } catch (Exception e) {
            log.error("获取头像上传URL异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取上传URL失败"));
        }
    }

    /**
     * 更新用户头像
     */
    @PutMapping("/me/avatar")
    public ResponseEntity<ApiResponse<Void>> updateAvatar(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody UpdateAvatarRequest request) {

        try {
            userService.updateAvatar(currentUser.getId(), request.getAvatarUrl());
            log.info("用户头像更新成功: {}", currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("头像更新成功"));
        } catch (Exception e) {
            log.error("更新头像异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("头像更新失败"));
        }
    }

    /**
     * 获取用户登录历史
     */
    @GetMapping("/me/login-history")
    public ResponseEntity<ApiResponse<PageResult<LoginHistory>>> getLoginHistory(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            PageResult<LoginHistory> history = userService.getLoginHistory(currentUser.getId(), page, size);
            return ResponseEntity.ok(ApiResponse.success("获取登录历史成功", history));
        } catch (Exception e) {
            log.error("获取登录历史异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取登录历史失败"));
        }
    }

    /**
     * 获取用户操作日志
     */
    @GetMapping("/me/activity-logs")
    public ResponseEntity<ApiResponse<PageResult<ActivityLog>>> getActivityLogs(
            @CurrentUser UserPrincipal currentUser,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            PageResult<ActivityLog> logs = userService.getActivityLogs(currentUser.getId(), page, size);
            return ResponseEntity.ok(ApiResponse.success("获取操作日志成功", logs));
        } catch (Exception e) {
            log.error("获取操作日志异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取操作日志失败"));
        }
    }

    /**
     * 启用/禁用双因子认证
     */
    @PutMapping("/me/2fa")
    public ResponseEntity<ApiResponse<TwoFactorAuthResponse>> toggleTwoFactorAuth(
            @CurrentUser UserPrincipal currentUser,
            @Valid @RequestBody TwoFactorAuthRequest request) {

        try {
            TwoFactorAuthResponse response = userService.toggleTwoFactorAuth(currentUser.getId(), request);
            log.info("用户双因子认证设置更新: {} - 启用: {}", currentUser.getUsername(), request.isEnabled());
            return ResponseEntity.ok(ApiResponse.success("双因子认证设置成功", response));
        } catch (Exception e) {
            log.error("设置双因子认证异常: {}", currentUser.getId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("双因子认证设置失败"));
        }
    }
}
```

#### AdminController - 管理员控制器

```java
@RestController
@RequestMapping("/api/admin")
@Validated
@Slf4j
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 获取所有用户列表
     */
    @GetMapping("/users")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<ApiResponse<PageResult<UserInfo>>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean enabled) {

        try {
            UserSearchCriteria criteria = UserSearchCriteria.builder()
                .search(search)
                .role(role)
                .enabled(enabled)
                .build();

            PageResult<UserInfo> users = userService.searchUsers(criteria, page, size);
            return ResponseEntity.ok(ApiResponse.success("获取用户列表成功", users));
        } catch (Exception e) {
            log.error("获取用户列表异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户列表失败"));
        }
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/users/{id}")
    @PreAuthorize("hasAuthority('USER_READ')")
    public ResponseEntity<ApiResponse<UserDetail>> getUserById(@PathVariable Long id) {

        try {
            UserDetail userDetail = userService.getUserDetailById(id);
            return ResponseEntity.ok(ApiResponse.success("获取用户详情成功", userDetail));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在"));
        } catch (Exception e) {
            log.error("获取用户详情异常: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取用户详情失败"));
        }
    }

    /**
     * 创建用户
     */
    @PostMapping("/users")
    @PreAuthorize("hasAuthority('USER_CREATE')")
    public ResponseEntity<ApiResponse<UserInfo>> createUser(
            @Valid @RequestBody CreateUserRequest request,
            @CurrentUser UserPrincipal currentUser) {

        try {
            User user = userService.createUserByAdmin(request, currentUser.getId());
            UserInfo userInfo = UserInfo.fromUser(user);

            log.info("管理员创建用户成功: {} by {}", user.getUsername(), currentUser.getUsername());
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("用户创建成功", userInfo));
        } catch (BadRequestException e) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("创建用户异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("用户创建失败"));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/users/{id}")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ResponseEntity<ApiResponse<UserInfo>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UpdateUserByAdminRequest request,
            @CurrentUser UserPrincipal currentUser) {

        try {
            User user = userService.updateUserByAdmin(id, request, currentUser.getId());
            UserInfo userInfo = UserInfo.fromUser(user);

            log.info("管理员更新用户成功: {} by {}", user.getUsername(), currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户更新成功", userInfo));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在"));
        } catch (Exception e) {
            log.error("更新用户异常: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("用户更新失败"));
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{id}")
    @PreAuthorize("hasAuthority('USER_DELETE')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(
            @PathVariable Long id,
            @CurrentUser UserPrincipal currentUser) {

        try {
            // 不能删除自己
            if (id.equals(currentUser.getId())) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("不能删除自己的账户"));
            }

            userService.deleteUser(id, currentUser.getId());
            log.info("管理员删除用户成功: {} by {}", id, currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在"));
        } catch (Exception e) {
            log.error("删除用户异常: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("用户删除失败"));
        }
    }

    /**
     * 启用/禁用用户
     */
    @PutMapping("/users/{id}/status")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ResponseEntity<ApiResponse<Void>> toggleUserStatus(
            @PathVariable Long id,
            @Valid @RequestBody ToggleUserStatusRequest request,
            @CurrentUser UserPrincipal currentUser) {

        try {
            // 不能禁用自己
            if (id.equals(currentUser.getId()) && !request.isEnabled()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("不能禁用自己的账户"));
            }

            userService.toggleUserStatus(id, request.isEnabled(), currentUser.getId());
            String action = request.isEnabled() ? "启用" : "禁用";
            log.info("管理员{}用户成功: {} by {}", action, id, currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("用户状态更新成功"));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在"));
        } catch (Exception e) {
            log.error("更新用户状态异常: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("用户状态更新失败"));
        }
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/users/{id}/password/reset")
    @PreAuthorize("hasAuthority('USER_UPDATE')")
    public ResponseEntity<ApiResponse<ResetPasswordResponse>> resetUserPassword(
            @PathVariable Long id,
            @CurrentUser UserPrincipal currentUser) {

        try {
            String newPassword = userService.resetUserPassword(id, currentUser.getId());
            ResetPasswordResponse response = new ResetPasswordResponse(newPassword);

            log.info("管理员重置用户密码成功: {} by {}", id, currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("密码重置成功", response));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在"));
        } catch (Exception e) {
            log.error("重置用户密码异常: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("密码重置失败"));
        }
    }

    /**
     * 分配用户角色
     */
    @PutMapping("/users/{id}/roles")
    @PreAuthorize("hasAuthority('USER_ROLE_ASSIGN')")
    public ResponseEntity<ApiResponse<Void>> assignUserRoles(
            @PathVariable Long id,
            @Valid @RequestBody AssignRolesRequest request,
            @CurrentUser UserPrincipal currentUser) {

        try {
            userService.assignRoles(id, request.getRoleIds(), currentUser.getId());
            log.info("管理员分配用户角色成功: {} roles: {} by {}",
                    id, request.getRoleIds(), currentUser.getUsername());
            return ResponseEntity.ok(ApiResponse.success("角色分配成功"));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("用户不存在"));
        } catch (Exception e) {
            log.error("分配用户角色异常: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("角色分配失败"));
        }
    }

    /**
     * 获取系统统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('SYSTEM_READ')")
    public ResponseEntity<ApiResponse<SystemStatistics>> getSystemStatistics() {

        try {
            SystemStatistics statistics = userService.getSystemStatistics();
            return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", statistics));
        } catch (Exception e) {
            log.error("获取系统统计信息异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取统计信息失败"));
        }
    }
}
```

### 4. Service层实现示例

#### UserService - 用户服务实现

```java
@Service
@Transactional
@Slf4j
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private EmailService emailService;

    @Autowired
    private FileStorageService fileStorageService;

    @Override
    @Transactional(readOnly = true)
    public User getUserById(Long id) {
        return userRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", id));
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User", "username", username));
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserByEmail(String email) {
        return userRepository.findByEmail(email)
            .orElseThrow(() -> new ResourceNotFoundException("User", "email", email));
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public User createUser(User user) {
        // 设置默认角色
        Role userRole = roleRepository.findByName(RoleName.ROLE_USER)
            .orElseThrow(() -> new RuntimeException("默认用户角色未找到"));

        user.setRoles(Set.of(userRole));
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        User savedUser = userRepository.save(user);

        // 发送欢迎邮件
        try {
            emailService.sendWelcomeEmail(savedUser.getEmail(), savedUser.getUsername());
        } catch (Exception e) {
            log.warn("发送欢迎邮件失败: {}", savedUser.getEmail(), e);
        }

        return savedUser;
    }

    @Override
    public User updateUser(Long id, UpdateUserRequest request) {
        User user = getUserById(id);

        // 检查邮箱是否被其他用户使用
        if (!user.getEmail().equals(request.getEmail()) && existsByEmail(request.getEmail())) {
            throw new BadRequestException("邮箱已被其他用户使用");
        }

        user.setEmail(request.getEmail());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhone(request.getPhone());
        user.setBirthDate(request.getBirthDate());
        user.setGender(request.getGender());
        user.setUpdatedAt(LocalDateTime.now());

        return userRepository.save(user);
    }

    @Override
    public void changePassword(Long userId, ChangePasswordRequest request) {
        User user = getUserById(userId);

        // 验证旧密码
        if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
            throw new BadCredentialsException("原密码不正确");
        }

        // 检查新密码强度
        validatePasswordStrength(request.getNewPassword());

        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setCredentialsNonExpired(true);
        user.setPasswordChangedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        userRepository.save(user);

        // 发送密码修改通知邮件
        try {
            emailService.sendPasswordChangeNotification(user.getEmail(), user.getUsername());
        } catch (Exception e) {
            log.warn("发送密码修改通知邮件失败: {}", user.getEmail(), e);
        }
    }

    @Override
    public void updateLastLoginTime(Long userId, String clientIp) {
        User user = getUserById(userId);
        user.setLastLoginAt(LocalDateTime.now());
        user.setLastLoginIp(clientIp);
        user.setLoginCount(user.getLoginCount() + 1);
        userRepository.save(user);

        // 记录登录历史
        LoginHistory loginHistory = LoginHistory.builder()
            .userId(userId)
            .loginTime(LocalDateTime.now())
            .clientIp(clientIp)
            .userAgent(getCurrentUserAgent())
            .success(true)
            .build();

        loginHistoryRepository.save(loginHistory);
    }

    @Override
    public UploadUrlResponse generateAvatarUploadUrl(Long userId) {
        User user = getUserById(userId);
        String fileName = "avatar_" + userId + "_" + System.currentTimeMillis();
        return fileStorageService.generateUploadUrl("avatars", fileName);
    }

    @Override
    public void updateAvatar(Long userId, String avatarUrl) {
        User user = getUserById(userId);

        // 删除旧头像
        if (user.getAvatarUrl() != null) {
            try {
                fileStorageService.deleteFile(user.getAvatarUrl());
            } catch (Exception e) {
                log.warn("删除旧头像失败: {}", user.getAvatarUrl(), e);
            }
        }

        user.setAvatarUrl(avatarUrl);
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<LoginHistory> getLoginHistory(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("loginTime").descending());
        Page<LoginHistory> historyPage = loginHistoryRepository.findByUserId(userId, pageable);

        return PageResult.<LoginHistory>builder()
            .content(historyPage.getContent())
            .pageNumber(historyPage.getNumber())
            .pageSize(historyPage.getSize())
            .totalElements(historyPage.getTotalElements())
            .totalPages(historyPage.getTotalPages())
            .first(historyPage.isFirst())
            .last(historyPage.isLast())
            .build();
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<ActivityLog> getActivityLogs(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<ActivityLog> logsPage = activityLogRepository.findByUserId(userId, pageable);

        return PageResult.<ActivityLog>builder()
            .content(logsPage.getContent())
            .pageNumber(logsPage.getNumber())
            .pageSize(logsPage.getSize())
            .totalElements(logsPage.getTotalElements())
            .totalPages(logsPage.getTotalPages())
            .first(logsPage.isFirst())
            .last(logsPage.isLast())
            .build();
    }

    @Override
    public TwoFactorAuthResponse toggleTwoFactorAuth(Long userId, TwoFactorAuthRequest request) {
        User user = getUserById(userId);

        if (request.isEnabled()) {
            // 启用双因子认证
            String secret = generateTwoFactorSecret();
            String qrCodeUrl = generateQRCodeUrl(user.getUsername(), secret);

            user.setTwoFactorEnabled(true);
            user.setTwoFactorSecret(secret);
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            return TwoFactorAuthResponse.builder()
                .enabled(true)
                .secret(secret)
                .qrCodeUrl(qrCodeUrl)
                .backupCodes(generateBackupCodes())
                .build();
        } else {
            // 禁用双因子认证
            user.setTwoFactorEnabled(false);
            user.setTwoFactorSecret(null);
            user.setUpdatedAt(LocalDateTime.now());
            userRepository.save(user);

            return TwoFactorAuthResponse.builder()
                .enabled(false)
                .build();
        }
    }

    // 管理员相关方法

    @Override
    @Transactional(readOnly = true)
    public PageResult<UserInfo> searchUsers(UserSearchCriteria criteria, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());

        Specification<User> spec = Specification.where(null);

        if (StringUtils.hasText(criteria.getSearch())) {
            spec = spec.and((root, query, cb) ->
                cb.or(
                    cb.like(cb.lower(root.get("username")), "%" + criteria.getSearch().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("email")), "%" + criteria.getSearch().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("firstName")), "%" + criteria.getSearch().toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("lastName")), "%" + criteria.getSearch().toLowerCase() + "%")
                )
            );
        }

        if (StringUtils.hasText(criteria.getRole())) {
            spec = spec.and((root, query, cb) -> {
                Join<User, Role> roleJoin = root.join("roles");
                return cb.equal(roleJoin.get("name"), RoleName.valueOf(criteria.getRole()));
            });
        }

        if (criteria.getEnabled() != null) {
            spec = spec.and((root, query, cb) -> cb.equal(root.get("enabled"), criteria.getEnabled()));
        }

        Page<User> userPage = userRepository.findAll(spec, pageable);
        List<UserInfo> userInfos = userPage.getContent().stream()
            .map(UserInfo::fromUser)
            .collect(Collectors.toList());

        return PageResult.<UserInfo>builder()
            .content(userInfos)
            .pageNumber(userPage.getNumber())
            .pageSize(userPage.getSize())
            .totalElements(userPage.getTotalElements())
            .totalPages(userPage.getTotalPages())
            .first(userPage.isFirst())
            .last(userPage.isLast())
            .build();
    }

    @Override
    @Transactional(readOnly = true)
    public UserDetail getUserDetailById(Long id) {
        User user = getUserById(id);
        return UserDetail.fromUser(user);
    }

    @Override
    public User createUserByAdmin(CreateUserRequest request, Long adminId) {
        // 检查用户名和邮箱是否已存在
        if (existsByUsername(request.getUsername())) {
            throw new BadRequestException("用户名已存在");
        }

        if (existsByEmail(request.getEmail())) {
            throw new BadRequestException("邮箱已存在");
        }

        // 验证密码强度
        validatePasswordStrength(request.getPassword());

        User user = User.builder()
            .username(request.getUsername())
            .email(request.getEmail())
            .password(passwordEncoder.encode(request.getPassword()))
            .firstName(request.getFirstName())
            .lastName(request.getLastName())
            .phone(request.getPhone())
            .enabled(request.isEnabled())
            .accountNonExpired(true)
            .accountNonLocked(true)
            .credentialsNonExpired(true)
            .createdBy(adminId)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // 设置角色
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            Set<Role> roles = roleRepository.findAllById(request.getRoleIds())
                .stream().collect(Collectors.toSet());
            user.setRoles(roles);
        } else {
            // 默认用户角色
            Role userRole = roleRepository.findByName(RoleName.ROLE_USER)
                .orElseThrow(() -> new RuntimeException("默认用户角色未找到"));
            user.setRoles(Set.of(userRole));
        }

        User savedUser = userRepository.save(user);

        // 发送账户创建通知邮件
        try {
            emailService.sendAccountCreatedNotification(savedUser.getEmail(),
                savedUser.getUsername(), request.getPassword());
        } catch (Exception e) {
            log.warn("发送账户创建通知邮件失败: {}", savedUser.getEmail(), e);
        }

        return savedUser;
    }

    private void validatePasswordStrength(String password) {
        if (password.length() < 8) {
            throw new BadRequestException("密码长度至少8位");
        }

        if (!password.matches(".*[A-Z].*")) {
            throw new BadRequestException("密码必须包含大写字母");
        }

        if (!password.matches(".*[a-z].*")) {
            throw new BadRequestException("密码必须包含小写字母");
        }

        if (!password.matches(".*[0-9].*")) {
            throw new BadRequestException("密码必须包含数字");
        }

        if (!password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            throw new BadRequestException("密码必须包含特殊字符");
        }
    }

    private String generateTwoFactorSecret() {
        return Base32.random();
    }

    private String generateQRCodeUrl(String username, String secret) {
        return String.format("otpauth://totp/%s?secret=%s&issuer=MyApp", username, secret);
    }

    private List<String> generateBackupCodes() {
        List<String> codes = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            codes.add(RandomStringUtils.randomAlphanumeric(8).toUpperCase());
        }
        return codes;
    }

    private String getCurrentUserAgent() {
        // 从当前请求中获取User-Agent
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader("User-Agent");
        }
        return "Unknown";
    }
}
```

### 5. 配置文件示例

#### application.yml

```yaml
# 应用配置
spring:
  application:
    name: spring-boot-security-demo

  # 数据源配置
  datasource:
    url: *****************************************?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
    username: ${DB_USERNAME:root}
    username: ${DB_PASSWORD:123456}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect

  # Redis配置
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # 邮件配置
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /
    session:
      timeout: 30m
      cookie:
        http-only: true
        secure: false # 生产环境设置为true
        same-site: lax

# 日志配置
logging:
  level:
    com.example: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/security-demo.log

# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:myVerySecretKeyThatIsAtLeast256BitsLongForHS256Algorithm}
    expiration: ${JWT_EXPIRATION:86400000} # 24小时
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7天

  # 安全配置
  security:
    # 密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special: true

    # 登录限制
    login:
      max-attempts: 5
      lockout-duration: 15m

    # 会话配置
    session:
      max-sessions: 1
      prevent-login-if-maximum-exceeded: false

  # CORS配置
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

  # 文件存储配置
  file-storage:
    type: ${FILE_STORAGE_TYPE:local} # local, s3, oss
    local:
      upload-dir: ${FILE_UPLOAD_DIR:./uploads}
    s3:
      bucket: ${S3_BUCKET:my-bucket}
      region: ${S3_REGION:us-east-1}
      access-key: ${S3_ACCESS_KEY:}
      secret-key: ${S3_SECRET_KEY:}

  # 邮件模板配置
  mail:
    from: ${MAIL_FROM:<EMAIL>}
    from-name: ${MAIL_FROM_NAME:Security Demo}
    templates:
      welcome: classpath:templates/email/welcome.html
      password-reset: classpath:templates/email/password-reset.html
      password-change: classpath:templates/email/password-change.html
      account-created: classpath:templates/email/account-created.html

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: Spring Boot Security Demo Application
    version: 1.0.0
    encoding: UTF-8
  java:
    version: ${java.version}
```

#### application-dev.yml

```yaml
# 开发环境配置
spring:
  datasource:
    url: *****************************************_dev?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8

  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop

  # 开发环境邮件配置（使用MailHog等工具）
  mail:
    host: localhost
    port: 1025
    username:
    password:
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false

# 开发环境日志
logging:
  level:
    com.example: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# 开发环境安全配置
app:
  jwt:
    secret: devSecretKeyForDevelopmentOnly
    expiration: 3600000 # 1小时

  cors:
    allowed-origins: "*"

  security:
    password:
      min-length: 6 # 开发环境降低密码要求
      require-uppercase: false
      require-lowercase: false
      require-digit: false
      require-special: false
```

#### application-prod.yml

```yaml
# 生产环境配置
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=GMT%2B8
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10

  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

# 生产环境服务器配置
server:
  servlet:
    session:
      cookie:
        secure: true # 生产环境启用HTTPS
        same-site: strict

# 生产环境日志
logging:
  level:
    com.example: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
  file:
    name: /var/log/security-demo/application.log

# 生产环境安全配置
app:
  jwt:
    expiration: 1800000 # 30分钟
    refresh-expiration: 86400000 # 1天

  cors:
    allowed-origins: https://yourdomain.com,https://www.yourdomain.com

  security:
    login:
      max-attempts: 3
      lockout-duration: 30m

    session:
      max-sessions: 1
      prevent-login-if-maximum-exceeded: true

# 生产环境监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: never
```

### 6. 测试示例

#### 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
@Rollback
public class SecurityIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenProvider tokenProvider;

    private User testUser;
    private Role userRole;

    @BeforeEach
    public void setUp() {
        // 创建测试角色
        userRole = Role.builder()
            .name(RoleName.ROLE_USER)
            .description("普通用户")
            .build();
        userRole = roleRepository.save(userRole);

        // 创建测试用户
        testUser = User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password(passwordEncoder.encode("Test123!"))
            .firstName("Test")
            .lastName("User")
            .enabled(true)
            .accountNonExpired(true)
            .accountNonLocked(true)
            .credentialsNonExpired(true)
            .roles(Set.of(userRole))
            .build();
        testUser = userRepository.save(testUser);
    }

    @Test
    public void testUserRegistration_Success() {
        SignUpRequest signUpRequest = SignUpRequest.builder()
            .username("newuser")
            .email("<EMAIL>")
            .password("NewUser123!")
            .firstName("New")
            .lastName("User")
            .build();

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/auth/signup", signUpRequest, ApiResponse.class);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());

        // 验证用户已创建
        assertTrue(userRepository.existsByUsername("newuser"));
    }

    @Test
    public void testUserRegistration_DuplicateUsername() {
        SignUpRequest signUpRequest = SignUpRequest.builder()
            .username("testuser") // 已存在的用户名
            .email("<EMAIL>")
            .password("Test123!")
            .firstName("Another")
            .lastName("User")
            .build();

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/auth/signup", signUpRequest, ApiResponse.class);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertFalse(response.getBody().isSuccess());
        assertEquals("用户名已存在", response.getBody().getMessage());
    }

    @Test
    public void testUserLogin_Success() {
        LoginRequest loginRequest = LoginRequest.builder()
            .usernameOrEmail("testuser")
            .password("Test123!")
            .build();

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/auth/signin", loginRequest, ApiResponse.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());

        // 验证响应包含JWT令牌
        Map<String, Object> data = (Map<String, Object>) response.getBody().getData();
        assertNotNull(data.get("accessToken"));
        assertEquals("Bearer", data.get("tokenType"));
    }

    @Test
    public void testUserLogin_InvalidCredentials() {
        LoginRequest loginRequest = LoginRequest.builder()
            .usernameOrEmail("testuser")
            .password("wrongpassword")
            .build();

        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/auth/signin", loginRequest, ApiResponse.class);

        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        assertFalse(response.getBody().isSuccess());
    }

    @Test
    public void testProtectedEndpoint_WithValidToken() {
        // 生成有效令牌
        Authentication authentication = new UsernamePasswordAuthenticationToken(
            UserPrincipal.create(testUser), null, UserPrincipal.create(testUser).getAuthorities());
        String token = tokenProvider.generateToken(authentication);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/users/me", HttpMethod.GET, entity, ApiResponse.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
    }

    @Test
    public void testProtectedEndpoint_WithoutToken() {
        ResponseEntity<ApiResponse> response = restTemplate.getForEntity(
            "/api/users/me", ApiResponse.class);

        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    public void testProtectedEndpoint_WithInvalidToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth("invalid-token");
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/users/me", HttpMethod.GET, entity, ApiResponse.class);

        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    public void testUpdateUserProfile_Success() {
        // 生成有效令牌
        Authentication authentication = new UsernamePasswordAuthenticationToken(
            UserPrincipal.create(testUser), null, UserPrincipal.create(testUser).getAuthorities());
        String token = tokenProvider.generateToken(authentication);

        UpdateUserRequest updateRequest = UpdateUserRequest.builder()
            .email("<EMAIL>")
            .firstName("Updated")
            .lastName("User")
            .phone("**********")
            .build();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateUserRequest> entity = new HttpEntity<>(updateRequest, headers);

        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/users/me", HttpMethod.PUT, entity, ApiResponse.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());

        // 验证用户信息已更新
        User updatedUser = userRepository.findById(testUser.getId()).orElse(null);
        assertNotNull(updatedUser);
        assertEquals("<EMAIL>", updatedUser.getEmail());
        assertEquals("Updated", updatedUser.getFirstName());
    }

    @Test
    public void testChangePassword_Success() {
        // 生成有效令牌
        Authentication authentication = new UsernamePasswordAuthenticationToken(
            UserPrincipal.create(testUser), null, UserPrincipal.create(testUser).getAuthorities());
        String token = tokenProvider.generateToken(authentication);

        ChangePasswordRequest changePasswordRequest = ChangePasswordRequest.builder()
            .oldPassword("Test123!")
            .newPassword("NewPassword123!")
            .build();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChangePasswordRequest> entity = new HttpEntity<>(changePasswordRequest, headers);

        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/users/me/password", HttpMethod.PUT, entity, ApiResponse.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());

        // 验证密码已更改
        User updatedUser = userRepository.findById(testUser.getId()).orElse(null);
        assertNotNull(updatedUser);
        assertTrue(passwordEncoder.matches("NewPassword123!", updatedUser.getPassword()));
    }

    @Test
    public void testChangePassword_WrongOldPassword() {
        // 生成有效令牌
        Authentication authentication = new UsernamePasswordAuthenticationToken(
            UserPrincipal.create(testUser), null, UserPrincipal.create(testUser).getAuthorities());
        String token = tokenProvider.generateToken(authentication);

        ChangePasswordRequest changePasswordRequest = ChangePasswordRequest.builder()
            .oldPassword("WrongPassword")
            .newPassword("NewPassword123!")
            .build();

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChangePasswordRequest> entity = new HttpEntity<>(changePasswordRequest, headers);

        ResponseEntity<ApiResponse> response = restTemplate.exchange(
            "/api/users/me/password", HttpMethod.PUT, entity, ApiResponse.class);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertFalse(response.getBody().isSuccess());
        assertEquals("原密码不正确", response.getBody().getMessage());
    }
}
```

### 7. 前端集成示例

#### JavaScript/TypeScript 客户端

```javascript
// auth.service.js
class AuthService {
    constructor() {
        this.baseURL = 'http://localhost:8080/api';
        this.tokenKey = 'access_token';
        this.refreshTokenKey = 'refresh_token';
    }

    async login(usernameOrEmail, password) {
        try {
            const response = await fetch(`${this.baseURL}/auth/signin`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    usernameOrEmail,
                    password
                })
            });

            const result = await response.json();

            if (result.success) {
                const { accessToken, refreshToken } = result.data;
                localStorage.setItem(this.tokenKey, accessToken);
                localStorage.setItem(this.refreshTokenKey, refreshToken);
                return result.data;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    async register(userData) {
        try {
            const response = await fetch(`${this.baseURL}/auth/signup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }

            return result.data;
        } catch (error) {
            console.error('Register error:', error);
            throw error;
        }
    }

    async logout() {
        try {
            const token = this.getToken();
            if (token) {
                await fetch(`${this.baseURL}/auth/signout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                    }
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            localStorage.removeItem(this.tokenKey);
            localStorage.removeItem(this.refreshTokenKey);
        }
    }

    async refreshToken() {
        try {
            const refreshToken = localStorage.getItem(this.refreshTokenKey);
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await fetch(`${this.baseURL}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    refreshToken
                })
            });

            const result = await response.json();

            if (result.success) {
                const { accessToken } = result.data;
                localStorage.setItem(this.tokenKey, accessToken);
                return accessToken;
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('Refresh token error:', error);
            this.logout();
            throw error;
        }
    }

    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    isAuthenticated() {
        const token = this.getToken();
        if (!token) return false;

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.exp * 1000 > Date.now();
        } catch (error) {
            return false;
        }
    }

    async apiCall(url, options = {}) {
        const token = this.getToken();

        const config = {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers,
            }
        };

        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        try {
            let response = await fetch(`${this.baseURL}${url}`, config);

            // 如果token过期，尝试刷新
            if (response.status === 401 && token) {
                try {
                    await this.refreshToken();
                    config.headers.Authorization = `Bearer ${this.getToken()}`;
                    response = await fetch(`${this.baseURL}${url}`, config);
                } catch (refreshError) {
                    this.logout();
                    throw new Error('Authentication failed');
                }
            }

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }

            return result.data;
        } catch (error) {
            console.error('API call error:', error);
            throw error;
        }
    }
}

// 使用示例
const authService = new AuthService();

// 登录
authService.login('<EMAIL>', 'password123')
    .then(userData => {
        console.log('Login successful:', userData);
        // 重定向到主页面
    })
    .catch(error => {
        console.error('Login failed:', error.message);
        // 显示错误信息
    });

// 获取当前用户信息
authService.apiCall('/users/me')
    .then(userProfile => {
        console.log('User profile:', userProfile);
    })
    .catch(error => {
        console.error('Failed to get user profile:', error.message);
    });

// 更新用户信息
authService.apiCall('/users/me', {
    method: 'PUT',
    body: JSON.stringify({
        firstName: 'Updated',
        lastName: 'Name',
        email: '<EMAIL>'
    })
})
    .then(updatedProfile => {
        console.log('Profile updated:', updatedProfile);
    })
    .catch(error => {
        console.error('Failed to update profile:', error.message);
    });
```

这个完整的示例展示了一个企业级的Spring Boot Security应用程序的所有关键组件，包括：

1. **完整的项目结构**
2. **详细的Controller实现**
3. **Service层业务逻辑**
4. **完整的配置文件**
5. **全面的测试用例**
6. **前端集成示例**

每个部分都包含了实际项目中会遇到的各种场景和最佳实践，可以直接用于生产环境。
```
