package com.example.jdbc.examples;

import com.example.jdbc.entity.User;
import com.example.jdbc.util.JdbcUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * JDBC事务处理示例
 * 演示事务的提交、回滚和保存点
 */
@Component
public class TransactionExample {
    
    private static final Logger logger = LoggerFactory.getLogger(TransactionExample.class);
    
    @Autowired
    private JdbcUtil jdbcUtil;
    
    /**
     * 基础事务示例 - 批量插入用户
     */
    public boolean batchInsertUsersWithTransaction(List<User> users) {
        String sql = "INSERT INTO users (username, email, phone, age, address, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = jdbcUtil.getConnection();
            
            // 关闭自动提交，开启事务
            conn.setAutoCommit(false);
            logger.info("开始事务，批量插入{}个用户", users.size());
            
            pstmt = conn.prepareStatement(sql);
            
            for (User user : users) {
                pstmt.setString(1, user.getUsername());
                pstmt.setString(2, user.getEmail());
                pstmt.setString(3, user.getPhone());
                pstmt.setObject(4, user.getAge());
                pstmt.setString(5, user.getAddress());
                pstmt.setTimestamp(6, Timestamp.valueOf(user.getCreatedAt()));
                pstmt.setTimestamp(7, Timestamp.valueOf(user.getUpdatedAt()));
                
                pstmt.addBatch(); // 添加到批处理
            }
            
            // 执行批处理
            int[] results = pstmt.executeBatch();
            
            // 检查执行结果
            boolean allSuccess = true;
            for (int result : results) {
                if (result <= 0) {
                    allSuccess = false;
                    break;
                }
            }
            
            if (allSuccess) {
                conn.commit(); // 提交事务
                logger.info("事务提交成功，批量插入完成");
                return true;
            } else {
                conn.rollback(); // 回滚事务
                logger.warn("部分插入失败，事务已回滚");
                return false;
            }
            
        } catch (SQLException e) {
            logger.error("批量插入失败: {}", e.getMessage(), e);
            
            // 发生异常时回滚事务
            if (conn != null) {
                try {
                    conn.rollback();
                    logger.info("事务已回滚");
                } catch (SQLException rollbackEx) {
                    logger.error("回滚失败: {}", rollbackEx.getMessage(), rollbackEx);
                }
            }
            return false;
            
        } finally {
            // 恢复自动提交
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                    logger.error("恢复自动提交失败: {}", e.getMessage(), e);
                }
            }
            jdbcUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 保存点事务示例
     */
    public boolean complexTransactionWithSavepoint() {
        Connection conn = null;
        PreparedStatement pstmt = null;
        Savepoint savepoint1 = null;
        Savepoint savepoint2 = null;
        
        try {
            conn = jdbcUtil.getConnection();
            conn.setAutoCommit(false);
            
            logger.info("开始复杂事务操作");
            
            // 第一步：插入用户1
            String insertSql = "INSERT INTO users (username, email, created_at, updated_at) VALUES (?, ?, ?, ?)";
            pstmt = conn.prepareStatement(insertSql);
            
            LocalDateTime now = LocalDateTime.now();
            pstmt.setString(1, "transaction_user_1");
            pstmt.setString(2, "<EMAIL>");
            pstmt.setTimestamp(3, Timestamp.valueOf(now));
            pstmt.setTimestamp(4, Timestamp.valueOf(now));
            
            int result1 = pstmt.executeUpdate();
            logger.info("插入用户1结果: {}", result1);
            
            // 设置保存点1
            savepoint1 = conn.setSavepoint("savepoint1");
            logger.info("设置保存点1");
            
            // 第二步：插入用户2
            pstmt.setString(1, "transaction_user_2");
            pstmt.setString(2, "<EMAIL>");
            pstmt.setTimestamp(3, Timestamp.valueOf(now));
            pstmt.setTimestamp(4, Timestamp.valueOf(now));
            
            int result2 = pstmt.executeUpdate();
            logger.info("插入用户2结果: {}", result2);
            
            // 设置保存点2
            savepoint2 = conn.setSavepoint("savepoint2");
            logger.info("设置保存点2");
            
            // 第三步：尝试插入重复用户名（会失败）
            pstmt.setString(1, "transaction_user_1"); // 重复用户名
            pstmt.setString(2, "<EMAIL>");
            pstmt.setTimestamp(3, Timestamp.valueOf(now));
            pstmt.setTimestamp(4, Timestamp.valueOf(now));
            
            try {
                int result3 = pstmt.executeUpdate();
                logger.info("插入用户3结果: {}", result3);
            } catch (SQLException e) {
                logger.warn("插入用户3失败（预期的重复键错误）: {}", e.getMessage());
                
                // 回滚到保存点2，保留前面的操作
                conn.rollback(savepoint2);
                logger.info("回滚到保存点2");
            }
            
            // 第四步：插入用户4
            pstmt.setString(1, "transaction_user_4");
            pstmt.setString(2, "<EMAIL>");
            pstmt.setTimestamp(3, Timestamp.valueOf(now));
            pstmt.setTimestamp(4, Timestamp.valueOf(now));
            
            int result4 = pstmt.executeUpdate();
            logger.info("插入用户4结果: {}", result4);
            
            // 提交整个事务
            conn.commit();
            logger.info("复杂事务提交成功");
            return true;
            
        } catch (SQLException e) {
            logger.error("复杂事务失败: {}", e.getMessage(), e);
            
            if (conn != null) {
                try {
                    if (savepoint1 != null) {
                        // 回滚到保存点1
                        conn.rollback(savepoint1);
                        logger.info("回滚到保存点1");
                    } else {
                        // 完全回滚
                        conn.rollback();
                        logger.info("完全回滚事务");
                    }
                } catch (SQLException rollbackEx) {
                    logger.error("回滚失败: {}", rollbackEx.getMessage(), rollbackEx);
                }
            }
            return false;
            
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                    logger.error("恢复自动提交失败: {}", e.getMessage(), e);
                }
            }
            jdbcUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 事务隔离级别示例
     */
    public void demonstrateTransactionIsolation() {
        Connection conn = null;
        
        try {
            conn = jdbcUtil.getConnection();
            
            logger.info("=== 事务隔离级别演示 ===");
            
            // 获取当前隔离级别
            int currentLevel = conn.getTransactionIsolation();
            logger.info("当前事务隔离级别: {}", getIsolationLevelName(currentLevel));
            
            // 演示不同隔离级别
            demonstrateIsolationLevel(conn, Connection.TRANSACTION_READ_UNCOMMITTED);
            demonstrateIsolationLevel(conn, Connection.TRANSACTION_READ_COMMITTED);
            demonstrateIsolationLevel(conn, Connection.TRANSACTION_REPEATABLE_READ);
            demonstrateIsolationLevel(conn, Connection.TRANSACTION_SERIALIZABLE);
            
            // 恢复原始隔离级别
            conn.setTransactionIsolation(currentLevel);
            
        } catch (SQLException e) {
            logger.error("事务隔离级别演示失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeConnection(conn);
        }
    }
    
    /**
     * 演示特定隔离级别
     */
    private void demonstrateIsolationLevel(Connection conn, int isolationLevel) throws SQLException {
        String levelName = getIsolationLevelName(isolationLevel);
        logger.info("--- 设置隔离级别为: {} ---", levelName);
        
        conn.setTransactionIsolation(isolationLevel);
        
        // 这里可以添加具体的隔离级别测试逻辑
        // 比如测试脏读、不可重复读、幻读等问题
        
        logger.info("隔离级别 {} 设置成功", levelName);
    }
    
    /**
     * 获取隔离级别名称
     */
    private String getIsolationLevelName(int level) {
        switch (level) {
            case Connection.TRANSACTION_NONE:
                return "TRANSACTION_NONE";
            case Connection.TRANSACTION_READ_UNCOMMITTED:
                return "TRANSACTION_READ_UNCOMMITTED";
            case Connection.TRANSACTION_READ_COMMITTED:
                return "TRANSACTION_READ_COMMITTED";
            case Connection.TRANSACTION_REPEATABLE_READ:
                return "TRANSACTION_REPEATABLE_READ";
            case Connection.TRANSACTION_SERIALIZABLE:
                return "TRANSACTION_SERIALIZABLE";
            default:
                return "UNKNOWN";
        }
    }
    
    /**
     * 转账示例 - 经典的事务场景
     */
    public boolean transferMoney(Long fromUserId, Long toUserId, double amount) {
        String updateSql = "UPDATE user_account SET balance = balance + ? WHERE user_id = ?";
        String selectSql = "SELECT balance FROM user_account WHERE user_id = ?";
        
        Connection conn = null;
        PreparedStatement updateStmt = null;
        PreparedStatement selectStmt = null;
        ResultSet rs = null;
        
        try {
            conn = jdbcUtil.getConnection();
            conn.setAutoCommit(false);
            
            logger.info("开始转账：从用户{}向用户{}转账{}", fromUserId, toUserId, amount);
            
            // 检查转出账户余额
            selectStmt = conn.prepareStatement(selectSql);
            selectStmt.setLong(1, fromUserId);
            rs = selectStmt.executeQuery();
            
            if (rs.next()) {
                double fromBalance = rs.getDouble("balance");
                if (fromBalance < amount) {
                    logger.warn("余额不足，转账失败。当前余额: {}, 转账金额: {}", fromBalance, amount);
                    conn.rollback();
                    return false;
                }
            } else {
                logger.warn("转出账户不存在: {}", fromUserId);
                conn.rollback();
                return false;
            }
            
            // 转出金额
            updateStmt = conn.prepareStatement(updateSql);
            updateStmt.setDouble(1, -amount);
            updateStmt.setLong(2, fromUserId);
            int result1 = updateStmt.executeUpdate();
            
            // 转入金额
            updateStmt.setDouble(1, amount);
            updateStmt.setLong(2, toUserId);
            int result2 = updateStmt.executeUpdate();
            
            if (result1 > 0 && result2 > 0) {
                conn.commit();
                logger.info("转账成功");
                return true;
            } else {
                conn.rollback();
                logger.warn("转账失败，事务已回滚");
                return false;
            }
            
        } catch (SQLException e) {
            logger.error("转账过程中发生错误: {}", e.getMessage(), e);
            if (conn != null) {
                try {
                    conn.rollback();
                    logger.info("转账事务已回滚");
                } catch (SQLException rollbackEx) {
                    logger.error("回滚失败: {}", rollbackEx.getMessage(), rollbackEx);
                }
            }
            return false;
            
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                } catch (SQLException e) {
                    logger.error("恢复自动提交失败: {}", e.getMessage(), e);
                }
            }
            jdbcUtil.closeAll(conn, updateStmt, rs);
            jdbcUtil.closeStatement(selectStmt);
        }
    }
}
