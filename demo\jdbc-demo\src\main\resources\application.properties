# JDBC Demo ????
spring.application.name=jdbc-demo
server.port=8082

# ???????
# MySQL??
mysql.jdbc.url=****************************************************************************************************************************
mysql.jdbc.username=root
mysql.jdbc.password=your_password
mysql.jdbc.driver=com.mysql.cj.jdbc.Driver

# PostgreSQL??
postgresql.jdbc.url=*********************************************
postgresql.jdbc.username=postgres
postgresql.jdbc.password=your_password
postgresql.jdbc.driver=org.postgresql.Driver

# H2???????????
h2.jdbc.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
h2.jdbc.username=sa
h2.jdbc.password=
h2.jdbc.driver=org.h2.Driver

# ?????????? (mysql/postgresql/h2)
database.type=mysql

# ?????
# HikariCP??
hikari.maximum-pool-size=10
hikari.minimum-idle=5
hikari.connection-timeout=30000
hikari.idle-timeout=600000
hikari.max-lifetime=1800000
hikari.leak-detection-threshold=60000

# DBCP2??
dbcp2.initial-size=5
dbcp2.max-total=10
dbcp2.max-idle=8
dbcp2.min-idle=2
dbcp2.max-wait-millis=10000
dbcp2.validation-query=SELECT 1
dbcp2.test-on-borrow=true
dbcp2.test-while-idle=true

# ????
logging.level.com.example.jdbc=DEBUG
logging.level.org.springframework.jdbc=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
