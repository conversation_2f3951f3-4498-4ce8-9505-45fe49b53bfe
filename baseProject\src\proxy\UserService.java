package proxy;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 用户服务接口
 * 用于动态代理演示
 */
public interface UserService {
    
    /**
     * 创建用户
     */
    void createUser(String name);
    
    /**
     * 根据ID获取用户
     */
    String getUserById(Long id);
    
    /**
     * 更新用户信息
     */
    void updateUser(Long id, String name);
    
    /**
     * 删除用户
     */
    void deleteUser(Long id);
    
    /**
     * 检查用户是否存在
     */
    boolean userExists(Long id);
    
    /**
     * 获取用户数量
     */
    int getUserCount();
}

/**
 * 用户服务实现类
 */
class UserServiceImpl implements UserService {
    
    private final Map<Long, String> users = new ConcurrentHashMap<>();
    private long nextId = 1L;
    
    @Override
    public void createUser(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        Long id = nextId++;
        users.put(id, name);
        System.out.println("创建用户成功: ID=" + id + ", Name=" + name);
        
        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public String getUserById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        String name = users.get(id);
        if (name == null) {
            // 模拟从数据库查询
            name = "User-" + id;
            users.put(id, name);
        }
        
        System.out.println("获取用户: ID=" + id + ", Name=" + name);
        
        // 模拟数据库查询时间
        try {
            Thread.sleep(30);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return name;
    }
    
    @Override
    public void updateUser(Long id, String name) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("用户ID无效");
        }
        
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        if (!users.containsKey(id)) {
            throw new IllegalArgumentException("用户不存在: " + id);
        }
        
        String oldName = users.put(id, name);
        System.out.println("更新用户成功: ID=" + id + ", OldName=" + oldName + ", NewName=" + name);
        
        // 模拟更新处理时间
        try {
            Thread.sleep(40);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public void deleteUser(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("用户ID无效: " + id);
        }
        
        String name = users.remove(id);
        if (name == null) {
            throw new IllegalArgumentException("用户不存在: " + id);
        }
        
        System.out.println("删除用户成功: ID=" + id + ", Name=" + name);
        
        // 模拟删除处理时间
        try {
            Thread.sleep(20);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public boolean userExists(Long id) {
        if (id == null || id <= 0) {
            return false;
        }
        
        boolean exists = users.containsKey(id);
        System.out.println("检查用户存在性: ID=" + id + ", Exists=" + exists);
        
        return exists;
    }
    
    @Override
    public int getUserCount() {
        int count = users.size();
        System.out.println("获取用户总数: " + count);
        return count;
    }
}

/**
 * 增强的用户服务实现
 * 包含更多业务逻辑，用于演示复杂场景
 */
class EnhancedUserServiceImpl implements UserService {
    
    private final Map<Long, User> users = new ConcurrentHashMap<>();
    private long nextId = 1L;
    
    @Override
    public void createUser(String name) {
        validateUserName(name);
        
        Long id = nextId++;
        User user = new User(id, name, System.currentTimeMillis());
        users.put(id, user);
        
        System.out.println("创建增强用户: " + user);
        
        // 模拟复杂的业务逻辑
        performBusinessLogic("create", user);
    }
    
    @Override
    public String getUserById(Long id) {
        validateUserId(id);
        
        User user = users.get(id);
        if (user == null) {
            // 模拟从外部系统获取用户信息
            user = fetchUserFromExternalSystem(id);
            if (user != null) {
                users.put(id, user);
            }
        }
        
        if (user != null) {
            // 更新最后访问时间
            user.setLastAccessTime(System.currentTimeMillis());
            System.out.println("获取增强用户: " + user);
            return user.getName();
        } else {
            throw new IllegalArgumentException("用户不存在: " + id);
        }
    }
    
    @Override
    public void updateUser(Long id, String name) {
        validateUserId(id);
        validateUserName(name);
        
        User user = users.get(id);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在: " + id);
        }
        
        String oldName = user.getName();
        user.setName(name);
        user.setLastModifiedTime(System.currentTimeMillis());
        
        System.out.println("更新增强用户: ID=" + id + ", OldName=" + oldName + ", NewName=" + name);
        
        // 模拟业务逻辑
        performBusinessLogic("update", user);
    }
    
    @Override
    public void deleteUser(Long id) {
        validateUserId(id);
        
        User user = users.remove(id);
        if (user == null) {
            throw new IllegalArgumentException("用户不存在: " + id);
        }
        
        System.out.println("删除增强用户: " + user);
        
        // 模拟清理相关数据
        cleanupUserData(user);
    }
    
    @Override
    public boolean userExists(Long id) {
        if (id == null || id <= 0) {
            return false;
        }
        return users.containsKey(id);
    }
    
    @Override
    public int getUserCount() {
        return users.size();
    }
    
    private void validateUserId(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("用户ID无效: " + id);
        }
    }
    
    private void validateUserName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (name.length() > 50) {
            throw new IllegalArgumentException("用户名长度不能超过50个字符");
        }
    }
    
    private User fetchUserFromExternalSystem(Long id) {
        // 模拟从外部系统获取用户
        System.out.println("从外部系统获取用户: " + id);
        
        try {
            Thread.sleep(100); // 模拟网络延迟
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        return new User(id, "ExternalUser-" + id, System.currentTimeMillis());
    }
    
    private void performBusinessLogic(String operation, User user) {
        System.out.println("执行业务逻辑: " + operation + " for " + user.getName());
        
        // 模拟复杂的业务处理
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private void cleanupUserData(User user) {
        System.out.println("清理用户数据: " + user.getName());
        
        // 模拟清理操作
        try {
            Thread.sleep(30);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 内部用户类
     */
    private static class User {
        private Long id;
        private String name;
        private long createTime;
        private long lastModifiedTime;
        private long lastAccessTime;
        
        public User(Long id, String name, long createTime) {
            this.id = id;
            this.name = name;
            this.createTime = createTime;
            this.lastModifiedTime = createTime;
            this.lastAccessTime = createTime;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }
        
        public long getLastModifiedTime() { return lastModifiedTime; }
        public void setLastModifiedTime(long lastModifiedTime) { this.lastModifiedTime = lastModifiedTime; }
        
        public long getLastAccessTime() { return lastAccessTime; }
        public void setLastAccessTime(long lastAccessTime) { this.lastAccessTime = lastAccessTime; }
        
        @Override
        public String toString() {
            return "User{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", createTime=" + createTime +
                    ", lastModifiedTime=" + lastModifiedTime +
                    ", lastAccessTime=" + lastAccessTime +
                    '}';
        }
    }
}

/**
 * 模拟的产品服务接口
 * 用于演示不同类型的服务代理
 */
interface ProductService {
    void addProduct(String name, double price);
    String getProduct(Long id);
    void updateProduct(Long id, String name, double price);
    void deleteProduct(Long id);
    double getProductPrice(Long id);
    int getProductCount();
}

/**
 * 产品服务实现类
 */
class ProductServiceImpl implements ProductService {
    
    private final Map<Long, Product> products = new ConcurrentHashMap<>();
    private long nextId = 1L;
    
    @Override
    public void addProduct(String name, double price) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("产品名称不能为空");
        }
        if (price < 0) {
            throw new IllegalArgumentException("产品价格不能为负数");
        }
        
        Long id = nextId++;
        Product product = new Product(id, name, price);
        products.put(id, product);
        
        System.out.println("添加产品: " + product);
    }
    
    @Override
    public String getProduct(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("产品ID无效");
        }
        
        Product product = products.get(id);
        if (product == null) {
            return "Product-" + id; // 模拟默认产品
        }
        
        System.out.println("获取产品: " + product);
        return product.getName();
    }
    
    @Override
    public void updateProduct(Long id, String name, double price) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("产品ID无效");
        }
        
        Product product = products.get(id);
        if (product == null) {
            throw new IllegalArgumentException("产品不存在: " + id);
        }
        
        product.setName(name);
        product.setPrice(price);
        
        System.out.println("更新产品: " + product);
    }
    
    @Override
    public void deleteProduct(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("产品ID无效");
        }
        
        Product product = products.remove(id);
        if (product == null) {
            throw new IllegalArgumentException("产品不存在: " + id);
        }
        
        System.out.println("删除产品: " + product);
    }
    
    @Override
    public double getProductPrice(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("产品ID无效");
        }
        
        Product product = products.get(id);
        if (product == null) {
            return 0.0; // 默认价格
        }
        
        return product.getPrice();
    }
    
    @Override
    public int getProductCount() {
        return products.size();
    }
    
    /**
     * 内部产品类
     */
    private static class Product {
        private Long id;
        private String name;
        private double price;
        
        public Product(Long id, String name, double price) {
            this.id = id;
            this.name = name;
            this.price = price;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public double getPrice() { return price; }
        public void setPrice(double price) { this.price = price; }
        
        @Override
        public String toString() {
            return "Product{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", price=" + price +
                    '}';
        }
    }
}
