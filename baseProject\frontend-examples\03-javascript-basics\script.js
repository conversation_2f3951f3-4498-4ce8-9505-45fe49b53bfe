// JavaScript基础示例功能文件

// ===== 全局变量 =====
let demoArray = [];
let timerInterval = null;
let timerSeconds = 0;
let isTimerRunning = false;
let todos = [];
let todoIdCounter = 1;
let currentFilter = 'all';

// 猜数字游戏变量
let randomNumber = Math.floor(Math.random() * 100) + 1;
let attemptsLeft = 10;
let guessHistory = [];

// ===== 页面加载完成后执行 =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 JavaScript基础示例页面加载完成！');
    
    // 初始化各种功能
    initializeEventListeners();
    initializeColorPicker();
    updateTodoStats();
    
    // 平滑滚动
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});

// ===== 变量与数据类型演示 =====
function demonstrateVariables() {
    const output = document.getElementById('variables-output');
    
    // 基本数据类型
    let name = "张三";
    const age = 25;
    let isStudent = true;
    let hobbies = ["读书", "游泳", "编程"];
    let person = {
        name: "李四",
        age: 30,
        job: "程序员"
    };
    
    let result = `变量演示结果：
📝 字符串: ${name} (类型: ${typeof name})
🔢 数字: ${age} (类型: ${typeof age})
✅ 布尔值: ${isStudent} (类型: ${typeof isStudent})
📋 数组: [${hobbies.join(', ')}] (长度: ${hobbies.length})
👤 对象: ${JSON.stringify(person, null, 2)}

🔍 类型检查:
- Array.isArray(hobbies): ${Array.isArray(hobbies)}
- person instanceof Object: ${person instanceof Object}`;
    
    output.textContent = result;
}

// ===== 数组操作 =====
function addToArray() {
    const input = document.getElementById('array-input');
    const value = input.value.trim();
    
    if (value) {
        demoArray.push(value);
        input.value = '';
        updateArrayDisplay();
    }
}

function removeFromArray() {
    if (demoArray.length > 0) {
        demoArray.pop();
        updateArrayDisplay();
    }
}

function clearArray() {
    demoArray = [];
    updateArrayDisplay();
}

function updateArrayDisplay() {
    const display = document.getElementById('array-display');
    
    if (demoArray.length === 0) {
        display.innerHTML = '<p style="color: #7f8c8d;">数组为空</p>';
    } else {
        display.innerHTML = demoArray.map((item, index) => 
            `<span class="array-item">${index}: ${item}</span>`
        ).join('');
    }
}

// ===== 对象操作 =====
function createPerson() {
    const name = document.getElementById('person-name').value;
    const age = document.getElementById('person-age').value;
    const job = document.getElementById('person-job').value;
    const display = document.getElementById('person-display');
    
    if (name && age && job) {
        const person = {
            name: name,
            age: parseInt(age),
            job: job,
            introduce: function() {
                return `你好，我是${this.name}，今年${this.age}岁，职业是${this.job}。`;
            }
        };
        
        display.innerHTML = `
            <div class="person-card">
                <h4>创建的对象：</h4>
                <p><strong>姓名:</strong> ${person.name}</p>
                <p><strong>年龄:</strong> ${person.age}</p>
                <p><strong>职业:</strong> ${person.job}</p>
                <p><strong>自我介绍:</strong> ${person.introduce()}</p>
                <p><strong>对象属性:</strong> ${Object.keys(person).join(', ')}</p>
            </div>
        `;
        
        // 清空输入框
        document.getElementById('person-name').value = '';
        document.getElementById('person-age').value = '';
        document.getElementById('person-job').value = '';
    } else {
        alert('请填写完整信息！');
    }
}

// ===== 函数演示 =====
function demonstrateFunctions() {
    const output = document.getElementById('functions-output');
    
    // 函数声明
    function greet(name) {
        return `你好，${name}！`;
    }
    
    // 箭头函数
    const add = (a, b) => a + b;
    const multiply = (a, b) => {
        const result = a * b;
        return result;
    };
    
    // 高阶函数
    const numbers = [1, 2, 3, 4, 5];
    const doubled = numbers.map(n => n * 2);
    const evens = numbers.filter(n => n % 2 === 0);
    const sum = numbers.reduce((acc, n) => acc + n, 0);
    
    // 闭包示例
    function createCounter() {
        let count = 0;
        return function() {
            count++;
            return count;
        };
    }
    
    const counter = createCounter();
    
    let result = `函数演示结果：
🎯 函数调用: ${greet('小明')}
➕ 箭头函数: add(5, 3) = ${add(5, 3)}
✖️ 复杂箭头函数: multiply(4, 6) = ${multiply(4, 6)}

📊 数组方法:
- 原数组: [${numbers.join(', ')}]
- map(翻倍): [${doubled.join(', ')}]
- filter(偶数): [${evens.join(', ')}]
- reduce(求和): ${sum}

🔒 闭包计数器:
- 第1次调用: ${counter()}
- 第2次调用: ${counter()}
- 第3次调用: ${counter()}`;
    
    output.textContent = result;
}

// ===== 计算器 =====
function calculate() {
    const num1 = parseFloat(document.getElementById('num1').value);
    const num2 = parseFloat(document.getElementById('num2').value);
    const operator = document.getElementById('operator').value;
    const result = document.getElementById('calc-result');
    
    if (isNaN(num1) || isNaN(num2)) {
        result.textContent = '请输入有效数字！';
        result.style.color = '#e74c3c';
        return;
    }
    
    let calculation;
    let answer;
    
    switch (operator) {
        case '+':
            answer = num1 + num2;
            calculation = `${num1} + ${num2} = ${answer}`;
            break;
        case '-':
            answer = num1 - num2;
            calculation = `${num1} - ${num2} = ${answer}`;
            break;
        case '*':
            answer = num1 * num2;
            calculation = `${num1} × ${num2} = ${answer}`;
            break;
        case '/':
            if (num2 === 0) {
                result.textContent = '错误：除数不能为零！';
                result.style.color = '#e74c3c';
                return;
            }
            answer = num1 / num2;
            calculation = `${num1} ÷ ${num2} = ${answer}`;
            break;
        default:
            result.textContent = '未知操作符！';
            result.style.color = '#e74c3c';
            return;
    }
    
    result.textContent = calculation;
    result.style.color = '#27ae60';
}

// ===== 作用域演示 =====
function demonstrateScope() {
    const output = document.getElementById('scope-output');
    
    // 全局变量
    var globalVar = '我是全局变量';
    
    function outerFunction() {
        var outerVar = '我是外层函数变量';
        let outerLet = '我是外层let变量';
        
        function innerFunction() {
            var innerVar = '我是内层函数变量';
            let innerLet = '我是内层let变量';
            
            return `内层函数可以访问:
- 全局变量: ${globalVar}
- 外层var: ${outerVar}
- 外层let: ${outerLet}
- 内层var: ${innerVar}
- 内层let: ${innerLet}`;
        }
        
        return innerFunction();
    }
    
    // 块级作用域演示
    let blockScopeDemo = '';
    if (true) {
        let blockVar = '我是块级变量';
        var functionVar = '我是函数级变量';
        blockScopeDemo = `块级作用域中:
- let变量只在块内有效
- var变量在整个函数内有效`;
    }
    
    const result = `作用域演示：
${outerFunction()}

${blockScopeDemo}

🔍 变量提升演示:
- var变量会被提升到函数顶部
- let/const不会被提升，存在暂时性死区`;
    
    output.textContent = result;
}

// ===== DOM操作 =====
function changeText() {
    const textElement = document.getElementById('demo-text');
    const currentText = textElement.textContent;
    
    if (currentText === '这是一个演示文本') {
        textElement.textContent = '文本已被修改！';
    } else {
        textElement.textContent = '这是一个演示文本';
    }
}

function changeStyle() {
    const box = document.getElementById('demo-box');
    const currentColor = box.style.backgroundColor;
    
    if (currentColor === 'lightblue') {
        box.style.backgroundColor = '';
        box.style.color = '';
        box.style.padding = '';
    } else {
        box.style.backgroundColor = 'lightblue';
        box.style.color = 'darkblue';
        box.style.padding = '20px';
    }
}

function addClass() {
    const box = document.getElementById('demo-box');
    box.classList.add('highlight');
}

function removeClass() {
    const box = document.getElementById('demo-box');
    box.classList.remove('highlight');
}

// ===== 动态创建元素 =====
function addListItem() {
    const input = document.getElementById('new-item-text');
    const list = document.getElementById('dynamic-list');
    const text = input.value.trim();
    
    if (text) {
        const li = document.createElement('li');
        li.innerHTML = `
            <span>${text}</span>
            <button onclick="removeListItem(this)">删除</button>
        `;
        list.appendChild(li);
        input.value = '';
    }
}

function removeListItem(button) {
    const li = button.parentElement;
    li.remove();
}

// ===== 表单处理 =====
function initializeEventListeners() {
    // 表单提交处理
    const form = document.getElementById('demo-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const output = document.getElementById('form-output');
            
            // 简单验证
            if (username.length < 3) {
                output.textContent = '用户名至少需要3个字符！';
                output.style.color = '#e74c3c';
                return;
            }
            
            if (!email.includes('@')) {
                output.textContent = '请输入有效的邮箱地址！';
                output.style.color = '#e74c3c';
                return;
            }
            
            if (password.length < 6) {
                output.textContent = '密码至少需要6个字符！';
                output.style.color = '#e74c3c';
                return;
            }
            
            output.innerHTML = `
                <div style="color: #27ae60;">
                    <h4>表单提交成功！</h4>
                    <p><strong>用户名:</strong> ${username}</p>
                    <p><strong>邮箱:</strong> ${email}</p>
                    <p><strong>密码:</strong> ${'*'.repeat(password.length)}</p>
                </div>
            `;
            
            form.reset();
        });
    }
    
    // 鼠标事件
    const mouseBox = document.getElementById('mouse-box');
    if (mouseBox) {
        mouseBox.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            document.getElementById('mouse-info').textContent = 
                `鼠标坐标: (${Math.round(x)}, ${Math.round(y)})`;
        });
        
        mouseBox.addEventListener('click', function() {
            this.style.backgroundColor = this.style.backgroundColor === 'lightgreen' ? '' : 'lightgreen';
        });
        
        mouseBox.addEventListener('dblclick', function() {
            this.style.transform = this.style.transform === 'scale(1.1)' ? '' : 'scale(1.1)';
        });
    }
    
    // 键盘事件
    const keyboardInput = document.getElementById('keyboard-input');
    if (keyboardInput) {
        keyboardInput.addEventListener('keydown', function(e) {
            const info = document.getElementById('keyboard-info');
            info.textContent = `按下: ${e.key} (键码: ${e.keyCode})`;
        });
        
        keyboardInput.addEventListener('input', function(e) {
            const info = document.getElementById('keyboard-info');
            info.textContent += `\n输入内容: "${e.target.value}"`;
        });
    }
    
    // 拖拽事件
    initializeDragAndDrop();
    
    // 事件委托
    const buttonContainer = document.getElementById('button-container');
    if (buttonContainer) {
        buttonContainer.addEventListener('click', function(e) {
            if (e.target.classList.contains('dynamic-button')) {
                const output = document.getElementById('delegation-output');
                output.textContent = `点击了按钮: ${e.target.textContent}`;
            }
        });
    }
}

// ===== 拖拽功能 =====
function initializeDragAndDrop() {
    const dragItems = document.querySelectorAll('.drag-item');
    const dropZone = document.getElementById('drop-zone');
    
    dragItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', this.dataset.color);
            e.dataTransfer.setData('text/html', this.outerHTML);
        });
    });
    
    if (dropZone) {
        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });
        
        dropZone.addEventListener('dragleave', function() {
            this.classList.remove('drag-over');
        });
        
        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
            
            const color = e.dataTransfer.getData('text/plain');
            const html = e.dataTransfer.getData('text/html');
            
            this.innerHTML = `<p>拖拽成功！颜色: ${color}</p>${html}`;
        });
    }
}

// ===== 事件委托演示 =====
let buttonCounter = 1;

function addButton() {
    const container = document.getElementById('button-container');
    const button = document.createElement('button');
    button.className = 'dynamic-button';
    button.textContent = `按钮 ${buttonCounter++}`;
    container.appendChild(button);
}

// ===== 定时器功能 =====
function startTimer() {
    if (!isTimerRunning) {
        isTimerRunning = true;
        timerInterval = setInterval(function() {
            timerSeconds++;
            updateTimerDisplay();
        }, 1000);
    }
}

function pauseTimer() {
    if (isTimerRunning) {
        isTimerRunning = false;
        clearInterval(timerInterval);
    }
}

function resetTimer() {
    isTimerRunning = false;
    clearInterval(timerInterval);
    timerSeconds = 0;
    updateTimerDisplay();
}

function updateTimerDisplay() {
    const display = document.getElementById('timer-display');
    const minutes = Math.floor(timerSeconds / 60);
    const seconds = timerSeconds % 60;
    display.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// ===== Promise演示 =====
function demonstratePromise() {
    const output = document.getElementById('promise-output');
    const loading = document.getElementById('loading');
    
    output.textContent = '';
    loading.style.display = 'block';
    
    // 模拟异步操作
    const simulateAsyncOperation = () => {
        return new Promise((resolve, reject) => {
            const success = Math.random() > 0.3; // 70% 成功率
            
            setTimeout(() => {
                if (success) {
                    resolve({
                        data: '模拟数据加载成功！',
                        timestamp: new Date().toLocaleString()
                    });
                } else {
                    reject(new Error('网络连接失败'));
                }
            }, 2000);
        });
    };
    
    simulateAsyncOperation()
        .then(result => {
            loading.style.display = 'none';
            output.innerHTML = `
                <div style="color: #27ae60;">
                    <h4>✅ Promise 成功</h4>
                    <p>${result.data}</p>
                    <p>时间: ${result.timestamp}</p>
                </div>
            `;
        })
        .catch(error => {
            loading.style.display = 'none';
            output.innerHTML = `
                <div style="color: #e74c3c;">
                    <h4>❌ Promise 失败</h4>
                    <p>错误: ${error.message}</p>
                </div>
            `;
        });
}

// ===== Fetch API演示 =====
function fetchData() {
    const output = document.getElementById('fetch-output');
    
    output.textContent = '正在获取数据...';
    
    // 使用JSONPlaceholder API获取示例数据
    fetch('https://jsonplaceholder.typicode.com/posts/1')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            output.innerHTML = `
                <div style="color: #27ae60;">
                    <h4>📡 Fetch 成功</h4>
                    <p><strong>标题:</strong> ${data.title}</p>
                    <p><strong>内容:</strong> ${data.body}</p>
                    <p><strong>用户ID:</strong> ${data.userId}</p>
                </div>
            `;
        })
        .catch(error => {
            output.innerHTML = `
                <div style="color: #e74c3c;">
                    <h4>❌ Fetch 失败</h4>
                    <p>错误: ${error.message}</p>
                    <p>可能是网络问题或CORS限制</p>
                </div>
            `;
        });
}

// ===== Async/Await演示 =====
async function demonstrateAsync() {
    const output = document.getElementById('async-output');
    
    output.textContent = '执行异步函数...';
    
    try {
        // 模拟多个异步操作
        const step1 = await simulateDelay('步骤1完成', 1000);
        output.textContent += `\n${step1}`;
        
        const step2 = await simulateDelay('步骤2完成', 1000);
        output.textContent += `\n${step2}`;
        
        const step3 = await simulateDelay('步骤3完成', 1000);
        output.textContent += `\n${step3}`;
        
        output.textContent += '\n🎉 所有异步操作完成！';
        
    } catch (error) {
        output.textContent = `❌ 异步操作失败: ${error.message}`;
    }
}

function simulateDelay(message, delay) {
    return new Promise(resolve => {
        setTimeout(() => resolve(message), delay);
    });
}

// ===== 待办事项应用 =====
function addTodo() {
    const input = document.getElementById('todo-input');
    const text = input.value.trim();

    if (text) {
        const todo = {
            id: todoIdCounter++,
            text: text,
            completed: false,
            createdAt: new Date()
        };

        todos.push(todo);
        input.value = '';
        renderTodos();
        updateTodoStats();
    }
}

function toggleTodo(id) {
    const todo = todos.find(t => t.id === id);
    if (todo) {
        todo.completed = !todo.completed;
        renderTodos();
        updateTodoStats();
    }
}

function deleteTodo(id) {
    todos = todos.filter(t => t.id !== id);
    renderTodos();
    updateTodoStats();
}

function filterTodos(filter) {
    currentFilter = filter;

    // 更新按钮状态
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    renderTodos();
}

function renderTodos() {
    const list = document.getElementById('todo-list');
    let filteredTodos = todos;

    switch (currentFilter) {
        case 'active':
            filteredTodos = todos.filter(t => !t.completed);
            break;
        case 'completed':
            filteredTodos = todos.filter(t => t.completed);
            break;
    }

    list.innerHTML = filteredTodos.map(todo => `
        <li class="todo-item ${todo.completed ? 'completed' : ''}">
            <input type="checkbox" ${todo.completed ? 'checked' : ''}
                   onchange="toggleTodo(${todo.id})">
            <span>${todo.text}</span>
            <button onclick="deleteTodo(${todo.id})">删除</button>
        </li>
    `).join('');
}

function updateTodoStats() {
    const count = document.getElementById('todo-count');
    const activeCount = todos.filter(t => !t.completed).length;
    count.textContent = `${activeCount} 项待办`;
}

function clearCompleted() {
    todos = todos.filter(t => !t.completed);
    renderTodos();
    updateTodoStats();
}

// 监听回车键添加待办事项
document.addEventListener('DOMContentLoaded', function() {
    const todoInput = document.getElementById('todo-input');
    if (todoInput) {
        todoInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addTodo();
            }
        });
    }
});

// ===== 猜数字游戏 =====
function makeGuess() {
    const input = document.getElementById('guess-input');
    const feedback = document.getElementById('guess-feedback');
    const history = document.getElementById('guess-history');
    const attemptsSpan = document.getElementById('attempts');

    const guess = parseInt(input.value);

    if (isNaN(guess) || guess < 1 || guess > 100) {
        feedback.textContent = '请输入1-100之间的数字！';
        feedback.className = 'game-feedback error';
        return;
    }

    attemptsLeft--;
    guessHistory.push(guess);

    if (guess === randomNumber) {
        feedback.textContent = `🎉 恭喜你猜对了！数字就是 ${randomNumber}`;
        feedback.className = 'game-feedback success';
        input.disabled = true;
    } else if (attemptsLeft === 0) {
        feedback.textContent = `😢 游戏结束！正确答案是 ${randomNumber}`;
        feedback.className = 'game-feedback error';
        input.disabled = true;
    } else {
        const hint = guess < randomNumber ? '太小了！' : '太大了！';
        feedback.textContent = `${hint} 还有 ${attemptsLeft} 次机会`;
        feedback.className = 'game-feedback info';
    }

    attemptsSpan.textContent = attemptsLeft;
    history.innerHTML = `猜测历史: ${guessHistory.join(', ')}`;
    input.value = '';
}

function resetGame() {
    randomNumber = Math.floor(Math.random() * 100) + 1;
    attemptsLeft = 10;
    guessHistory = [];

    document.getElementById('guess-input').disabled = false;
    document.getElementById('guess-input').value = '';
    document.getElementById('guess-feedback').textContent = '';
    document.getElementById('guess-feedback').className = 'game-feedback';
    document.getElementById('attempts').textContent = '10';
    document.getElementById('guess-history').textContent = '';
}

// 监听回车键猜数字
document.addEventListener('DOMContentLoaded', function() {
    const guessInput = document.getElementById('guess-input');
    if (guessInput) {
        guessInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                makeGuess();
            }
        });
    }
});

// ===== 颜色选择器 =====
function initializeColorPicker() {
    const redSlider = document.getElementById('red-slider');
    const greenSlider = document.getElementById('green-slider');
    const blueSlider = document.getElementById('blue-slider');

    if (redSlider && greenSlider && blueSlider) {
        [redSlider, greenSlider, blueSlider].forEach(slider => {
            slider.addEventListener('input', updateColor);
        });

        // 初始化颜色
        updateColor();
    }
}

function updateColor() {
    const red = document.getElementById('red-slider').value;
    const green = document.getElementById('green-slider').value;
    const blue = document.getElementById('blue-slider').value;

    // 更新数值显示
    document.getElementById('red-value').textContent = red;
    document.getElementById('green-value').textContent = green;
    document.getElementById('blue-value').textContent = blue;

    // 更新颜色预览
    const rgb = `rgb(${red}, ${green}, ${blue})`;
    const hex = rgbToHex(parseInt(red), parseInt(green), parseInt(blue));

    document.getElementById('color-preview').style.backgroundColor = rgb;
    document.getElementById('rgb-value').textContent = rgb;
    document.getElementById('hex-value').textContent = hex;
}

function rgbToHex(r, g, b) {
    const toHex = (n) => {
        const hex = n.toString(16);
        return hex.length === 1 ? '0' + hex : hex;
    };

    return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
}

// ===== 实用工具函数 =====

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 深拷贝函数
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }

    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }

    if (typeof obj === 'object') {
        const clonedObj = {};
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// 随机数生成器
function randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 数组去重
function uniqueArray(arr) {
    return [...new Set(arr)];
}

// 格式化日期
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

// ===== 控制台输出学习提示 =====
console.log(`
🎉 JavaScript基础示例加载完成！

📚 学习建议：
1. 打开浏览器开发者工具 (F12)
2. 在Console面板中尝试运行JavaScript代码
3. 使用debugger语句调试代码
4. 观察Network面板了解网络请求

🔧 可以尝试的命令：
- console.log('Hello World!')
- document.querySelector('.demo-btn')
- localStorage.setItem('test', 'value')
- fetch('https://api.github.com/users/octocat')

💡 进阶学习：
- 学习ES6+新特性 (箭头函数、解构、模板字符串等)
- 了解异步编程 (Promise、async/await)
- 掌握DOM操作和事件处理
- 学习前端框架 (Vue.js、React)
`);

// ===== 页面性能监控 =====
window.addEventListener('load', function() {
    const loadTime = performance.now();
    console.log(`📊 页面加载时间: ${loadTime.toFixed(2)}ms`);

    // 监控页面可见性
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            console.log('📱 页面隐藏');
        } else {
            console.log('👀 页面可见');
        }
    });
});

// ===== 错误处理 =====
window.addEventListener('error', function(e) {
    console.error('❌ JavaScript错误:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('❌ 未处理的Promise拒绝:', e.reason);
});

// ===== 本地存储演示 =====
function saveToLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        console.log(`💾 已保存到本地存储: ${key}`);
    } catch (e) {
        console.error('❌ 保存失败:', e);
    }
}

function loadFromLocalStorage(key) {
    try {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    } catch (e) {
        console.error('❌ 读取失败:', e);
        return null;
    }
}

// 自动保存待办事项到本地存储
function saveTodosToStorage() {
    saveToLocalStorage('todos', todos);
}

function loadTodosFromStorage() {
    const savedTodos = loadFromLocalStorage('todos');
    if (savedTodos) {
        todos = savedTodos;
        todoIdCounter = Math.max(...todos.map(t => t.id), 0) + 1;
        renderTodos();
        updateTodoStats();
    }
}

// 页面加载时恢复待办事项
document.addEventListener('DOMContentLoaded', function() {
    loadTodosFromStorage();

    // 监听待办事项变化，自动保存
    const originalPush = todos.push;
    todos.push = function(...args) {
        const result = originalPush.apply(this, args);
        saveTodosToStorage();
        return result;
    };
});

console.log('✅ JavaScript基础示例脚本加载完成！');
