package com.example.mybatisdemo.dto;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 用户更新DTO - 用于接收更新用户的请求参数
 */
@Data
public class UserUpdateDTO {
    
    /**
     * 用户ID - 必填
     */
    @NotNull(message = "用户ID不能为空")
    @Positive(message = "用户ID必须为正数")
    private Long id;
    
    /**
     * 用户名 - 可选，如果提供则必须符合格式
     */
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;
    
    /**
     * 邮箱 - 可选，但格式必须正确
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 手机号 - 可选，但格式必须正确
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 真实姓名 - 可选，长度1-50个字符
     */
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;
    
    /**
     * 昵称 - 可选，长度1-50个字符
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;
    
    /**
     * 年龄 - 可选，范围1-150
     */
    @Min(value = 1, message = "年龄不能小于1岁")
    @Max(value = 150, message = "年龄不能大于150岁")
    private Integer age;
    
    /**
     * 性别 - 可选，0-女，1-男
     */
    @Min(value = 0, message = "性别值不正确，0-女，1-男")
    @Max(value = 1, message = "性别值不正确，0-女，1-男")
    private Integer gender;
    
    /**
     * 生日 - 可选，不能是未来日期
     */
    @Past(message = "生日不能是未来日期")
    private LocalDate birthday;
    
    /**
     * 地址 - 可选，长度不超过200个字符
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 个人简介 - 可选，长度不超过500个字符
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;
    
    /**
     * 头像URL - 可选，长度不超过500个字符
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatar;
    
    /**
     * 状态 - 可选，0-禁用，1-启用
     */
    @Min(value = 0, message = "状态值不正确，0-禁用，1-启用")
    @Max(value = 1, message = "状态值不正确，0-禁用，1-启用")
    private Integer status;
}
