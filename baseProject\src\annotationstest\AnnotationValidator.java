package annotationstest;

import annotationstest.AnnotationTestDemo.Validate;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;

public class AnnotationValidator {
    public static ValidationResult validate(Object obj) {
        ValidationResult result = new ValidationResult();
        Class<?> clazz = obj.getClass();

        // 获取所有字段
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // 检查字段是否有@Validate注解
            if (field.isAnnotationPresent(Validate.class)) {
                Validate validate = field.getAnnotation(Validate.class);

                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);

                    // 如果字段不是必需的且值为空，跳过验证
                    if (!validate.required() && (value == null || (value instanceof String && ((String) value).trim().isEmpty()))) {
                        continue;
                    }

                    // 执行验证
                    String error = validateField(field.getName(), value, validate);
                    if (error != null) {
                        result.addError(field.getName(), error);
                    }

                } catch (IllegalAccessException e) {
                    result.addError(field.getName(), "无法访问字段");
                }
            }
        }

        return result;
    }

    private static String validateField(String fieldName, Object value, Validate validate) {
        String stringValue = value == null ? null : value.toString();

        // 必填验证
        if (validate.required() && (value == null || stringValue.trim().isEmpty())) {
            return validate.message();
        }

        // 如果值为空且不是必填，跳过其他验证
        if (value == null || stringValue.trim().isEmpty()) {
            return null;
        }

        // 长度验证
        if (stringValue.length() < validate.minLength() ||
                stringValue.length() > validate.maxLength()) {
            return validate.message();
        }

        // 类型验证
        switch (validate.type()) {
            case EMAIL:
                if (!isValidEmail(stringValue)) {
                    return validate.message();
                }
                break;
            case PHONE:
                if (!isValidPhone(stringValue)) {
                    return validate.message();
                }
                break;
            case ID_CARD:
                if (!isValidIdCard(stringValue)) {
                    return validate.message();
                }
                break;
            case CUSTOM:
                if (!validate.pattern().isEmpty() && !Pattern.matches(validate.pattern(), stringValue)) {
                    return validate.message();
                }
                break;
        }

        return null;
    }

    // 验证邮箱
    private static boolean isValidEmail(String email) {
        String emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(emailPattern, email);
    }

    // 验证手机号
    private static boolean isValidPhone(String phone) {
        String phonePattern = "^1[3-9]\\d{9}$";
        return Pattern.matches(phonePattern, phone);
    }

    // 验证身份证
    private static boolean isValidIdCard(String idCard) {
        String idCardPattern = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
        return Pattern.matches(idCardPattern, idCard);
    }

}

class ValidationResult {
    private boolean valid = true;
    private Map<String, String> errorMap = new HashMap<>();
    private List<String> errorList = new ArrayList<>();

    public void addError(String field, String message) {
        this.valid = false;
        String errorMsg = field + ": " + message;
        this.errorMap.put(field, message);
        this.errorList.add(errorMsg);
    }

    public boolean isValid() {
        return valid;
    }

    public Map<String, String> getErrorMap() {
        return errorMap;
    }

    public List<String> getErrors() {
        return errorList;
    }

    public String getErrorMessage() {
        if (valid) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : errorMap.entrySet()) {
            sb.append(entry.getKey()).append(": ").append(entry.getValue()).append("; ");
        }
        return sb.toString();
    }
}
