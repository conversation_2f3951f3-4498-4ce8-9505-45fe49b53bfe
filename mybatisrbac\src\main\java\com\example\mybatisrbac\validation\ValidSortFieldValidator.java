package com.example.mybatisrbac.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

/**
 * 排序字段验证器
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public class ValidSortFieldValidator implements ConstraintValidator<ValidSortField, String> {

    private List<String> allowedFields;
    private ValidSortField.EntityType entityType;

    @Override
    public void initialize(ValidSortField constraintAnnotation) {
        this.entityType = constraintAnnotation.entityType();
        
        // 如果注解中指定了允许的字段，使用指定的字段
        if (constraintAnnotation.allowedFields().length > 0) {
            this.allowedFields = Arrays.asList(constraintAnnotation.allowedFields());
        } else {
            // 否则根据实体类型确定允许的字段
            switch (entityType) {
                case ROLE:
                    this.allowedFields = Arrays.asList(
                            "id", "roleName", "roleCode", "status", "createTime", "updateTime"
                    );
                    break;
                case USER:
                    this.allowedFields = Arrays.asList(
                            "id", "username", "email", "phone", "realName", "status", 
                            "createTime", "updateTime", "lastLoginTime"
                    );
                    break;
                case PERMISSION:
                    this.allowedFields = Arrays.asList(
                            "id", "permissionName", "permissionCode", "permissionType", 
                            "status", "createTime", "updateTime", "sortOrder"
                    );
                    break;
                default:
                    this.allowedFields = Arrays.asList("id", "createTime", "updateTime");
            }
        }
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // null值或空字符串认为是有效的（使用默认值）
        if (value == null || value.trim().isEmpty()) {
            return true;
        }
        
        boolean isValid = allowedFields.contains(value);
        
        if (!isValid) {
            // 自定义错误消息
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(
                    "排序字段只能为：" + String.join(",", allowedFields)
            ).addConstraintViolation();
        }
        
        return isValid;
    }
}
