# Spring Boot应用配置文件

# 应用基本配置
spring:
  application:
    name: spring-boot-web-demo
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************
    username: root
    password: 123456
    
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      
      # 监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123
  
  # MyBatis配置
  mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: com.example.entity
    configuration:
      map-underscore-to-camel-case: true
      cache-enabled: true
      lazy-loading-enabled: true
      aggressive-lazy-loading: false
      log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB
      file-size-threshold: 2KB
  
  # 缓存配置
  cache:
    type: simple
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # 安全配置
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  
  # Tomcat配置
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10
    max-connections: 10000
    accept-count: 100
    connection-timeout: 20000

# 日志配置
logging:
  level:
    root: INFO
    com.example: DEBUG
    org.springframework.web: DEBUG
    org.mybatis: DEBUG
    com.alibaba.druid: INFO
  
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  
  file:
    name: logs/spring-boot-demo.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  
  endpoint:
    health:
      show-details: always
    shutdown:
      enabled: true
  
  info:
    env:
      enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: Spring Boot Web应用示例
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  # 开发工具配置
  devtools:
    restart:
      enabled: true
      additional-paths: src/main/java
    livereload:
      enabled: true

# 开发环境日志级别
logging:
  level:
    com.example: DEBUG
    org.springframework: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: ****************************************************************************************************************

# 测试环境日志级别
logging:
  level:
    com.example: INFO
    org.springframework: INFO

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: ************************************************************************************************************
    username: ${DB_USERNAME:prod_user}
    password: ${DB_PASSWORD:prod_password}

# 生产环境日志级别
logging:
  level:
    root: WARN
    com.example: INFO
