package Threadtest;

import java.util.concurrent.*;
import java.util.concurrent.Callable;

public class CallableDemo implements Callable<String> {
    private String taskName;
    private int duration;

    public CallableDemo(String name, int duration) {
        this.taskName = name;
        this.duration = duration;
    }

    @Override
    public String call() throws Exception {
        System.out.println(taskName + " 开始执行");

        for (int i = 1; i <= 5; i++) {
            System.out.println(taskName + " - 进度: " + (i * 20) + "%");
            Thread.sleep(duration);
        }

        return taskName + " 执行完成，耗时: " + (duration * 5) + "ms";
    }

    public static void main(String[] args) {
        ExecutorService executor = Executors.newFixedThreadPool(2);
        System.out.println("CallableDemo.main");
        CallableDemo task1 = new CallableDemo("计算任务1", 300);
        CallableDemo task2 = new CallableDemo("计算任务2", 500);
        try {


            // 获取任务结果（会阻塞直到任务完成）
            Future<String> future1 = executor.submit(task1);
            Future<String> future2 = executor.submit(task2);

            // 获取任务结果（会阻塞直到任务完成）
            String result1 = future1.get();
            String result2 = future2.get();

            System.out.println("结果1: " + result1);
            System.out.println("结果2: " + result2);
        }
        catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
    }
}
