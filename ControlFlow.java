/**
 * 这个程序展示Java中的控制流语句
 */
public class ControlFlow {
    public static void main(String[] args) {
        // 1. if-else语句
        int score = 85;
        
        System.out.println("===== if-else示例 =====");
        if (score >= 90) {
            System.out.println("优秀");
        } else if (score >= 80) {
            System.out.println("良好");
        } else if (score >= 60) {
            System.out.println("及格");
        } else {
            System.out.println("不及格");
        }
        
        // 2. switch语句
        int day = 3;
        
        System.out.println("\n===== switch示例 =====");
        switch (day) {
            case 1:
                System.out.println("星期一");
                break;
            case 2:
                System.out.println("星期二");
                break;
            case 3:
                System.out.println("星期三");
                break;
            case 4:
                System.out.println("星期四");
                break;
            case 5:
                System.out.println("星期五");
                break;
            case 6:
            case 7:
                System.out.println("周末");
                break;
            default:
                System.out.println("无效的日期");
        }
        
        // 3. for循环
        System.out.println("\n===== for循环示例 =====");
        for (int i = 1; i <= 5; i++) {
            System.out.println("for循环计数: " + i);
        }
        
        // 4. while循环
        System.out.println("\n===== while循环示例 =====");
        int count = 1;
        while (count <= 5) {
            System.out.println("while循环计数: " + count);
            count++;
        }
        
        // 5. do-while循环
        System.out.println("\n===== do-while循环示例 =====");
        int num = 1;
        do {
            System.out.println("do-while循环计数: " + num);
            num++;
        } while (num <= 5);
        
        // 6. foreach循环（增强for循环）
        System.out.println("\n===== foreach循环示例 =====");
        String[] fruits = {"苹果", "香蕉", "橙子", "葡萄"};
        for (String fruit : fruits) {
            System.out.println("水果: " + fruit);
        }
    }
} 