package oop;

import java.util.ArrayList;
import java.util.List;

/**
 * Course类 - 课程类
 * 演示封装和对象间的关联关系
 */
public class Course {
    private String courseId;                    // 课程编号
    private String courseName;                  // 课程名称
    private int credits;                        // 学分
    private Teacher teacher;                    // 授课教师
    private List<Student> enrolledStudents;     // 已选学生列表
    private int maxCapacity;                    // 最大容量
    private String description;                 // 课程描述
    
    /**
     * 构造方法
     * @param courseId 课程编号
     * @param courseName 课程名称
     * @param credits 学分
     * @param maxCapacity 最大容量
     */
    public Course(String courseId, String courseName, int credits, int maxCapacity) {
        this.courseId = courseId;
        this.courseName = courseName;
        this.credits = credits;
        this.maxCapacity = maxCapacity;
        this.enrolledStudents = new ArrayList<>();
        this.description = "";
    }
    
    /**
     * 重载构造方法 - 包含课程描述
     */
    public Course(String courseId, String courseName, int credits, int maxCapacity, String description) {
        this(courseId, courseName, credits, maxCapacity);  // 调用主构造方法
        this.description = description;
    }
    
    /**
     * 添加学生到课程
     * @param student 要添加的学生
     * @return 是否添加成功
     */
    public boolean addStudent(Student student) {
        if (student == null) {
            System.out.println("学生不能为空");
            return false;
        }
        
        if (enrolledStudents.contains(student)) {
            System.out.println("学生" + student.getName() + "已经选择了这门课程");
            return false;
        }
        
        if (enrolledStudents.size() >= maxCapacity) {
            System.out.println("课程" + courseName + "已满员，无法添加学生");
            return false;
        }
        
        enrolledStudents.add(student);
        System.out.println("学生" + student.getName() + "成功加入课程：" + courseName);
        return true;
    }
    
    /**
     * 从课程中移除学生
     * @param student 要移除的学生
     * @return 是否移除成功
     */
    public boolean removeStudent(Student student) {
        if (enrolledStudents.remove(student)) {
            System.out.println("学生" + student.getName() + "已从课程" + courseName + "中移除");
            return true;
        } else {
            System.out.println("在课程" + courseName + "中未找到学生" + student.getName());
            return false;
        }
    }
    
    /**
     * 显示课程信息
     */
    public void displayCourseInfo() {
        System.out.println("=== 课程信息 ===");
        System.out.println("课程编号：" + courseId);
        System.out.println("课程名称：" + courseName);
        System.out.println("学分：" + credits);
        System.out.println("授课教师：" + (teacher != null ? teacher.getName() : "未分配"));
        System.out.println("已选学生：" + enrolledStudents.size() + "/" + maxCapacity);
        
        if (!description.isEmpty()) {
            System.out.println("课程描述：" + description);
        }
        
        if (!enrolledStudents.isEmpty()) {
            System.out.println("学生名单：");
            for (int i = 0; i < enrolledStudents.size(); i++) {
                Student student = enrolledStudents.get(i);
                System.out.println("  " + (i + 1) + ". " + student.getName() + 
                                 " (" + student.getId() + ")");
            }
        }
    }
    
    /**
     * 开始上课
     */
    public void startClass() {
        if (teacher == null) {
            System.out.println("课程" + courseName + "还没有分配教师，无法开课");
            return;
        }
        
        if (enrolledStudents.isEmpty()) {
            System.out.println("课程" + courseName + "没有学生选课，无法开课");
            return;
        }
        
        System.out.println("=== " + courseName + "课程开始 ===");
        System.out.println("授课教师：" + teacher.getName());
        System.out.println("学生人数：" + enrolledStudents.size());
        System.out.println("课程正在进行中...");
    }
    
    /**
     * 检查课程是否已满
     * @return 是否已满
     */
    public boolean isFull() {
        return enrolledStudents.size() >= maxCapacity;
    }
    
    /**
     * 获取剩余容量
     * @return 剩余容量
     */
    public int getRemainingCapacity() {
        return maxCapacity - enrolledStudents.size();
    }
    
    // Getter和Setter方法
    public String getCourseId() {
        return courseId;
    }
    
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    public int getCredits() {
        return credits;
    }
    
    public void setCredits(int credits) {
        if (credits > 0 && credits <= 10) {
            this.credits = credits;
        } else {
            System.out.println("学分必须在1-10之间");
        }
    }
    
    public Teacher getTeacher() {
        return teacher;
    }
    
    public void setTeacher(Teacher teacher) {
        this.teacher = teacher;
    }
    
    public List<Student> getEnrolledStudents() {
        return new ArrayList<>(enrolledStudents);  // 返回副本，保护内部数据
    }
    
    public int getMaxCapacity() {
        return maxCapacity;
    }
    
    public void setMaxCapacity(int maxCapacity) {
        if (maxCapacity >= enrolledStudents.size()) {
            this.maxCapacity = maxCapacity;
        } else {
            System.out.println("最大容量不能小于当前学生数量");
        }
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * 重写equals方法 - 基于课程编号比较
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Course course = (Course) obj;
        return courseId != null ? courseId.equals(course.courseId) : course.courseId == null;
    }
    
    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {
        return courseId != null ? courseId.hashCode() : 0;
    }
    
    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        return "Course{" +
                "courseId='" + courseId + '\'' +
                ", courseName='" + courseName + '\'' +
                ", credits=" + credits +
                ", teacher=" + (teacher != null ? teacher.getName() : "未分配") +
                ", enrolledStudents=" + enrolledStudents.size() +
                ", maxCapacity=" + maxCapacity +
                '}';
    }
}
