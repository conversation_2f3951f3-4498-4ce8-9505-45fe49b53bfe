# Java学习项目 - 多模块结构

这是一个多模块的Java学习项目，每个模块专注于不同的技术栈和功能。

## 项目结构

```
java-learning-parent/
├── pom.xml                    # 父项目POM文件
├── README-MODULES.md          # 本文档
├── mysql-demo/                # MySQL数据库学习模块
│   ├── pom.xml
│   └── src/
├── redis-demo/                # Redis缓存学习模块
│   ├── pom.xml
│   └── src/
├── mongodb-demo/              # MongoDB文档数据库学习模块
│   ├── pom.xml
│   └── src/
├── security-demo/             # Spring Security安全学习模块
│   ├── pom.xml
│   └── src/
├── web-demo/                  # Web开发学习模块
│   ├── pom.xml
│   └── src/
└── microservice-demo/         # 微服务学习模块
    ├── pom.xml
    └── src/
```

## 模块说明

### 1. mysql-demo - MySQL数据库学习
**学习内容：**
- Spring Data JPA
- MySQL数据库操作
- 实体类设计
- Repository模式
- 数据库事务
- 查询优化

**主要依赖：**
- spring-boot-starter-data-jpa
- mysql-connector-j
- h2database (测试用)

### 2. redis-demo - Redis缓存学习
**学习内容：**
- Redis基本操作
- Spring Data Redis
- 缓存策略
- 分布式锁
- 消息队列
- 数据结构应用

**主要依赖：**
- spring-boot-starter-data-redis
- commons-pool2
- embedded-redis (测试用)

### 3. mongodb-demo - MongoDB学习
**学习内容：**
- MongoDB文档数据库
- Spring Data MongoDB
- 文档查询
- 聚合操作
- 索引优化

### 4. security-demo - Spring Security学习
**学习内容：**
- 用户认证
- 权限控制
- JWT Token
- OAuth2
- 安全配置

### 5. web-demo - Web开发学习
**学习内容：**
- RESTful API
- 参数验证
- 异常处理
- 文件上传
- 跨域处理
- API文档

### 6. microservice-demo - 微服务学习
**学习内容：**
- Spring Cloud
- 服务注册与发现
- 配置中心
- 网关
- 熔断器
- 链路追踪

## 在IDEA中的操作步骤

### 1. 导入多模块项目
1. **File** → **Open** → 选择父项目目录
2. IDEA会自动识别为Maven多模块项目
3. 等待Maven依赖下载完成

### 2. 创建新模块
1. 右键点击父项目 → **New** → **Module**
2. 选择 **Maven** 或 **Spring Initializr**
3. 配置模块信息：
   - **Parent**: 选择父项目
   - **ArtifactId**: 模块名称
   - **GroupId**: 继承父项目

### 3. 运行特定模块
1. 在Project面板中选择要运行的模块
2. 找到该模块的主类（带@SpringBootApplication注解）
3. 右键 → **Run 'Application'**

### 4. 模块间依赖
如果模块间需要相互依赖，在子模块的pom.xml中添加：
```xml
<dependency>
    <groupId>com.example</groupId>
    <artifactId>其他模块名</artifactId>
    <version>${project.version}</version>
</dependency>
```

## 使用建议

### 1. 学习路径
建议按以下顺序学习：
1. **mysql-demo** - 掌握数据库基础
2. **web-demo** - 学习Web API开发
3. **redis-demo** - 学习缓存应用
4. **security-demo** - 学习安全控制
5. **mongodb-demo** - 学习NoSQL数据库
6. **microservice-demo** - 学习微服务架构

### 2. 开发规范
- 每个模块保持独立性
- 统一的包命名规范：`com.example.模块名`
- 统一的配置文件命名
- 统一的日志配置

### 3. 测试策略
- 每个模块都有完整的单元测试
- 使用内存数据库进行测试
- 集成测试与单元测试分离

## 快速开始

1. **替换当前pom.xml**：
   ```bash
   mv pom.xml pom-old.xml
   mv pom-parent.xml pom.xml
   ```

2. **刷新Maven项目**：
   - IDEA中右键项目 → **Maven** → **Reload project**

3. **开始学习**：
   - 选择感兴趣的模块
   - 查看该模块的README文档
   - 运行示例代码

每个模块都是独立的Spring Boot应用，可以单独运行和测试！
