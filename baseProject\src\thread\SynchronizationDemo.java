package thread;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 线程同步机制演示
 * 包括synchronized、Lock、volatile等
 */
public class SynchronizationDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 线程同步演示 ===");
        
        // 1. synchronized演示
        synchronizedDemo();
        
        // 2. Lock演示
        lockDemo();
        
        // 3. volatile演示
        volatileDemo();
        
        // 4. 读写锁演示
        readWriteLockDemo();
    }
    
    /**
     * synchronized关键字演示
     */
    public static void synchronizedDemo() {
        System.out.println("\n--- synchronized演示 ---");
        
        class Counter {
            private int count = 0;
            
            // 同步方法
            public synchronized void increment() {
                count++;
                System.out.println(Thread.currentThread().getName() + 
                    " - synchronized方法，count: " + count);
            }
            
            // 同步代码块
            public void incrementWithBlock() {
                synchronized (this) {
                    count++;
                    System.out.println(Thread.currentThread().getName() + 
                        " - synchronized代码块，count: " + count);
                }
            }
            
            public int getCount() {
                return count;
            }
        }
        
        Counter counter = new Counter();
        
        // 创建多个线程同时访问
        Thread[] threads = new Thread[5];
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                if (threadId % 2 == 0) {
                    counter.increment();
                } else {
                    counter.incrementWithBlock();
                }
            }, "线程-" + i);
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        System.out.println("最终计数: " + counter.getCount());
    }
    
    /**
     * Lock接口演示
     */
    public static void lockDemo() {
        System.out.println("\n--- Lock演示 ---");
        
        class LockCounter {
            private int count = 0;
            private final Lock lock = new ReentrantLock();
            
            public void increment() {
                lock.lock();
                try {
                    count++;
                    System.out.println(Thread.currentThread().getName() + 
                        " - Lock机制，count: " + count);
                    Thread.sleep(100); // 模拟一些处理时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    lock.unlock();
                }
            }
            
            public void tryLockExample() {
                if (lock.tryLock()) {
                    try {
                        count++;
                        System.out.println(Thread.currentThread().getName() + 
                            " - tryLock成功，count: " + count);
                    } finally {
                        lock.unlock();
                    }
                } else {
                    System.out.println(Thread.currentThread().getName() + 
                        " - tryLock失败，无法获取锁");
                }
            }
            
            public int getCount() {
                return count;
            }
        }
        
        LockCounter counter = new LockCounter();
        
        // 创建多个线程
        Thread[] threads = new Thread[3];
        for (int i = 0; i < threads.length; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                if (threadId == 0) {
                    counter.tryLockExample();
                } else {
                    counter.increment();
                }
            }, "Lock线程-" + i);
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        System.out.println("Lock演示最终计数: " + counter.getCount());
    }
    
    /**
     * volatile关键字演示
     */
    public static void volatileDemo() {
        System.out.println("\n--- volatile演示 ---");
        
        class VolatileExample {
            private volatile boolean flag = false;
            private int count = 0;
            
            public void writer() {
                count = 42;
                flag = true; // volatile写操作
                System.out.println("写线程设置 count=42, flag=true");
            }
            
            public void reader() {
                if (flag) { // volatile读操作
                    System.out.println("读线程看到 flag=true, count=" + count);
                } else {
                    System.out.println("读线程看到 flag=false");
                }
            }
        }
        
        VolatileExample example = new VolatileExample();
        
        // 读线程
        Thread readerThread = new Thread(() -> {
            for (int i = 0; i < 5; i++) {
                example.reader();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }, "读线程");
        
        // 写线程
        Thread writerThread = new Thread(() -> {
            try {
                Thread.sleep(200); // 延迟一下再写
                example.writer();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "写线程");
        
        readerThread.start();
        writerThread.start();
        
        try {
            readerThread.join();
            writerThread.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 读写锁演示
     */
    public static void readWriteLockDemo() {
        System.out.println("\n--- 读写锁演示 ---");
        
        class ReadWriteData {
            private final ReadWriteLock lock = new ReentrantReadWriteLock();
            private String data = "初始数据";
            
            public String read() {
                lock.readLock().lock();
                try {
                    System.out.println(Thread.currentThread().getName() + 
                        " 开始读取: " + data);
                    Thread.sleep(1000); // 模拟读取耗时
                    System.out.println(Thread.currentThread().getName() + 
                        " 读取完成: " + data);
                    return data;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return null;
                } finally {
                    lock.readLock().unlock();
                }
            }
            
            public void write(String newData) {
                lock.writeLock().lock();
                try {
                    System.out.println(Thread.currentThread().getName() + 
                        " 开始写入: " + newData);
                    this.data = newData;
                    Thread.sleep(1000); // 模拟写入耗时
                    System.out.println(Thread.currentThread().getName() + 
                        " 写入完成: " + newData);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    lock.writeLock().unlock();
                }
            }
        }
        
        ReadWriteData data = new ReadWriteData();
        
        // 创建多个读线程和写线程
        Thread[] readers = new Thread[3];
        Thread[] writers = new Thread[2];
        
        // 创建读线程
        for (int i = 0; i < readers.length; i++) {
            readers[i] = new Thread(() -> {
                data.read();
            }, "读线程-" + i);
        }
        
        // 创建写线程
        for (int i = 0; i < writers.length; i++) {
            final int writerIndex = i;
            writers[i] = new Thread(() -> {
                data.write("新数据-" + writerIndex);
            }, "写线程-" + i);
        }
        
        // 启动所有线程
        for (Thread reader : readers) {
            reader.start();
        }
        for (Thread writer : writers) {
            writer.start();
        }
        
        // 等待所有线程完成
        try {
            for (Thread reader : readers) {
                reader.join();
            }
            for (Thread writer : writers) {
                writer.join();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

/**
 * 死锁演示和预防
 */
class DeadlockDemo {
    private static final Object lock1 = new Object();
    private static final Object lock2 = new Object();
    
    public static void main(String[] args) {
        System.out.println("=== 死锁演示 ===");
        
        // 可能导致死锁的代码
        Thread thread1 = new Thread(() -> {
            synchronized (lock1) {
                System.out.println("线程1获取锁1");
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                System.out.println("线程1尝试获取锁2");
                synchronized (lock2) {
                    System.out.println("线程1获取锁2");
                }
            }
        }, "线程1");
        
        Thread thread2 = new Thread(() -> {
            synchronized (lock2) {
                System.out.println("线程2获取锁2");
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                
                System.out.println("线程2尝试获取锁1");
                synchronized (lock1) {
                    System.out.println("线程2获取锁1");
                }
            }
        }, "线程2");
        
        thread1.start();
        thread2.start();
        
        // 等待一段时间检查是否死锁
        try {
            Thread.sleep(3000);
            if (thread1.isAlive() || thread2.isAlive()) {
                System.out.println("检测到死锁！强制中断线程");
                thread1.interrupt();
                thread2.interrupt();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
