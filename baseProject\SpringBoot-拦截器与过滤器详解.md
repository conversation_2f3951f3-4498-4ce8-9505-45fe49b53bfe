# Spring Boot 拦截器与过滤器详解

## 📚 目录

1. [概述与对比](#概述与对比)
2. [过滤器详解](#过滤器详解)
3. [拦截器详解](#拦截器详解)
4. [执行顺序](#执行顺序)
5. [实战案例](#实战案例)
6. [性能监控](#性能监控)
7. [安全控制](#安全控制)
8. [日志记录](#日志记录)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

---

## 概述与对比

### 1. 基本概念

#### 过滤器 (Filter)
- **定义**: 基于Servlet规范的组件，在请求到达Servlet之前和响应返回客户端之前进行处理
- **作用范围**: 整个Web应用
- **生命周期**: 由Servlet容器管理

#### 拦截器 (Interceptor)
- **定义**: Spring MVC框架提供的组件，在Controller方法执行前后进行处理
- **作用范围**: Spring MVC管理的请求
- **生命周期**: 由Spring容器管理

### 2. 对比分析

| 特性 | 过滤器 (Filter) | 拦截器 (Interceptor) |
|------|----------------|---------------------|
| **规范** | Servlet规范 | Spring MVC规范 |
| **容器** | Servlet容器 | Spring容器 |
| **作用时机** | Servlet前后 | Controller前后 |
| **配置方式** | web.xml或@WebFilter | 实现HandlerInterceptor |
| **依赖注入** | 不支持@Autowired | 支持@Autowired |
| **异常处理** | 无法处理Controller异常 | 可以处理Controller异常 |
| **访问Spring上下文** | 困难 | 容易 |

### 3. 执行流程图

```
请求 → 过滤器1 → 过滤器2 → DispatcherServlet → 拦截器1.preHandle → 拦截器2.preHandle 
→ Controller → 拦截器2.postHandle → 拦截器1.postHandle → 视图渲染 
→ 拦截器2.afterCompletion → 拦截器1.afterCompletion → 过滤器2 → 过滤器1 → 响应
```

---

## 过滤器详解

### 1. Filter接口

```java
public interface Filter {
    
    /**
     * 初始化方法，容器启动时调用
     */
    default void init(FilterConfig filterConfig) throws ServletException {}
    
    /**
     * 核心方法，每次请求都会调用
     */
    void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException;
    
    /**
     * 销毁方法，容器关闭时调用
     */
    default void destroy() {}
}
```

### 2. 基础过滤器实现

```java
@Component
@Slf4j
public class LoggingFilter implements Filter {
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("LoggingFilter 初始化");
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String requestId = UUID.randomUUID().toString();
        String method = httpRequest.getMethod();
        String uri = httpRequest.getRequestURI();
        String queryString = httpRequest.getQueryString();
        String fullUrl = queryString != null ? uri + "?" + queryString : uri;
        
        // 记录请求开始
        long startTime = System.currentTimeMillis();
        log.info("请求开始 [{}] {} {}", requestId, method, fullUrl);
        
        try {
            // 继续执行过滤器链
            chain.doFilter(request, response);
        } finally {
            // 记录请求结束
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            int status = httpResponse.getStatus();
            
            log.info("请求结束 [{}] {} {} - 状态: {} - 耗时: {}ms", 
                    requestId, method, fullUrl, status, duration);
        }
    }
    
    @Override
    public void destroy() {
        log.info("LoggingFilter 销毁");
    }
}
```

### 3. 字符编码过滤器

```java
@Component
@Order(1)
public class CharacterEncodingFilter implements Filter {
    
    private static final String DEFAULT_ENCODING = "UTF-8";
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 设置请求编码
        if (httpRequest.getCharacterEncoding() == null) {
            httpRequest.setCharacterEncoding(DEFAULT_ENCODING);
        }
        
        // 设置响应编码
        httpResponse.setCharacterEncoding(DEFAULT_ENCODING);
        httpResponse.setContentType("application/json;charset=" + DEFAULT_ENCODING);
        
        chain.doFilter(request, response);
    }
}
```

### 4. CORS过滤器

```java
@Component
@Order(2)
public class CorsFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 设置CORS头
        httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        httpResponse.setHeader("Access-Control-Allow-Headers", 
                "Origin, X-Requested-With, Content-Type, Accept, Authorization");
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        
        // 处理预检请求
        if ("OPTIONS".equalsIgnoreCase(httpRequest.getMethod())) {
            httpResponse.setStatus(HttpServletResponse.SC_OK);
            return;
        }
        
        chain.doFilter(request, response);
    }
}
```

### 5. 请求包装过滤器

```java
@Component
@Order(3)
public class RequestWrapperFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        // 包装请求，使请求体可重复读取
        if (isJsonRequest(httpRequest)) {
            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(httpRequest);
            chain.doFilter(wrappedRequest, response);
        } else {
            chain.doFilter(request, response);
        }
    }
    
    private boolean isJsonRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        return contentType != null && contentType.contains("application/json");
    }
}

/**
 * 可重复读取请求体的HttpServletRequest包装类
 */
public class CachedBodyHttpServletRequest extends HttpServletRequestWrapper {
    
    private byte[] cachedBody;
    
    public CachedBodyHttpServletRequest(HttpServletRequest request) throws IOException {
        super(request);
        InputStream requestInputStream = request.getInputStream();
        this.cachedBody = StreamUtils.copyToByteArray(requestInputStream);
    }
    
    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CachedBodyServletInputStream(this.cachedBody);
    }
    
    @Override
    public BufferedReader getReader() throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.cachedBody);
        return new BufferedReader(new InputStreamReader(byteArrayInputStream));
    }
    
    public byte[] getCachedBody() {
        return this.cachedBody;
    }
}

public class CachedBodyServletInputStream extends ServletInputStream {
    
    private InputStream cachedBodyInputStream;
    
    public CachedBodyServletInputStream(byte[] cachedBody) {
        this.cachedBodyInputStream = new ByteArrayInputStream(cachedBody);
    }
    
    @Override
    public boolean isFinished() {
        try {
            return cachedBodyInputStream.available() == 0;
        } catch (IOException e) {
            return false;
        }
    }
    
    @Override
    public boolean isReady() {
        return true;
    }
    
    @Override
    public void setReadListener(ReadListener readListener) {
        throw new UnsupportedOperationException();
    }
    
    @Override
    public int read() throws IOException {
        return cachedBodyInputStream.read();
    }
}
```

### 6. 过滤器注册配置

```java
@Configuration
public class FilterConfig {
    
    /**
     * 注册自定义过滤器
     */
    @Bean
    public FilterRegistrationBean<RequestValidationFilter> requestValidationFilter() {
        FilterRegistrationBean<RequestValidationFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new RequestValidationFilter());
        registration.addUrlPatterns("/api/*");
        registration.setOrder(1);
        registration.setName("requestValidationFilter");
        return registration;
    }
    
    /**
     * 注册IP限制过滤器
     */
    @Bean
    public FilterRegistrationBean<IpRestrictionFilter> ipRestrictionFilter() {
        FilterRegistrationBean<IpRestrictionFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new IpRestrictionFilter());
        registration.addUrlPatterns("/admin/*");
        registration.setOrder(2);
        registration.setName("ipRestrictionFilter");
        return registration;
    }
}
```

---

## 拦截器详解

### 1. HandlerInterceptor接口

```java
public interface HandlerInterceptor {
    
    /**
     * 在Controller方法执行前调用
     * @return true继续执行，false中断执行
     */
    default boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        return true;
    }
    
    /**
     * 在Controller方法执行后，视图渲染前调用
     */
    default void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            @Nullable ModelAndView modelAndView) throws Exception {
    }
    
    /**
     * 在整个请求完成后调用（包括视图渲染）
     */
    default void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            @Nullable Exception ex) throws Exception {
    }
}
```

### 2. 基础拦截器实现

```java
@Component
@Slf4j
public class LoggingInterceptor implements HandlerInterceptor {
    
    private static final String START_TIME = "startTime";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        
        long startTime = System.currentTimeMillis();
        request.setAttribute(START_TIME, startTime);
        
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String handlerName = getHandlerName(handler);
        
        log.info("拦截器-请求开始: {} {} -> {}", method, uri, handlerName);
        
        return true;
    }
    
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView modelAndView) throws Exception {
        
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String handlerName = getHandlerName(handler);
        
        log.info("拦截器-Controller执行完成: {} {} -> {}", method, uri, handlerName);
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) throws Exception {
        
        Long startTime = (Long) request.getAttribute(START_TIME);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String handlerName = getHandlerName(handler);
        int status = response.getStatus();
        
        if (ex != null) {
            log.error("拦截器-请求异常: {} {} -> {} - 状态: {} - 耗时: {}ms - 异常: {}", 
                    method, uri, handlerName, status, duration, ex.getMessage());
        } else {
            log.info("拦截器-请求完成: {} {} -> {} - 状态: {} - 耗时: {}ms", 
                    method, uri, handlerName, status, duration);
        }
    }
    
    private String getHandlerName(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            return handlerMethod.getBean().getClass().getSimpleName() + "." + handlerMethod.getMethod().getName();
        }
        return handler.toString();
    }
}
```

### 3. 认证拦截器

```java
@Component
@Slf4j
public class AuthenticationInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        // 检查是否需要认证
        if (!requiresAuthentication(handler)) {
            return true;
        }

        String token = extractToken(request);
        if (token == null) {
            sendUnauthorizedResponse(response, "缺少认证令牌");
            return false;
        }

        try {
            if (!tokenProvider.validateToken(token)) {
                sendUnauthorizedResponse(response, "无效的认证令牌");
                return false;
            }

            Long userId = tokenProvider.getUserIdFromJWT(token);
            User user = userService.getUserById(userId);

            if (user == null || !user.getEnabled()) {
                sendUnauthorizedResponse(response, "用户不存在或已被禁用");
                return false;
            }

            // 将用户信息存储到请求属性中
            request.setAttribute("currentUser", user);
            request.setAttribute("userId", userId);

            log.debug("用户认证成功: {}", user.getUsername());
            return true;

        } catch (Exception e) {
            log.error("认证过程中发生异常", e);
            sendUnauthorizedResponse(response, "认证失败");
            return false;
        }
    }

    private boolean requiresAuthentication(Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return false;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;

        // 检查方法上的注解
        if (handlerMethod.hasMethodAnnotation(RequiresAuthentication.class)) {
            return true;
        }

        // 检查类上的注解
        if (handlerMethod.getBeanType().isAnnotationPresent(RequiresAuthentication.class)) {
            return true;
        }

        // 检查是否有@Public注解（跳过认证）
        if (handlerMethod.hasMethodAnnotation(Public.class) ||
            handlerMethod.getBeanType().isAnnotationPresent(Public.class)) {
            return false;
        }

        return false;
    }

    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("timestamp", new Date());

        ObjectMapper objectMapper = new ObjectMapper();
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
```

### 4. 权限控制拦截器

```java
@Component
@Slf4j
public class AuthorizationInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;

        // 检查是否需要权限验证
        RequiresPermission requiresPermission = handlerMethod.getMethodAnnotation(RequiresPermission.class);
        if (requiresPermission == null) {
            requiresPermission = handlerMethod.getBeanType().getAnnotation(RequiresPermission.class);
        }

        if (requiresPermission == null) {
            return true;
        }

        // 获取当前用户
        User currentUser = (User) request.getAttribute("currentUser");
        if (currentUser == null) {
            sendForbiddenResponse(response, "用户未认证");
            return false;
        }

        // 检查权限
        String[] requiredPermissions = requiresPermission.value();
        boolean hasPermission = checkPermissions(currentUser, requiredPermissions, requiresPermission.logical());

        if (!hasPermission) {
            log.warn("用户 {} 尝试访问需要权限 {} 的资源: {}",
                    currentUser.getUsername(), Arrays.toString(requiredPermissions), request.getRequestURI());
            sendForbiddenResponse(response, "权限不足");
            return false;
        }

        log.debug("用户 {} 权限验证通过: {}", currentUser.getUsername(), Arrays.toString(requiredPermissions));
        return true;
    }

    private boolean checkPermissions(User user, String[] requiredPermissions, RequiresPermission.Logical logical) {
        Set<String> userPermissions = getUserPermissions(user);

        if (logical == RequiresPermission.Logical.AND) {
            // 需要拥有所有权限
            return Arrays.stream(requiredPermissions).allMatch(userPermissions::contains);
        } else {
            // 需要拥有任一权限
            return Arrays.stream(requiredPermissions).anyMatch(userPermissions::contains);
        }
    }

    private Set<String> getUserPermissions(User user) {
        Set<String> permissions = new HashSet<>();

        // 添加角色权限
        user.getRoles().forEach(role -> {
            permissions.add(role.getName().name());
            // 添加角色下的具体权限
            role.getPermissions().forEach(permission -> permissions.add(permission.getName()));
        });

        return permissions;
    }

    private void sendForbiddenResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("timestamp", new Date());

        ObjectMapper objectMapper = new ObjectMapper();
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
```

### 5. 限流拦截器

```java
@Component
@Slf4j
public class RateLimitInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String RATE_LIMIT_KEY_PREFIX = "rate_limit:";
    private static final int DEFAULT_LIMIT = 100; // 默认每分钟100次
    private static final int DEFAULT_WINDOW = 60; // 默认时间窗口60秒

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        RateLimit rateLimit = handlerMethod.getMethodAnnotation(RateLimit.class);

        if (rateLimit == null) {
            rateLimit = handlerMethod.getBeanType().getAnnotation(RateLimit.class);
        }

        if (rateLimit == null) {
            return true;
        }

        String key = generateKey(request, rateLimit);
        int limit = rateLimit.value() > 0 ? rateLimit.value() : DEFAULT_LIMIT;
        int window = rateLimit.window() > 0 ? rateLimit.window() : DEFAULT_WINDOW;

        if (!checkRateLimit(key, limit, window)) {
            sendTooManyRequestsResponse(response, "请求过于频繁，请稍后再试");
            return false;
        }

        return true;
    }

    private String generateKey(HttpServletRequest request, RateLimit rateLimit) {
        StringBuilder keyBuilder = new StringBuilder(RATE_LIMIT_KEY_PREFIX);

        switch (rateLimit.type()) {
            case IP:
                keyBuilder.append("ip:").append(getClientIp(request));
                break;
            case USER:
                Long userId = (Long) request.getAttribute("userId");
                keyBuilder.append("user:").append(userId != null ? userId : "anonymous");
                break;
            case API:
                keyBuilder.append("api:").append(request.getRequestURI());
                break;
            default:
                keyBuilder.append("global");
        }

        return keyBuilder.toString();
    }

    private boolean checkRateLimit(String key, int limit, int window) {
        try {
            String currentCountStr = redisTemplate.opsForValue().get(key);
            int currentCount = currentCountStr != null ? Integer.parseInt(currentCountStr) : 0;

            if (currentCount >= limit) {
                return false;
            }

            // 增加计数
            if (currentCount == 0) {
                redisTemplate.opsForValue().set(key, "1", Duration.ofSeconds(window));
            } else {
                redisTemplate.opsForValue().increment(key);
            }

            return true;
        } catch (Exception e) {
            log.error("限流检查失败", e);
            return true; // 发生异常时允许通过
        }
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private void sendTooManyRequestsResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(429); // Too Many Requests
        response.setContentType("application/json;charset=UTF-8");

        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("timestamp", new Date());

        ObjectMapper objectMapper = new ObjectMapper();
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
```

### 6. 拦截器注册配置

```java
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private LoggingInterceptor loggingInterceptor;

    @Autowired
    private AuthenticationInterceptor authenticationInterceptor;

    @Autowired
    private AuthorizationInterceptor authorizationInterceptor;

    @Autowired
    private RateLimitInterceptor rateLimitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 日志拦截器 - 拦截所有请求
        registry.addInterceptor(loggingInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/static/**", "/public/**")
                .order(1);

        // 限流拦截器 - 拦截API请求
        registry.addInterceptor(rateLimitInterceptor)
                .addPathPatterns("/api/**")
                .order(2);

        // 认证拦截器 - 拦截需要认证的请求
        registry.addInterceptor(authenticationInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/auth/**", "/api/public/**")
                .order(3);

        // 权限拦截器 - 拦截需要权限的请求
        registry.addInterceptor(authorizationInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/auth/**", "/api/public/**")
                .order(4);
    }
}
```

---

## 执行顺序

### 1. 完整执行流程

```java
@RestController
@Slf4j
public class OrderTestController {

    @GetMapping("/test/order")
    public ResponseEntity<String> testOrder() {
        log.info("Controller方法执行");
        return ResponseEntity.ok("执行顺序测试");
    }
}
```

**执行顺序示例：**

```
1. CharacterEncodingFilter.doFilter() - 开始
2. CorsFilter.doFilter() - 开始
3. RequestWrapperFilter.doFilter() - 开始
4. LoggingFilter.doFilter() - 开始
5. DispatcherServlet处理请求
6. LoggingInterceptor.preHandle()
7. RateLimitInterceptor.preHandle()
8. AuthenticationInterceptor.preHandle()
9. AuthorizationInterceptor.preHandle()
10. Controller方法执行
11. AuthorizationInterceptor.postHandle()
12. AuthenticationInterceptor.postHandle()
13. RateLimitInterceptor.postHandle()
14. LoggingInterceptor.postHandle()
15. 视图渲染（如果有）
16. LoggingInterceptor.afterCompletion()
17. RateLimitInterceptor.afterCompletion()
18. AuthenticationInterceptor.afterCompletion()
19. AuthorizationInterceptor.afterCompletion()
20. LoggingFilter.doFilter() - 结束
21. RequestWrapperFilter.doFilter() - 结束
22. CorsFilter.doFilter() - 结束
23. CharacterEncodingFilter.doFilter() - 结束
```

### 2. 异常情况执行顺序

```java
@Component
@Slf4j
public class ExceptionHandlingInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        log.info("ExceptionHandlingInterceptor.preHandle()");
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
            ModelAndView modelAndView) throws Exception {
        log.info("ExceptionHandlingInterceptor.postHandle()");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) throws Exception {
        if (ex != null) {
            log.error("ExceptionHandlingInterceptor.afterCompletion() - 异常: {}", ex.getMessage());
        } else {
            log.info("ExceptionHandlingInterceptor.afterCompletion() - 正常");
        }
    }
}
```

**异常时的执行顺序：**

```
1. 过滤器链正常执行
2. 拦截器preHandle()按顺序执行
3. Controller方法执行时抛出异常
4. 跳过所有拦截器的postHandle()方法
5. 拦截器afterCompletion()按逆序执行（会收到异常参数）
6. 过滤器链逆序执行完成
```

---

## 实战案例

### 1. API接口监控系统

```java
@Component
@Slf4j
public class ApiMonitoringInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ApiMetricsService apiMetricsService;

    private static final String METRICS_KEY_PREFIX = "api_metrics:";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        String apiKey = generateApiKey(request, handlerMethod);

        // 记录API调用开始时间
        request.setAttribute("apiKey", apiKey);
        request.setAttribute("startTime", System.currentTimeMillis());

        // 增加调用计数
        incrementApiCallCount(apiKey);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) throws Exception {

        String apiKey = (String) request.getAttribute("apiKey");
        Long startTime = (Long) request.getAttribute("startTime");

        if (apiKey != null && startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            int statusCode = response.getStatus();

            // 记录API调用指标
            ApiMetrics metrics = ApiMetrics.builder()
                    .apiKey(apiKey)
                    .method(request.getMethod())
                    .uri(request.getRequestURI())
                    .statusCode(statusCode)
                    .duration(duration)
                    .timestamp(LocalDateTime.now())
                    .success(ex == null && statusCode < 400)
                    .errorMessage(ex != null ? ex.getMessage() : null)
                    .build();

            // 异步保存指标数据
            apiMetricsService.saveMetrics(metrics);

            // 更新Redis中的实时统计
            updateRealTimeMetrics(apiKey, duration, statusCode, ex == null);
        }
    }

    private String generateApiKey(HttpServletRequest request, HandlerMethod handlerMethod) {
        return request.getMethod() + ":" + request.getRequestURI();
    }

    private void incrementApiCallCount(String apiKey) {
        String key = METRICS_KEY_PREFIX + "count:" + apiKey;
        redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, Duration.ofDays(1));
    }

    private void updateRealTimeMetrics(String apiKey, long duration, int statusCode, boolean success) {
        String baseKey = METRICS_KEY_PREFIX + apiKey;

        // 更新平均响应时间
        redisTemplate.opsForList().leftPush(baseKey + ":durations", duration);
        redisTemplate.opsForList().trim(baseKey + ":durations", 0, 99); // 保留最近100次
        redisTemplate.expire(baseKey + ":durations", Duration.ofHours(1));

        // 更新成功/失败计数
        if (success) {
            redisTemplate.opsForValue().increment(baseKey + ":success");
        } else {
            redisTemplate.opsForValue().increment(baseKey + ":failure");
        }

        // 更新状态码统计
        redisTemplate.opsForHash().increment(baseKey + ":status_codes", String.valueOf(statusCode), 1);
    }
}
```

### 2. 用户行为追踪

```java
@Component
@Slf4j
public class UserBehaviorTrackingInterceptor implements HandlerInterceptor {

    @Autowired
    private UserBehaviorService userBehaviorService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;

        // 检查是否需要追踪
        if (!needsTracking(handlerMethod)) {
            return true;
        }

        // 收集用户行为数据
        UserBehavior behavior = collectUserBehavior(request, handlerMethod);
        request.setAttribute("userBehavior", behavior);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) throws Exception {

        UserBehavior behavior = (UserBehavior) request.getAttribute("userBehavior");
        if (behavior != null) {
            behavior.setResponseStatus(response.getStatus());
            behavior.setSuccess(ex == null && response.getStatus() < 400);
            behavior.setEndTime(LocalDateTime.now());

            if (ex != null) {
                behavior.setErrorMessage(ex.getMessage());
            }

            // 异步保存用户行为数据
            userBehaviorService.saveBehavior(behavior);
        }
    }

    private boolean needsTracking(HandlerMethod handlerMethod) {
        return handlerMethod.hasMethodAnnotation(TrackUserBehavior.class) ||
               handlerMethod.getBeanType().isAnnotationPresent(TrackUserBehavior.class);
    }

    private UserBehavior collectUserBehavior(HttpServletRequest request, HandlerMethod handlerMethod) {
        Long userId = (Long) request.getAttribute("userId");
        String sessionId = request.getSession().getId();
        String userAgent = request.getHeader("User-Agent");
        String clientIp = getClientIp(request);
        String referer = request.getHeader("Referer");

        return UserBehavior.builder()
                .userId(userId)
                .sessionId(sessionId)
                .action(generateActionName(handlerMethod))
                .method(request.getMethod())
                .uri(request.getRequestURI())
                .parameters(getRequestParameters(request))
                .userAgent(userAgent)
                .clientIp(clientIp)
                .referer(referer)
                .startTime(LocalDateTime.now())
                .build();
    }

    private String generateActionName(HandlerMethod handlerMethod) {
        TrackUserBehavior annotation = handlerMethod.getMethodAnnotation(TrackUserBehavior.class);
        if (annotation != null && StringUtils.hasText(annotation.value())) {
            return annotation.value();
        }

        return handlerMethod.getBean().getClass().getSimpleName() + "." + handlerMethod.getMethod().getName();
    }

    private Map<String, String> getRequestParameters(HttpServletRequest request) {
        Map<String, String> parameters = new HashMap<>();
        request.getParameterMap().forEach((key, values) -> {
            if (values.length > 0) {
                parameters.put(key, values[0]);
            }
        });
        return parameters;
    }

    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
```

### 3. 自定义注解

```java
// 需要认证的注解
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresAuthentication {
}

// 公开访问的注解
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Public {
}

// 需要权限的注解
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermission {
    String[] value();
    Logical logical() default Logical.AND;

    enum Logical {
        AND, OR
    }
}

// 限流注解
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {
    int value() default 100; // 每分钟允许的请求次数
    int window() default 60; // 时间窗口（秒）
    Type type() default Type.IP; // 限流类型

    enum Type {
        IP, USER, API, GLOBAL
    }
}

// 用户行为追踪注解
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TrackUserBehavior {
    String value() default ""; // 行为描述
}
```

---

## 最佳实践

### 1. 性能优化

```java
@Component
@Slf4j
public class PerformanceOptimizedInterceptor implements HandlerInterceptor {

    // 使用ThreadLocal避免线程安全问题
    private static final ThreadLocal<Long> START_TIME = new ThreadLocal<>();

    // 使用缓存避免重复计算
    private final Map<String, Boolean> authRequiredCache = new ConcurrentHashMap<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        START_TIME.set(System.currentTimeMillis());

        // 快速路径：静态资源直接放行
        if (isStaticResource(request.getRequestURI())) {
            return true;
        }

        // 缓存认证检查结果
        String handlerKey = getHandlerKey(handler);
        Boolean authRequired = authRequiredCache.computeIfAbsent(handlerKey,
                k -> checkAuthenticationRequired(handler));

        if (authRequired) {
            return performAuthentication(request, response);
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) throws Exception {

        try {
            Long startTime = START_TIME.get();
            if (startTime != null) {
                long duration = System.currentTimeMillis() - startTime;

                // 只记录慢请求
                if (duration > 1000) {
                    log.warn("慢请求检测: {} {} - 耗时: {}ms",
                            request.getMethod(), request.getRequestURI(), duration);
                }
            }
        } finally {
            // 清理ThreadLocal避免内存泄漏
            START_TIME.remove();
        }
    }

    private boolean isStaticResource(String uri) {
        return uri.startsWith("/static/") ||
               uri.startsWith("/css/") ||
               uri.startsWith("/js/") ||
               uri.startsWith("/images/") ||
               uri.endsWith(".css") ||
               uri.endsWith(".js") ||
               uri.endsWith(".png") ||
               uri.endsWith(".jpg") ||
               uri.endsWith(".ico");
    }

    private String getHandlerKey(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod hm = (HandlerMethod) handler;
            return hm.getBeanType().getName() + "#" + hm.getMethod().getName();
        }
        return handler.toString();
    }

    private boolean checkAuthenticationRequired(Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return false;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        return handlerMethod.hasMethodAnnotation(RequiresAuthentication.class) ||
               handlerMethod.getBeanType().isAnnotationPresent(RequiresAuthentication.class);
    }

    private boolean performAuthentication(HttpServletRequest request, HttpServletResponse response) {
        // 实现认证逻辑
        return true;
    }
}
```

### 2. 异常处理

```java
@Component
@Slf4j
public class RobustInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        try {
            // 核心逻辑
            return doPreHandle(request, response, handler);
        } catch (Exception e) {
            log.error("拦截器preHandle异常: {}", e.getMessage(), e);

            // 根据异常类型决定是否继续执行
            if (e instanceof SecurityException) {
                sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, "安全检查失败");
                return false;
            }

            // 其他异常允许继续执行，避免影响正常业务
            return true;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) throws Exception {

        try {
            doAfterCompletion(request, response, handler, ex);
        } catch (Exception e) {
            // afterCompletion中的异常不应该影响响应
            log.error("拦截器afterCompletion异常: {}", e.getMessage(), e);
        }
    }

    private boolean doPreHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 实际的preHandle逻辑
        return true;
    }

    private void doAfterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 实际的afterCompletion逻辑
    }

    private void sendErrorResponse(HttpServletResponse response, int status, String message) {
        try {
            response.setStatus(status);
            response.setContentType("application/json;charset=UTF-8");

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", message);
            result.put("timestamp", new Date());

            ObjectMapper objectMapper = new ObjectMapper();
            response.getWriter().write(objectMapper.writeValueAsString(result));
        } catch (IOException e) {
            log.error("发送错误响应失败", e);
        }
    }
}
```

### 3. 配置管理

```java
@ConfigurationProperties(prefix = "app.interceptor")
@Component
@Data
public class InterceptorProperties {

    private boolean enabled = true;
    private Logging logging = new Logging();
    private RateLimit rateLimit = new RateLimit();
    private Authentication authentication = new Authentication();

    @Data
    public static class Logging {
        private boolean enabled = true;
        private boolean logRequestBody = false;
        private boolean logResponseBody = false;
        private List<String> excludePatterns = Arrays.asList("/health", "/metrics");
    }

    @Data
    public static class RateLimit {
        private boolean enabled = true;
        private int defaultLimit = 100;
        private int defaultWindow = 60;
        private String redisKeyPrefix = "rate_limit:";
    }

    @Data
    public static class Authentication {
        private boolean enabled = true;
        private List<String> excludePatterns = Arrays.asList("/api/auth/**", "/api/public/**");
        private String tokenHeader = "Authorization";
        private String tokenPrefix = "Bearer ";
    }
}
```

### 4. 测试

```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class InterceptorTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    private JwtTokenProvider tokenProvider;

    @Test
    public void testAuthenticationInterceptor_WithValidToken() {
        // 模拟有效令牌
        when(tokenProvider.validateToken(anyString())).thenReturn(true);
        when(tokenProvider.getUserIdFromJWT(anyString())).thenReturn(1L);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth("valid-token");
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(
                "/api/users/me", HttpMethod.GET, entity, String.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    public void testAuthenticationInterceptor_WithInvalidToken() {
        // 模拟无效令牌
        when(tokenProvider.validateToken(anyString())).thenReturn(false);

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth("invalid-token");
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(
                "/api/users/me", HttpMethod.GET, entity, String.class);

        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
    }

    @Test
    public void testRateLimitInterceptor() {
        // 测试限流功能
        for (int i = 0; i < 10; i++) {
            ResponseEntity<String> response = restTemplate.getForEntity("/api/test", String.class);
            if (i < 5) {
                assertEquals(HttpStatus.OK, response.getStatusCode());
            } else {
                assertEquals(429, response.getStatusCodeValue()); // Too Many Requests
            }
        }
    }
}
```

---

## 常见问题

### 1. 过滤器无法注入Spring Bean

**问题**: 在Filter中使用@Autowired注入Bean失败

**解决方案**:
```java
// 方法1: 使用@Component注解
@Component
public class MyFilter implements Filter {
    @Autowired
    private MyService myService; // 可以正常注入
}

// 方法2: 手动获取Bean
public class MyFilter implements Filter {

    private MyService myService;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        ServletContext servletContext = filterConfig.getServletContext();
        WebApplicationContext webApplicationContext =
            WebApplicationContextUtils.getWebApplicationContext(servletContext);
        this.myService = webApplicationContext.getBean(MyService.class);
    }
}
```

### 2. 拦截器执行顺序问题

**问题**: 拦截器执行顺序不符合预期

**解决方案**:
```java
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 使用order()方法明确指定执行顺序
        registry.addInterceptor(new FirstInterceptor()).order(1);
        registry.addInterceptor(new SecondInterceptor()).order(2);
        registry.addInterceptor(new ThirdInterceptor()).order(3);
    }
}
```

### 3. 请求体只能读取一次

**问题**: 在Filter中读取请求体后，Controller无法再次读取

**解决方案**:
```java
// 使用HttpServletRequestWrapper包装请求
public class CachedBodyHttpServletRequest extends HttpServletRequestWrapper {
    private byte[] cachedBody;

    public CachedBodyHttpServletRequest(HttpServletRequest request) throws IOException {
        super(request);
        InputStream requestInputStream = request.getInputStream();
        this.cachedBody = StreamUtils.copyToByteArray(requestInputStream);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return new CachedBodyServletInputStream(this.cachedBody);
    }

    @Override
    public BufferedReader getReader() throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.cachedBody);
        return new BufferedReader(new InputStreamReader(byteArrayInputStream));
    }
}
```

### 4. 异步请求处理

**问题**: 异步请求中拦截器行为异常

**解决方案**:
```java
@Component
public class AsyncHandlerInterceptor implements AsyncHandlerInterceptor {

    @Override
    public void afterConcurrentHandlingStarted(HttpServletRequest request,
                                             HttpServletResponse response,
                                             Object handler) throws Exception {
        // 异步处理开始时的逻辑
    }

    @Override
    public void afterCompletion(HttpServletRequest request,
                              HttpServletResponse response,
                              Object handler,
                              Exception ex) throws Exception {
        // 异步处理完成后的逻辑
    }
}
```

## 📝 总结

### 选择建议

1. **使用过滤器的场景**:
   - 字符编码设置
   - CORS处理
   - 请求/响应包装
   - 全局安全检查

2. **使用拦截器的场景**:
   - 用户认证和授权
   - 日志记录
   - 性能监控
   - 业务逻辑处理

### 最佳实践

1. **性能考虑**: 避免在拦截器中进行耗时操作
2. **异常处理**: 妥善处理异常，避免影响正常业务
3. **资源清理**: 使用ThreadLocal时注意清理
4. **配置管理**: 使用配置文件管理拦截器行为
5. **测试覆盖**: 编写完整的单元测试和集成测试

通过合理使用过滤器和拦截器，可以实现强大的横切关注点处理，提高应用的可维护性和扩展性。
```
