package com.example.jdbc.examples;

import com.example.jdbc.entity.User;
import com.example.jdbc.util.JdbcUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * JDBC基础CRUD操作示例
 * 演示增删改查的基本操作
 */
@Component
public class BasicCrudExample {
    
    private static final Logger logger = LoggerFactory.getLogger(BasicCrudExample.class);
    
    @Autowired
    private JdbcUtil jdbcUtil;
    
    /**
     * 创建用户表
     */
    public void createUserTable() {
        String sql = "CREATE TABLE IF NOT EXISTS users (" +
                     "id BIGINT AUTO_INCREMENT PRIMARY KEY," +
                     "username VARCHAR(50) NOT NULL UNIQUE," +
                     "email VARCHAR(100) NOT NULL," +
                     "phone VARCHAR(20)," +
                     "age INT," +
                     "address VARCHAR(200)," +
                     "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                     "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP," +
                     "INDEX idx_username (username)," +
                     "INDEX idx_email (email)" +
                     ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

        if (jdbcUtil.executeDDL(sql)) {
            logger.info("用户表创建成功");
        } else {
            logger.error("用户表创建失败");
        }
    }
    
    /**
     * 插入用户 - 基础方式
     */
    public boolean insertUser(User user) {
        String sql = "INSERT INTO users (username, email, phone, age, address, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = jdbcUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            // 设置参数
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getEmail());
            pstmt.setString(3, user.getPhone());
            pstmt.setObject(4, user.getAge());
            pstmt.setString(5, user.getAddress());
            pstmt.setTimestamp(6, Timestamp.valueOf(user.getCreatedAt()));
            pstmt.setTimestamp(7, Timestamp.valueOf(user.getUpdatedAt()));
            
            int result = pstmt.executeUpdate();
            
            if (result > 0) {
                logger.info("用户插入成功: {}", user.getUsername());
                return true;
            }
            
        } catch (SQLException e) {
            logger.error("插入用户失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 插入用户并返回生成的ID
     */
    public Long insertUserAndReturnId(User user) {
        String sql = "INSERT INTO users (username, email, phone, age, address, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = jdbcUtil.getConnection();
            // 指定返回生成的键
            pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getEmail());
            pstmt.setString(3, user.getPhone());
            pstmt.setObject(4, user.getAge());
            pstmt.setString(5, user.getAddress());
            pstmt.setTimestamp(6, Timestamp.valueOf(user.getCreatedAt()));
            pstmt.setTimestamp(7, Timestamp.valueOf(user.getUpdatedAt()));
            
            int result = pstmt.executeUpdate();
            
            if (result > 0) {
                // 获取生成的ID
                rs = pstmt.getGeneratedKeys();
                if (rs.next()) {
                    Long id = rs.getLong(1);
                    user.setId(id);
                    logger.info("用户插入成功，生成ID: {}", id);
                    return id;
                }
            }
            
        } catch (SQLException e) {
            logger.error("插入用户失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 根据ID查询用户
     */
    public User findUserById(Long id) {
        String sql = "SELECT id, username, email, phone, age, address, created_at, updated_at FROM users WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = jdbcUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setLong(1, id);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                User user = mapResultSetToUser(rs);
                logger.info("查询用户成功: {}", user.getUsername());
                return user;
            } else {
                logger.warn("未找到ID为{}的用户", id);
            }
            
        } catch (SQLException e) {
            logger.error("查询用户失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 查询所有用户
     */
    public List<User> findAllUsers() {
        String sql = "SELECT id, username, email, phone, age, address, created_at, updated_at FROM users ORDER BY created_at DESC";
        
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        List<User> users = new ArrayList<>();
        
        try {
            conn = jdbcUtil.getConnection();
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                User user = mapResultSetToUser(rs);
                users.add(user);
            }
            
            logger.info("查询到{}个用户", users.size());
            
        } catch (SQLException e) {
            logger.error("查询所有用户失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, stmt, rs);
        }
        
        return users;
    }
    
    /**
     * 根据用户名查询用户
     */
    public User findUserByUsername(String username) {
        String sql = "SELECT id, username, email, phone, age, address, created_at, updated_at FROM users WHERE username = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = jdbcUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                User user = mapResultSetToUser(rs);
                logger.info("根据用户名查询成功: {}", username);
                return user;
            }
            
        } catch (SQLException e) {
            logger.error("根据用户名查询失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 更新用户信息
     */
    public boolean updateUser(User user) {
        String sql = "UPDATE users SET username = ?, email = ?, phone = ?, age = ?, address = ?, updated_at = ? WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = jdbcUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            
            user.setUpdatedAt(LocalDateTime.now());
            
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getEmail());
            pstmt.setString(3, user.getPhone());
            pstmt.setObject(4, user.getAge());
            pstmt.setString(5, user.getAddress());
            pstmt.setTimestamp(6, Timestamp.valueOf(user.getUpdatedAt()));
            pstmt.setLong(7, user.getId());
            
            int result = pstmt.executeUpdate();
            
            if (result > 0) {
                logger.info("用户更新成功: {}", user.getUsername());
                return true;
            } else {
                logger.warn("未找到要更新的用户，ID: {}", user.getId());
            }
            
        } catch (SQLException e) {
            logger.error("更新用户失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 删除用户
     */
    public boolean deleteUser(Long id) {
        String sql = "DELETE FROM users WHERE id = ?";
        
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = jdbcUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setLong(1, id);
            
            int result = pstmt.executeUpdate();
            
            if (result > 0) {
                logger.info("用户删除成功，ID: {}", id);
                return true;
            } else {
                logger.warn("未找到要删除的用户，ID: {}", id);
            }
            
        } catch (SQLException e) {
            logger.error("删除用户失败: {}", e.getMessage(), e);
        } finally {
            jdbcUtil.closeAll(conn, pstmt, null);
        }
        
        return false;
    }
    
    /**
     * 将ResultSet映射为User对象
     */
    private User mapResultSetToUser(ResultSet rs) throws SQLException {
        User user = new User();
        user.setId(rs.getLong("id"));
        user.setUsername(rs.getString("username"));
        user.setEmail(rs.getString("email"));
        user.setPhone(rs.getString("phone"));
        
        // 处理可能为null的整数
        int age = rs.getInt("age");
        if (!rs.wasNull()) {
            user.setAge(age);
        }
        
        user.setAddress(rs.getString("address"));
        
        // 处理时间戳
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            user.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            user.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        return user;
    }
}
