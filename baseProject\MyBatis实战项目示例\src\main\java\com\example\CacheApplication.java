package com.example;

import com.example.entity.User;
import com.example.service.CacheService;
import com.example.service.UserService;
import com.example.vo.UserDetailVO;
import com.example.vo.UserStatsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Spring Boot缓存示例应用启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableCaching
public class CacheApplication implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheApplication.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CacheService cacheService;
    
    public static void main(String[] args) {
        SpringApplication.run(CacheApplication.class, args);
    }
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("=== Spring Boot缓存示例应用启动 ===");
        
        // 演示缓存功能
        demonstrateCache();
    }
    
    /**
     * 演示缓存功能
     */
    private void demonstrateCache() {
        try {
            logger.info("\n=== 开始演示缓存功能 ===");
            
            // 1. 演示基本缓存
            demonstrateBasicCache();
            
            // 2. 演示用户详情缓存
            demonstrateUserDetailCache();
            
            // 3. 演示批量操作
            demonstrateBatchOperations();
            
            // 4. 演示缓存更新
            demonstrateCacheUpdate();
            
            // 5. 演示系统配置缓存
            demonstrateSystemConfigCache();
            
            // 6. 演示缓存统计
            demonstrateCacheStatistics();
            
            logger.info("\n=== 缓存功能演示完成 ===");
            
        } catch (Exception e) {
            logger.error("演示缓存功能时发生异常", e);
        }
    }
    
    /**
     * 演示基本缓存功能
     */
    private void demonstrateBasicCache() {
        logger.info("\n--- 1. 基本缓存功能演示 ---");
        
        // 创建测试用户
        User user = createTestUser("demo_user");
        
        // 第一次查询 - 从数据库
        logger.info("第一次查询用户（从数据库）...");
        long start1 = System.currentTimeMillis();
        User user1 = userService.getUserById(user.getId());
        long time1 = System.currentTimeMillis() - start1;
        logger.info("查询结果: {}, 耗时: {}ms", user1.getUsername(), time1);
        
        // 第二次查询 - 从缓存
        logger.info("第二次查询用户（从缓存）...");
        long start2 = System.currentTimeMillis();
        User user2 = userService.getUserById(user.getId());
        long time2 = System.currentTimeMillis() - start2;
        logger.info("查询结果: {}, 耗时: {}ms", user2.getUsername(), time2);
        
        logger.info("缓存效果: 第二次查询比第一次快 {}ms", time1 - time2);
    }
    
    /**
     * 演示用户详情缓存
     */
    private void demonstrateUserDetailCache() {
        logger.info("\n--- 2. 用户详情缓存演示 ---");
        
        // 创建测试用户
        User user = createTestUser("detail_user");
        
        // 获取用户详情（包含角色和权限）
        logger.info("获取用户详情（包含角色和权限）...");
        UserDetailVO detail = cacheService.getUserDetail(user.getId());
        logger.info("用户详情: {}", detail.getUsername());
        logger.info("用户角色: {}", detail.getRoles());
        logger.info("用户权限: {}", detail.getPermissions());
        
        // 再次获取（从缓存）
        logger.info("再次获取用户详情（从缓存）...");
        UserDetailVO cachedDetail = cacheService.getUserDetail(user.getId());
        logger.info("缓存的用户详情: {}", cachedDetail.getUsername());
    }
    
    /**
     * 演示批量操作
     */
    private void demonstrateBatchOperations() {
        logger.info("\n--- 3. 批量操作演示 ---");
        
        // 创建多个测试用户
        User user1 = createTestUser("batch_user1");
        User user2 = createTestUser("batch_user2");
        User user3 = createTestUser("batch_user3");
        
        List<Long> userIds = Arrays.asList(user1.getId(), user2.getId(), user3.getId());
        
        // 批量获取用户
        logger.info("批量获取用户...");
        Map<Long, User> users = cacheService.batchGetUsers(userIds);
        logger.info("获取到{}个用户", users.size());
        
        users.forEach((id, user) -> {
            logger.info("  用户: {} - {}", id, user.getUsername());
        });
        
        // 预热缓存
        logger.info("预热用户缓存...");
        cacheService.warmupUserCache(userIds);
        logger.info("缓存预热已启动（异步执行）");
    }
    
    /**
     * 演示缓存更新
     */
    private void demonstrateCacheUpdate() {
        logger.info("\n--- 4. 缓存更新演示 ---");
        
        // 创建测试用户
        User user = createTestUser("update_user");
        
        // 查询用户（加入缓存）
        User cachedUser = userService.getUserById(user.getId());
        logger.info("更新前用户名: {}", cachedUser.getUsername());
        
        // 更新用户
        user.setUsername("updated_user_" + System.currentTimeMillis());
        user.setRealName("更新后的用户");
        user.setUpdateTime(LocalDateTime.now());
        userService.updateUser(user);
        logger.info("用户信息已更新");
        
        // 再次查询（应该从缓存获取更新后的数据）
        User updatedUser = userService.getUserById(user.getId());
        logger.info("更新后用户名: {}", updatedUser.getUsername());
        logger.info("更新后真实姓名: {}", updatedUser.getRealName());
    }
    
    /**
     * 演示系统配置缓存
     */
    private void demonstrateSystemConfigCache() {
        logger.info("\n--- 5. 系统配置缓存演示 ---");
        
        // 获取系统配置
        String appName = cacheService.getSystemConfig("app.name");
        logger.info("应用名称: {}", appName);
        
        String appVersion = cacheService.getSystemConfig("app.version");
        logger.info("应用版本: {}", appVersion);
        
        String appDescription = cacheService.getSystemConfig("app.description");
        logger.info("应用描述: {}", appDescription);
        
        // 更新配置
        String newAppName = "MyBatis缓存实战项目 - " + System.currentTimeMillis();
        cacheService.updateSystemConfig("app.name", newAppName);
        logger.info("配置已更新");
        
        // 再次获取配置
        String updatedAppName = cacheService.getSystemConfig("app.name");
        logger.info("更新后应用名称: {}", updatedAppName);
    }
    
    /**
     * 演示缓存统计
     */
    private void demonstrateCacheStatistics() {
        logger.info("\n--- 6. 缓存统计演示 ---");
        
        // 获取用户统计
        UserStatsVO stats = cacheService.getUserStats();
        logger.info("用户统计信息:");
        logger.info("  总用户数: {}", stats.getTotalUsers());
        logger.info("  活跃用户数: {}", stats.getActiveUsers());
        logger.info("  非活跃用户数: {}", stats.getInactiveUsers());
        logger.info("  最后更新时间: {}", stats.getLastUpdateTime());
        
        // 获取缓存统计
        Map<String, Object> cacheStats = cacheService.getCacheStatistics();
        logger.info("缓存统计信息:");
        cacheStats.forEach((cacheName, stats_info) -> {
            logger.info("  缓存: {} - {}", cacheName, stats_info);
        });
        
        // 获取活跃用户列表
        List<User> activeUsers = cacheService.getActiveUsers();
        logger.info("活跃用户列表（前5个）:");
        activeUsers.stream().limit(5).forEach(user -> {
            logger.info("  用户: {} - {}", user.getId(), user.getUsername());
        });
    }
    
    /**
     * 创建测试用户
     */
    private User createTestUser(String username) {
        User user = new User();
        user.setUsername(username);
        user.setPassword("123456");
        user.setEmail(username + "@example.com");
        user.setPhone("138" + String.format("%08d", (int)(Math.random() * 100000000)));
        user.setRealName("演示用户-" + username);
        user.setStatus(1);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        boolean success = userService.saveUser(user);
        if (success) {
            logger.info("创建演示用户: {} (ID: {})", user.getUsername(), user.getId());
        } else {
            logger.error("创建演示用户失败: {}", username);
        }
        
        return user;
    }
}
