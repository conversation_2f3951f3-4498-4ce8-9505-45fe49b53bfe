# Spring Boot + Security + MyBatis RBAC权限管理系统

## 📋 项目简介

这是一个基于Spring Boot + Spring Security + MyBatis实现的RBAC（基于角色的访问控制）权限管理系统。系统采用简洁的设计，易于理解和扩展，适合学习和实际项目使用。

## 🚀 功能特性

### 核心功能
- ✅ **用户管理**: 用户的增删改查、状态管理、密码重置
- ✅ **角色管理**: 角色的增删改查、权限分配
- ✅ **权限管理**: 权限的增删改查、树形结构展示
- ✅ **用户认证**: 基于Spring Security的登录认证
- ✅ **权限控制**: 基于注解的方法级权限控制
- ✅ **密码加密**: BCrypt密码加密存储

### RBAC模型
- **用户（User）**: 系统的使用者
- **角色（Role）**: 权限的集合，用户通过角色获得权限
- **权限（Permission）**: 具体的操作权限，支持菜单、按钮、API三种类型
- **用户-角色关联**: 多对多关系，一个用户可以有多个角色
- **角色-权限关联**: 多对多关系，一个角色可以有多个权限

## 🛠️ 技术栈

- **Spring Boot 2.7.14**: 应用框架
- **Spring Security 5.7.2**: 安全框架
- **MyBatis 3.5.13**: ORM框架
- **MySQL 8.0**: 数据库
- **HikariCP**: 数据库连接池
- **BCrypt**: 密码加密
- **Maven**: 项目管理工具

## 📦 项目结构

```
src/
├── main/
│   ├── java/com/example/
│   │   ├── config/
│   │   │   └── SecurityConfig.java           # Spring Security配置
│   │   ├── controller/
│   │   │   ├── AuthController.java           # 认证控制器
│   │   │   ├── SysUserController.java        # 用户管理控制器
│   │   │   ├── SysRoleController.java        # 角色管理控制器
│   │   │   └── SysPermissionController.java  # 权限管理控制器
│   │   ├── entity/
│   │   │   ├── SysUser.java                  # 用户实体
│   │   │   ├── SysRole.java                  # 角色实体
│   │   │   └── SysPermission.java            # 权限实体
│   │   ├── mapper/
│   │   │   ├── SysUserMapper.java            # 用户Mapper
│   │   │   ├── SysRoleMapper.java            # 角色Mapper
│   │   │   └── SysPermissionMapper.java      # 权限Mapper
│   │   ├── security/
│   │   │   ├── UserPrincipal.java            # 用户主体类
│   │   │   └── CustomUserDetailsService.java # 用户详情服务
│   │   ├── service/
│   │   │   ├── SysUserService.java           # 用户服务接口
│   │   │   ├── SysRoleService.java           # 角色服务接口
│   │   │   ├── SysPermissionService.java     # 权限服务接口
│   │   │   └── impl/                         # 服务实现类
│   │   ├── vo/
│   │   │   ├── ApiResponse.java              # API响应对象
│   │   │   ├── LoginRequest.java             # 登录请求对象
│   │   │   ├── LoginResponse.java            # 登录响应对象
│   │   │   └── PageResult.java               # 分页结果对象
│   │   └── RbacApplication.java              # 启动类
│   └── resources/
│       ├── mapper/                           # MyBatis映射文件
│       │   ├── SysUserMapper.xml
│       │   ├── SysRoleMapper.xml
│       │   └── SysPermissionMapper.xml
│       └── application.yml                   # 应用配置
├── test/
│   └── java/com/example/
│       └── RbacApplicationTest.java          # 测试类
└── sql/
    └── rbac_schema.sql                       # 数据库表结构
```

## 🔧 环境准备

### 1. 数据库准备

```sql
-- 创建数据库
CREATE DATABASE rbac_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行sql/rbac_schema.sql文件创建表结构和初始数据
```

### 2. 配置文件

确保 `application.yml` 中的数据库连接信息正确：

```yaml
spring:
  datasource:
    url: ****************************************************************************************************************
    username: root
    password: 123456
```

## 🚀 运行项目

### 1. 编译项目

```bash
mvn clean compile
```

### 2. 运行应用

```bash
mvn spring-boot:run
```

或者运行主类：
```bash
java -cp target/classes com.example.RbacApplication
```

### 3. 访问应用

- 应用地址: http://localhost:8080
- 健康检查: http://localhost:8080/actuator/health

## 👥 默认用户

系统预置了三个测试用户：

| 用户名 | 密码 | 角色 | 权限 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 所有权限 |
| manager | 123456 | 部门经理 | 用户管理、个人中心 |
| user | 123456 | 普通用户 | 个人中心 |

## 📡 API接口

### 认证接口

```bash
# 用户登录
POST /api/auth/login
Content-Type: application/json
{
  "username": "admin",
  "password": "123456"
}

# 用户登出
POST /api/auth/logout

# 获取当前用户信息
GET /api/auth/current

# 检查登录状态
GET /api/auth/check

# 修改密码
POST /api/auth/change-password
Content-Type: application/json
{
  "oldPassword": "123456",
  "newPassword": "newpassword"
}
```

### 用户管理接口

```bash
# 分页查询用户列表
GET /api/system/users?page=1&size=10&username=admin&status=1

# 查询用户详情
GET /api/system/users/1

# 查询用户详细信息（包含角色权限）
GET /api/system/users/1/details

# 创建用户
POST /api/system/users
Content-Type: application/json
{
  "username": "newuser",
  "password": "123456",
  "email": "<EMAIL>",
  "realName": "新用户",
  "phone": "13800000000"
}

# 更新用户
PUT /api/system/users/1
Content-Type: application/json
{
  "username": "updateduser",
  "email": "<EMAIL>",
  "realName": "更新用户"
}

# 删除用户
DELETE /api/system/users/1

# 启用/禁用用户
PUT /api/system/users/1/status?status=0

# 重置用户密码
PUT /api/system/users/1/password/reset
Content-Type: application/json
{
  "newPassword": "newpassword"
}

# 查询用户角色
GET /api/system/users/1/roles

# 查询用户权限
GET /api/system/users/1/permissions

# 为用户分配角色
PUT /api/system/users/1/roles
Content-Type: application/json
{
  "roleIds": [1, 2]
}

# 检查用户名是否存在
GET /api/system/users/check/username?username=admin

# 检查邮箱是否存在
GET /api/system/users/check/email?email=<EMAIL>
```

## 🔐 权限控制

### 1. 方法级权限控制

使用Spring Security的注解进行权限控制：

```java
@PreAuthorize("hasAuthority('system:user:list')")
public ApiResponse<PageResult<SysUser>> getUserList() {
    // 需要 system:user:list 权限
}

@PreAuthorize("hasRole('ADMIN')")
public ApiResponse<Void> deleteUser() {
    // 需要 ADMIN 角色
}

@PreAuthorize("hasAnyAuthority('system:user:add', 'system:user:edit')")
public ApiResponse<SysUser> saveUser() {
    // 需要任意一个权限
}
```

### 2. URL级权限控制

在SecurityConfig中配置URL权限：

```java
.authorizeRequests()
    .antMatchers("/api/auth/**").permitAll()
    .antMatchers("/api/system/users/**").hasAuthority("system:user:list")
    .antMatchers("/api/system/roles/**").hasAuthority("system:role:list")
    .anyRequest().authenticated()
```

## 🧪 运行测试

```bash
# 运行所有测试
mvn test

# 运行指定测试
mvn test -Dtest=RbacApplicationTest
```

## 📊 数据库设计

### 核心表结构

1. **sys_user**: 用户表
   - 存储用户基本信息：用户名、密码、邮箱等

2. **sys_role**: 角色表
   - 存储角色信息：角色名称、角色编码、描述等

3. **sys_permission**: 权限表
   - 存储权限信息：权限名称、权限编码、资源类型、URL等

4. **sys_user_role**: 用户角色关联表
   - 多对多关系，关联用户和角色

5. **sys_role_permission**: 角色权限关联表
   - 多对多关系，关联角色和权限

### 权限类型

- **MENU**: 菜单权限，用于前端菜单显示控制
- **BUTTON**: 按钮权限，用于前端按钮显示控制
- **API**: 接口权限，用于后端API访问控制

## 🎯 学习要点

1. **RBAC模型理解**: 掌握用户、角色、权限的关系
2. **Spring Security集成**: 理解认证和授权的流程
3. **MyBatis使用**: 掌握复杂查询和关联查询
4. **权限注解**: 学会使用@PreAuthorize等注解
5. **密码安全**: 理解BCrypt密码加密

## 🔧 扩展功能

### 1. JWT令牌认证

可以扩展为JWT令牌认证，实现无状态认证：

```java
// 添加JWT工具类和过滤器
// 修改SecurityConfig配置
// 在登录成功后返回JWT令牌
```

### 2. 数据权限控制

可以扩展数据权限控制，实现用户只能访问自己的数据：

```java
// 添加数据权限注解
// 实现数据权限切面
// 在SQL中添加数据权限过滤条件
```

### 3. 在线用户管理

可以扩展在线用户管理功能：

```java
// 记录用户登录状态
// 实现强制下线功能
// 显示在线用户列表
```

## 🔍 常见问题

### 1. 登录失败

- 检查用户名和密码是否正确
- 检查用户状态是否为启用
- 查看日志确认具体错误信息

### 2. 权限不足

- 检查用户是否有对应的角色
- 检查角色是否有对应的权限
- 确认权限编码是否正确

### 3. 数据库连接失败

- 检查数据库服务是否启动
- 确认连接配置是否正确
- 检查数据库是否存在

## 📚 参考资料

- [Spring Security官方文档](https://docs.spring.io/spring-security/site/docs/current/reference/html5/)
- [MyBatis官方文档](https://mybatis.org/mybatis-3/)
- [Spring Boot官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/)

---

**注意**: 这是一个学习示例项目，生产环境使用时请根据实际需求调整安全配置和功能实现。
