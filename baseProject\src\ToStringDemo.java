/**
 * toString()方法演示程序
 */
public class ToStringDemo {
    public static void main(String[] args) {
        System.out.println("=== toString()方法演示 ===\n");
        
        // 1. 创建一个简单的类来演示toString()
        Person person1 = new Person("李明", 25);
        Person person2 = new Person("王红", 22);
        
        // 2. 演示不同的toString()调用方式
        System.out.println("1. 直接打印对象（自动调用toString()）：");
        System.out.println(person1);
        System.out.println();
        
        System.out.println("2. 显式调用toString()方法：");
        System.out.println(person1.toString());
        System.out.println();
        
        System.out.println("3. 字符串拼接（自动调用toString()）：");
        String info = "员工信息：" + person1;
        System.out.println(info);
        System.out.println();
        
        System.out.println("4. 在数组中使用：");
        Person[] people = {person1, person2};
        for (Person p : people) {
            System.out.println("  " + p);  // 自动调用toString()
        }
        System.out.println();
        
        // 5. 演示没有重写toString()的情况
        System.out.println("5. 没有重写toString()的对象：");
        BadExample bad = new BadExample("测试");
        System.out.println("BadExample对象：" + bad);
        System.out.println();
        
        // 6. 演示不同格式的toString()
        System.out.println("6. 不同格式的toString()：");
        PersonSimple simple = new PersonSimple("张三", 30);
        PersonDetailed detailed = new PersonDetailed("张三", 30, "工程师");
        
        System.out.println("简单格式：" + simple);
        System.out.println("详细格式：" + detailed);
    }
}

/**
 * 重写了toString()方法的Person类
 */
class Person {
    private String name;
    private int age;
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    // 重写toString()方法
    @Override
    public String toString() {
        return "Person{name='" + name + "', age=" + age + "}";
    }
    
    // Getter方法
    public String getName() { return name; }
    public int getAge() { return age; }
}

/**
 * 没有重写toString()方法的类
 */
class BadExample {
    private String data;
    
    public BadExample(String data) {
        this.data = data;
    }
    
    // 没有重写toString()，会使用Object类的默认实现
    // 默认实现返回：类名@哈希码
}

/**
 * 简单格式的toString()
 */
class PersonSimple {
    private String name;
    private int age;
    
    public PersonSimple(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    @Override
    public String toString() {
        return name + "(" + age + "岁)";
    }
}

/**
 * 详细格式的toString()
 */
class PersonDetailed {
    private String name;
    private int age;
    private String job;
    
    public PersonDetailed(String name, int age, String job) {
        this.name = name;
        this.age = age;
        this.job = job;
    }
    
    @Override
    public String toString() {
        return String.format("PersonDetailed{\n  姓名: %s\n  年龄: %d\n  职业: %s\n}", 
                           name, age, job);
    }
}
