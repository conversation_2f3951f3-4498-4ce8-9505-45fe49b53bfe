package Threadtest;

public class TheadStateDemo {
    public static void main(String[] args) throws InterruptedException {
        Thread thread = new Thread(() -> {
            try {
                System.out.println("线程开始执行");
                Thread.sleep(2000); // TIMED_WAITING状态
                System.out.println("线程执行完成");
            } catch (InterruptedException e) {
                System.out.println("线程被中断");
            }
        });

        System.out.println("创建后状态: " + thread.getState()); // NEW

        thread.start();
        System.out.println("启动后状态: " + thread.getState()); // RUNNABLE

        Thread.sleep(500);
        System.out.println("运行中状态: " + thread.getState()); // TIMED_WAITING

        thread.join(); // 等待线程完成
        System.out.println("完成后状态: " + thread.getState()); // TERMINATED
    }
}
