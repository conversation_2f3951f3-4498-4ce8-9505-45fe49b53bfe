package oop;

/**
 * Manageable接口 - 演示接口的使用
 * 定义了可管理对象的基本操作
 */
public interface Manageable {
    
    /**
     * 创建对象
     * @return 是否创建成功
     */
    boolean create();
    
    /**
     * 更新对象信息
     * @return 是否更新成功
     */
    boolean update();
    
    /**
     * 删除对象
     * @return 是否删除成功
     */
    boolean delete();
    
    /**
     * 查找对象
     * @param id 对象ID
     * @return 是否找到
     */
    boolean find(String id);
    
    /**
     * 默认方法 - Java 8新特性
     * 显示管理状态
     */
    default void showManagementStatus() {
        System.out.println("对象管理状态：正常");
    }
    
    /**
     * 静态方法 - Java 8新特性
     * 获取管理系统版本
     */
    static String getManagementSystemVersion() {
        return "Management System v1.0";
    }
}

/**
 * Printable接口 - 演示多接口实现
 */
interface Printable {
    void print();
    
    default void printWithTimestamp() {
        System.out.println("[" + java.time.LocalDateTime.now() + "] ");
        print();
    }
}

/**
 * SchoolManager类 - 实现多个接口
 * 演示接口的实际应用
 */
class SchoolManager implements Manageable, Printable {
    private String managerId;
    private String managerName;
    private boolean isActive;
    
    public SchoolManager(String managerId, String managerName) {
        this.managerId = managerId;
        this.managerName = managerName;
        this.isActive = false;
    }
    
    @Override
    public boolean create() {
        System.out.println("创建管理员：" + managerName);
        isActive = true;
        return true;
    }
    
    @Override
    public boolean update() {
        if (isActive) {
            System.out.println("更新管理员信息：" + managerName);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean delete() {
        if (isActive) {
            System.out.println("删除管理员：" + managerName);
            isActive = false;
            return true;
        }
        return false;
    }
    
    @Override
    public boolean find(String id) {
        boolean found = managerId.equals(id) && isActive;
        System.out.println("查找管理员 " + id + "：" + (found ? "找到" : "未找到"));
        return found;
    }
    
    @Override
    public void print() {
        System.out.println("管理员信息 - ID：" + managerId + "，姓名：" + managerName + 
                         "，状态：" + (isActive ? "活跃" : "非活跃"));
    }
    
    /**
     * 重写默认方法
     */
    @Override
    public void showManagementStatus() {
        System.out.println("学校管理系统状态：" + (isActive ? "运行中" : "已停止"));
    }
    
    // Getter方法
    public String getManagerId() { return managerId; }
    public String getManagerName() { return managerName; }
    public boolean isActive() { return isActive; }
}
