package com.example.util;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 响应工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ResponseUtil {
    
    // 响应状态码常量
    public static final int SUCCESS_CODE = 200;
    public static final int ERROR_CODE = 500;
    public static final int BAD_REQUEST_CODE = 400;
    public static final int UNAUTHORIZED_CODE = 401;
    public static final int FORBIDDEN_CODE = 403;
    public static final int NOT_FOUND_CODE = 404;
    
    // 响应消息常量
    public static final String SUCCESS_MESSAGE = "操作成功";
    public static final String ERROR_MESSAGE = "操作失败";
    public static final String BAD_REQUEST_MESSAGE = "请求参数错误";
    public static final String UNAUTHORIZED_MESSAGE = "未授权访问";
    public static final String FORBIDDEN_MESSAGE = "禁止访问";
    public static final String NOT_FOUND_MESSAGE = "资源不存在";
    
    /**
     * 成功响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> success() {
        return success(SUCCESS_MESSAGE);
    }
    
    /**
     * 成功响应
     * 
     * @param message 响应消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> success(String message) {
        return success(message, null);
    }
    
    /**
     * 成功响应
     * 
     * @param data 响应数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> success(Object data) {
        return success(SUCCESS_MESSAGE, data);
    }
    
    /**
     * 成功响应
     * 
     * @param message 响应消息
     * @param data 响应数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> success(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", SUCCESS_CODE);
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 错误响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> error() {
        return error(ERROR_MESSAGE);
    }
    
    /**
     * 错误响应
     * 
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> error(String message) {
        return error(ERROR_CODE, message);
    }
    
    /**
     * 错误响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> error(int code, String message) {
        return error(code, message, null);
    }
    
    /**
     * 错误响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param data 响应数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> error(int code, String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", code);
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", false);
        
        HttpStatus status = getHttpStatus(code);
        return ResponseEntity.status(status).body(response);
    }
    
    /**
     * 请求参数错误响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> badRequest() {
        return badRequest(BAD_REQUEST_MESSAGE);
    }
    
    /**
     * 请求参数错误响应
     * 
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> badRequest(String message) {
        return error(BAD_REQUEST_CODE, message);
    }
    
    /**
     * 请求参数错误响应
     * 
     * @param message 错误消息
     * @param data 响应数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> badRequest(String message, Object data) {
        return error(BAD_REQUEST_CODE, message, data);
    }
    
    /**
     * 未授权响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> unauthorized() {
        return unauthorized(UNAUTHORIZED_MESSAGE);
    }
    
    /**
     * 未授权响应
     * 
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> unauthorized(String message) {
        return error(UNAUTHORIZED_CODE, message);
    }
    
    /**
     * 禁止访问响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> forbidden() {
        return forbidden(FORBIDDEN_MESSAGE);
    }
    
    /**
     * 禁止访问响应
     * 
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> forbidden(String message) {
        return error(FORBIDDEN_CODE, message);
    }
    
    /**
     * 资源不存在响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> notFound() {
        return notFound(NOT_FOUND_MESSAGE);
    }
    
    /**
     * 资源不存在响应
     * 
     * @param message 错误消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> notFound(String message) {
        return error(NOT_FOUND_CODE, message);
    }
    
    /**
     * 自定义响应
     * 
     * @param code 响应码
     * @param message 响应消息
     * @param data 响应数据
     * @param success 是否成功
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> custom(int code, String message, Object data, boolean success) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", code);
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", success);
        
        HttpStatus status = getHttpStatus(code);
        return ResponseEntity.status(status).body(response);
    }
    
    /**
     * 分页响应
     * 
     * @param data 分页数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> page(Object data) {
        return success("查询成功", data);
    }
    
    /**
     * 分页响应
     * 
     * @param message 响应消息
     * @param data 分页数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> page(String message, Object data) {
        return success(message, data);
    }
    
    /**
     * 创建响应
     * 
     * @param data 创建的数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> created(Object data) {
        return created("创建成功", data);
    }
    
    /**
     * 创建响应
     * 
     * @param message 响应消息
     * @param data 创建的数据
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> created(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", HttpStatus.CREATED.value());
        response.put("message", message);
        response.put("data", data);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    /**
     * 无内容响应
     * 
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> noContent() {
        return noContent("操作成功");
    }
    
    /**
     * 无内容响应
     * 
     * @param message 响应消息
     * @return 响应实体
     */
    public static ResponseEntity<Map<String, Object>> noContent(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", HttpStatus.NO_CONTENT.value());
        response.put("message", message);
        response.put("data", null);
        response.put("timestamp", LocalDateTime.now());
        response.put("success", true);
        
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(response);
    }
    
    /**
     * 根据错误码获取HTTP状态
     * 
     * @param code 错误码
     * @return HTTP状态
     */
    private static HttpStatus getHttpStatus(int code) {
        switch (code) {
            case 200:
                return HttpStatus.OK;
            case 201:
                return HttpStatus.CREATED;
            case 204:
                return HttpStatus.NO_CONTENT;
            case 400:
                return HttpStatus.BAD_REQUEST;
            case 401:
                return HttpStatus.UNAUTHORIZED;
            case 403:
                return HttpStatus.FORBIDDEN;
            case 404:
                return HttpStatus.NOT_FOUND;
            case 500:
                return HttpStatus.INTERNAL_SERVER_ERROR;
            default:
                return HttpStatus.OK;
        }
    }
}
