# JDBC连接数据库完整指南

## 📚 目录
1. [JDBC基础概念](#jdbc基础概念)
2. [环境准备](#环境准备)
3. [基本连接步骤](#基本连接步骤)
4. [CRUD操作示例](#crud操作示例)
5. [连接池配置](#连接池配置)
6. [最佳实践](#最佳实践)
7. [常见问题](#常见问题)

## JDBC基础概念

### 什么是JDBC？
JDBC (Java Database Connectivity) 是Java提供的用于连接和操作数据库的标准API。

### JDBC核心组件
- **DriverManager**: 管理数据库驱动
- **Connection**: 数据库连接对象
- **Statement/PreparedStatement**: SQL语句执行器
- **ResultSet**: 查询结果集
- **SQLException**: 数据库异常

### JDBC工作流程
```
1. 加载数据库驱动
2. 建立数据库连接
3. 创建Statement对象
4. 执行SQL语句
5. 处理结果集
6. 关闭连接资源
```

## 环境准备

### 1. 添加数据库驱动依赖

#### MySQL驱动
```xml
<dependency>
    <groupId>com.mysql</groupId>
    <artifactId>mysql-connector-j</artifactId>
    <version>8.0.33</version>
</dependency>
```

#### PostgreSQL驱动
```xml
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
    <version>42.6.0</version>
</dependency>
```

#### Oracle驱动
```xml
<dependency>
    <groupId>com.oracle.database.jdbc</groupId>
    <artifactId>ojdbc8</artifactId>
    <version>********</version>
</dependency>
```

### 2. 数据库连接信息
```properties
# MySQL
jdbc.url=*******************************************************************
jdbc.username=root
jdbc.password=your_password
jdbc.driver=com.mysql.cj.jdbc.Driver

# PostgreSQL
jdbc.url=****************************************
jdbc.username=postgres
jdbc.password=your_password
jdbc.driver=org.postgresql.Driver

# Oracle
jdbc.url=***********************************
jdbc.username=system
jdbc.password=your_password
jdbc.driver=oracle.jdbc.driver.OracleDriver
```

## 基本连接步骤

### 方式1: 传统方式（不推荐）
```java
// 1. 加载驱动（新版本可省略）
Class.forName("com.mysql.cj.jdbc.Driver");

// 2. 获取连接
Connection conn = DriverManager.getConnection(
    "***********************************", 
    "root", 
    "password"
);
```

### 方式2: 现代方式（推荐）
```java
// 直接获取连接（驱动自动加载）
Connection conn = DriverManager.getConnection(
    "*******************************************************************", 
    "root", 
    "password"
);
```

### 方式3: 使用Properties
```java
Properties props = new Properties();
props.setProperty("user", "root");
props.setProperty("password", "password");
props.setProperty("useSSL", "false");
props.setProperty("serverTimezone", "UTC");

Connection conn = DriverManager.getConnection(
    "***********************************", 
    props
);
```

## 快速开始

### 1. 环境准备
- Java 17+
- MySQL 8.0+ (或PostgreSQL/H2)
- Maven 3.6+

### 2. 数据库设置
```sql
-- 执行初始化脚本
mysql -u root -p < src/main/resources/sql/init-mysql.sql
```

### 3. 配置数据库连接
编辑 `src/main/resources/application.properties`:
```properties
# 修改数据库连接信息
mysql.jdbc.username=your_username
mysql.jdbc.password=your_password
```

### 4. 运行应用
```bash
mvn spring-boot:run
```

## 核心功能演示

### 1. 基础连接
```java
// 获取数据库连接
Connection conn = jdbcUtil.getConnection();

// 测试连接
boolean isConnected = jdbcUtil.testConnection();

// 获取数据库信息
jdbcUtil.printDatabaseInfo();
```

### 2. CRUD操作
```java
// 创建用户
User user = User.builder()
    .username("test_user")
    .email("<EMAIL>")
    .age(25)
    .build();

// 插入
Long id = basicCrudExample.insertUserAndReturnId(user);

// 查询
User foundUser = basicCrudExample.findUserById(id);

// 更新
foundUser.setAge(26);
basicCrudExample.updateUser(foundUser);

// 删除
basicCrudExample.deleteUser(id);
```

### 3. 事务处理
```java
// 批量插入事务
List<User> users = Arrays.asList(user1, user2, user3);
boolean success = transactionExample.batchInsertUsersWithTransaction(users);

// 保存点事务
boolean result = transactionExample.complexTransactionWithSavepoint();
```

### 4. 连接池使用
```java
// HikariCP连接池
@Autowired
@Qualifier("hikariDataSource")
private DataSource hikariDataSource;

// 获取连接
Connection conn = hikariDataSource.getConnection();
```

## 文件结构
```
jdbc-demo/
├── pom.xml                          # Maven配置
├── README.md                        # 说明文档
├── src/main/java/com/example/jdbc/
│   ├── JdbcDemoApplication.java     # 启动类
│   ├── config/
│   │   ├── DatabaseConfig.java     # 数据库配置
│   │   └── ConnectionPoolConfig.java # 连接池配置
│   ├── entity/
│   │   └── User.java               # 用户实体
│   ├── util/
│   │   └── JdbcUtil.java           # JDBC工具类
│   └── examples/
│       ├── BasicCrudExample.java   # CRUD示例
│       └── TransactionExample.java # 事务示例
└── src/main/resources/
    ├── application.properties       # 配置文件
    └── sql/
        └── init-mysql.sql          # MySQL初始化脚本
```

## 学习路径

### 第一阶段：基础连接
1. 理解JDBC核心概念
2. 学习数据库连接方式
3. 掌握资源管理

### 第二阶段：CRUD操作
1. Statement vs PreparedStatement
2. 结果集处理
3. 参数设置

### 第三阶段：高级特性
1. 事务处理
2. 批处理操作
3. 连接池使用

### 第四阶段：性能优化
1. SQL优化
2. 连接池调优
3. 资源管理

## 常见问题

### Q1: 连接失败怎么办？
```java
// 检查连接参数
DatabaseConfig.DatabaseInfo dbInfo = databaseConfig.getCurrentDatabaseInfo();
System.out.println("连接URL: " + dbInfo.getUrl());
System.out.println("用户名: " + dbInfo.getUsername());

// 测试连接
boolean connected = jdbcUtil.testConnection();
```

### Q2: 如何处理中文乱码？
```properties
# 在连接URL中添加字符编码
mysql.jdbc.url=*******************************************************************************
```

### Q3: 如何优化性能？
1. 使用PreparedStatement
2. 合理使用批处理
3. 配置连接池
4. 及时关闭资源

### Q4: 事务回滚失败？
```java
try {
    conn.setAutoCommit(false);
    // 业务操作
    conn.commit();
} catch (SQLException e) {
    if (conn != null) {
        try {
            conn.rollback();
        } catch (SQLException ex) {
            // 记录回滚失败日志
        }
    }
} finally {
    if (conn != null) {
        conn.setAutoCommit(true);
    }
}
```

## 最佳实践

### 1. 资源管理
```java
// 使用try-with-resources
try (Connection conn = jdbcUtil.getConnection();
     PreparedStatement pstmt = conn.prepareStatement(sql);
     ResultSet rs = pstmt.executeQuery()) {

    // 处理结果

} catch (SQLException e) {
    // 异常处理
}
```

### 2. 参数设置
```java
// 使用PreparedStatement防止SQL注入
PreparedStatement pstmt = conn.prepareStatement(
    "SELECT * FROM users WHERE username = ? AND age > ?"
);
pstmt.setString(1, username);
pstmt.setInt(2, minAge);
```

### 3. 批处理
```java
PreparedStatement pstmt = conn.prepareStatement(sql);
for (User user : users) {
    pstmt.setString(1, user.getUsername());
    pstmt.setString(2, user.getEmail());
    pstmt.addBatch();
}
int[] results = pstmt.executeBatch();
```

### 4. 连接池配置
```properties
# HikariCP优化配置
hikari.maximum-pool-size=20
hikari.minimum-idle=5
hikari.connection-timeout=30000
hikari.idle-timeout=600000
hikari.max-lifetime=1800000
```

## 扩展学习

### 相关技术
- **MyBatis**: SQL映射框架
- **Spring Data JPA**: 更高级的ORM
- **Hibernate**: 全功能ORM框架
- **连接池**: HikariCP, DBCP2, C3P0

### 推荐阅读
1. JDBC官方文档
2. 《Java数据库编程》
3. MySQL性能优化指南
4. Spring Boot数据访问

---

**开始您的JDBC学习之旅吧！** 🚀
