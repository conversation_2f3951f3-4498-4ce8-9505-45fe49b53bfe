package com.example.mybatisrbac.mapper;

import com.example.mybatisrbac.dto.UserQueryDTO;
import com.example.mybatisrbac.entity.User;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 用户数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface UserMapper {

    /**
     * 根据条件查询用户总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM sys_user WHERE 1=1 " +
            "<if test='username != null and username != \"\"'>" +
            "AND username LIKE CONCAT('%', #{username}, '%') " +
            "</if>" +
            "<if test='email != null and email != \"\"'>" +
            "AND email LIKE CONCAT('%', #{email}, '%') " +
            "</if>" +
            "<if test='phone != null and phone != \"\"'>" +
            "AND phone LIKE CONCAT('%', #{phone}, '%') " +
            "</if>" +
            "<if test='realName != null and realName != \"\"'>" +
            "AND real_name LIKE CONCAT('%', #{realName}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "</script>")
    Long countByCondition(UserQueryDTO queryDTO);

    /**
     * 根据条件分页查询用户列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_user WHERE 1=1 " +
            "<if test='queryDTO.username != null and queryDTO.username != \"\"'>" +
            "AND username LIKE CONCAT('%', #{queryDTO.username}, '%') " +
            "</if>" +
            "<if test='queryDTO.email != null and queryDTO.email != \"\"'>" +
            "AND email LIKE CONCAT('%', #{queryDTO.email}, '%') " +
            "</if>" +
            "<if test='queryDTO.phone != null and queryDTO.phone != \"\"'>" +
            "AND phone LIKE CONCAT('%', #{queryDTO.phone}, '%') " +
            "</if>" +
            "<if test='queryDTO.realName != null and queryDTO.realName != \"\"'>" +
            "AND real_name LIKE CONCAT('%', #{queryDTO.realName}, '%') " +
            "</if>" +
            "<if test='queryDTO.status != null'>" +
            "AND status = #{queryDTO.status} " +
            "</if>" +
            "ORDER BY ${sortField} ${sortOrder} " +
            "LIMIT #{offset}, #{size}" +
            "</script>")
    List<User> selectByCondition(@Param("queryDTO") UserQueryDTO queryDTO, 
                                @Param("offset") Long offset, 
                                @Param("size") Long size,
                                @Param("sortField") String sortField,
                                @Param("sortOrder") String sortOrder);

    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM sys_user WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "password", column = "password"),
        @Result(property = "email", column = "email"),
        @Result(property = "phone", column = "phone"),
        @Result(property = "realName", column = "real_name"),
        @Result(property = "avatar", column = "avatar"),
        @Result(property = "status", column = "status"),
        @Result(property = "lastLoginTime", column = "last_login_time"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "createBy", column = "create_by"),
        @Result(property = "updateBy", column = "update_by")
    })
    User selectById(Long id);

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username}")
    User selectByUsername(String username);

    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email}")
    User selectByEmail(String email);

    /**
     * 插入用户
     */
    @Insert("INSERT INTO sys_user (username, password, email, phone, real_name, avatar, status, create_time, update_time, create_by, update_by) " +
            "VALUES (#{username}, #{password}, #{email}, #{phone}, #{realName}, #{avatar}, #{status}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);

    /**
     * 更新用户
     */
    @Update("<script>" +
            "UPDATE sys_user SET " +
            "<if test='username != null'>username = #{username},</if>" +
            "<if test='password != null'>password = #{password},</if>" +
            "<if test='email != null'>email = #{email},</if>" +
            "<if test='phone != null'>phone = #{phone},</if>" +
            "<if test='realName != null'>real_name = #{realName},</if>" +
            "<if test='avatar != null'>avatar = #{avatar},</if>" +
            "<if test='status != null'>status = #{status},</if>" +
            "<if test='lastLoginTime != null'>last_login_time = #{lastLoginTime},</if>" +
            "update_time = #{updateTime}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}" +
            "</script>")
    int updateById(User user);

    /**
     * 根据ID删除用户
     */
    @Delete("DELETE FROM sys_user WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 批量删除用户
     */
    @Delete("<script>" +
            "DELETE FROM sys_user WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE username = #{username} AND id != #{excludeId}")
    int checkUsernameExists(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE email = #{email} AND id != #{excludeId}")
    int checkEmailExists(@Param("email") String email, @Param("excludeId") Long excludeId);

    /**
     * 更新最后登录时间
     */
    @Update("UPDATE sys_user SET last_login_time = #{lastLoginTime} WHERE id = #{id}")
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") java.util.Date lastLoginTime);

    /**
     * 查询用户的角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1")
    List<com.example.mybatisrbac.entity.Role> selectUserRoles(Long userId);
}
