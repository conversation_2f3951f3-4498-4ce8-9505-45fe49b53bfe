package com.example.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 用户统计视图对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class UserStatsVO {
    
    private Integer totalUsers;
    private Integer activeUsers;
    private Integer inactiveUsers;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;
    
    // 构造方法
    public UserStatsVO() {}
    
    public UserStatsVO(Integer totalUsers, Integer activeUsers, Integer inactiveUsers) {
        this.totalUsers = totalUsers;
        this.activeUsers = activeUsers;
        this.inactiveUsers = inactiveUsers;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public Integer getTotalUsers() {
        return totalUsers;
    }
    
    public void setTotalUsers(Integer totalUsers) {
        this.totalUsers = totalUsers;
    }
    
    public Integer getActiveUsers() {
        return activeUsers;
    }
    
    public void setActiveUsers(Integer activeUsers) {
        this.activeUsers = activeUsers;
    }
    
    public Integer getInactiveUsers() {
        return inactiveUsers;
    }
    
    public void setInactiveUsers(Integer inactiveUsers) {
        this.inactiveUsers = inactiveUsers;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
    
    @Override
    public String toString() {
        return "UserStatsVO{" +
                "totalUsers=" + totalUsers +
                ", activeUsers=" + activeUsers +
                ", inactiveUsers=" + inactiveUsers +
                ", lastUpdateTime=" + lastUpdateTime +
                '}';
    }
}
