<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.SysUserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.entity.SysUser">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 用户角色权限结果映射 -->
    <resultMap id="UserWithRolesAndPermissionsMap" type="com.example.entity.SysUser" extends="BaseResultMap">
        <collection property="roles" ofType="com.example.entity.SysRole">
            <id column="role_id" property="id" jdbcType="BIGINT"/>
            <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
            <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
            <result column="role_description" property="description" jdbcType="VARCHAR"/>
            <result column="role_status" property="status" jdbcType="TINYINT"/>
        </collection>
        <collection property="permissions" ofType="com.example.entity.SysPermission">
            <id column="permission_id" property="id" jdbcType="BIGINT"/>
            <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
            <result column="permission_code" property="permissionCode" jdbcType="VARCHAR"/>
            <result column="resource_type" property="resourceType" jdbcType="VARCHAR"/>
            <result column="url" property="url" jdbcType="VARCHAR"/>
            <result column="method" property="method" jdbcType="VARCHAR"/>
            <result column="permission_description" property="description" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <!-- 查询用户详细信息（包含角色和权限） -->
    <select id="selectUserWithRolesAndPermissions" parameterType="long" resultMap="UserWithRolesAndPermissionsMap">
        SELECT 
            u.*,
            r.id as role_id,
            r.role_name,
            r.role_code,
            r.description as role_description,
            r.status as role_status,
            p.id as permission_id,
            p.permission_name,
            p.permission_code,
            p.resource_type,
            p.url,
            p.method,
            p.description as permission_description
        FROM sys_user u
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id AND r.status = 1
        LEFT JOIN sys_role_permission rp ON r.id = rp.role_id
        LEFT JOIN sys_permission p ON rp.permission_id = p.id AND p.status = 1
        WHERE u.id = #{userId}
    </select>

    <!-- 分页查询用户 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT * FROM sys_user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据条件统计用户数 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*) FROM sys_user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

</mapper>
