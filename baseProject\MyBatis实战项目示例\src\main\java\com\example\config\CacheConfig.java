package com.example.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis缓存配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableCaching
public class CacheConfig extends CachingConfigurerSupport {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheConfig.class);
    
    @Value("${spring.redis.host:localhost}")
    private String redisHost;
    
    @Value("${spring.redis.port:6379}")
    private int redisPort;
    
    @Value("${spring.redis.password:}")
    private String redisPassword;
    
    @Value("${spring.redis.database:0}")
    private int redisDatabase;
    
    /**
     * Redis连接工厂配置
     */
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisHost);
        config.setPort(redisPort);
        config.setDatabase(redisDatabase);
        
        if (StringUtils.hasText(redisPassword)) {
            config.setPassword(redisPassword);
        }
        
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .poolConfig(jedisPoolConfig())
            .commandTimeout(Duration.ofSeconds(5))
            .build();
        
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config, clientConfig);
        logger.info("Redis连接工厂配置完成: {}:{}", redisHost, redisPort);
        return factory;
    }
    
    /**
     * 连接池配置
     */
    @Bean
    public GenericObjectPoolConfig jedisPoolConfig() {
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxTotal(20);        // 最大连接数
        poolConfig.setMaxIdle(10);         // 最大空闲连接
        poolConfig.setMinIdle(2);          // 最小空闲连接
        poolConfig.setMaxWaitMillis(3000); // 最大等待时间
        poolConfig.setTestOnBorrow(true);  // 借用时测试
        poolConfig.setTestOnReturn(true);  // 归还时测试
        poolConfig.setTestWhileIdle(true); // 空闲时测试
        return poolConfig;
    }
    
    /**
     * RedisTemplate配置
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = createJacksonSerializer();
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        template.setHashKeySerializer(stringRedisSerializer);
        
        // value采用jackson的序列化方式
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        
        template.afterPropertiesSet();
        logger.info("RedisTemplate配置完成");
        return template;
    }
    
    /**
     * 缓存管理器配置
     */
    @Bean
    @Primary
    public CacheManager cacheManager(LettuceConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))  // 默认过期时间30分钟
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(createJacksonSerializer()))
            .disableCachingNullValues()  // 不缓存空值
            .prefixCacheNameWith("cache:");  // 缓存key前缀
        
        // 针对不同缓存的个性化配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 用户缓存 - 1小时过期
        cacheConfigurations.put("users", defaultConfig
            .entryTtl(Duration.ofHours(1))
            .prefixCacheNameWith("user:"));
        
        // 用户详情缓存 - 30分钟过期
        cacheConfigurations.put("userDetails", defaultConfig
            .entryTtl(Duration.ofMinutes(30))
            .prefixCacheNameWith("user:detail:"));
        
        // 用户角色缓存 - 2小时过期
        cacheConfigurations.put("userRoles", defaultConfig
            .entryTtl(Duration.ofHours(2))
            .prefixCacheNameWith("user:roles:"));
        
        // 用户权限缓存 - 2小时过期
        cacheConfigurations.put("userPermissions", defaultConfig
            .entryTtl(Duration.ofHours(2))
            .prefixCacheNameWith("user:permissions:"));
        
        // 用户列表缓存 - 10分钟过期
        cacheConfigurations.put("userList", defaultConfig
            .entryTtl(Duration.ofMinutes(10))
            .prefixCacheNameWith("user:list:"));
        
        // 用户统计缓存 - 5分钟过期
        cacheConfigurations.put("userStats", defaultConfig
            .entryTtl(Duration.ofMinutes(5))
            .prefixCacheNameWith("user:stats:"));
        
        // 系统配置缓存 - 24小时过期
        cacheConfigurations.put("systemConfig", defaultConfig
            .entryTtl(Duration.ofHours(24))
            .prefixCacheNameWith("system:config:"));
        
        RedisCacheManager cacheManager = RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .transactionAware()  // 支持事务
            .build();
        
        logger.info("缓存管理器配置完成，缓存数量: {}", cacheConfigurations.size());
        return cacheManager;
    }
    
    /**
     * 自定义key生成器
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName()).append(".");
            sb.append(method.getName()).append("(");
            for (int i = 0; i < params.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                if (params[i] != null) {
                    sb.append(params[i].toString());
                } else {
                    sb.append("null");
                }
            }
            sb.append(")");
            return sb.toString();
        };
    }
    
    /**
     * 缓存异常处理器
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, org.springframework.cache.Cache cache, Object key) {
                logger.error("缓存获取异常 - cache: {}, key: {}", cache.getName(), key, exception);
            }
            
            @Override
            public void handleCachePutError(RuntimeException exception, org.springframework.cache.Cache cache, Object key, Object value) {
                logger.error("缓存存储异常 - cache: {}, key: {}", cache.getName(), key, exception);
            }
            
            @Override
            public void handleCacheEvictError(RuntimeException exception, org.springframework.cache.Cache cache, Object key) {
                logger.error("缓存清除异常 - cache: {}, key: {}", cache.getName(), key, exception);
            }
            
            @Override
            public void handleCacheClearError(RuntimeException exception, org.springframework.cache.Cache cache) {
                logger.error("缓存清空异常 - cache: {}", cache.getName(), exception);
            }
        };
    }
    
    /**
     * 创建Jackson序列化器
     */
    private Jackson2JsonRedisSerializer<Object> createJacksonSerializer() {
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        return jackson2JsonRedisSerializer;
    }
}
