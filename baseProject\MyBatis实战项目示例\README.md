# MyBatis实战项目示例

这是一个完整的MyBatis框架实战项目，展示了企业级应用中MyBatis的最佳实践和高级特性。

## 🚀 项目特性

### 核心技术栈
- **MyBatis 3.5.13** - 持久层框架
- **Spring Boot 2.7.14** - 应用框架
- **MySQL 8.0** - 数据库
- **Druid 1.2.18** - 连接池
- **Lombok** - 代码简化
- **Validation** - 参数验证
- **JUnit 4** - 单元测试

### 项目亮点
- ✅ 完整的用户权限管理系统
- ✅ 动态SQL的各种使用场景
- ✅ 复杂的关联查询和结果映射
- ✅ 分页查询和统计分析
- ✅ 事务管理和异常处理
- ✅ 参数验证和安全控制
- ✅ 完整的单元测试覆盖
- ✅ 企业级代码规范和最佳实践

## 📁 项目结构

```
MyBatis实战项目示例/
├── pom.xml                                 # Maven配置文件
├── README.md                               # 项目说明文档
├── src/
│   ├── main/
│   │   ├── java/com/example/
│   │   │   ├── entity/                     # 实体类
│   │   │   │   ├── User.java              # 用户实体
│   │   │   │   ├── Role.java              # 角色实体
│   │   │   │   └── Permission.java        # 权限实体
│   │   │   ├── mapper/                     # 数据访问层
│   │   │   │   └── UserMapper.java        # 用户Mapper接口
│   │   │   ├── service/                    # 业务逻辑层
│   │   │   │   ├── UserService.java       # 用户服务接口
│   │   │   │   └── impl/
│   │   │   │       └── UserServiceImpl.java # 用户服务实现
│   │   │   ├── controller/                 # 控制层
│   │   │   │   └── UserController.java    # 用户控制器
│   │   │   ├── dto/                        # 数据传输对象
│   │   │   │   └── UserQueryDTO.java      # 用户查询DTO
│   │   │   ├── vo/                         # 视图对象
│   │   │   │   └── PageResult.java        # 分页结果VO
│   │   │   ├── util/                       # 工具类
│   │   │   │   ├── PasswordUtil.java      # 密码工具类
│   │   │   │   ├── ValidationUtil.java    # 验证工具类
│   │   │   │   └── ResponseUtil.java      # 响应工具类
│   │   │   └── exception/                  # 异常类
│   │   │       └── BusinessException.java # 业务异常
│   │   └── resources/
│   │       ├── mapper/                     # MyBatis映射文件
│   │       │   └── UserMapper.xml         # 用户映射文件
│   │       ├── db/migration/               # 数据库迁移脚本
│   │       │   ├── V1__Create_initial_tables.sql
│   │       │   └── V2__Insert_initial_data.sql
│   │       ├── mybatis-config.xml          # MyBatis配置文件
│   │       ├── database.properties         # 数据库配置
│   │       ├── application.yml             # Spring Boot配置
│   │       └── logback.xml                 # 日志配置
│   └── test/
│       └── java/com/example/
│           └── service/
│               └── UserServiceTest.java    # 用户服务测试
```

## 🛠️ 快速开始

### 1. 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE mybatis_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE mybatis_demo;

-- 执行数据库脚本
-- 运行 src/main/resources/db/migration/ 目录下的SQL脚本
```

### 3. 配置修改
修改 `src/main/resources/database.properties` 中的数据库连接信息：
```properties
database.url=*******************************************************************************************************************
database.username=your_username
database.password=your_password
```

### 4. 运行项目
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动应用
mvn spring-boot:run
```

### 5. 访问应用
- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html
- Druid监控: http://localhost:8080/druid (admin/admin123)

## 📚 核心功能

### 1. 用户管理
- **基本CRUD**: 用户的增删改查操作
- **条件查询**: 支持多条件动态查询
- **分页查询**: 高效的分页实现
- **关联查询**: 用户与角色、权限的关联查询
- **统计分析**: 用户数据的统计和分析

### 2. 动态SQL示例
```xml
<!-- 动态条件查询 -->
<select id="selectByCondition" parameterType="UserQueryDTO" resultMap="UserResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM user
    <where>
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="createTimeStart != null">
            AND create_time >= #{createTimeStart}
        </if>
    </where>
    ORDER BY create_time DESC
</select>
```

### 3. 复杂关联查询
```xml
<!-- 用户角色权限关联查询 -->
<select id="selectUserWithRolesAndPermissions" resultMap="UserWithRolesAndPermissionsResultMap">
    SELECT u.*, r.*, p.*
    FROM user u
    LEFT JOIN user_role ur ON u.id = ur.user_id
    LEFT JOIN role r ON ur.role_id = r.id
    LEFT JOIN role_permission rp ON r.id = rp.role_id
    LEFT JOIN permission p ON rp.permission_id = p.id
    WHERE u.id = #{id} AND u.deleted = 0
</select>
```

### 4. 批量操作
```xml
<!-- 批量插入 -->
<insert id="batchInsert" parameterType="list">
    INSERT INTO user (username, email, password) VALUES
    <foreach collection="users" item="user" separator=",">
        (#{user.username}, #{user.email}, #{user.password})
    </foreach>
</insert>
```

## 🔧 API接口

### 用户管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/users/{id}` | 根据ID查询用户 |
| GET | `/api/users/{id}/roles` | 查询用户角色信息 |
| GET | `/api/users/{id}/permissions` | 查询用户权限信息 |
| GET | `/api/users` | 分页查询用户列表 |
| POST | `/api/users/search` | 条件查询用户列表 |
| POST | `/api/users` | 创建用户 |
| POST | `/api/users/batch` | 批量创建用户 |
| PUT | `/api/users/{id}` | 更新用户信息 |
| PATCH | `/api/users/{id}` | 选择性更新用户 |
| DELETE | `/api/users/{id}` | 删除用户 |
| PUT | `/api/users/{id}/status/{status}` | 更新用户状态 |
| PUT | `/api/users/{id}/password/reset` | 重置用户密码 |

### 统计分析接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/users/statistics` | 用户统计信息 |
| GET | `/api/users/statistics/age` | 年龄分布统计 |
| GET | `/api/users/statistics/gender` | 性别分布统计 |
| GET | `/api/users/statistics/trend` | 注册趋势统计 |

### 验证接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/users/check/username` | 检查用户名是否存在 |
| GET | `/api/users/check/email` | 检查邮箱是否存在 |
| GET | `/api/users/check/phone` | 检查手机号是否存在 |

## 🧪 测试

### 运行单元测试
```bash
mvn test
```

### 测试覆盖率
```bash
mvn jacoco:report
```

### API测试示例
```bash
# 查询用户
curl -X GET "http://localhost:8080/api/users/1"

# 创建用户
curl -X POST "http://localhost:8080/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "nickname": "新用户"
  }'

# 分页查询
curl -X GET "http://localhost:8080/api/users?pageNum=1&pageSize=10&status=1"
```

## 📈 性能优化

### 1. 数据库优化
- 合理的索引设计
- 分页查询优化
- 连接池配置优化

### 2. MyBatis优化
- 二级缓存配置
- 延迟加载设置
- 批量操作使用

### 3. 应用优化
- 参数验证
- 异常处理
- 日志管理

## 🔒 安全特性

- 密码加密存储
- SQL注入防护
- 参数验证
- 敏感信息脱敏
- 操作日志记录

## 📝 开发规范

### 代码规范
- 统一的命名规范
- 完整的注释文档
- 异常处理规范
- 日志记录规范

### 数据库规范
- 表结构设计规范
- 索引设计规范
- SQL编写规范

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 项目地址: https://github.com/example/mybatis-demo

---

**注意**: 这是一个学习和演示项目，生产环境使用前请根据实际需求进行调整和优化。
