package com.example.shiro;

import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import com.example.service.SysUserService;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Shiro自定义Realm
 * 负责用户认证和授权
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class ShiroRealm extends AuthorizingRealm {
    
    private static final Logger logger = LoggerFactory.getLogger(ShiroRealm.class);
    
    @Autowired
    private SysUserService userService;
    
    /**
     * 授权方法
     * 获取用户的角色和权限信息
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        logger.debug("开始执行授权操作");
        
        // 获取当前登录用户
        SysUser user = (SysUser) principals.getPrimaryPrincipal();
        if (user == null) {
            logger.warn("授权失败：用户信息为空");
            return null;
        }
        
        logger.debug("为用户 {} 执行授权", user.getUsername());
        
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        
        try {
            // 查询用户的角色和权限
            SysUser userWithRolesAndPermissions = userService.getUserWithRolesAndPermissions(user.getId());
            
            if (userWithRolesAndPermissions != null) {
                // 设置角色
                Set<String> roles = new HashSet<>();
                List<SysRole> userRoles = userWithRolesAndPermissions.getRoles();
                if (userRoles != null) {
                    for (SysRole role : userRoles) {
                        if (role.isEnabled()) {
                            roles.add(role.getRoleCode());
                        }
                    }
                }
                authorizationInfo.setRoles(roles);
                
                // 设置权限
                Set<String> permissions = new HashSet<>();
                List<SysPermission> userPermissions = userWithRolesAndPermissions.getPermissions();
                if (userPermissions != null) {
                    for (SysPermission permission : userPermissions) {
                        if (permission.isEnabled()) {
                            permissions.add(permission.getPermissionCode());
                        }
                    }
                }
                authorizationInfo.setStringPermissions(permissions);
                
                logger.debug("用户 {} 授权成功，角色数: {}, 权限数: {}", 
                    user.getUsername(), roles.size(), permissions.size());
            }
            
        } catch (Exception e) {
            logger.error("授权过程中发生异常", e);
        }
        
        return authorizationInfo;
    }
    
    /**
     * 认证方法
     * 验证用户登录信息
     *
     * 认证流程：
     * 1. 从Token中获取用户名和密码
     * 2. 根据用户名查询数据库中的用户信息
     * 3. 检查用户状态（是否启用、是否锁定等）
     * 4. 构建AuthenticationInfo对象，包含用户信息、数据库密码、盐值
     * 5. Shiro自动使用CredentialsMatcher验证密码
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        logger.debug("开始执行认证操作");

        // 1. 获取用户名和密码（从前端传来的）
        UsernamePasswordToken upToken = (UsernamePasswordToken) token;
        String username = upToken.getUsername();
        char[] passwordChars = upToken.getPassword();
        String inputPassword = new String(passwordChars);  // 用户输入的密码

        if (username == null || username.trim().isEmpty()) {
            logger.warn("认证失败：用户名为空");
            throw new AccountException("用户名不能为空");
        }

        logger.debug("正在认证用户: {}, 输入密码长度: {}", username, inputPassword.length());

        try {
            // 2. 根据用户名查询数据库中的用户信息
            SysUser user = userService.getUserByUsername(username);

            if (user == null) {
                logger.warn("认证失败：用户不存在 - {}", username);
                throw new UnknownAccountException("用户不存在");
            }

            logger.debug("找到用户: {}, 数据库密码: {}, 盐值: {}",
                user.getUsername(), user.getPassword(), user.getSalt());

            // 3. 检查用户状态
            if (!user.isEnabled()) {
                logger.warn("认证失败：用户已被禁用 - {}", username);
                throw new LockedAccountException("用户已被禁用");
            }

            // 4. 手动验证密码（可选，用于调试）
            String expectedPassword = encryptPassword(inputPassword, user.getCredentialsSalt());
            logger.debug("期望密码: {}, 数据库密码: {}", expectedPassword, user.getPassword());

            // 5. 构建认证信息
            // Shiro会自动使用HashedCredentialsMatcher来验证密码
            // 第一个参数：用户主体信息（认证成功后通过getPrincipal()获取）
            // 第二个参数：数据库中的加密密码
            // 第三个参数：盐值（用于密码验证）
            // 第四个参数：Realm名称
            SimpleAuthenticationInfo authenticationInfo = new SimpleAuthenticationInfo(
                user,                                           // 主体 - 认证成功后subject.getPrincipal()返回这个对象
                user.getPassword(),                             // 数据库中的加密密码
                ByteSource.Util.bytes(user.getCredentialsSalt()), // 盐值
                getName()                                       // Realm名称
            );

            logger.debug("用户 {} 认证信息构建完成，等待密码验证", username);
            return authenticationInfo;

        } catch (AuthenticationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("认证过程中发生异常", e);
            throw new AuthenticationException("认证失败", e);
        }
    }

    /**
     * 手动加密密码（用于调试和验证）
     */
    private String encryptPassword(String password, String salt) {
        // 使用与HashedCredentialsMatcher相同的加密方式
        return DigestUtil.md5Hex(password + salt);
    }
    
    /**
     * 清除指定用户的授权缓存
     */
    public void clearCachedAuthorizationInfo(String username) {
        SimplePrincipalCollection principals = new SimplePrincipalCollection(username, getName());
        clearCachedAuthorizationInfo(principals);
    }
    
    /**
     * 清除指定用户的认证缓存
     */
    public void clearCachedAuthenticationInfo(String username) {
        SimplePrincipalCollection principals = new SimplePrincipalCollection(username, getName());
        clearCachedAuthenticationInfo(principals);
    }
    
    /**
     * 清除指定用户的所有缓存
     */
    public void clearCache(String username) {
        SimplePrincipalCollection principals = new SimplePrincipalCollection(username, getName());
        clearCache(principals);
    }
    
    /**
     * 清除所有用户的授权缓存
     */
    public void clearAllCachedAuthorizationInfo() {
        getAuthorizationCache().clear();
    }
    
    /**
     * 清除所有用户的认证缓存
     */
    public void clearAllCachedAuthenticationInfo() {
        getAuthenticationCache().clear();
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        clearAllCachedAuthenticationInfo();
        clearAllCachedAuthorizationInfo();
    }
}
