package com.example.mybatisdemo.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Permission implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private String permissionName;
    private String permissionCode;
    private String permissionDesc;
    private Integer resourceType;
    private Long parentId;
    private String path;
    private String url;
    private String method;
    private String icon;
    private Integer sortOrder;
    private Integer level;
    private Integer leaf;
    private Integer status;
    private Integer deleted;
    private Integer version;
    private Long createUser;
    private Long updateUser;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
