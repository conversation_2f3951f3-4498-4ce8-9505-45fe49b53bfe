<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mybatisdemo.mapper.RoleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="Role">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
        <result column="role_desc" property="roleDesc" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_user" property="createUser" jdbcType="BIGINT"/>
        <result column="update_user" property="updateUser" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, role_name, role_code, role_desc, sort_order, status,
        create_user, update_user, create_time, update_time
    </sql>

    <!-- 查询条件 - 标准PageHelper实现 -->
    <sql id="Query_Where">
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null and roleCode != ''">
                AND role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="roleDesc != null and roleDesc != ''">
                AND role_desc LIKE CONCAT('%', #{roleDesc}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    role_name LIKE CONCAT('%', #{keyword}, '%') 
                    OR role_code LIKE CONCAT('%', #{keyword}, '%')
                    OR role_desc LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd}
            </if>
            <if test="createUser != null">
                AND create_user = #{createUser}
            </if>
        </where>
    </sql>

    <!-- ==================== 查询方法 ==================== -->

    <!-- 查询所有启用的角色（不分页） -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        WHERE status = 1
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 
        根据条件查询角色列表（PageHelper会自动拦截此方法）
        重要：这个方法不要手动添加LIMIT，PageHelper会自动处理
    -->
    <select id="selectRoleList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        <include refid="Query_Where"/>
        <!-- 注意：不要在这里写ORDER BY，应该通过PageHelper的orderBy参数处理 -->
    </select>

    <!-- 根据ID查询角色 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        WHERE id = #{id}
    </select>

    <!-- 根据角色编码查询角色 -->
    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        WHERE role_code = #{roleCode}
    </select>

    <!-- 根据角色名称查询角色 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        WHERE role_name = #{roleName}
    </select>

    <!-- ==================== CRUD操作 ==================== -->

    <!-- 插入角色 -->
    <insert id="insert" parameterType="Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO role (
            role_name, role_code, role_desc, sort_order, status,
            create_user, update_user, create_time, update_time
        ) VALUES (
            #{roleName}, #{roleCode}, #{roleDesc}, #{sortOrder}, #{status},
            #{createUser}, #{updateUser}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新角色 -->
    <update id="updateById" parameterType="Role">
        UPDATE role
        <set>
            <if test="roleName != null and roleName != ''">
                role_name = #{roleName},
            </if>
            <if test="roleDesc != null">
                role_desc = #{roleDesc},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除角色 -->
    <delete id="deleteById">
        DELETE FROM role WHERE id = #{id}
    </delete>

    <!-- 批量删除角色 -->
    <delete id="deleteBatchByIds">
        DELETE FROM role WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- ==================== 状态管理 ==================== -->

    <!-- 更新角色状态 -->
    <update id="updateStatus">
        UPDATE role 
        SET status = #{status}, 
            update_user = #{updateUser}, 
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新角色状态 -->
    <update id="updateStatusBatch">
        UPDATE role 
        SET status = #{status}, 
            update_user = #{updateUser}, 
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- ==================== 数据验证 ==================== -->

    <!-- 检查角色编码是否存在 -->
    <select id="countByCode" resultType="int">
        SELECT COUNT(*)
        FROM role
        WHERE role_code = #{roleCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="countByName" resultType="int">
        SELECT COUNT(*)
        FROM role
        WHERE role_name = #{roleName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
