package com.example.mybatisrbac.service;

import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.dto.UserQueryDTO;
import com.example.mybatisrbac.entity.Role;
import com.example.mybatisrbac.entity.User;

import java.util.List;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface UserService {

    /**
     * 分页查询用户列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<User> getUserList(UserQueryDTO queryDTO);

    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);

    /**
     * 创建用户
     * 
     * @param user 用户信息
     * @return 创建的用户
     */
    User createUser(User user);

    /**
     * 更新用户
     * 
     * @param id 用户ID
     * @param user 用户信息
     * @return 更新的用户
     */
    User updateUser(Long id, User user);

    /**
     * 删除用户
     * 
     * @param id 用户ID
     */
    void deleteUser(Long id);

    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     */
    void batchDeleteUsers(List<Long> ids);

    /**
     * 重置用户密码
     * 
     * @param id 用户ID
     * @param newPassword 新密码
     */
    void resetPassword(Long id, String newPassword);

    /**
     * 更新用户状态
     * 
     * @param id 用户ID
     * @param status 状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 获取用户的角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getUserRoles(Long userId);

    /**
     * 分配用户角色
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void assignUserRoles(Long userId, List<Long> roleIds);
}
