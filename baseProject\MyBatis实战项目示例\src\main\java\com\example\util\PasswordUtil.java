package com.example.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class PasswordUtil {
    
    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private static final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * 生成盐值
     * 
     * @return 盐值
     */
    public static String generateSalt() {
        byte[] salt = new byte[16];
        secureRandom.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 加密密码
     * 
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public static String encrypt(String password, String salt) {
        if (!StringUtils.hasText(password)) {
            throw new IllegalArgumentException("密码不能为空");
        }
        if (!StringUtils.hasText(salt)) {
            throw new IllegalArgumentException("盐值不能为空");
        }
        
        // 使用BCrypt加密，盐值会自动包含在结果中
        return passwordEncoder.encode(password + salt);
    }
    
    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @param salt 盐值
     * @return 验证结果
     */
    public static boolean verify(String rawPassword, String encodedPassword, String salt) {
        if (!StringUtils.hasText(rawPassword) || !StringUtils.hasText(encodedPassword)) {
            return false;
        }
        if (!StringUtils.hasText(salt)) {
            salt = "";
        }
        
        try {
            return passwordEncoder.matches(rawPassword + salt, encodedPassword);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 生成随机密码
     * 
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 6) {
            length = 6;
        }
        if (length > 32) {
            length = 32;
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            int index = secureRandom.nextInt(chars.length());
            password.append(chars.charAt(index));
        }
        
        return password.toString();
    }
    
    /**
     * 生成随机密码（默认8位）
     * 
     * @return 随机密码
     */
    public static String generateRandomPassword() {
        return generateRandomPassword(8);
    }
    
    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 强度等级：0-弱，1-中，2-强
     */
    public static int checkPasswordStrength(String password) {
        if (!StringUtils.hasText(password)) {
            return 0;
        }
        
        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) {
            score++;
        }
        if (password.length() >= 12) {
            score++;
        }
        
        // 字符类型检查
        boolean hasLower = password.matches(".*[a-z].*");
        boolean hasUpper = password.matches(".*[A-Z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        boolean hasSpecial = password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*");
        
        if (hasLower) score++;
        if (hasUpper) score++;
        if (hasDigit) score++;
        if (hasSpecial) score++;
        
        // 返回强度等级
        if (score <= 2) {
            return 0; // 弱
        } else if (score <= 4) {
            return 1; // 中
        } else {
            return 2; // 强
        }
    }
    
    /**
     * 获取密码强度描述
     * 
     * @param password 密码
     * @return 强度描述
     */
    public static String getPasswordStrengthDesc(String password) {
        int strength = checkPasswordStrength(password);
        switch (strength) {
            case 0:
                return "弱";
            case 1:
                return "中";
            case 2:
                return "强";
            default:
                return "未知";
        }
    }
    
    /**
     * 验证密码格式
     * 
     * @param password 密码
     * @return 验证结果
     */
    public static boolean isValidPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return false;
        }
        
        // 长度检查：6-32位
        if (password.length() < 6 || password.length() > 32) {
            return false;
        }
        
        // 至少包含字母和数字
        boolean hasLetter = password.matches(".*[a-zA-Z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        
        return hasLetter && hasDigit;
    }
}
