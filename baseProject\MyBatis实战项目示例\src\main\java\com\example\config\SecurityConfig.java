package com.example.config;

import com.example.security.CustomUserDetailsService;
import com.example.security.JwtAuthenticationEntryPoint;
import com.example.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * Spring Security安全配置类
 * 配置认证、授权、密码加密等安全相关设置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(
    prePostEnabled = true,  // 启用@PreAuthorize和@PostAuthorize注解
    securedEnabled = true,  // 启用@Secured注解
    jsr250Enabled = true    // 启用@RolesAllowed注解
)
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Autowired
    private CustomUserDetailsService userDetailsService;
    
    /**
     * 密码编码器
     * 使用BCrypt加密算法
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 认证管理器
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
    
    /**
     * 配置认证管理器
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsService)
            .passwordEncoder(passwordEncoder());
    }
    
    /**
     * 配置HTTP安全
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF（因为使用JWT，不需要CSRF保护）
            .csrf().disable()
            
            // 配置会话管理为无状态（使用JWT，不需要Session）
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            
            // 配置请求授权
            .authorizeRequests()
                // 公开端点 - 不需要认证
                .antMatchers("/api/auth/**").permitAll()           // 认证相关接口
                .antMatchers("/api/public/**").permitAll()         // 公开接口
                .antMatchers("/actuator/health").permitAll()       // 健康检查
                .antMatchers("/swagger-ui/**").permitAll()         // Swagger文档
                .antMatchers("/v3/api-docs/**").permitAll()        // API文档
                
                // 系统管理 - 需要管理员权限
                .antMatchers("/api/system/users/**").hasAuthority("system:user:list")
                .antMatchers("/api/system/roles/**").hasAuthority("system:role:list")
                .antMatchers("/api/system/permissions/**").hasAuthority("system:permission:list")
                
                // 个人中心 - 需要登录
                .antMatchers("/api/profile/**").hasAnyRole("USER", "MANAGER", "ADMIN")
                
                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            .and()
            
            // 配置异常处理
            .exceptionHandling()
                .authenticationEntryPoint((request, response, authException) -> {
                    // 未认证时返回401
                    response.setStatus(401);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"code\":401,\"message\":\"未认证，请先登录\"}");
                })
                .accessDeniedHandler((request, response, accessDeniedException) -> {
                    // 权限不足时返回403
                    response.setStatus(403);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"code\":403,\"message\":\"权限不足，拒绝访问\"}");
                })
            .and()
            
            // 配置登录
            .formLogin()
                .loginPage("/login")                    // 登录页面
                .loginProcessingUrl("/api/auth/login")  // 登录处理URL
                .usernameParameter("username")          // 用户名参数名
                .passwordParameter("password")          // 密码参数名
                .successHandler((request, response, authentication) -> {
                    // 登录成功处理
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"code\":200,\"message\":\"登录成功\"}");
                })
                .failureHandler((request, response, exception) -> {
                    // 登录失败处理
                    response.setStatus(401);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"code\":401,\"message\":\"登录失败：" + exception.getMessage() + "\"}");
                })
                .permitAll()
            .and()
            
            // 配置登出
            .logout()
                .logoutUrl("/api/auth/logout")          // 登出URL
                .logoutSuccessHandler((request, response, authentication) -> {
                    // 登出成功处理
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"code\":200,\"message\":\"登出成功\"}");
                })
                .permitAll();
    }
    
    /**
     * 配置Web安全忽略的路径
     */
    @Override
    public void configure(org.springframework.security.config.annotation.web.builders.WebSecurity web) throws Exception {
        web.ignoring()
            .antMatchers("/css/**", "/js/**", "/images/**", "/favicon.ico")  // 静态资源
            .antMatchers("/error")                                           // 错误页面
            .antMatchers("/actuator/**");                                    // 监控端点
    }
}
