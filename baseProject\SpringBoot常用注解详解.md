# Spring Boot 常用注解详解

## 📚 目录

1. [核心注解](#核心注解)
2. [依赖注入注解](#依赖注入注解)
3. [Web相关注解](#web相关注解)
4. [数据访问注解](#数据访问注解)
5. [配置相关注解](#配置相关注解)
6. [验证注解](#验证注解)
7. [缓存注解](#缓存注解)
8. [事务注解](#事务注解)
9. [测试注解](#测试注解)
10. [条件注解](#条件注解)

---

## 核心注解

### @SpringBootApplication

Spring Boot 应用的核心注解，是以下三个注解的组合：

```java
@SpringBootApplication
// 等价于：
// @SpringBootConfiguration
// @EnableAutoConfiguration
// @ComponentScan
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

**属性说明：**
- `exclude`: 排除特定的自动配置类
- `excludeName`: 通过类名排除自动配置类
- `scanBasePackages`: 指定组件扫描的基础包
- `scanBasePackageClasses`: 指定组件扫描的基础类

```java
@SpringBootApplication(
    exclude = {DataSourceAutoConfiguration.class},
    scanBasePackages = {"com.example.service", "com.example.controller"}
)
public class Application {
    // ...
}
```

### @Configuration

标识一个类为配置类，相当于XML配置文件：

```java
@Configuration
public class AppConfig {
    
    @Bean
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl("********************************");
        dataSource.setUsername("root");
        dataSource.setPassword("123456");
        return dataSource;
    }
    
    @Bean
    @Primary // 当有多个相同类型的Bean时，优先使用此Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 配置序列化器等
        return template;
    }
}
```

### @Component 系列

#### @Component
通用的组件注解，标识一个类为Spring管理的组件：

```java
@Component
public class EmailService {
    
    public void sendEmail(String to, String subject, String content) {
        // 发送邮件逻辑
    }
}
```

#### @Service
标识业务逻辑层组件：

```java
@Service
@Transactional(readOnly = true)
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    public User findById(Long id) {
        return userRepository.findById(id).orElse(null);
    }
    
    @Transactional
    public User save(User user) {
        return userRepository.save(user);
    }
}
```

#### @Repository
标识数据访问层组件：

```java
@Repository
public class UserRepositoryImpl implements UserRepository {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<User> findByCustomCondition(String condition) {
        String jpql = "SELECT u FROM User u WHERE " + condition;
        return entityManager.createQuery(jpql, User.class).getResultList();
    }
}
```

#### @Controller
标识控制层组件：

```java
@Controller
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/users/{id}")
    public String getUser(@PathVariable Long id, Model model) {
        User user = userService.findById(id);
        model.addAttribute("user", user);
        return "user/detail"; // 返回视图名
    }
}
```

#### @RestController
RESTful控制器，等价于 @Controller + @ResponseBody：

```java
@RestController
@RequestMapping("/api/users")
public class UserRestController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        User user = userService.findById(id);
        return ResponseEntity.ok(user);
    }
    
    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody @Valid User user) {
        User savedUser = userService.save(user);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
    }
}
```

---

## 依赖注入注解

### @Autowired

自动装配依赖，可以用在字段、构造器、方法上：

```java
@Service
public class UserService {
    
    // 字段注入（不推荐）
    @Autowired
    private UserRepository userRepository;
    
    // 构造器注入（推荐）
    private final EmailService emailService;
    private final NotificationService notificationService;
    
    @Autowired
    public UserService(EmailService emailService, NotificationService notificationService) {
        this.emailService = emailService;
        this.notificationService = notificationService;
    }
    
    // Setter注入
    private AuditService auditService;
    
    @Autowired
    public void setAuditService(AuditService auditService) {
        this.auditService = auditService;
    }
    
    // 可选依赖
    @Autowired(required = false)
    private OptionalService optionalService;
}
```

### @Qualifier

当有多个相同类型的Bean时，指定注入哪一个：

```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Qualifier("primaryDataSource")
    public DataSource primaryDataSource() {
        return new HikariDataSource();
    }
    
    @Bean
    @Qualifier("secondaryDataSource")
    public DataSource secondaryDataSource() {
        return new HikariDataSource();
    }
}

@Service
public class UserService {
    
    @Autowired
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;
    
    @Autowired
    @Qualifier("secondaryDataSource")
    private DataSource secondaryDataSource;
}
```

### @Primary

当有多个相同类型的Bean时，标记为主要的Bean：

```java
@Configuration
public class CacheConfig {
    
    @Bean
    @Primary
    public CacheManager redisCacheManager() {
        return new RedisCacheManager.Builder(redisConnectionFactory).build();
    }
    
    @Bean
    public CacheManager caffeineCacheManager() {
        return new CaffeineCacheManager();
    }
}
```

### @Resource

JSR-250标准的依赖注入注解：

```java
@Service
public class UserService {
    
    @Resource // 按名称注入
    private UserRepository userRepository;
    
    @Resource(name = "emailService")
    private EmailService emailService;
}
```

### @Value

注入配置属性值：

```java
@Component
public class AppProperties {
    
    @Value("${app.name}")
    private String appName;
    
    @Value("${app.version:1.0.0}") // 默认值
    private String appVersion;
    
    @Value("${app.debug:false}")
    private boolean debug;
    
    @Value("#{systemProperties['java.home']}")
    private String javaHome;
    
    @Value("#{T(java.lang.Math).random() * 100}")
    private double randomNumber;
    
    @Value("#{'${app.servers}'.split(',')}")
    private List<String> servers;
}
```

---

## Web相关注解

### @RequestMapping 系列

#### @RequestMapping
通用的请求映射注解：

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public User getUser(@PathVariable Long id) {
        return userService.findById(id);
    }
    
    @RequestMapping(
        value = "/search",
        method = RequestMethod.GET,
        params = "keyword",
        headers = "Accept=application/json",
        consumes = "application/json",
        produces = "application/json"
    )
    public List<User> searchUsers(@RequestParam String keyword) {
        return userService.searchByKeyword(keyword);
    }
}
```

#### HTTP方法特定注解

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping // 等价于 @RequestMapping(method = RequestMethod.GET)
    public List<User> getAllUsers() {
        return userService.findAll();
    }
    
    @GetMapping("/{id}")
    public User getUser(@PathVariable Long id) {
        return userService.findById(id);
    }
    
    @PostMapping
    public User createUser(@RequestBody @Valid User user) {
        return userService.save(user);
    }
    
    @PutMapping("/{id}")
    public User updateUser(@PathVariable Long id, @RequestBody @Valid User user) {
        return userService.update(id, user);
    }
    
    @PatchMapping("/{id}")
    public User partialUpdateUser(@PathVariable Long id, @RequestBody Map<String, Object> updates) {
        return userService.partialUpdate(id, updates);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.delete(id);
        return ResponseEntity.noContent().build();
    }
}
```

### 参数绑定注解

#### @PathVariable
绑定URL路径变量：

```java
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public User getUser(@PathVariable Long id) {
        return userService.findById(id);
    }
    
    @GetMapping("/users/{userId}/posts/{postId}")
    public Post getUserPost(@PathVariable Long userId, @PathVariable Long postId) {
        return postService.findByUserAndId(userId, postId);
    }
    
    @GetMapping("/users/{id}")
    public User getUser(@PathVariable("id") Long userId) { // 指定变量名
        return userService.findById(userId);
    }
}
```

#### @RequestParam
绑定请求参数：

```java
@RestController
public class UserController {
    
    @GetMapping("/users")
    public List<User> getUsers(
            @RequestParam(required = false) String name,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(value = "sort", defaultValue = "id") String sortBy) {
        return userService.findUsers(name, page, size, sortBy);
    }
    
    @GetMapping("/users/search")
    public List<User> searchUsers(@RequestParam Map<String, String> params) {
        return userService.searchByParams(params);
    }
}
```

#### @RequestBody
绑定请求体：

```java
@RestController
public class UserController {
    
    @PostMapping("/users")
    public User createUser(@RequestBody @Valid User user) {
        return userService.save(user);
    }
    
    @PostMapping("/users/batch")
    public List<User> createUsers(@RequestBody @Valid List<User> users) {
        return userService.saveAll(users);
    }
}
```

#### @RequestHeader
绑定请求头：

```java
@RestController
public class UserController {
    
    @GetMapping("/users/info")
    public Map<String, String> getUserInfo(
            @RequestHeader("User-Agent") String userAgent,
            @RequestHeader(value = "Accept-Language", defaultValue = "en") String language,
            @RequestHeader Map<String, String> headers) {
        
        Map<String, String> info = new HashMap<>();
        info.put("userAgent", userAgent);
        info.put("language", language);
        info.put("allHeaders", headers.toString());
        return info;
    }
}
```

#### @CookieValue
绑定Cookie值：

```java
@RestController
public class UserController {
    
    @GetMapping("/users/session")
    public String getSessionInfo(@CookieValue("JSESSIONID") String sessionId) {
        return "Session ID: " + sessionId;
    }
    
    @GetMapping("/users/preferences")
    public String getUserPreferences(
            @CookieValue(value = "theme", defaultValue = "light") String theme) {
        return "User theme: " + theme;
    }
}
```
