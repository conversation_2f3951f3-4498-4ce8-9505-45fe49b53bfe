package reflection;

import java.lang.annotation.*;

/**
 * 学生类 - 用于反射演示
 */
public class Student {
    private static int count = 0;
    
    private String name;
    private int age;
    private String email;
    
    // 默认构造函数
    public Student() {
        this.name = "Unknown";
        this.age = 0;
        this.email = "<EMAIL>";
        count++;
    }
    
    // 参数构造函数
    public Student(String name, int age, String email) {
        this.name = name;
        this.age = age;
        this.email = email;
        count++;
    }
    
    // Getter和Setter方法
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public static int getCount() {
        return count;
    }
    
    // 业务方法
    public void study(String subject) {
        System.out.println(name + " 正在学习 " + subject);
    }
    
    public double calculateGPA(double... scores) {
        if (scores.length == 0) return 0.0;
        
        double sum = 0;
        for (double score : scores) {
            sum += score;
        }
        return sum / scores.length;
    }
    
    private void privateMethod() {
        System.out.println("这是一个私有方法");
    }
    
    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", email='" + email + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Student)) return false;
        Student student = (Student) o;
        return age == student.age &&
                name.equals(student.name) &&
                email.equals(student.email);
    }
    
    @Override
    public int hashCode() {
        return name.hashCode() + age + email.hashCode();
    }
}

/**
 * 带注解的学生类
 */
@Entity(name = "Student", tableName = "students")
class AnnotatedStudent {
    
    @Column(name = "id", nullable = false)
    private Long id;
    
    @Required(message = "姓名不能为空")
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "age")
    private Integer age;
    
    @Required(message = "邮箱不能为空")
    @Column(name = "email", nullable = false)
    private String email;
    
    @Column(name = "phone")
    private String phone;
    
    // 构造函数
    public AnnotatedStudent() {}
    
    public AnnotatedStudent(String name, Integer age, String email) {
        this.name = name;
        this.age = age;
        this.email = email;
    }
    
    // 带注解的方法
    @Transactional(readOnly = false, timeout = 30)
    public void save() {
        System.out.println("保存学生信息: " + name);
    }
    
    @Transactional(readOnly = true)
    public AnnotatedStudent findById(Long id) {
        System.out.println("查找学生: " + id);
        return this;
    }
    
    @Deprecated
    public void oldMethod() {
        System.out.println("这是一个过时的方法");
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    @Override
    public String toString() {
        return "AnnotatedStudent{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", age=" + age +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                '}';
    }
}

/**
 * 计算器类 - 用于方法反射演示
 */
class Calculator {
    
    // public方法
    public int add(int a, int b) {
        return a + b;
    }
    
    public double add(double a, double b) {
        return a + b;
    }
    
    // private方法
    private int subtract(int a, int b) {
        return a - b;
    }
    
    // protected方法
    protected int divide(int a, int b) {
        if (b == 0) {
            throw new IllegalArgumentException("除数不能为0");
        }
        return a / b;
    }
    
    // static方法
    public static int multiply(int a, int b) {
        return a * b;
    }
    
    // 带异常的方法
    public double sqrt(double number) throws IllegalArgumentException {
        if (number < 0) {
            throw new IllegalArgumentException("不能计算负数的平方根");
        }
        return Math.sqrt(number);
    }
    
    // 可变参数方法
    public int sum(int... numbers) {
        int total = 0;
        for (int num : numbers) {
            total += num;
        }
        return total;
    }
    
    // 泛型方法
    public <T extends Number> double average(T... numbers) {
        if (numbers.length == 0) return 0.0;
        
        double sum = 0;
        for (T number : numbers) {
            sum += number.doubleValue();
        }
        return sum / numbers.length;
    }
}

// 自定义注解定义
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@interface Entity {
    String name() default "";
    String tableName() default "";
}

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@interface Column {
    String name() default "";
    boolean nullable() default true;
}

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@interface Required {
    String message() default "字段不能为空";
}

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@interface Transactional {
    boolean readOnly() default false;
    int timeout() default 0;
}
