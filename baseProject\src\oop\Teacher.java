package oop;

import java.util.ArrayList;
import java.util.List;

/**
 * Teacher类 - 继承Person类
 * 演示继承和方法重写
 */
public class Teacher extends Person {
    private String department;              // 院系
    private String title;                   // 职称
    private List<Course> teachingCourses;   // 授课列表
    private double salary;                  // 薪资
    
    /**
     * 构造方法
     * @param name 姓名
     * @param age 年龄
     * @param teacherId 工号
     * @param department 院系
     * @param title 职称
     */
    public Teacher(String name, int age, String teacherId, String department, String title) {
        super(name, age, teacherId);
        this.department = department;
        this.title = title;
        this.teachingCourses = new ArrayList<>();
        this.salary = calculateBaseSalary(title);
    }
    
    /**
     * 实现父类的抽象方法
     */
    @Override
    public void displayInfo() {
        System.out.println("=== 教师信息 ===");
        System.out.println("姓名：" + name);
        System.out.println("年龄：" + age);
        System.out.println("工号：" + id);
        System.out.println("院系：" + department);
        System.out.println("职称：" + title);
        System.out.println("薪资：￥" + String.format("%.2f", salary));
        System.out.println("授课数量：" + teachingCourses.size());
        
        if (!teachingCourses.isEmpty()) {
            System.out.println("授课列表：");
            for (Course course : teachingCourses) {
                System.out.println("  - " + course.getCourseName() + 
                                 " (学生数：" + course.getEnrolledStudents().size() + ")");
            }
        }
    }
    
    /**
     * 分配课程
     * @param course 要分配的课程
     */
    public void assignCourse(Course course) {
        if (course == null) {
            System.out.println("课程不能为空");
            return;
        }
        
        if (!teachingCourses.contains(course)) {
            teachingCourses.add(course);
            course.setTeacher(this);
            System.out.println(name + "被分配教授课程：" + course.getCourseName());
            updateSalary();
        } else {
            System.out.println(name + "已经在教授课程：" + course.getCourseName());
        }
    }
    
    /**
     * 移除课程
     * @param course 要移除的课程
     */
    public void removeCourse(Course course) {
        if (teachingCourses.remove(course)) {
            course.setTeacher(null);
            System.out.println(name + "不再教授课程：" + course.getCourseName());
            updateSalary();
        } else {
            System.out.println("未找到该课程：" + course.getCourseName());
        }
    }
    
    /**
     * 教学方法 - 教师特有的行为
     */
    public void teach() {
        if (teachingCourses.isEmpty()) {
            System.out.println(name + "目前没有分配课程");
        } else {
            System.out.println(name + "正在教授" + teachingCourses.size() + "门课程");
        }
    }
    
    /**
     * 给学生评分
     * @param student 学生
     * @param courseName 课程名称
     * @param score 分数
     */
    public void gradeStudent(Student student, String courseName, double score) {
        System.out.println("教师" + name + "给学生" + student.getName() + 
                         "的" + courseName + "课程评分：" + score);
        student.takeExam(courseName, score);
    }
    
    /**
     * 根据职称计算基础薪资
     * @param title 职称
     * @return 基础薪资
     */
    private double calculateBaseSalary(String title) {
        switch (title.toLowerCase()) {
            case "教授":
                return 15000.0;
            case "副教授":
                return 12000.0;
            case "讲师":
                return 8000.0;
            case "助教":
                return 5000.0;
            default:
                return 6000.0;
        }
    }
    
    /**
     * 更新薪资（基础薪资 + 课程津贴）
     */
    private void updateSalary() {
        double baseSalary = calculateBaseSalary(title);
        double courseAllowance = teachingCourses.size() * 1000.0;  // 每门课1000元津贴
        this.salary = baseSalary + courseAllowance;
    }
    
    /**
     * 重写greet方法，展示多态
     */
    @Override
    public void greet() {
        System.out.println("你好，我是" + name + "，我是" + department + "的" + title);
    }
    
    // Getter和Setter方法
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
        updateSalary();  // 职称变化时更新薪资
    }
    
    public List<Course> getTeachingCourses() {
        return new ArrayList<>(teachingCourses);  // 返回副本
    }
    
    public double getSalary() {
        return salary;
    }
    
    /**
     * 重写toString方法
     */
    @Override
    public String toString() {
        return "Teacher{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", teacherId='" + id + '\'' +
                ", department='" + department + '\'' +
                ", title='" + title + '\'' +
                ", salary=" + String.format("%.2f", salary) +
                ", coursesCount=" + teachingCourses.size() +
                '}';
    }
}
