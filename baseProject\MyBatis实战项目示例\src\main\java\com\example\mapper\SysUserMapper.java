package com.example.mapper;

import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 系统用户Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SysUserMapper {
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM sys_user WHERE id = #{id}")
    SysUser selectById(Long id);
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username}")
    SysUser selectByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email}")
    SysUser selectByEmail(String email);
    
    /**
     * 查询所有用户
     */
    @Select("SELECT * FROM sys_user ORDER BY create_time DESC")
    List<SysUser> selectAll();
    
    /**
     * 根据状态查询用户
     */
    @Select("SELECT * FROM sys_user WHERE status = #{status} ORDER BY create_time DESC")
    List<SysUser> selectByStatus(Integer status);
    
    /**
     * 插入用户
     */
    @Insert("INSERT INTO sys_user(username, password, email, phone, real_name, avatar, status, create_time, update_time, create_by) " +
            "VALUES(#{username}, #{password}, #{email}, #{phone}, #{realName}, #{avatar}, #{status}, #{createTime}, #{updateTime}, #{createBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SysUser user);
    
    /**
     * 更新用户
     */
    @Update("UPDATE sys_user SET username = #{username}, email = #{email}, phone = #{phone}, " +
            "real_name = #{realName}, avatar = #{avatar}, status = #{status}, update_time = #{updateTime}, update_by = #{updateBy} " +
            "WHERE id = #{id}")
    int update(SysUser user);
    
    /**
     * 更新用户密码
     */
    @Update("UPDATE sys_user SET password = #{password}, update_time = #{updateTime}, update_by = #{updateBy} WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, @Param("password") String password, 
                      @Param("updateTime") java.time.LocalDateTime updateTime, @Param("updateBy") Long updateBy);
    
    /**
     * 更新用户最后登录时间
     */
    @Update("UPDATE sys_user SET last_login_time = #{lastLoginTime} WHERE id = #{id}")
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") java.time.LocalDateTime lastLoginTime);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM sys_user WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE username = #{username}")
    int countByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE email = #{email}")
    int countByEmail(String email);
    
    /**
     * 检查用户名是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE username = #{username} AND id != #{id}")
    int countByUsernameExcludeId(@Param("username") String username, @Param("id") Long id);
    
    /**
     * 检查邮箱是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE email = #{email} AND id != #{id}")
    int countByEmailExcludeId(@Param("email") String email, @Param("id") Long id);
    
    /**
     * 查询用户的角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1")
    List<SysRole> selectUserRoles(Long userId);
    
    /**
     * 查询用户的权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.status = 1 " +
            "ORDER BY p.sort_order")
    List<SysPermission> selectUserPermissions(Long userId);
    
    /**
     * 查询用户详细信息（包含角色和权限）
     */
    SysUser selectUserWithRolesAndPermissions(Long userId);
    
    /**
     * 为用户分配角色
     */
    @Insert("INSERT INTO sys_user_role(user_id, role_id, create_time, create_by) " +
            "VALUES(#{userId}, #{roleId}, #{createTime}, #{createBy})")
    int insertUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId, 
                      @Param("createTime") java.time.LocalDateTime createTime, @Param("createBy") Long createBy);
    
    /**
     * 删除用户的所有角色
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId}")
    int deleteUserRoles(Long userId);
    
    /**
     * 删除用户的指定角色
     */
    @Delete("DELETE FROM sys_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 检查用户是否有指定角色
     */
    @Select("SELECT COUNT(*) FROM sys_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int countUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 分页查询用户
     */
    List<SysUser> selectByPage(@Param("offset") int offset, @Param("limit") int limit, 
                              @Param("username") String username, @Param("email") String email, 
                              @Param("status") Integer status);
    
    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM sys_user")
    int countTotal();
    
    /**
     * 根据条件统计用户数
     */
    int countByCondition(@Param("username") String username, @Param("email") String email, @Param("status") Integer status);
}
