<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="examples.UserMapper">
    
    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="examples.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="email" column="email"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>
    
    <!-- 查询所有用户 -->
    <select id="findAllUsers" resultMap="UserResultMap">
        SELECT id, username, email, created_at 
        FROM users 
        ORDER BY created_at DESC
    </select>
    
    <!-- 根据ID查询用户 -->
    <select id="findUserById" parameterType="long" resultMap="UserResultMap">
        SELECT id, username, email, created_at 
        FROM users 
        WHERE id = #{id}
    </select>
    
    <!-- 插入用户 -->
    <insert id="insertUser" parameterType="examples.User" 
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, email, created_at)
        VALUES (#{username}, #{email}, #{createdAt})
    </insert>
    
    <!-- 动态查询 - MyBatis的强大功能 -->
    <select id="findUsersWithConditions" resultMap="UserResultMap">
        SELECT * FROM users
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
        </where>
        ORDER BY created_at DESC
    </select>
    
    <!-- 批量插入 -->
    <insert id="batchInsertUsers" parameterType="list">
        INSERT INTO users (username, email, created_at)
        VALUES
        <foreach collection="list" item="user" separator=",">
            (#{user.username}, #{user.email}, #{user.createdAt})
        </foreach>
    </insert>
    
    <!-- 复杂查询示例 -->
    <select id="findUsersWithPagination" resultMap="UserResultMap">
        SELECT u.*, 
               COUNT(*) OVER() as total_count
        FROM users u
        <where>
            <if test="keyword != null and keyword != ''">
                AND (u.username LIKE CONCAT('%', #{keyword}, '%') 
                     OR u.email LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="startDate != null">
                AND u.created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND u.created_at <= #{endDate}
            </if>
        </where>
        ORDER BY u.created_at DESC
        LIMIT #{offset}, #{pageSize}
    </select>
    
    <!-- 统计查询 -->
    <select id="getUserStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalUsers,
            COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as todayUsers,
            COUNT(CASE WHEN created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as weekUsers
        FROM users
    </select>
    
</mapper>
