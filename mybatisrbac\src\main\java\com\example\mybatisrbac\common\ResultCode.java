package com.example.mybatisrbac.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_FAILED(422, "参数验证失败"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 业务错误 1xxx
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    USER_DISABLED(1003, "用户已被禁用"),
    INVALID_PASSWORD(1004, "密码错误"),
    PASSWORD_EXPIRED(1005, "密码已过期"),

    ROLE_NOT_FOUND(1101, "角色不存在"),
    ROLE_ALREADY_EXISTS(1102, "角色已存在"),
    ROLE_IN_USE(1103, "角色正在使用中，无法删除"),

    PERMISSION_NOT_FOUND(1201, "权限不存在"),
    PERMISSION_DENIED(1202, "权限不足"),

    // 数据库错误 2xxx
    DATABASE_ERROR(2001, "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION(2002, "数据完整性约束违反"),
    DUPLICATE_KEY(2003, "数据重复"),

    // 外部服务错误 3xxx
    EXTERNAL_SERVICE_ERROR(3001, "外部服务调用失败"),
    NETWORK_TIMEOUT(3002, "网络超时"),

    // 文件操作错误 4xxx
    FILE_NOT_FOUND(4001, "文件不存在"),
    FILE_UPLOAD_FAILED(4002, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(4003, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(4004, "文件大小超出限制");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}
