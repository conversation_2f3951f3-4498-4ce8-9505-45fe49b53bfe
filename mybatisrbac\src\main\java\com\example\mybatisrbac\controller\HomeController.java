package com.example.mybatisrbac.controller;

import com.example.mybatisrbac.common.Result;
import com.example.mybatisrbac.util.ResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 主页控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Tag(name = "主页管理", description = "系统主页和基础信息接口")
@RestController
public class HomeController {

    /**
     * 应用程序主页
     *
     * @return 欢迎信息
     */
    @Operation(summary = "系统主页", description = "获取系统欢迎信息和可用接口列表")
    @GetMapping("/")
    public Result<String> home() {
        String welcomeMessage = "欢迎使用 MyBatis RBAC 系统！\n\n" +
               "可用的接口：\n" +
               "- GET / - 主页\n" +
               "- GET /test - 测试控制器\n" +
               "- GET /test/health - 健康检查\n" +
               "- GET /test/db - 数据库连接测试\n" +
               "- GET /test/datasource - 数据源信息\n" +
               "- GET /druid - Druid 监控页面（用户名：admin，密码：123456）\n\n" +
               "API 文档：\n" +
               "- GET /swagger-ui.html - Swagger UI 界面\n" +
               "- GET /swagger - Swagger UI 快捷访问\n" +
               "- GET /v3/api-docs - OpenAPI JSON 规范\n" +
               "- GET /v3/api-docs.yaml - OpenAPI YAML 规范";

        return ResponseUtil.success("系统信息获取成功", welcomeMessage);
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @Operation(summary = "健康检查", description = "检查应用程序运行状态")
    @GetMapping("/health")
    public Result<String> health() {
        return ResponseUtil.success("健康检查成功", "应用程序运行正常！");
    }
}
