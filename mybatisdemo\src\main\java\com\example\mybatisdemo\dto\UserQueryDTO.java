package com.example.mybatisdemo.dto;

import com.example.mybatisdemo.common.page.BasePageQuery;
import com.example.mybatisdemo.common.page.PageUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import org.springframework.util.StringUtils;

/**
 * 用户查询DTO
 * 演示如何继承BasePageQuery实现通用分页
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryDTO extends BasePageQuery {
    
    /**
     * 用户名（模糊查询）
     */
    @Size(max = 50, message = "用户名长度不能超过50个字符")
    private String username;
    
    /**
     * 邮箱（模糊查询）
     */
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;
    
    /**
     * 手机号（模糊查询）
     */
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;
    
    /**
     * 真实姓名（模糊查询）
     */
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;
    
    /**
     * 状态：0-禁用，1-启用
     */
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 年龄范围 - 最小值
     */
    @Min(value = 1, message = "年龄不能小于1")
    @Max(value = 150, message = "年龄不能大于150")
    private Integer minAge;
    
    /**
     * 年龄范围 - 最大值
     */
    @Min(value = 1, message = "年龄不能小于1")
    @Max(value = 150, message = "年龄不能大于150")
    private Integer maxAge;
    
    /**
     * 关键字搜索（同时搜索用户名、邮箱、真实姓名）
     */
    @Size(max = 50, message = "关键字长度不能超过50个字符")
    private String keyword;
    
    /**
     * 排序字段
     */
    private String sortField = "createTime";
    
    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortDirection = "desc";
    
    /**
     * 初始化排序参数
     */
    public void initOrderBy() {
        String orderBy = PageUtils.buildOrderBy(sortField, sortDirection);
        if (StringUtils.hasText(orderBy)) {
            this.setOrderBy(orderBy);
        }
    }
}
