# Lombok注解详细说明

## 概述

Lombok是一个Java库，通过注解的方式自动生成常用的Java代码，减少样板代码的编写，提高开发效率。

## 主要注解详解

### 1. @Data

**功能**: 组合注解，包含以下功能：
- `@Getter` - 生成所有字段的getter方法
- `@Setter` - 生成所有非final字段的setter方法
- `@ToString` - 生成toString()方法
- `@EqualsAndHashCode` - 生成equals()和hashCode()方法
- `@RequiredArgsConstructor` - 生成必需参数的构造函数

**使用场景**: 普通的数据类、实体类、DTO类

```java
@Data
public class User {
    private Long id;
    private String name;
    private String email;
}

// 自动生成的方法：
// getId(), getName(), getEmail()
// setId(), setName(), setEmail()
// toString(), equals(), hashCode()
```

### 2. @NoArgsConstructor

**功能**: 生成无参构造函数

**参数**:
- `access` - 设置访问级别（默认PUBLIC）
- `staticName` - 生成静态工厂方法
- `force` - 强制生成，即使有final字段

```java
@NoArgsConstructor
public class User {
    private String name;
}

// 生成：
// public User() {}

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Singleton {
    // 生成私有无参构造函数
}

@NoArgsConstructor(staticName = "of")
public class User {
    // 生成：public static User of() { return new User(); }
}
```

### 3. @AllArgsConstructor

**功能**: 生成包含所有字段的构造函数

**参数**:
- `access` - 设置访问级别
- `staticName` - 生成静态工厂方法

```java
@AllArgsConstructor
public class User {
    private Long id;
    private String name;
    private String email;
}

// 生成：
// public User(Long id, String name, String email) {
//     this.id = id;
//     this.name = name;
//     this.email = email;
// }

@AllArgsConstructor(staticName = "of")
public class User {
    // 生成：public static User of(Long id, String name, String email)
}
```

### 4. @Builder

**功能**: 生成建造者模式代码，支持链式调用

**特点**:
- 支持链式调用
- 可以部分设置字段
- 生成toBuilder()方法用于复制对象

```java
@Builder
public class User {
    private Long id;
    private String name;
    private String email;
}

// 使用方式：
User user = User.builder()
    .id(1L)
    .name("张三")
    .email("<EMAIL>")
    .build();

// 复制并修改：
User newUser = user.toBuilder()
    .name("李四")
    .build();
```

## 常用组合

### 1. 实体类常用组合

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    private Long id;
    private String username;
    private String email;
    private LocalDateTime createTime;
}
```

**优势**:
- 支持无参构造（JPA/MyBatis需要）
- 支持全参构造（测试时方便）
- 支持建造者模式（灵活创建对象）
- 自动生成getter/setter等方法

### 2. DTO类常用组合

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    private Long id;
    private String username;
    private String email;
}
```

## 其他常用注解

### @Getter / @Setter

```java
public class User {
    @Getter @Setter
    private String name;
    
    @Getter
    private final Long id = 1L;  // 只生成getter，因为是final
}
```

### @ToString

```java
@ToString
public class User {
    private String name;
    private String email;
}

@ToString(exclude = "password")  // 排除敏感字段
public class User {
    private String name;
    private String password;
}

@ToString(includeFieldNames = false)  // 不包含字段名
public class User {
    private String name;
}
```

### @EqualsAndHashCode

```java
@EqualsAndHashCode
public class User {
    private Long id;
    private String name;
}

@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class User {
    @EqualsAndHashCode.Include
    private Long id;  // 只包含id字段
    
    private String name;  // 不包含在equals/hashCode中
}
```

## 测试API

启动应用后，可以测试以下API来了解Lombok的用法：

```bash
# 无参构造函数演示
GET http://localhost:8080/api/lombok/no-args-constructor

# 全参构造函数演示
GET http://localhost:8080/api/lombok/all-args-constructor

# 建造者模式演示
GET http://localhost:8080/api/lombok/builder

# @Data生成的方法演示
GET http://localhost:8080/api/lombok/data-methods

# 部分字段Builder演示
GET http://localhost:8080/api/lombok/partial-builder

# toBuilder()方法演示
GET http://localhost:8080/api/lombok/to-builder

# POST请求创建用户
POST http://localhost:8080/api/lombok/create-user
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "realName": "测试用户",
  "status": 1
}
```

## 注意事项

### 1. IDE支持
确保IDE安装了Lombok插件：
- **IntelliJ IDEA**: 安装Lombok Plugin
- **Eclipse**: 下载lombok.jar并安装

### 2. 编译时处理
Lombok在编译时生成代码，运行时不需要Lombok依赖。

### 3. 调试困难
生成的代码在源码中看不到，调试时可能不太直观。

### 4. 团队约定
团队需要统一使用Lombok，否则可能造成代码风格不一致。

### 5. 版本兼容性
注意Lombok版本与JDK版本的兼容性。

## 最佳实践

1. **实体类使用完整组合**: `@Data + @NoArgsConstructor + @AllArgsConstructor + @Builder`
2. **DTO类简化组合**: `@Data + @Builder`
3. **敏感字段排除**: 在`@ToString`中排除密码等敏感字段
4. **equals/hashCode优化**: 对于实体类，只包含ID字段
5. **静态工厂方法**: 使用`staticName`参数创建更语义化的构造方法

## 性能影响

Lombok对运行时性能几乎没有影响，因为：
1. 代码在编译时生成
2. 生成的代码与手写代码相同
3. 运行时不需要反射或其他开销
