package annotations;

import annotations.CustomAnnotations.*;

/**
 * 用户实体类
 * 演示自定义注解的使用
 */
@Entity(name = "User", tableName = "users", description = "用户信息表")
public class User {
    
    @Column(name = "id", type = "BIGINT", nullable = false, unique = true)
    private Long id;
    
    @Validate(type = ValidationType.NOT_NULL, message = "用户名不能为空")
    @Column(name = "username", type = "VARCHAR", length = 50, nullable = false, unique = true)
    private String username;
    
    @Validate(
        type = ValidationType.CUSTOM,
        pattern = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$",
        message = "密码必须包含字母和数字，长度6-20位"
    )
    @Column(name = "password", type = "VARCHAR", length = 100, nullable = false)
    private String password;
    
    @Validate(type = ValidationType.EMAIL, message = "邮箱格式不正确")
    @Column(name = "email", type = "VARCHAR", length = 100, nullable = false, unique = true)
    private String email;
    
    @Validate(type = ValidationType.PHONE, message = "手机号格式不正确")
    @Column(name = "phone", type = "VARCHAR", length = 11)
    private String phone;
    
    @Validate(
        minLength = 2,
        maxLength = 50,
        message = "真实姓名长度必须在2-50之间"
    )
    @Column(name = "real_name", type = "VARCHAR", length = 50)
    private String realName;
    
    @Column(name = "age", type = "INT")
    private Integer age;
    
    @Column(name = "avatar_url", type = "VARCHAR", length = 500)
    private String avatarUrl;
    
    @Column(name = "status", type = "VARCHAR", length = 20, defaultValue = "ACTIVE")
    private String status;
    
    @Column(name = "created_at", type = "TIMESTAMP", nullable = false)
    private java.time.LocalDateTime createdAt;
    
    @Column(name = "updated_at", type = "TIMESTAMP")
    private java.time.LocalDateTime updatedAt;
    
    // 构造函数
    public User() {}
    
    public User(String username, String email) {
        this.username = username;
        this.email = email;
        this.createdAt = java.time.LocalDateTime.now();
    }
    
    public User(String username, String email, String phone, String password, String realName) {
        this.username = username;
        this.email = email;
        this.phone = phone;
        this.password = password;
        this.realName = realName;
        this.createdAt = java.time.LocalDateTime.now();
    }
    
    // 业务方法（演示方法注解）
    
    @Log(value = "获取用户信息", level = LogLevel.INFO, logArgs = true, logResult = true)
    @Cache(name = "users", key = "#id", expireTime = 600)
    public User getUserInfo(Long id) {
        // 模拟从数据库获取用户信息
        System.out.println("从数据库查询用户: " + id);
        return this;
    }
    
    @Log(value = "更新用户信息", level = LogLevel.INFO, logTime = true)
    @RequirePermission(value = {"USER_UPDATE"}, message = "没有用户更新权限")
    public void updateUserInfo(String email, String phone) {
        this.email = email;
        this.phone = phone;
        this.updatedAt = java.time.LocalDateTime.now();
        System.out.println("用户信息已更新");
    }
    
    @Log(value = "删除用户", level = LogLevel.WARN, logArgs = true)
    @RequirePermission(value = {"USER_DELETE", "ADMIN"}, type = PermissionType.OR, message = "没有用户删除权限")
    public void deleteUser() {
        this.status = "DELETED";
        System.out.println("用户已删除");
    }
    
    @ApiVersion(value = "1.0", deprecated = true, deprecatedMessage = "请使用v2.0版本的接口")
    @RateLimit(maxRequests = 10, window = 60, type = RateLimitType.USER)
    public String getOldUserProfile() {
        return "旧版用户资料";
    }
    
    @ApiVersion("2.0")
    @Cache(name = "userProfile", key = "#userId", expireTime = 300)
    public String getUserProfile() {
        return "新版用户资料";
    }
    
    @Retry(maxAttempts = 3, delay = 1000, retryFor = {RuntimeException.class})
    @Async(executor = "userTaskExecutor", timeout = 5000)
    public void sendWelcomeEmail() {
        // 模拟发送欢迎邮件
        System.out.println("发送欢迎邮件到: " + this.email);
        
        // 模拟可能的异常
        if (Math.random() < 0.3) {
            throw new RuntimeException("邮件发送失败");
        }
    }
    
    @Scheduled(cron = "0 0 2 * * ?")  // 每天凌晨2点执行
    public void cleanupExpiredData() {
        System.out.println("清理过期数据");
    }
    
    @Scheduled(fixedRate = 300000)  // 每5分钟执行一次
    public void updateUserStatistics() {
        System.out.println("更新用户统计信息");
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public java.time.LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(java.time.LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public java.time.LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(java.time.LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", realName='" + realName + '\'' +
                ", age=" + age +
                ", status='" + status + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof User)) return false;
        User user = (User) o;
        return id != null && id.equals(user.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
