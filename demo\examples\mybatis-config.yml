# MyBatis在Spring Boot中的配置
spring:
  datasource:
    url: ***********************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
# MyBatis配置
mybatis:
  # XML映射文件位置
  mapper-locations: classpath:mapper/*.xml
  # 实体类包路径
  type-aliases-package: com.example.entity
  # MyBatis配置文件
  config-location: classpath:mybatis-config.xml
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启延迟加载
    lazy-loading-enabled: true
    # 开启二级缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 分页插件配置（PageHelper）
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
