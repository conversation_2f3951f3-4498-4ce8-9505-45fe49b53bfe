# Java异常处理和IO详解

## 目录
1. [异常处理基础](#异常处理基础)
2. [异常类型](#异常类型)
3. [异常处理机制](#异常处理机制)
4. [自定义异常](#自定义异常)
5. [IO基础概念](#io基础概念)
6. [字节流操作](#字节流操作)
7. [字符流操作](#字符流操作)
8. [文件操作](#文件操作)
9. [NIO操作](#nio操作)
10. [最佳实践](#最佳实践)

## 异常处理基础

### 什么是异常
异常是程序运行时发生的错误事件，它会中断程序的正常执行流程。Java通过异常处理机制来处理运行时错误。

### 异常的优势
- 将错误处理代码与正常业务逻辑分离
- 提供详细的错误信息
- 支持错误的传播和集中处理
- 提高代码的可读性和维护性

## 异常类型

### 异常层次结构
```
Throwable
├── Error (系统级错误，不应被捕获)
│   ├── OutOfMemoryError
│   ├── StackOverflowError
│   └── VirtualMachineError
└── Exception
    ├── RuntimeException (运行时异常，非检查异常)
    │   ├── NullPointerException
    │   ├── ArrayIndexOutOfBoundsException
    │   ├── IllegalArgumentException
    │   └── NumberFormatException
    └── 检查异常 (编译时异常)
        ├── IOException
        ├── SQLException
        ├── ClassNotFoundException
        └── InterruptedException
```

### 检查异常 vs 非检查异常

**检查异常（Checked Exception）**
- 编译时必须处理
- 继承自Exception但不继承自RuntimeException
- 例如：IOException, SQLException

**非检查异常（Unchecked Exception）**
- 运行时异常，编译时不强制处理
- 继承自RuntimeException
- 例如：NullPointerException, ArrayIndexOutOfBoundsException

## 异常处理机制

### try-catch-finally语句

```java
public class ExceptionHandlingDemo {
    public static void main(String[] args) {
        // 基本的try-catch结构
        try {
            int result = 10 / 0; // 会抛出ArithmeticException
        } catch (ArithmeticException e) {
            System.out.println("捕获到算术异常: " + e.getMessage());
        }
        
        // 多重catch块
        try {
            String str = null;
            int length = str.length(); // NullPointerException
            int[] arr = new int[5];
            arr[10] = 1; // ArrayIndexOutOfBoundsException
        } catch (NullPointerException e) {
            System.out.println("空指针异常: " + e.getMessage());
        } catch (ArrayIndexOutOfBoundsException e) {
            System.out.println("数组越界异常: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("其他异常: " + e.getMessage());
        }
        
        // try-catch-finally结构
        try {
            // 可能抛出异常的代码
            performRiskyOperation();
        } catch (Exception e) {
            System.out.println("异常处理: " + e.getMessage());
        } finally {
            // 无论是否发生异常都会执行
            System.out.println("清理资源");
        }
    }
    
    private static void performRiskyOperation() throws Exception {
        throw new Exception("模拟异常");
    }
}
```

### try-with-resources语句

```java
import java.io.*;

public class TryWithResourcesDemo {
    public static void main(String[] args) {
        // 自动资源管理
        try (FileInputStream fis = new FileInputStream("test.txt");
             BufferedReader reader = new BufferedReader(new InputStreamReader(fis))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
        } catch (IOException e) {
            System.out.println("IO异常: " + e.getMessage());
        }
        // 资源会自动关闭，无需手动调用close()
    }
}
```

### 抛出异常

```java
public class ThrowExceptionDemo {
    
    // 使用throws声明可能抛出的异常
    public static void validateAge(int age) throws IllegalArgumentException {
        if (age < 0 || age > 150) {
            // 使用throw抛出异常
            throw new IllegalArgumentException("年龄必须在0-150之间，当前值: " + age);
        }
        System.out.println("年龄验证通过: " + age);
    }
    
    public static void readFile(String filename) throws IOException {
        if (filename == null || filename.trim().isEmpty()) {
            throw new IOException("文件名不能为空");
        }
        
        File file = new File(filename);
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + filename);
        }
        
        // 文件读取逻辑...
    }
    
    public static void main(String[] args) {
        try {
            validateAge(-5);
        } catch (IllegalArgumentException e) {
            System.out.println("参数异常: " + e.getMessage());
        }
        
        try {
            readFile("nonexistent.txt");
        } catch (IOException e) {
            System.out.println("IO异常: " + e.getMessage());
        }
    }
}
```

## 自定义异常

### 创建自定义异常类

```java
// 自定义检查异常
class InsufficientFundsException extends Exception {
    private double amount;
    
    public InsufficientFundsException(double amount) {
        super("余额不足，尝试提取: " + amount);
        this.amount = amount;
    }
    
    public double getAmount() {
        return amount;
    }
}

// 自定义运行时异常
class InvalidAccountException extends RuntimeException {
    public InvalidAccountException(String message) {
        super(message);
    }
    
    public InvalidAccountException(String message, Throwable cause) {
        super(message, cause);
    }
}

// 银行账户类示例
class BankAccount {
    private String accountNumber;
    private double balance;
    
    public BankAccount(String accountNumber, double initialBalance) {
        if (accountNumber == null || accountNumber.trim().isEmpty()) {
            throw new InvalidAccountException("账户号码不能为空");
        }
        this.accountNumber = accountNumber;
        this.balance = initialBalance;
    }
    
    public void withdraw(double amount) throws InsufficientFundsException {
        if (amount <= 0) {
            throw new IllegalArgumentException("提取金额必须大于0");
        }
        
        if (amount > balance) {
            throw new InsufficientFundsException(amount);
        }
        
        balance -= amount;
        System.out.println("成功提取: " + amount + "，余额: " + balance);
    }
    
    public double getBalance() {
        return balance;
    }
}

public class CustomExceptionDemo {
    public static void main(String[] args) {
        try {
            BankAccount account = new BankAccount("12345", 1000.0);
            account.withdraw(1500.0); // 会抛出InsufficientFundsException
        } catch (InsufficientFundsException e) {
            System.out.println("自定义异常: " + e.getMessage());
            System.out.println("尝试提取金额: " + e.getAmount());
        } catch (InvalidAccountException e) {
            System.out.println("账户异常: " + e.getMessage());
        }
    }
}
```

## IO基础概念

### IO流的分类

**按数据流向分类：**
- 输入流（Input Stream）：从数据源读取数据
- 输出流（Output Stream）：向目标写入数据

**按数据类型分类：**
- 字节流（Byte Stream）：处理二进制数据，以字节为单位
- 字符流（Character Stream）：处理文本数据，以字符为单位

**按功能分类：**
- 节点流：直接连接数据源的流
- 处理流：对其他流进行包装，提供额外功能

### IO流层次结构

```
字节流：
InputStream (抽象类)
├── FileInputStream
├── ByteArrayInputStream
├── FilterInputStream
│   ├── BufferedInputStream
│   └── DataInputStream
└── ObjectInputStream

OutputStream (抽象类)
├── FileOutputStream
├── ByteArrayOutputStream
├── FilterOutputStream
│   ├── BufferedOutputStream
│   ├── DataOutputStream
│   └── PrintStream
└── ObjectOutputStream

字符流：
Reader (抽象类)
├── InputStreamReader
│   └── FileReader
├── BufferedReader
├── CharArrayReader
└── StringReader

Writer (抽象类)
├── OutputStreamWriter
│   └── FileWriter
├── BufferedWriter
├── CharArrayWriter
├── StringWriter
└── PrintWriter
```

## 字节流操作

### FileInputStream和FileOutputStream

```java
import java.io.*;

public class ByteStreamDemo {
    public static void main(String[] args) {
        // 文件复制示例
        copyFile("source.txt", "destination.txt");

        // 读取文件内容
        readFileBytes("destination.txt");
    }

    // 使用字节流复制文件
    public static void copyFile(String sourcePath, String destPath) {
        try (FileInputStream fis = new FileInputStream(sourcePath);
             FileOutputStream fos = new FileOutputStream(destPath)) {

            byte[] buffer = new byte[1024]; // 缓冲区
            int bytesRead;

            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }

            System.out.println("文件复制完成");

        } catch (IOException e) {
            System.out.println("文件操作异常: " + e.getMessage());
        }
    }

    // 读取文件字节
    public static void readFileBytes(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            int byteData;
            System.out.print("文件内容（字节）: ");

            while ((byteData = fis.read()) != -1) {
                System.out.print((char) byteData);
            }
            System.out.println();

        } catch (IOException e) {
            System.out.println("读取文件异常: " + e.getMessage());
        }
    }
}
```

### BufferedInputStream和BufferedOutputStream

```java
import java.io.*;

public class BufferedStreamDemo {
    public static void main(String[] args) {
        // 比较缓冲流和非缓冲流的性能
        String sourceFile = "large_file.txt";
        String destFile1 = "copy_without_buffer.txt";
        String destFile2 = "copy_with_buffer.txt";

        // 不使用缓冲流
        long startTime = System.currentTimeMillis();
        copyWithoutBuffer(sourceFile, destFile1);
        long endTime = System.currentTimeMillis();
        System.out.println("不使用缓冲流耗时: " + (endTime - startTime) + "ms");

        // 使用缓冲流
        startTime = System.currentTimeMillis();
        copyWithBuffer(sourceFile, destFile2);
        endTime = System.currentTimeMillis();
        System.out.println("使用缓冲流耗时: " + (endTime - startTime) + "ms");
    }

    // 不使用缓冲流复制文件
    public static void copyWithoutBuffer(String source, String dest) {
        try (FileInputStream fis = new FileInputStream(source);
             FileOutputStream fos = new FileOutputStream(dest)) {

            int byteData;
            while ((byteData = fis.read()) != -1) {
                fos.write(byteData);
            }

        } catch (IOException e) {
            System.out.println("复制失败: " + e.getMessage());
        }
    }

    // 使用缓冲流复制文件
    public static void copyWithBuffer(String source, String dest) {
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(source));
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(dest))) {

            int byteData;
            while ((byteData = bis.read()) != -1) {
                bos.write(byteData);
            }

        } catch (IOException e) {
            System.out.println("复制失败: " + e.getMessage());
        }
    }
}
```

### DataInputStream和DataOutputStream

```java
import java.io.*;

public class DataStreamDemo {
    public static void main(String[] args) {
        String fileName = "data.dat";

        // 写入基本数据类型
        writeData(fileName);

        // 读取基本数据类型
        readData(fileName);
    }

    public static void writeData(String fileName) {
        try (DataOutputStream dos = new DataOutputStream(
                new BufferedOutputStream(new FileOutputStream(fileName)))) {

            // 写入不同类型的数据
            dos.writeInt(123);
            dos.writeDouble(45.67);
            dos.writeBoolean(true);
            dos.writeUTF("Hello World");
            dos.writeLong(9876543210L);

            System.out.println("数据写入完成");

        } catch (IOException e) {
            System.out.println("写入数据异常: " + e.getMessage());
        }
    }

    public static void readData(String fileName) {
        try (DataInputStream dis = new DataInputStream(
                new BufferedInputStream(new FileInputStream(fileName)))) {

            // 按写入顺序读取数据
            int intValue = dis.readInt();
            double doubleValue = dis.readDouble();
            boolean boolValue = dis.readBoolean();
            String stringValue = dis.readUTF();
            long longValue = dis.readLong();

            System.out.println("读取的数据:");
            System.out.println("int: " + intValue);
            System.out.println("double: " + doubleValue);
            System.out.println("boolean: " + boolValue);
            System.out.println("String: " + stringValue);
            System.out.println("long: " + longValue);

        } catch (IOException e) {
            System.out.println("读取数据异常: " + e.getMessage());
        }
    }
}
```

## 字符流操作

### FileReader和FileWriter

```java
import java.io.*;

public class CharacterStreamDemo {
    public static void main(String[] args) {
        String fileName = "text_file.txt";

        // 写入文本文件
        writeTextFile(fileName);

        // 读取文本文件
        readTextFile(fileName);
    }

    public static void writeTextFile(String fileName) {
        try (FileWriter writer = new FileWriter(fileName)) {
            writer.write("这是第一行文本\n");
            writer.write("这是第二行文本\n");
            writer.write("这是第三行文本\n");
            writer.write("支持中文字符");

            System.out.println("文本写入完成");

        } catch (IOException e) {
            System.out.println("写入文件异常: " + e.getMessage());
        }
    }

    public static void readTextFile(String fileName) {
        try (FileReader reader = new FileReader(fileName)) {
            int charData;
            System.out.println("文件内容:");

            while ((charData = reader.read()) != -1) {
                System.out.print((char) charData);
            }
            System.out.println();

        } catch (IOException e) {
            System.out.println("读取文件异常: " + e.getMessage());
        }
    }
}
```

### BufferedReader和BufferedWriter

```java
import java.io.*;
import java.util.ArrayList;
import java.util.List;

public class BufferedCharacterStreamDemo {
    public static void main(String[] args) {
        String fileName = "buffered_text.txt";

        // 写入多行文本
        writeLines(fileName);

        // 逐行读取文本
        readLines(fileName);

        // 处理大文件
        processLargeFile(fileName);
    }

    public static void writeLines(String fileName) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName))) {
            writer.write("第一行内容");
            writer.newLine(); // 写入换行符
            writer.write("第二行内容");
            writer.newLine();
            writer.write("第三行内容");
            writer.newLine();

            // 也可以直接写入包含换行符的字符串
            writer.write("第四行内容\n第五行内容\n");

            System.out.println("多行文本写入完成");

        } catch (IOException e) {
            System.out.println("写入异常: " + e.getMessage());
        }
    }

    public static void readLines(String fileName) {
        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
            String line;
            int lineNumber = 1;

            System.out.println("逐行读取文件内容:");
            while ((line = reader.readLine()) != null) {
                System.out.println("第" + lineNumber + "行: " + line);
                lineNumber++;
            }

        } catch (IOException e) {
            System.out.println("读取异常: " + e.getMessage());
        }
    }

    public static void processLargeFile(String fileName) {
        List<String> lines = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
            String line;

            // 读取所有行到内存中
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }

            System.out.println("文件总行数: " + lines.size());

            // 处理每一行
            for (int i = 0; i < lines.size(); i++) {
                String processedLine = "处理后的 " + lines.get(i);
                lines.set(i, processedLine);
            }

            // 写回文件
            try (BufferedWriter writer = new BufferedWriter(new FileWriter("processed_" + fileName))) {
                for (String processedLine : lines) {
                    writer.write(processedLine);
                    writer.newLine();
                }
            }

            System.out.println("文件处理完成");

        } catch (IOException e) {
            System.out.println("处理文件异常: " + e.getMessage());
        }
    }
}
```

### InputStreamReader和OutputStreamWriter

```java
import java.io.*;
import java.nio.charset.StandardCharsets;

public class StreamReaderWriterDemo {
    public static void main(String[] args) {
        String fileName = "encoding_test.txt";

        // 指定编码写入文件
        writeWithEncoding(fileName);

        // 指定编码读取文件
        readWithEncoding(fileName);

        // 转换编码
        convertEncoding("utf8_file.txt", "gbk_file.txt");
    }

    public static void writeWithEncoding(String fileName) {
        try (OutputStreamWriter writer = new OutputStreamWriter(
                new FileOutputStream(fileName), StandardCharsets.UTF_8)) {

            writer.write("这是UTF-8编码的中文文本\n");
            writer.write("English text with UTF-8 encoding\n");
            writer.write("特殊字符: ©®™€\n");

            System.out.println("UTF-8编码文件写入完成");

        } catch (IOException e) {
            System.out.println("写入异常: " + e.getMessage());
        }
    }

    public static void readWithEncoding(String fileName) {
        try (InputStreamReader reader = new InputStreamReader(
                new FileInputStream(fileName), StandardCharsets.UTF_8);
             BufferedReader bufferedReader = new BufferedReader(reader)) {

            String line;
            System.out.println("读取UTF-8编码文件:");

            while ((line = bufferedReader.readLine()) != null) {
                System.out.println(line);
            }

        } catch (IOException e) {
            System.out.println("读取异常: " + e.getMessage());
        }
    }

    public static void convertEncoding(String sourceFile, String destFile) {
        try (InputStreamReader reader = new InputStreamReader(
                new FileInputStream(sourceFile), StandardCharsets.UTF_8);
             OutputStreamWriter writer = new OutputStreamWriter(
                new FileOutputStream(destFile), "GBK")) {

            int charData;
            while ((charData = reader.read()) != -1) {
                writer.write(charData);
            }

            System.out.println("编码转换完成: UTF-8 -> GBK");

        } catch (IOException e) {
            System.out.println("编码转换异常: " + e.getMessage());
        }
    }
}
```

## 文件操作

### File类的使用

```java
import java.io.*;
import java.util.Date;

public class FileOperationsDemo {
    public static void main(String[] args) {
        // 文件和目录操作
        fileBasicOperations();

        // 目录遍历
        traverseDirectory(".");

        // 文件过滤
        filterFiles(".", ".java");
    }

    public static void fileBasicOperations() {
        File file = new File("test_file.txt");
        File directory = new File("test_directory");

        try {
            // 创建文件
            if (file.createNewFile()) {
                System.out.println("文件创建成功: " + file.getName());
            } else {
                System.out.println("文件已存在: " + file.getName());
            }

            // 创建目录
            if (directory.mkdir()) {
                System.out.println("目录创建成功: " + directory.getName());
            } else {
                System.out.println("目录已存在: " + directory.getName());
            }

            // 文件信息
            System.out.println("\n文件信息:");
            System.out.println("文件名: " + file.getName());
            System.out.println("绝对路径: " + file.getAbsolutePath());
            System.out.println("父目录: " + file.getParent());
            System.out.println("文件大小: " + file.length() + " 字节");
            System.out.println("最后修改时间: " + new Date(file.lastModified()));
            System.out.println("是否为文件: " + file.isFile());
            System.out.println("是否为目录: " + file.isDirectory());
            System.out.println("是否可读: " + file.canRead());
            System.out.println("是否可写: " + file.canWrite());
            System.out.println("是否可执行: " + file.canExecute());

        } catch (IOException e) {
            System.out.println("文件操作异常: " + e.getMessage());
        }
    }

    public static void traverseDirectory(String dirPath) {
        File directory = new File(dirPath);

        if (!directory.exists() || !directory.isDirectory()) {
            System.out.println("目录不存在或不是目录: " + dirPath);
            return;
        }

        System.out.println("\n遍历目录: " + directory.getAbsolutePath());
        File[] files = directory.listFiles();

        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    System.out.println("[目录] " + file.getName());
                } else {
                    System.out.println("[文件] " + file.getName() +
                                     " (" + file.length() + " 字节)");
                }
            }
        }
    }

    public static void filterFiles(String dirPath, String extension) {
        File directory = new File(dirPath);

        // 使用文件过滤器
        File[] javaFiles = directory.listFiles(new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                return pathname.isFile() && pathname.getName().endsWith(extension);
            }
        });

        System.out.println("\n" + extension + " 文件列表:");
        if (javaFiles != null) {
            for (File file : javaFiles) {
                System.out.println(file.getName());
            }
        }

        // 使用Lambda表达式的文件过滤器
        File[] txtFiles = directory.listFiles(
            (dir, name) -> name.toLowerCase().endsWith(".txt")
        );

        System.out.println("\n.txt 文件列表:");
        if (txtFiles != null) {
            for (File file : txtFiles) {
                System.out.println(file.getName());
            }
        }
    }
}
```

### 对象序列化

```java
import java.io.*;
import java.util.ArrayList;
import java.util.List;

// 可序列化的学生类
class Student implements Serializable {
    private static final long serialVersionUID = 1L;

    private String name;
    private int age;
    private transient String password; // transient字段不会被序列化
    private static String school = "某某大学"; // static字段不会被序列化

    public Student(String name, int age, String password) {
        this.name = name;
        this.age = age;
        this.password = password;
    }

    // getter和setter方法
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public int getAge() { return age; }
    public void setAge(int age) { this.age = age; }
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    @Override
    public String toString() {
        return "Student{name='" + name + "', age=" + age +
               ", password='" + password + "', school='" + school + "'}";
    }
}

public class SerializationDemo {
    public static void main(String[] args) {
        String fileName = "students.ser";

        // 序列化对象
        serializeObjects(fileName);

        // 反序列化对象
        deserializeObjects(fileName);
    }

    public static void serializeObjects(String fileName) {
        List<Student> students = new ArrayList<>();
        students.add(new Student("张三", 20, "password123"));
        students.add(new Student("李四", 21, "secret456"));
        students.add(new Student("王五", 19, "mypass789"));

        try (ObjectOutputStream oos = new ObjectOutputStream(
                new FileOutputStream(fileName))) {

            // 序列化整个列表
            oos.writeObject(students);

            // 也可以逐个序列化
            oos.writeObject(new Student("赵六", 22, "password000"));

            System.out.println("对象序列化完成");

        } catch (IOException e) {
            System.out.println("序列化异常: " + e.getMessage());
        }
    }

    @SuppressWarnings("unchecked")
    public static void deserializeObjects(String fileName) {
        try (ObjectInputStream ois = new ObjectInputStream(
                new FileInputStream(fileName))) {

            // 反序列化列表
            List<Student> students = (List<Student>) ois.readObject();

            System.out.println("反序列化的学生列表:");
            for (Student student : students) {
                System.out.println(student);
            }

            // 反序列化单个对象
            Student singleStudent = (Student) ois.readObject();
            System.out.println("单个学生对象: " + singleStudent);

        } catch (IOException | ClassNotFoundException e) {
            System.out.println("反序列化异常: " + e.getMessage());
        }
    }
}
```

## NIO简介

### NIO基本概念

Java NIO (New IO) 是Java 1.4引入的新的IO API，提供了与传统IO不同的工作方式：

**NIO的核心组件：**
- **Channel（通道）**：类似于流，但可以双向读写
- **Buffer（缓冲区）**：数据容器，所有数据都通过Buffer处理
- **Selector（选择器）**：用于监控多个Channel的事件

### NIO文件操作示例

```java
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.*;
import java.util.List;

public class NIODemo {
    public static void main(String[] args) {
        // NIO文件读写
        nioFileOperations();

        // Path和Files类操作
        pathAndFilesOperations();
    }

    public static void nioFileOperations() {
        Path sourcePath = Paths.get("source.txt");
        Path destPath = Paths.get("nio_copy.txt");

        try {
            // 创建测试文件
            Files.write(sourcePath, "Hello NIO World!\n这是NIO测试文件。".getBytes());

            // 使用FileChannel复制文件
            try (FileChannel sourceChannel = FileChannel.open(sourcePath, StandardOpenOption.READ);
                 FileChannel destChannel = FileChannel.open(destPath,
                     StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {

                ByteBuffer buffer = ByteBuffer.allocate(1024);

                while (sourceChannel.read(buffer) > 0) {
                    buffer.flip(); // 切换到读模式
                    destChannel.write(buffer);
                    buffer.clear(); // 清空缓冲区
                }

                System.out.println("NIO文件复制完成");
            }

            // 读取文件内容
            try (FileChannel channel = FileChannel.open(destPath, StandardOpenOption.READ)) {
                ByteBuffer buffer = ByteBuffer.allocate(1024);
                StringBuilder content = new StringBuilder();

                while (channel.read(buffer) > 0) {
                    buffer.flip();
                    while (buffer.hasRemaining()) {
                        content.append((char) buffer.get());
                    }
                    buffer.clear();
                }

                System.out.println("文件内容: " + content.toString());
            }

        } catch (IOException e) {
            System.out.println("NIO操作异常: " + e.getMessage());
        }
    }

    public static void pathAndFilesOperations() {
        try {
            // Path操作
            Path path = Paths.get("test", "example", "file.txt");
            System.out.println("路径: " + path);
            System.out.println("绝对路径: " + path.toAbsolutePath());
            System.out.println("父目录: " + path.getParent());
            System.out.println("文件名: " + path.getFileName());

            // 创建目录
            Path dirPath = Paths.get("nio_test_dir");
            if (!Files.exists(dirPath)) {
                Files.createDirectory(dirPath);
                System.out.println("目录创建成功: " + dirPath);
            }

            // 创建文件并写入内容
            Path filePath = dirPath.resolve("test_file.txt");
            List<String> lines = List.of(
                "第一行内容",
                "第二行内容",
                "第三行内容"
            );
            Files.write(filePath, lines);
            System.out.println("文件写入完成");

            // 读取所有行
            List<String> readLines = Files.readAllLines(filePath);
            System.out.println("读取的内容:");
            readLines.forEach(System.out::println);

            // 文件属性
            System.out.println("\n文件属性:");
            System.out.println("文件大小: " + Files.size(filePath) + " 字节");
            System.out.println("是否为目录: " + Files.isDirectory(filePath));
            System.out.println("是否为常规文件: " + Files.isRegularFile(filePath));
            System.out.println("是否可读: " + Files.isReadable(filePath));
            System.out.println("是否可写: " + Files.isWritable(filePath));

        } catch (IOException e) {
            System.out.println("Path和Files操作异常: " + e.getMessage());
        }
    }
}
```

## 最佳实践

### 1. 异常处理最佳实践

```java
public class ExceptionBestPractices {

    // ✅ 好的做法：具体的异常处理
    public void goodExceptionHandling() {
        try {
            // 可能抛出异常的代码
            processFile("data.txt");
        } catch (FileNotFoundException e) {
            // 处理文件未找到异常
            logger.error("文件未找到: " + e.getMessage());
            // 提供备选方案
            createDefaultFile();
        } catch (IOException e) {
            // 处理其他IO异常
            logger.error("IO操作失败: " + e.getMessage());
            throw new ServiceException("文件处理失败", e);
        }
    }

    // ❌ 不好的做法：捕获所有异常
    public void badExceptionHandling() {
        try {
            processFile("data.txt");
        } catch (Exception e) {
            // 过于宽泛的异常捕获
            e.printStackTrace(); // 不应该直接打印堆栈
        }
    }

    // ✅ 好的做法：资源管理
    public void goodResourceManagement() {
        try (FileInputStream fis = new FileInputStream("file.txt");
             BufferedInputStream bis = new BufferedInputStream(fis)) {
            // 使用资源
        } catch (IOException e) {
            // 异常处理
        }
        // 资源自动关闭
    }

    // ✅ 好的做法：自定义异常提供更多信息
    public void validateUser(User user) throws ValidationException {
        if (user == null) {
            throw new ValidationException("用户对象不能为空");
        }
        if (user.getAge() < 0) {
            throw new ValidationException("用户年龄不能为负数: " + user.getAge());
        }
    }
}
```

### 2. IO操作最佳实践

```java
public class IOBestPractices {

    // ✅ 使用缓冲流提高性能
    public void efficientFileReading(String fileName) {
        try (BufferedReader reader = new BufferedReader(
                new FileReader(fileName))) {
            String line;
            while ((line = reader.readLine()) != null) {
                processLine(line);
            }
        } catch (IOException e) {
            logger.error("文件读取失败", e);
        }
    }

    // ✅ 处理大文件时使用流式处理
    public void processLargeFile(String fileName) {
        try (Stream<String> lines = Files.lines(Paths.get(fileName))) {
            lines.filter(line -> !line.trim().isEmpty())
                 .map(String::toUpperCase)
                 .forEach(this::processLine);
        } catch (IOException e) {
            logger.error("大文件处理失败", e);
        }
    }

    // ✅ 正确的编码处理
    public void handleEncoding(String fileName) {
        try (BufferedReader reader = Files.newBufferedReader(
                Paths.get(fileName), StandardCharsets.UTF_8)) {
            // 处理文件内容
        } catch (IOException e) {
            logger.error("编码处理失败", e);
        }
    }

    // ✅ 安全的文件操作
    public void safeFileOperations(String fileName) {
        Path path = Paths.get(fileName);

        // 检查文件是否存在
        if (!Files.exists(path)) {
            logger.warn("文件不存在: " + fileName);
            return;
        }

        // 检查文件权限
        if (!Files.isReadable(path)) {
            throw new SecurityException("文件不可读: " + fileName);
        }

        // 执行文件操作
        try {
            // 文件处理逻辑
        } catch (IOException e) {
            logger.error("文件操作失败", e);
        }
    }
}
```

### 3. 性能优化建议

1. **使用缓冲流**：BufferedInputStream/BufferedOutputStream 可以显著提高IO性能
2. **合适的缓冲区大小**：通常8KB-64KB是比较好的选择
3. **避免频繁的小IO操作**：批量处理数据
4. **及时关闭资源**：使用try-with-resources语句
5. **选择合适的IO方式**：
   - 小文件：传统IO
   - 大文件：NIO或流式处理
   - 网络IO：NIO + Selector

### 4. 常见错误避免

```java
public class CommonMistakes {

    // ❌ 错误：忘记关闭资源
    public void mistake1() throws IOException {
        FileInputStream fis = new FileInputStream("file.txt");
        // 处理文件
        // 忘记关闭fis
    }

    // ❌ 错误：吞掉异常
    public void mistake2() {
        try {
            // 危险操作
        } catch (Exception e) {
            // 什么都不做，异常被吞掉了
        }
    }

    // ❌ 错误：在finally中抛出异常
    public void mistake3() throws IOException {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream("file.txt");
        } finally {
            fis.close(); // 可能抛出IOException，会掩盖try块中的异常
        }
    }

    // ✅ 正确的做法
    public void correctWay() {
        try (FileInputStream fis = new FileInputStream("file.txt")) {
            // 处理文件
        } catch (IOException e) {
            logger.error("文件处理失败", e);
            // 适当的异常处理
        }
    }
}
```

## 总结

Java的异常处理和IO操作是Java编程的重要基础：

**异常处理要点：**
- 理解检查异常和非检查异常的区别
- 合理使用try-catch-finally和try-with-resources
- 创建有意义的自定义异常
- 不要忽略或吞掉异常

**IO操作要点：**
- 选择合适的流类型（字节流vs字符流）
- 使用缓冲流提高性能
- 正确处理字符编码
- 及时关闭资源
- 考虑使用NIO处理大文件或高并发场景

掌握这些知识点将帮助你编写更加健壮和高效的Java程序。
```
