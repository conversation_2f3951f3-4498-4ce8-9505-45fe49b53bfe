# MyBatis配置和使用说明

## 项目结构

```
src/main/java/com/example/mybatisdemo/
├── entity/                 # 实体类
│   ├── User.java          # 用户实体
│   └── Article.java       # 文章实体
├── mapper/                # Mapper接口
│   ├── UserMapper.java    # 用户Mapper
│   └── ArticleMapper.java # 文章Mapper
├── service/               # 服务层
│   ├── UserService.java   # 用户服务接口
│   └── impl/
│       └── UserServiceImpl.java # 用户服务实现
├── controller/            # 控制器层
│   └── UserController.java # 用户控制器
└── common/                # 公共组件
    ├── result/            # 统一响应
    ├── exception/         # 异常处理
    └── enums/             # 枚举类

src/main/resources/
├── mapper/                # MyBatis XML映射文件
│   ├── UserMapper.xml     # 用户映射文件
│   └── ArticleMapper.xml  # 文章映射文件
├── sql/                   # SQL脚本
│   └── schema.sql         # 数据库表结构
└── application.yml        # 配置文件
```

## 配置说明

### 1. application.yml配置

```yaml
spring:
  datasource:
    url: ***************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml    # XML映射文件位置
  type-aliases-package: com.example.mybatisdemo.entity  # 实体类包路径
  configuration:
    map-underscore-to-camel-case: true        # 下划线转驼峰
    default-fetch-size: 100                   # 默认获取数量
    default-statement-timeout: 30             # 默认超时时间
    cache-enabled: true                       # 启用缓存
    lazy-loading-enabled: true                # 启用懒加载
    multiple-result-sets-enabled: true        # 启用多结果集
    use-generated-keys: true                  # 使用生成的主键
    default-executor-type: reuse              # 执行器类型
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  # 日志实现

logging:
  level:
    com.example.mybatisdemo.mapper: debug     # Mapper日志级别
```

### 2. 主类配置

```java
@SpringBootApplication
@MapperScan("com.example.mybatisdemo.mapper")  // 扫描Mapper接口
public class MybatisdemoApplication {
    // ...
}
```

## 数据库准备

### 1. 创建数据库和表

执行 `src/main/resources/sql/schema.sql` 中的SQL脚本：

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS mybatisdemo 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户表
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
);
```

### 2. 插入测试数据

脚本中已包含测试数据，包括4个用户和4篇文章。

## MyBatis使用方式

### 1. 实体类（Entity）

使用Lombok注解简化代码：

```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    private Long id;
    private String username;
    private String password;
    // ... 其他字段
}
```

### 2. Mapper接口

```java
@Mapper
public interface UserMapper {
    User selectById(@Param("id") Long id);
    List<User> selectAll();
    int insert(User user);
    int update(User user);
    int deleteById(@Param("id") Long id);
    // ... 其他方法
}
```

### 3. XML映射文件

```xml
<mapper namespace="com.example.mybatisdemo.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <!-- ... 其他字段映射 -->
    </resultMap>
    
    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM user WHERE id = #{id}
    </select>
    
    <!-- ... 其他SQL -->
</mapper>
```

### 4. Service层

```java
@Service
@Transactional
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }
    
    // ... 其他业务方法
}
```

## 测试API

启动应用后，可以测试以下API：

### 用户管理API

```bash
# 查询所有用户
GET http://localhost:8080/api/users

# 根据ID查询用户
GET http://localhost:8080/api/users/1

# 根据用户名查询用户
GET http://localhost:8080/api/users/username/admin

# 分页查询用户
GET http://localhost:8080/api/users/page?page=1&size=5

# 搜索用户
GET http://localhost:8080/api/users/search?keyword=张

# 根据状态查询用户
GET http://localhost:8080/api/users/status/1

# 获取用户统计信息
GET http://localhost:8080/api/users/statistics

# 检查用户名是否存在
GET http://localhost:8080/api/users/check-username?username=admin

# 创建测试用户
POST http://localhost:8080/api/users/create-test-user

# 创建用户
POST http://localhost:8080/api/users
Content-Type: application/json

{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>",
  "phone": "13800138888",
  "realName": "新用户",
  "status": 1
}

# 更新用户
PUT http://localhost:8080/api/users/1
Content-Type: application/json

{
  "realName": "更新后的姓名",
  "email": "<EMAIL>"
}

# 启用用户
PUT http://localhost:8080/api/users/1/enable

# 禁用用户
PUT http://localhost:8080/api/users/1/disable

# 删除用户
DELETE http://localhost:8080/api/users/1

# 批量删除用户
DELETE http://localhost:8080/api/users/batch
Content-Type: application/json

[2, 3, 4]
```

## MyBatis特性演示

### 1. 动态SQL

在XML中使用`<if>`、`<where>`、`<foreach>`等标签：

```xml
<update id="update">
    UPDATE user
    <set>
        <if test="username != null and username != ''">
            username = #{username},
        </if>
        <if test="email != null and email != ''">
            email = #{email},
        </if>
        update_time = NOW()
    </set>
    WHERE id = #{id}
</update>
```

### 2. 结果映射

处理数据库字段名与Java属性名的映射：

```xml
<resultMap id="BaseResultMap" type="User">
    <result column="real_name" property="realName" jdbcType="VARCHAR"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
</resultMap>
```

### 3. 主键回填

插入数据后自动获取生成的主键：

```xml
<insert id="insert" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO user (username, password, email) 
    VALUES (#{username}, #{password}, #{email})
</insert>
```

### 4. 批量操作

```xml
<delete id="deleteBatch">
    DELETE FROM user WHERE id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
</delete>
```

## 注意事项

1. **事务管理**: Service层方法使用`@Transactional`注解
2. **参数校验**: 在Service层进行业务参数校验
3. **异常处理**: 使用自定义业务异常和全局异常处理
4. **日志记录**: 重要操作记录日志
5. **SQL注入防护**: 使用`#{}`参数占位符
6. **性能优化**: 合理使用缓存和懒加载

## 下一步扩展

1. 添加分页插件（PageHelper）
2. 添加代码生成器（MyBatis Generator）
3. 集成Redis缓存
4. 添加数据库连接池监控
5. 实现读写分离
