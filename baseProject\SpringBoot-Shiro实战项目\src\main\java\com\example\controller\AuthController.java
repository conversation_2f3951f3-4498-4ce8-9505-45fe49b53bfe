package com.example.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.example.entity.SysUser;
import com.example.service.SysUserService;
import com.example.vo.ApiResponse;
import com.example.vo.LoginRequest;
import com.example.vo.LoginResponse;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证控制器
 * 处理用户登录、登出等认证相关操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    @Autowired
    private SysUserService userService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        try {
            logger.info("用户登录请求: {}", loginRequest.getUsername());
            
            // 1. 参数验证
            if (StrUtil.isBlank(loginRequest.getUsername())) {
                return ApiResponse.error("用户名不能为空");
            }
            if (StrUtil.isBlank(loginRequest.getPassword())) {
                return ApiResponse.error("密码不能为空");
            }
            
            // 2. 获取Subject
            Subject subject = SecurityUtils.getSubject();
            
            // 3. 创建认证令牌
            UsernamePasswordToken token = new UsernamePasswordToken(
                loginRequest.getUsername(), 
                loginRequest.getPassword(),
                loginRequest.getRememberMe() != null ? loginRequest.getRememberMe() : false
            );
            
            // 4. 执行登录
            subject.login(token);
            
            // 5. 获取用户信息
            SysUser user = (SysUser) subject.getPrincipal();
            
            // 6. 更新最后登录信息
            String clientIp = ServletUtil.getClientIP(request);
            userService.updateLastLogin(user.getId(), clientIp);
            
            // 7. 构建响应
            LoginResponse loginResponse = new LoginResponse();
            loginResponse.setUserId(user.getId());
            loginResponse.setUsername(user.getUsername());
            loginResponse.setRealName(user.getRealName());
            loginResponse.setEmail(user.getEmail());
            
            // 获取用户角色和权限
            SysUser userWithRoles = userService.getUserWithRolesAndPermissions(user.getId());
            if (userWithRoles != null) {
                loginResponse.setRoles(userWithRoles.getRoles());
                loginResponse.setPermissions(userWithRoles.getPermissions());
            }
            
            logger.info("用户登录成功: {}", loginRequest.getUsername());
            return ApiResponse.success("登录成功", loginResponse);
            
        } catch (UnknownAccountException e) {
            logger.warn("用户登录失败 - 用户不存在: {}", loginRequest.getUsername());
            return ApiResponse.error("用户名或密码错误");
        } catch (IncorrectCredentialsException e) {
            logger.warn("用户登录失败 - 密码错误: {}", loginRequest.getUsername());
            return ApiResponse.error("用户名或密码错误");
        } catch (LockedAccountException e) {
            logger.warn("用户登录失败 - 账户被锁定: {}", loginRequest.getUsername());
            return ApiResponse.error("账户已被锁定，请联系管理员");
        } catch (DisabledAccountException e) {
            logger.warn("用户登录失败 - 账户被禁用: {}", loginRequest.getUsername());
            return ApiResponse.error("账户已被禁用，请联系管理员");
        } catch (ExcessiveAttemptsException e) {
            logger.warn("用户登录失败 - 尝试次数过多: {}", loginRequest.getUsername());
            return ApiResponse.error("登录尝试次数过多，请稍后再试");
        } catch (AuthenticationException e) {
            logger.error("用户登录失败 - 认证异常: {}", loginRequest.getUsername(), e);
            return ApiResponse.error("登录失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("用户登录失败 - 系统异常: {}", loginRequest.getUsername(), e);
            return ApiResponse.error("登录失败，系统异常");
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout() {
        try {
            Subject subject = SecurityUtils.getSubject();
            if (subject.isAuthenticated()) {
                SysUser user = (SysUser) subject.getPrincipal();
                logger.info("用户登出: {}", user.getUsername());
                subject.logout();
            }
            return ApiResponse.success("登出成功");
        } catch (Exception e) {
            logger.error("用户登出失败", e);
            return ApiResponse.error("登出失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    public ApiResponse<LoginResponse> getCurrentUser() {
        try {
            Subject subject = SecurityUtils.getSubject();
            if (!subject.isAuthenticated()) {
                return ApiResponse.error("用户未登录");
            }
            
            SysUser user = (SysUser) subject.getPrincipal();
            
            // 查询完整的用户信息
            SysUser userWithRoles = userService.getUserWithRolesAndPermissions(user.getId());
            if (userWithRoles == null) {
                return ApiResponse.error("用户信息不存在");
            }
            
            // 构建响应
            LoginResponse response = new LoginResponse();
            response.setUserId(userWithRoles.getId());
            response.setUsername(userWithRoles.getUsername());
            response.setRealName(userWithRoles.getRealName());
            response.setEmail(userWithRoles.getEmail());
            response.setRoles(userWithRoles.getRoles());
            response.setPermissions(userWithRoles.getPermissions());
            
            return ApiResponse.success("获取用户信息成功", response);
            
        } catch (Exception e) {
            logger.error("获取当前用户信息失败", e);
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户是否已登录
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> checkLogin() {
        try {
            Subject subject = SecurityUtils.getSubject();
            boolean isLoggedIn = subject.isAuthenticated();
            return ApiResponse.success("检查登录状态成功", isLoggedIn);
        } catch (Exception e) {
            logger.error("检查登录状态失败", e);
            return ApiResponse.error("检查登录状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public ApiResponse<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        try {
            // 1. 获取当前用户
            Subject subject = SecurityUtils.getSubject();
            if (!subject.isAuthenticated()) {
                return ApiResponse.error("用户未登录");
            }
            
            SysUser user = (SysUser) subject.getPrincipal();
            
            // 2. 参数验证
            if (StrUtil.isBlank(request.getOldPassword())) {
                return ApiResponse.error("原密码不能为空");
            }
            if (StrUtil.isBlank(request.getNewPassword())) {
                return ApiResponse.error("新密码不能为空");
            }
            if (request.getNewPassword().length() < 6) {
                return ApiResponse.error("新密码长度不能少于6位");
            }
            
            // 3. 修改密码
            boolean success = userService.changePassword(
                user.getId(), 
                request.getOldPassword(), 
                request.getNewPassword()
            );
            
            if (success) {
                logger.info("用户密码修改成功: {}", user.getUsername());
                return ApiResponse.success("密码修改成功");
            } else {
                return ApiResponse.error("密码修改失败");
            }
            
        } catch (Exception e) {
            logger.error("修改密码失败", e);
            return ApiResponse.error("密码修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改密码请求对象
     */
    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;
        
        // Getter和Setter
        public String getOldPassword() {
            return oldPassword;
        }
        
        public void setOldPassword(String oldPassword) {
            this.oldPassword = oldPassword;
        }
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
