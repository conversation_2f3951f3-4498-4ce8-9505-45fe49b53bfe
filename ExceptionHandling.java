import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.Scanner;

/**
 * 这个程序展示Java中的异常处理
 */
public class ExceptionHandling {
    public static void main(String[] args) {
        // 1. try-catch 基本异常处理
        System.out.println("===== try-catch示例 =====");
        try {
            int result = divide(10, 0);  // 尝试除以零
            System.out.println("结果: " + result);  // 这行不会执行
        } catch (ArithmeticException e) {
            System.out.println("捕获到算术异常: " + e.getMessage());
        }
        
        // 2. 多重catch块
        System.out.println("\n===== 多重catch示例 =====");
        try {
            int[] numbers = {1, 2, 3};
            System.out.println(numbers[5]);  // 数组索引越界
        } catch (ArrayIndexOutOfBoundsException e) {
            System.out.println("数组索引越界: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("发生其他异常: " + e.getMessage());
        }
        
        // 3. finally块
        System.out.println("\n===== finally示例 =====");
        try {
            System.out.println("尝试执行可能抛出异常的代码");
            // 模拟异常
            if (true) {
                throw new RuntimeException("模拟的运行时异常");
            }
        } catch (Exception e) {
            System.out.println("捕获到异常: " + e.getMessage());
        } finally {
            System.out.println("无论是否发生异常，finally块总是执行");
        }
        
        // 4. try-with-resources (Java 7+)
        System.out.println("\n===== try-with-resources示例 =====");
        // 这里的Scanner会自动关闭，无需finally块
        try (Scanner scanner = new Scanner(System.in)) {
            System.out.println("Scanner已创建，将在try块结束时自动关闭");
            // 这里不实际读取输入，只是演示
        }
        
        // 5. 抛出异常
        System.out.println("\n===== 抛出异常示例 =====");
        try {
            validateAge(15);
            validateAge(-5);  // 这会抛出异常
        } catch (IllegalArgumentException e) {
            System.out.println("年龄验证失败: " + e.getMessage());
        }
        
        // 6. 检查型异常
        System.out.println("\n===== 检查型异常示例 =====");
        try {
            readFile("不存在的文件.txt");
        } catch (FileNotFoundException e) {
            System.out.println("文件未找到: " + e.getMessage());
        } catch (IOException e) {
            System.out.println("IO异常: " + e.getMessage());
        }
        
        // 7. 自定义异常
        System.out.println("\n===== 自定义异常示例 =====");
        BankAccount account = new BankAccount(1000);
        
        try {
            account.withdraw(500);
            System.out.println("取款成功，当前余额: " + account.getBalance());
            
            account.withdraw(800);  // 这会抛出自定义异常
        } catch (InsufficientFundsException e) {
            System.out.println("取款失败: " + e.getMessage());
            System.out.println("当前余额: " + e.getBalance() + ", 尝试取款: " + e.getAmount());
        }
    }
    
    // 可能抛出运行时异常的方法
    public static int divide(int a, int b) {
        return a / b;  // 如果b为0，会抛出ArithmeticException
    }
    
    // 手动抛出异常的方法
    public static void validateAge(int age) {
        if (age < 0) {
            throw new IllegalArgumentException("年龄不能为负数");
        }
        System.out.println("年龄有效: " + age);
    }
    
    // 抛出检查型异常的方法
    public static void readFile(String fileName) throws IOException {
        File file = new File(fileName);
        FileReader reader = new FileReader(file);  // 可能抛出FileNotFoundException
        reader.read();  // 可能抛出IOException
        reader.close();
    }
}

// 自定义异常类
class InsufficientFundsException extends Exception {
    private double balance;
    private double amount;
    
    public InsufficientFundsException(String message, double balance, double amount) {
        super(message);
        this.balance = balance;
        this.amount = amount;
    }
    
    public double getBalance() {
        return balance;
    }
    
    public double getAmount() {
        return amount;
    }
}

// 使用自定义异常的类
class BankAccount {
    private double balance;
    
    public BankAccount(double initialBalance) {
        this.balance = initialBalance;
    }
    
    public void deposit(double amount) {
        balance += amount;
    }
    
    public void withdraw(double amount) throws InsufficientFundsException {
        if (amount > balance) {
            throw new InsufficientFundsException("余额不足", balance, amount);
        }
        balance -= amount;
    }
    
    public double getBalance() {
        return balance;
    }
} 