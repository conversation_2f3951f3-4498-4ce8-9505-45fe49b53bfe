package com.example.mybatisrbac.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 状态值验证器
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public class ValidStatusValidator implements ConstraintValidator<ValidStatus, Integer> {

    @Override
    public void initialize(ValidStatus constraintAnnotation) {
        // 初始化方法，可以获取注解参数
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        // null值由@NotNull等其他注解处理
        if (value == null) {
            return true;
        }
        
        // 状态值只能为0或1
        return value == 0 || value == 1;
    }
}
