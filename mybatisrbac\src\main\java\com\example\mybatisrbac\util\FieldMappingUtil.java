package com.example.mybatisrbac.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 字段映射工具类
 * 用于 Java 字段名和数据库字段名之间的转换
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public class FieldMappingUtil {

    /**
     * 角色表字段映射
     * Java字段名 -> 数据库字段名
     */
    private static final Map<String, String> ROLE_FIELD_MAPPING = new HashMap<>();
    
    /**
     * 用户表字段映射
     * Java字段名 -> 数据库字段名
     */
    private static final Map<String, String> USER_FIELD_MAPPING = new HashMap<>();

    static {
        // 角色表字段映射
        ROLE_FIELD_MAPPING.put("id", "id");
        ROLE_FIELD_MAPPING.put("roleName", "role_name");
        ROLE_FIELD_MAPPING.put("roleCode", "role_code");
        ROLE_FIELD_MAPPING.put("description", "description");
        ROLE_FIELD_MAPPING.put("status", "status");
        ROLE_FIELD_MAPPING.put("createTime", "create_time");
        ROLE_FIELD_MAPPING.put("updateTime", "update_time");
        ROLE_FIELD_MAPPING.put("createBy", "create_by");
        ROLE_FIELD_MAPPING.put("updateBy", "update_by");

        // 用户表字段映射
        USER_FIELD_MAPPING.put("id", "id");
        USER_FIELD_MAPPING.put("username", "username");
        USER_FIELD_MAPPING.put("password", "password");
        USER_FIELD_MAPPING.put("email", "email");
        USER_FIELD_MAPPING.put("phone", "phone");
        USER_FIELD_MAPPING.put("realName", "real_name");
        USER_FIELD_MAPPING.put("avatar", "avatar");
        USER_FIELD_MAPPING.put("status", "status");
        USER_FIELD_MAPPING.put("lastLoginTime", "last_login_time");
        USER_FIELD_MAPPING.put("createTime", "create_time");
        USER_FIELD_MAPPING.put("updateTime", "update_time");
        USER_FIELD_MAPPING.put("createBy", "create_by");
        USER_FIELD_MAPPING.put("updateBy", "update_by");
    }

    /**
     * 将 Java 字段名转换为数据库字段名（角色表）
     * 
     * @param javaFieldName Java 字段名
     * @return 数据库字段名
     */
    public static String getRoleDbFieldName(String javaFieldName) {
        if (javaFieldName == null || javaFieldName.isEmpty()) {
            return "create_time"; // 默认排序字段
        }
        return ROLE_FIELD_MAPPING.getOrDefault(javaFieldName, "create_time");
    }

    /**
     * 将 Java 字段名转换为数据库字段名（用户表）
     * 
     * @param javaFieldName Java 字段名
     * @return 数据库字段名
     */
    public static String getUserDbFieldName(String javaFieldName) {
        if (javaFieldName == null || javaFieldName.isEmpty()) {
            return "create_time"; // 默认排序字段
        }
        return USER_FIELD_MAPPING.getOrDefault(javaFieldName, "create_time");
    }

    /**
     * 验证排序字段是否有效（角色表）
     * 
     * @param fieldName 字段名
     * @return 是否有效
     */
    public static boolean isValidRoleField(String fieldName) {
        return ROLE_FIELD_MAPPING.containsKey(fieldName);
    }

    /**
     * 验证排序字段是否有效（用户表）
     * 
     * @param fieldName 字段名
     * @return 是否有效
     */
    public static boolean isValidUserField(String fieldName) {
        return USER_FIELD_MAPPING.containsKey(fieldName);
    }

    /**
     * 获取所有有效的角色字段名
     * 
     * @return 字段名集合
     */
    public static String[] getValidRoleFields() {
        return ROLE_FIELD_MAPPING.keySet().toArray(new String[0]);
    }

    /**
     * 获取所有有效的用户字段名
     * 
     * @return 字段名集合
     */
    public static String[] getValidUserFields() {
        return USER_FIELD_MAPPING.keySet().toArray(new String[0]);
    }
}
