# Java 学习教程

这个项目包含了一系列Java示例程序，从基础到进阶，帮助你系统地学习Java编程。

## 环境要求

- JDK 8或更高版本
- 命令行工具（如PowerShell、命令提示符）
- 可选：集成开发环境（如IntelliJ IDEA、Eclipse、VS Code）

## 如何编译和运行Java程序

1. 编译Java文件：
   ```
   javac 文件名.java
   ```

2. 运行Java程序：
   ```
   java 类名
   ```

例如，编译和运行HelloWorld.java：
```
javac HelloWorld.java
java HelloWorld
```

## 示例程序列表

### 1. [HelloWorld.java](HelloWorld.java)
- Java程序的基本结构
- main方法
- 控制台输出

### 2. [DataTypes.java](DataTypes.java)
- Java基本数据类型
- 变量声明和初始化
- 类型转换

### 3. [ControlFlow.java](ControlFlow.java)
- if-else条件语句
- switch语句
- for循环
- while和do-while循环
- foreach循环

### 4. [ArraysAndCollections.java](ArraysAndCollections.java)
- 数组
- ArrayList（动态数组）
- HashSet（集合）
- HashMap（键值对映射）

### 5. [OOP.java](OOP.java)
- 类和对象
- 继承
- 封装（访问修饰符）
- 多态

### 6. [InterfaceAndAbstract.java](InterfaceAndAbstract.java)
- 接口
- 抽象类
- 默认方法
- 方法重写

### 7. [ExceptionHandling.java](ExceptionHandling.java)
- try-catch-finally
- 多重catch块
- try-with-resources
- 抛出异常
- 自定义异常

## SpringBoot示例

我们还创建了一个简单的SpringBoot应用程序，位于src目录下。这个应用程序包含：

- 主应用类：`DemoApplication.java`
- REST控制器：`HelloController.java`
- 配置文件：`application.properties`

要运行SpringBoot应用，你需要：
1. 安装Maven
2. 执行命令：`mvn spring-boot:run`
3. 访问：http://localhost:8080/hello

## 学习路径建议

1. 先学习Java基础语法（变量、数据类型、控制流）
2. 掌握面向对象编程概念（类、对象、继承、多态）
3. 学习Java集合框架
4. 理解异常处理
5. 学习Java I/O和多线程
6. 进阶到Java Web开发（Servlet、JSP）
7. 学习Spring框架和SpringBoot

## 推荐资源

- [Oracle Java官方教程](https://docs.oracle.com/javase/tutorial/)
- [Spring官方文档](https://spring.io/guides)
- 《Java核心技术》
- 《Spring实战》

## 实践建议

- 编写代码时，尝试修改示例并观察结果
- 创建自己的小项目来应用所学知识
- 参与开源项目或找同学一起学习
- 定期回顾基础知识 