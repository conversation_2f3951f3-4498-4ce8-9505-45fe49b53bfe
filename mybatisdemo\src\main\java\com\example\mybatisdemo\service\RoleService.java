package com.example.mybatisdemo.service;

import com.example.mybatisdemo.dto.PageResult;
import com.example.mybatisdemo.dto.RoleQueryDTO;
import com.example.mybatisdemo.entity.Role;

import java.util.List;
import java.util.Map;

/**
 * 角色服务接口 - 企业级角色管理
 *
 * 提供完整的角色管理功能，包括：
 * - 分页查询和条件筛选
 * - 角色层次管理
 * - 权限关联管理
 * - 数据权限控制
 * - 缓存优化
 * - 审计日志
 */
public interface RoleService {

    // ==================== 查询功能 ====================

    /**
     * 分页查询角色（企业级查询，支持复杂条件）
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<Role> getRolesByPage(RoleQueryDTO queryDTO);

    /**
     * 查询所有启用的角色（用于下拉选择，带缓存）
     * @return 角色列表
     */
    List<Role> getAllActiveRoles();

    /**
     * 查询所有角色（包含禁用的，管理员使用）
     * @return 角色列表
     */
    List<Role> getAllRoles();

    /**
     * 根据角色层级查询子角色
     * @param parentId 父角色ID
     * @param includeDisabled 是否包含禁用的角色
     * @return 子角色列表
     */
    List<Role> getChildRoles(Long parentId, boolean includeDisabled);

    /**
     * 获取角色树形结构
     * @param includeDisabled 是否包含禁用的角色
     * @return 角色树
     */
    List<Role> getRoleTree(boolean includeDisabled);

    /**
     * 根据用户ID查询用户拥有的角色
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getRolesByUserId(Long userId);

    /**
     * 批量查询角色信息
     * @param roleIds 角色ID列表
     * @return 角色映射 Map<roleId, Role>
     */
    Map<Long, Role> getRolesByIds(List<Long> roleIds);

    // ==================== 基础CRUD ====================

    /**
     * 根据ID查询角色（带缓存）
     * @param id 角色ID
     * @return 角色信息
     */
    Role getRoleById(Long id);

    /**
     * 根据角色编码查询角色（带缓存）
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Role getRoleByCode(String roleCode);

    /**
     * 创建角色（企业级，包含权限验证和审计）
     * @param role 角色信息
     * @param operatorId 操作人ID
     * @return 创建的角色
     */
    Role createRole(Role role);

    /**
     * 更新角色（企业级，包含权限验证和审计）
     * @param role 角色信息
     * @param operatorId 操作人ID
     * @return 更新的角色
     */
    Role updateRole(Role role, Long operatorId);

    /**
     * 删除角色（软删除，包含依赖检查）
     * @param id 角色ID
     * @param operatorId 操作人ID
     * @return 是否删除成功
     */
    boolean deleteRole(Long id, Long operatorId);

    /**
     * 批量删除角色
     * @param ids 角色ID列表
     * @param operatorId 操作人ID
     * @return 删除成功的数量
     */
    int batchDeleteRoles(List<Long> ids, Long operatorId);

    // ==================== 状态管理 ====================

    /**
     * 启用角色
     * @param id 角色ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean enableRole(Long id, Long operatorId);

    /**
     * 禁用角色
     * @param id 角色ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean disableRole(Long id, Long operatorId);

    /**
     * 批量更新角色状态
     * @param ids 角色ID列表
     * @param status 目标状态
     * @param operatorId 操作人ID
     * @return 更新成功的数量
     */
    int batchUpdateRoleStatus(List<Long> ids, Integer status, Long operatorId);

    // ==================== 权限管理 ====================

    /**
     * 为角色分配权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean assignPermissions(Long roleId, List<Long> permissionIds, Long operatorId);

    /**
     * 移除角色权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean removePermissions(Long roleId, List<Long> permissionIds, Long operatorId);

    /**
     * 获取角色的所有权限
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<String> getRolePermissions(Long roleId);

    // ==================== 用户角色关联 ====================

    /**
     * 为用户分配角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds, Long operatorId);

    /**
     * 移除用户角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean removeRolesFromUser(Long userId, List<Long> roleIds, Long operatorId);

    // ==================== 数据统计 ====================

    /**
     * 获取角色统计信息
     * @return 统计数据
     */
    Map<String, Object> getRoleStatistics();

    /**
     * 获取角色使用情况统计
     * @param roleId 角色ID
     * @return 使用统计
     */
    Map<String, Object> getRoleUsageStatistics(Long roleId);

    // ==================== 数据验证 ====================

    /**
     * 检查角色编码是否存在
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isRoleCodeExists(String roleCode, Long excludeId);

    /**
     * 检查角色名称是否存在
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isRoleNameExists(String roleName, Long excludeId);

    /**
     * 检查角色是否可以删除（是否有用户在使用）
     * @param roleId 角色ID
     * @return 是否可以删除
     */
    boolean canDeleteRole(Long roleId);

    // ==================== 缓存管理 ====================

    /**
     * 刷新角色缓存
     * @param roleId 角色ID，null表示刷新所有
     */
    void refreshRoleCache(Long roleId);

    /**
     * 清空角色缓存
     */
    void clearRoleCache();
}
