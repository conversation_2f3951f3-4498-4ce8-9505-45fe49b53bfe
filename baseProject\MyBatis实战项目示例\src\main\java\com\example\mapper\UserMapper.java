package com.example.mapper;

import com.example.entity.User;
import com.example.dto.UserQueryDTO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户数据访问层接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
@Mapper
public interface UserMapper {
    
    // ===== 基本CRUD操作 =====
    
    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    User selectById(@Param("id") Long id);
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    User selectByUsername(@Param("username") String username);
    
    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    User selectByEmail(@Param("email") String email);
    
    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    User selectByPhone(@Param("phone") String phone);
    
    /**
     * 查询所有用户
     * 
     * @return 用户列表
     */
    List<User> selectAll();
    
    /**
     * 根据条件查询用户列表
     * 
     * @param query 查询条件
     * @return 用户列表
     */
    List<User> selectByCondition(UserQueryDTO query);
    
    /**
     * 分页查询用户列表
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 用户列表
     */
    List<User> selectByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 插入用户
     * 
     * @param user 用户信息
     * @return 影响行数
     */
    int insert(User user);
    
    /**
     * 批量插入用户
     * 
     * @param users 用户列表
     * @return 影响行数
     */
    int batchInsert(@Param("users") List<User> users);
    
    /**
     * 更新用户信息
     * 
     * @param user 用户信息
     * @return 影响行数
     */
    int update(User user);
    
    /**
     * 选择性更新用户信息
     * 
     * @param user 用户信息
     * @return 影响行数
     */
    int updateSelective(User user);
    
    /**
     * 根据ID删除用户
     * 
     * @param id 用户ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     * @return 影响行数
     */
    int batchDelete(@Param("ids") List<Long> ids);
    
    /**
     * 逻辑删除用户
     * 
     * @param id 用户ID
     * @param updateUser 更新人ID
     * @return 影响行数
     */
    int logicDelete(@Param("id") Long id, @Param("updateUser") Long updateUser);
    
    // ===== 关联查询 =====
    
    /**
     * 查询用户及其角色信息
     * 
     * @param id 用户ID
     * @return 用户信息（包含角色）
     */
    User selectUserWithRoles(@Param("id") Long id);
    
    /**
     * 查询用户及其权限信息
     * 
     * @param id 用户ID
     * @return 用户信息（包含权限）
     */
    User selectUserWithPermissions(@Param("id") Long id);
    
    /**
     * 查询用户完整信息（包含角色和权限）
     * 
     * @param id 用户ID
     * @return 用户完整信息
     */
    User selectUserWithRolesAndPermissions(@Param("id") Long id);
    
    /**
     * 查询所有用户及其角色信息
     * 
     * @return 用户列表（包含角色）
     */
    List<User> selectUsersWithRoles();
    
    /**
     * 根据角色ID查询用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> selectUsersByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据角色编码查询用户列表
     * 
     * @param roleCode 角色编码
     * @return 用户列表
     */
    List<User> selectUsersByRoleCode(@Param("roleCode") String roleCode);
    
    /**
     * 根据权限编码查询用户列表
     * 
     * @param permissionCode 权限编码
     * @return 用户列表
     */
    List<User> selectUsersByPermissionCode(@Param("permissionCode") String permissionCode);
    
    // ===== 统计查询 =====
    
    /**
     * 统计用户总数
     * 
     * @return 用户总数
     */
    int count();
    
    /**
     * 根据条件统计用户数量
     * 
     * @param query 查询条件
     * @return 用户数量
     */
    int countByCondition(UserQueryDTO query);
    
    /**
     * 根据状态统计用户数量
     * 
     * @param status 状态
     * @return 用户数量
     */
    int countByStatus(@Param("status") Integer status);
    
    /**
     * 统计今日新增用户数量
     * 
     * @return 今日新增用户数量
     */
    int countTodayNew();
    
    /**
     * 统计指定日期范围内的用户数量
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 用户数量
     */
    int countByDateRange(@Param("startDate") LocalDateTime startDate, 
                        @Param("endDate") LocalDateTime endDate);
    
    /**
     * 获取用户统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getUserStatistics();
    
    /**
     * 获取用户年龄分布统计
     * 
     * @return 年龄分布统计
     */
    List<Map<String, Object>> getAgeDistribution();
    
    /**
     * 获取用户性别分布统计
     * 
     * @return 性别分布统计
     */
    List<Map<String, Object>> getGenderDistribution();
    
    /**
     * 获取用户注册趋势统计
     * 
     * @param days 统计天数
     * @return 注册趋势统计
     */
    List<Map<String, Object>> getRegistrationTrend(@Param("days") int days);
    
    // ===== 业务查询 =====
    
    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean existsByUsername(@Param("username") String username, 
                           @Param("excludeId") Long excludeId);
    
    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean existsByEmail(@Param("email") String email, 
                        @Param("excludeId") Long excludeId);
    
    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean existsByPhone(@Param("phone") String phone, 
                        @Param("excludeId") Long excludeId);
    
    /**
     * 更新用户状态
     * 
     * @param id 用户ID
     * @param status 状态
     * @param updateUser 更新人ID
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, 
                    @Param("status") Integer status, 
                    @Param("updateUser") Long updateUser);
    
    /**
     * 更新用户密码
     * 
     * @param id 用户ID
     * @param password 新密码
     * @param salt 盐值
     * @param updateUser 更新人ID
     * @return 影响行数
     */
    int updatePassword(@Param("id") Long id, 
                      @Param("password") String password, 
                      @Param("salt") String salt, 
                      @Param("updateUser") Long updateUser);
    
    /**
     * 更新用户最后登录信息
     * 
     * @param id 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @return 影响行数
     */
    int updateLastLogin(@Param("id") Long id, 
                       @Param("loginTime") LocalDateTime loginTime, 
                       @Param("loginIp") String loginIp);
    
    /**
     * 增加用户登录次数
     * 
     * @param id 用户ID
     * @return 影响行数
     */
    int incrementLoginCount(@Param("id") Long id);
    
    /**
     * 验证用户邮箱
     * 
     * @param id 用户ID
     * @param updateUser 更新人ID
     * @return 影响行数
     */
    int verifyEmail(@Param("id") Long id, @Param("updateUser") Long updateUser);
    
    /**
     * 验证用户手机
     * 
     * @param id 用户ID
     * @param updateUser 更新人ID
     * @return 影响行数
     */
    int verifyPhone(@Param("id") Long id, @Param("updateUser") Long updateUser);
}
