package com.example.mybatisrbac.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 权限查询请求对象
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "权限查询请求对象")
public class PermissionQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权限名称关键字
     */
    @Schema(description = "权限名称关键字", example = "用户")
    private String permissionName;

    /**
     * 权限编码关键字
     */
    @Schema(description = "权限编码关键字", example = "user")
    private String permissionCode;

    /**
     * 权限类型：1-菜单，2-按钮，3-接口
     */
    @Schema(description = "权限类型：1-菜单，2-按钮，3-接口", example = "1")
    private Integer permissionType;

    /**
     * 父权限ID
     */
    @Schema(description = "父权限ID", example = "0")
    private Long parentId;

    /**
     * 权限状态：0-禁用，1-启用
     */
    @Schema(description = "权限状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Long size = 10L;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "sortOrder")
    private String sortField;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @Schema(description = "排序方向：asc-升序，desc-降序", example = "asc")
    private String sortOrder = "asc";
}
