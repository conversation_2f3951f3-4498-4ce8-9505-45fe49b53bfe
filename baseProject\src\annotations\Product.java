package annotations;

import annotations.CustomAnnotations.*;

/**
 * 产品实体类
 * 演示注解在不同场景下的应用
 */
@Entity(name = "Product", tableName = "products", description = "产品信息表")
public class Product {
    
    @Column(name = "id", type = "BIGINT", nullable = false, unique = true)
    private Long id;
    
    @Validate(type = ValidationType.NOT_NULL, message = "产品名称不能为空")
    @Column(name = "name", type = "VARCHAR", length = 100, nullable = false)
    private String name;
    
    @Validate(
        type = ValidationType.CUSTOM,
        pattern = "^\\d+(\\.\\d{1,2})?$",
        message = "价格格式不正确，最多保留两位小数"
    )
    @Column(name = "price", type = "DECIMAL", nullable = false)
    private Double price;
    
    @Validate(type = ValidationType.NOT_NULL, message = "产品类别不能为空")
    @Column(name = "category", type = "VARCHAR", length = 50, nullable = false)
    private String category;
    
    @Validate(
        maxLength = 1000,
        message = "产品描述不能超过1000字符"
    )
    @Column(name = "description", type = "TEXT")
    private String description;
    
    @Column(name = "stock", type = "INT", defaultValue = "0")
    private Integer stock;
    
    @Column(name = "status", type = "VARCHAR", length = 20, defaultValue = "ACTIVE")
    private String status;
    
    @Column(name = "created_at", type = "TIMESTAMP", nullable = false)
    private java.time.LocalDateTime createdAt;
    
    @Column(name = "updated_at", type = "TIMESTAMP")
    private java.time.LocalDateTime updatedAt;
    
    // 构造函数
    public Product() {
        this.createdAt = java.time.LocalDateTime.now();
        this.status = "ACTIVE";
        this.stock = 0;
    }
    
    public Product(String name, Double price, String category) {
        this();
        this.name = name;
        this.price = price;
        this.category = category;
    }
    
    // 业务方法（演示各种注解的使用）
    
    @Log(value = "获取产品详情", level = LogLevel.INFO)
    @Cache(name = "products", key = "#id", expireTime = 1800)
    @ApiVersion("1.0")
    public Product getProductDetails(Long id) {
        System.out.println("从数据库查询产品详情: " + id);
        return this;
    }
    
    @Log(value = "更新产品信息", level = LogLevel.INFO, logArgs = true, logTime = true)
    @RequirePermission(value = {"PRODUCT_UPDATE"}, message = "没有产品更新权限")
    public void updateProduct(String name, Double price, String description) {
        this.name = name;
        this.price = price;
        this.description = description;
        this.updatedAt = java.time.LocalDateTime.now();
        System.out.println("产品信息已更新");
    }
    
    @Log(value = "更新库存", level = LogLevel.INFO)
    @RequirePermission(value = {"INVENTORY_UPDATE"})
    @RateLimit(maxRequests = 50, window = 60, type = RateLimitType.USER)
    public void updateStock(Integer quantity) {
        this.stock = quantity;
        this.updatedAt = java.time.LocalDateTime.now();
        System.out.println("库存已更新为: " + quantity);
    }
    
    @Log(value = "删除产品", level = LogLevel.WARN)
    @RequirePermission(value = {"PRODUCT_DELETE", "ADMIN"}, type = PermissionType.OR)
    public void deleteProduct() {
        this.status = "DELETED";
        this.updatedAt = java.time.LocalDateTime.now();
        System.out.println("产品已删除");
    }
    
    @Retry(maxAttempts = 3, delay = 2000, retryFor = {RuntimeException.class})
    @Async(executor = "productTaskExecutor")
    public void syncToExternalSystem() {
        System.out.println("同步产品信息到外部系统: " + this.name);
        
        // 模拟可能的网络异常
        if (Math.random() < 0.4) {
            throw new RuntimeException("网络连接失败");
        }
        
        System.out.println("同步成功");
    }
    
    @Cache(name = "productStats", key = "#category", expireTime = 3600)
    @Log(value = "统计产品数量", level = LogLevel.DEBUG)
    public int countProductsByCategory(String category) {
        System.out.println("统计类别产品数量: " + category);
        // 模拟数据库查询
        return (int) (Math.random() * 100);
    }
    
    @ApiVersion(value = "1.0", deprecated = true, deprecatedMessage = "请使用getProductRecommendationsV2")
    @RateLimit(maxRequests = 20, window = 60)
    public java.util.List<Product> getProductRecommendations() {
        System.out.println("获取产品推荐（旧版）");
        return java.util.Arrays.asList();
    }
    
    @ApiVersion("2.0")
    @Cache(name = "recommendations", key = "#userId + '_' + #category", expireTime = 1800)
    @RateLimit(maxRequests = 30, window = 60, type = RateLimitType.USER)
    public java.util.List<Product> getProductRecommendationsV2(Long userId, String category) {
        System.out.println("获取产品推荐（新版）- 用户: " + userId + ", 类别: " + category);
        return java.util.Arrays.asList();
    }
    
    @Scheduled(cron = "0 0 1 * * ?")  // 每天凌晨1点执行
    @Log(value = "清理过期产品", level = LogLevel.INFO)
    public void cleanupExpiredProducts() {
        System.out.println("清理过期产品数据");
    }
    
    @Scheduled(fixedRate = 600000)  // 每10分钟执行一次
    @Log(value = "更新产品统计", level = LogLevel.DEBUG)
    public void updateProductStatistics() {
        System.out.println("更新产品统计信息");
    }
    
    @RateLimit(maxRequests = 5, window = 60, type = RateLimitType.IP, message = "价格查询过于频繁")
    @Cache(name = "priceHistory", key = "#productId", expireTime = 7200)
    public java.util.List<Double> getPriceHistory(Long productId) {
        System.out.println("获取产品价格历史: " + productId);
        return java.util.Arrays.asList(99.9, 89.9, 79.9);
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Double getPrice() {
        return price;
    }
    
    public void setPrice(Double price) {
        this.price = price;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStock() {
        return stock;
    }
    
    public void setStock(Integer stock) {
        this.stock = stock;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public java.time.LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(java.time.LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public java.time.LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(java.time.LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", category='" + category + '\'' +
                ", description='" + description + '\'' +
                ", stock=" + stock +
                ", status='" + status + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Product)) return false;
        Product product = (Product) o;
        return id != null && id.equals(product.id);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
