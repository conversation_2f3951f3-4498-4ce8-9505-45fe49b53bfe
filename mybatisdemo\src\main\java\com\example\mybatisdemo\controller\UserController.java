package com.example.mybatisdemo.controller;

import com.example.mybatisdemo.common.result.Result;
import com.example.mybatisdemo.dto.UserCreateDTO;
import com.example.mybatisdemo.dto.UserUpdateDTO;
import com.example.mybatisdemo.entity.User;
import com.example.mybatisdemo.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户管理Controller - 演示MyBatis功能
 */
// @Api(tags = "用户管理", description = "用户相关的API接口")
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 根据ID查询用户
     */
    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    public Result<User> getUserById(
            @PathVariable
            @NotNull(message = "用户ID不能为空")
            @Positive(message = "用户ID必须为正数")
            Long id) {
        User user = userService.getUserById(id);
        return Result.success(user);
    }

    /**
     * 根据用户名查询用户
     */
//    @Operation(summary = "根据用户名查询用户", description = "通过用户名获取用户信息")
    @GetMapping("/username/{username}")
    public Result<User> getUserByUsername(
            @PathVariable String username) {
        User user = userService.getUserByUsername(username);
        if (user == null) {
            return Result.error("用户不存在");
        }
        return Result.success(user);
    }

    /**
     * 查询所有用户
     */
//    @Operation(summary = "查询所有用户", description = "获取系统中所有用户的列表")
    @GetMapping
    public Result<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return Result.success(users);
    }
    
    /**
     * 根据状态查询用户
     */
    @GetMapping("/status/{status}")
    public Result<List<User>> getUsersByStatus(@PathVariable Integer status) {
        List<User> users = userService.getUsersByStatus(status);
        return Result.success(users);
    }
    
    /**
     * 分页查询用户
     */
//    @Operation(summary = "分页查询用户", description = "分页获取用户列表，支持自定义页码和每页数量")
    @GetMapping("/page")
    public Result<Map<String, Object>> getUsersByPage(

            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        
        List<User> users = userService.getUsersByPage(page, size);
        Long total = userService.getTotalUserCount();
        
        Map<String, Object> result = new HashMap<>();
        result.put("users", users);
        result.put("total", total);
        result.put("page", page);
        result.put("size", size);
        result.put("totalPages", (total + size - 1) / size);
        
        return Result.success(result);
    }
    
    /**
     * 搜索用户
     */
    @GetMapping("/search")
    public Result<List<User>> searchUsers(@RequestParam String keyword) {
        List<User> users = userService.searchUsers(keyword);
        return Result.success(users);
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody UserCreateDTO userCreateDTO) {
        // 将DTO转换为实体
        User user = new User();
        BeanUtils.copyProperties(userCreateDTO, user);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        User createdUser = userService.createUser(user);
        return Result.success("用户创建成功", createdUser);
    }

    /**
     * 更新用户信息
     */
//    @Operation(summary = "更新用户信息", description = "根据用户ID更新用户信息")
    @PutMapping("/{id}")
    public Result<User> updateUser(
//            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long id,
//            @Parameter(description = "用户信息", required = true)
            @RequestBody User user) {
        user.setId(id);
        User updatedUser = userService.updateUser(user);
        return Result.success("用户更新成功", updatedUser);
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        boolean success = userService.deleteUser(id);
        if (success) {
            return Result.success("用户删除成功");
        } else {
            return Result.error("用户删除失败");
        }
    }
    
    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    public Result<String> deleteUsers(@RequestBody List<Long> ids) {
        int deletedCount = userService.deleteUsers(ids);
        return Result.success("批量删除成功，删除了 " + deletedCount + " 个用户");
    }
    
    /**
     * 启用用户
     */
    @PutMapping("/{id}/enable")
    public Result<String> enableUser(@PathVariable Long id) {
        boolean success = userService.enableUser(id);
        if (success) {
            return Result.success("用户启用成功");
        } else {
            return Result.error("用户启用失败");
        }
    }

    /**
     * 禁用用户
     */
    @PutMapping("/{id}/disable")
    public Result<String> disableUser(@PathVariable Long id) {
        boolean success = userService.disableUser(id);
        if (success) {
            return Result.success("用户禁用成功");
        } else {
            return Result.error("用户禁用失败");
        }
    }
    
    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalUsers", userService.getTotalUserCount());
        statistics.put("activeUsers", userService.getUserCountByStatus(1));
        statistics.put("inactiveUsers", userService.getUserCountByStatus(0));
        
        return Result.success(statistics);
    }
    
    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public Result<Map<String, Boolean>> checkUsername(@RequestParam String username) {
        boolean exists = userService.isUsernameExists(username);
        Map<String, Boolean> result = new HashMap<>();
        result.put("exists", exists);
        return Result.success(result);
    }
    
    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public Result<Map<String, Boolean>> checkEmail(@RequestParam String email) {
        boolean exists = userService.isEmailExists(email);
        Map<String, Boolean> result = new HashMap<>();
        result.put("exists", exists);
        return Result.success(result);
    }
    
    /**
     * 创建测试用户（用于快速测试）
     */
    @PostMapping("/create-test-user")
    public Result<User> createTestUser() {
        User testUser = User.builder()
            .username("testuser_" + System.currentTimeMillis())
            .password("password123")
            .email("test" + System.currentTimeMillis() + "@example.com")
            .phone("13800138" + String.format("%03d", (int)(Math.random() * 1000)))
            .nickname("测试用户")
            .status(1)
            .build();
        
        User createdUser = userService.createUser(testUser);
        return Result.success("测试用户创建成功", createdUser);
    }
}
