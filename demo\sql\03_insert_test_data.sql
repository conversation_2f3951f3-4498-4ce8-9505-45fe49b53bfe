-- =============================================
-- 测试数据插入脚本
-- 包含各种场景的测试数据
-- =============================================

USE demo_db;

-- 清空现有数据
TRUNCATE TABLE users;

-- 插入测试用户数据
INSERT INTO users (username, email, created_at) VALUES
-- 管理员用户
('admin', '<EMAIL>', '2024-01-01 10:00:00'),
('superuser', '<EMAIL>', '2024-01-02 09:30:00'),

-- 普通用户
('john_doe', '<EMAIL>', '2024-01-15 14:20:00'),
('jane_smith', '<EMAIL>', '2024-01-16 16:45:00'),
('mike_wilson', '<EMAIL>', '2024-01-20 11:15:00'),
('sarah_johnson', '<EMAIL>', '2024-01-25 13:30:00'),

-- 开发者用户
('developer1', '<EMAIL>', '2024-02-01 08:00:00'),
('developer2', '<EMAIL>', '2024-02-01 08:15:00'),
('frontend_dev', '<EMAIL>', '2024-02-05 10:30:00'),
('backend_dev', '<EMAIL>', '2024-02-05 10:45:00'),

-- 测试用户
('test_user1', '<EMAIL>', '2024-02-10 12:00:00'),
('test_user2', '<EMAIL>', '2024-02-10 12:15:00'),
('qa_tester', '<EMAIL>', '2024-02-12 15:20:00'),

-- 客户用户
('customer1', '<EMAIL>', '2024-02-15 09:00:00'),
('customer2', '<EMAIL>', '2024-02-16 10:30:00'),
('vip_customer', '<EMAIL>', '2024-02-18 14:00:00'),

-- 国际用户（测试Unicode）
('用户张三', '<EMAIL>', '2024-02-20 16:30:00'),
('user_李四', '<EMAIL>', '2024-02-21 17:45:00'),
('guest_王五', '<EMAIL>', '2024-02-22 18:20:00'),

-- 最近注册的用户
('new_user1', '<EMAIL>', NOW() - INTERVAL 1 DAY),
('new_user2', '<EMAIL>', NOW() - INTERVAL 2 HOUR),
('latest_user', '<EMAIL>', NOW());

-- 验证插入的数据
SELECT 
    COUNT(*) as total_users,
    MIN(created_at) as earliest_user,
    MAX(created_at) as latest_user
FROM users;

-- 显示所有用户
SELECT 
    id,
    username,
    email,
    created_at
FROM users 
ORDER BY created_at DESC;
