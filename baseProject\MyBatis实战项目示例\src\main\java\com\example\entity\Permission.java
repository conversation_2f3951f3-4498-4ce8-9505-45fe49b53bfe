package com.example.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限实体类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Permission implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 权限ID
     */
    private Long id;
    
    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(min = 2, max = 100, message = "权限名称长度必须在2-100个字符之间")
    private String permissionName;
    
    /**
     * 权限编码
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(min = 2, max = 100, message = "权限编码长度必须在2-100个字符之间")
    @Pattern(regexp = "^[a-z:_]+$", message = "权限编码只能包含小写字母、冒号和下划线")
    private String permissionCode;
    
    /**
     * 权限描述
     */
    @Size(max = 200, message = "权限描述长度不能超过200个字符")
    private String permissionDesc;
    
    /**
     * 资源类型: 1-菜单, 2-按钮, 3-接口
     */
    @NotNull(message = "资源类型不能为空")
    @Min(value = 1, message = "资源类型值不正确")
    @Max(value = 3, message = "资源类型值不正确")
    private Integer resourceType;
    
    /**
     * 父权限ID
     */
    private Long parentId;
    
    /**
     * 权限路径
     */
    @Size(max = 255, message = "权限路径长度不能超过255个字符")
    private String path;
    
    /**
     * 权限URL
     */
    @Size(max = 255, message = "权限URL长度不能超过255个字符")
    private String url;
    
    /**
     * HTTP方法
     */
    @Size(max = 10, message = "HTTP方法长度不能超过10个字符")
    private String method;
    
    /**
     * 图标
     */
    @Size(max = 50, message = "图标长度不能超过50个字符")
    private String icon;
    
    /**
     * 排序顺序
     */
    @Min(value = 0, message = "排序顺序不能为负数")
    private Integer sortOrder;
    
    /**
     * 权限层级
     */
    @Min(value = 1, message = "权限层级不能小于1")
    private Integer level;
    
    /**
     * 是否叶子节点: 0-否, 1-是
     */
    private Integer leaf;
    
    /**
     * 状态: 0-禁用, 1-启用
     */
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人ID
     */
    private Long createUser;
    
    /**
     * 更新人ID
     */
    private Long updateUser;
    
    /**
     * 版本号(乐观锁)
     */
    private Integer version;
    
    /**
     * 是否删除: 0-未删除, 1-已删除
     */
    private Integer deleted;
    
    // ===== 关联属性 =====
    
    /**
     * 子权限列表
     */
    private List<Permission> children;
    
    /**
     * 拥有该权限的角色列表
     */
    private List<Role> roles;
    
    // ===== 业务方法 =====
    
    /**
     * 检查权限是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    /**
     * 检查是否为叶子节点
     */
    public boolean isLeaf() {
        return leaf != null && leaf == 1;
    }
    
    /**
     * 检查是否为菜单权限
     */
    public boolean isMenu() {
        return resourceType != null && resourceType == 1;
    }
    
    /**
     * 检查是否为按钮权限
     */
    public boolean isButton() {
        return resourceType != null && resourceType == 2;
    }
    
    /**
     * 检查是否为接口权限
     */
    public boolean isApi() {
        return resourceType != null && resourceType == 3;
    }
    
    /**
     * 获取资源类型描述
     */
    public String getResourceTypeDesc() {
        if (resourceType == null) {
            return "未知";
        }
        switch (resourceType) {
            case 1: return "菜单";
            case 2: return "按钮";
            case 3: return "接口";
            default: return "未知";
        }
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) {
            return "未知";
        }
        return status == 1 ? "启用" : "禁用";
    }
    
    /**
     * 获取完整路径
     */
    public String getFullPath() {
        if (path == null) {
            return "";
        }
        return path;
    }
    
    @Override
    public String toString() {
        return "Permission{" +
                "id=" + id +
                ", permissionName='" + permissionName + '\'' +
                ", permissionCode='" + permissionCode + '\'' +
                ", resourceType=" + resourceType +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}
