package examples;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * JDBC原生操作示例
 * 展示传统JDBC的使用方式
 */
public class JdbcExample {
    
    private static final String URL = "***********************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "password";
    
    // 查询所有用户 - JDBC方式
    public List<User> findAllUsers() {
        List<User> users = new ArrayList<>();
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            // 1. 获取数据库连接
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            // 2. 准备SQL语句
            String sql = "SELECT id, username, email, created_at FROM users";
            stmt = conn.prepareStatement(sql);
            
            // 3. 执行查询
            rs = stmt.executeQuery();
            
            // 4. 处理结果集
            while (rs.next()) {
                User user = new User();
                user.setId(rs.getLong("id"));
                user.setUsername(rs.getString("username"));
                user.setEmail(rs.getString("email"));
                user.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                users.add(user);
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            // 5. 关闭资源（必须手动关闭）
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        
        return users;
    }
    
    // 插入用户 - JDBC方式
    public boolean insertUser(User user) {
        Connection conn = null;
        PreparedStatement stmt = null;
        
        try {
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            String sql = "INSERT INTO users (username, email, created_at) VALUES (?, ?, ?)";
            stmt = conn.prepareStatement(sql);
            
            // 设置参数
            stmt.setString(1, user.getUsername());
            stmt.setString(2, user.getEmail());
            stmt.setTimestamp(3, Timestamp.valueOf(user.getCreatedAt()));
            
            // 执行更新
            int result = stmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    // 根据ID查询用户 - JDBC方式
    public User findUserById(Long id) {
        Connection conn = null;
        PreparedStatement stmt = null;
        ResultSet rs = null;
        
        try {
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            String sql = "SELECT id, username, email, created_at FROM users WHERE id = ?";
            stmt = conn.prepareStatement(sql);
            stmt.setLong(1, id);
            
            rs = stmt.executeQuery();
            
            if (rs.next()) {
                User user = new User();
                user.setId(rs.getLong("id"));
                user.setUsername(rs.getString("username"));
                user.setEmail(rs.getString("email"));
                user.setCreatedAt(rs.getTimestamp("created_at").toLocalDateTime());
                return user;
            }
            
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        
        return null;
    }
}
