-- MyBatis实战项目初始化数据库脚本
-- 版本: V1
-- 描述: 创建用户管理系统的基础表结构

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ===== 用户表 =====
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `email` VARCHAR(100) NOT NULL COMMENT '邮箱地址',
    `password` VARCHAR(255) NOT NULL COMMENT '密码(加密后)',
    `salt` VARCHAR(32) DEFAULT NULL COMMENT '密码盐值',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
    `gender` TINYINT(1) DEFAULT 0 COMMENT '性别: 0-未知, 1-男, 2-女',
    `birthday` DATE DEFAULT NULL COMMENT '生日',
    `age` INT(3) DEFAULT NULL COMMENT '年龄',
    `address` VARCHAR(255) DEFAULT NULL COMMENT '地址',
    `bio` TEXT DEFAULT NULL COMMENT '个人简介',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) DEFAULT NULL COMMENT '最后登录IP',
    `login_count` INT(11) DEFAULT 0 COMMENT '登录次数',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用, 2-锁定',
    `email_verified` TINYINT(1) DEFAULT 0 COMMENT '邮箱是否验证: 0-未验证, 1-已验证',
    `phone_verified` TINYINT(1) DEFAULT 0 COMMENT '手机是否验证: 0-未验证, 1-已验证',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    `update_user` BIGINT(20) DEFAULT NULL COMMENT '更新人ID',
    `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号(乐观锁)',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_phone` (`phone`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ===== 角色表 =====
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
    `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码',
    `role_desc` VARCHAR(200) DEFAULT NULL COMMENT '角色描述',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序顺序',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    `update_user` BIGINT(20) DEFAULT NULL COMMENT '更新人ID',
    `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号(乐观锁)',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_name` (`role_name`),
    UNIQUE KEY `uk_role_code` (`role_code`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- ===== 权限表 =====
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
    `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
    `permission_code` VARCHAR(100) NOT NULL COMMENT '权限编码',
    `permission_desc` VARCHAR(200) DEFAULT NULL COMMENT '权限描述',
    `resource_type` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '资源类型: 1-菜单, 2-按钮, 3-接口',
    `parent_id` BIGINT(20) DEFAULT 0 COMMENT '父权限ID',
    `path` VARCHAR(255) DEFAULT NULL COMMENT '权限路径',
    `url` VARCHAR(255) DEFAULT NULL COMMENT '权限URL',
    `method` VARCHAR(10) DEFAULT NULL COMMENT 'HTTP方法',
    `icon` VARCHAR(50) DEFAULT NULL COMMENT '图标',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序顺序',
    `level` INT(11) DEFAULT 1 COMMENT '权限层级',
    `leaf` TINYINT(1) DEFAULT 1 COMMENT '是否叶子节点: 0-否, 1-是',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    `update_user` BIGINT(20) DEFAULT NULL COMMENT '更新人ID',
    `version` INT(11) NOT NULL DEFAULT 1 COMMENT '版本号(乐观锁)',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除, 1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_permission_code` (`permission_code`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_resource_type` (`resource_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- ===== 用户角色关联表 =====
DROP TABLE IF EXISTS `user_role`;
CREATE TABLE `user_role` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role_id` (`role_id`),
    CONSTRAINT `fk_user_role_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_role_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- ===== 角色权限关联表 =====
DROP TABLE IF EXISTS `role_permission`;
CREATE TABLE `role_permission` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
    `permission_id` BIGINT(20) NOT NULL COMMENT '权限ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
    KEY `idx_role_id` (`role_id`),
    KEY `idx_permission_id` (`permission_id`),
    CONSTRAINT `fk_role_permission_role` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_role_permission_permission` FOREIGN KEY (`permission_id`) REFERENCES `permission` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- ===== 用户登录日志表 =====
DROP TABLE IF EXISTS `user_login_log`;
CREATE TABLE `user_login_log` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `login_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `login_ip` VARCHAR(45) NOT NULL COMMENT '登录IP',
    `login_location` VARCHAR(100) DEFAULT NULL COMMENT '登录地点',
    `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    `browser` VARCHAR(50) DEFAULT NULL COMMENT '浏览器',
    `os` VARCHAR(50) DEFAULT NULL COMMENT '操作系统',
    `device` VARCHAR(50) DEFAULT NULL COMMENT '设备类型',
    `login_status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '登录状态: 0-失败, 1-成功',
    `login_message` VARCHAR(255) DEFAULT NULL COMMENT '登录消息',
    `session_id` VARCHAR(128) DEFAULT NULL COMMENT '会话ID',
    `logout_time` DATETIME DEFAULT NULL COMMENT '退出时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_login_ip` (`login_ip`),
    KEY `idx_login_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';

-- ===== 系统配置表 =====
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT DEFAULT NULL COMMENT '配置值',
    `config_desc` VARCHAR(200) DEFAULT NULL COMMENT '配置描述',
    `config_type` VARCHAR(20) DEFAULT 'string' COMMENT '配置类型: string, number, boolean, json',
    `config_group` VARCHAR(50) DEFAULT 'default' COMMENT '配置分组',
    `is_system` TINYINT(1) DEFAULT 0 COMMENT '是否系统配置: 0-否, 1-是',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序顺序',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_user` BIGINT(20) DEFAULT NULL COMMENT '创建人ID',
    `update_user` BIGINT(20) DEFAULT NULL COMMENT '更新人ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    KEY `idx_config_group` (`config_group`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ===== 操作日志表 =====
DROP TABLE IF EXISTS `operation_log`;
CREATE TABLE `operation_log` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    `user_id` BIGINT(20) DEFAULT NULL COMMENT '操作用户ID',
    `username` VARCHAR(50) DEFAULT NULL COMMENT '操作用户名',
    `operation` VARCHAR(100) NOT NULL COMMENT '操作名称',
    `method` VARCHAR(200) NOT NULL COMMENT '操作方法',
    `params` TEXT DEFAULT NULL COMMENT '操作参数',
    `result` TEXT DEFAULT NULL COMMENT '操作结果',
    `ip` VARCHAR(45) DEFAULT NULL COMMENT '操作IP',
    `location` VARCHAR(100) DEFAULT NULL COMMENT '操作地点',
    `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    `execution_time` BIGINT(20) DEFAULT NULL COMMENT '执行时间(毫秒)',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '操作状态: 0-失败, 1-成功',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_operation` (`operation`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
