@echo off
set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

mkdir target\classes\com\example\demo\controller 2>nul

"%JAVA_HOME%\bin\javac" -encoding UTF-8 -cp ".;target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.0\spring-boot-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.0\spring-boot-autoconfigure-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.20\spring-web-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.20\spring-beans-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.20\spring-context-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.20\spring-aop-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.20\spring-expression-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.20\spring-core-5.3.20.jar" -d target\classes src\main\java\com\example\demo\DemoApplication.java

"%JAVA_HOME%\bin\javac" -encoding UTF-8 -cp ".;target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.0\spring-boot-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.0\spring-boot-autoconfigure-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.20\spring-web-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.20\spring-beans-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.20\spring-context-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.20\spring-aop-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.20\spring-expression-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.20\spring-core-5.3.20.jar" -d target\classes src\main\java\com\example\demo\controller\HelloController.java

echo Compilation completed. 