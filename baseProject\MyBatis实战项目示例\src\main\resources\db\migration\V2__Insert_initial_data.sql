-- MyBatis实战项目初始化数据脚本
-- 版本: V2
-- 描述: 插入系统初始化数据

-- 设置字符集
SET NAMES utf8mb4;

-- ===== 插入初始用户数据 =====
INSERT INTO `user` (
    `id`, `username`, `email`, `password`, `salt`, `nickname`, `phone`, 
    `gender`, `age`, `status`, `email_verified`, `phone_verified`, 
    `create_time`, `update_time`, `create_user`, `update_user`
) VALUES 
-- 超级管理员
(1, 'admin', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: admin123
 'salt_admin_001', '系统管理员', '13800138000', 1, 30, 1, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 普通管理员
(2, 'manager', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: manager123
 'salt_manager_001', '部门经理', '13800138001', 1, 35, 1, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 普通用户
(3, 'user1', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: user123
 'salt_user1_001', '张三', '13800138002', 1, 25, 1, 1, 0, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(4, 'user2', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: user123
 'salt_user2_001', '李四', '13800138003', 2, 28, 1, 1, 0, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(5, 'user3', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: user123
 'salt_user3_001', '王五', '13800138004', 1, 32, 1, 0, 0, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 测试用户
(6, 'test', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: test123
 'salt_test_001', '测试用户', '13800138005', 0, 20, 1, 0, 0, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 禁用用户
(7, 'disabled', '<EMAIL>', 
 '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', -- 密码: disabled123
 'salt_disabled_001', '禁用用户', '13800138006', 1, 40, 0, 0, 0, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1);

-- ===== 插入角色数据 =====
INSERT INTO `role` (
    `id`, `role_name`, `role_code`, `role_desc`, `sort_order`, `status`, 
    `create_time`, `update_time`, `create_user`, `update_user`
) VALUES 
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(2, '系统管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 2, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(3, '部门经理', 'MANAGER', '部门经理，拥有部门管理权限', 3, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(4, '普通用户', 'USER', '普通用户，拥有基本功能权限', 4, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(5, '访客用户', 'GUEST', '访客用户，只有查看权限', 5, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

(6, '测试角色', 'TEST', '测试角色，用于系统测试', 6, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1);

-- ===== 插入权限数据 =====
INSERT INTO `permission` (
    `id`, `permission_name`, `permission_code`, `permission_desc`, `resource_type`, 
    `parent_id`, `path`, `url`, `method`, `icon`, `sort_order`, `level`, `leaf`, `status`, 
    `create_time`, `update_time`, `create_user`, `update_user`
) VALUES 
-- 系统管理模块
(1, '系统管理', 'system', '系统管理模块', 1, 0, '/system', NULL, NULL, 'system', 1, 1, 0, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 用户管理
(2, '用户管理', 'system:user', '用户管理功能', 1, 1, '/system/user', '/user', 'GET', 'user', 1, 2, 0, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(3, '用户查询', 'system:user:list', '查询用户列表', 2, 2, NULL, '/user/list', 'GET', NULL, 1, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(4, '用户新增', 'system:user:add', '新增用户', 2, 2, NULL, '/user/add', 'POST', NULL, 2, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(5, '用户修改', 'system:user:edit', '修改用户信息', 2, 2, NULL, '/user/edit', 'PUT', NULL, 3, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(6, '用户删除', 'system:user:delete', '删除用户', 2, 2, NULL, '/user/delete', 'DELETE', NULL, 4, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 角色管理
(7, '角色管理', 'system:role', '角色管理功能', 1, 1, '/system/role', '/role', 'GET', 'role', 2, 2, 0, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(8, '角色查询', 'system:role:list', '查询角色列表', 2, 7, NULL, '/role/list', 'GET', NULL, 1, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(9, '角色新增', 'system:role:add', '新增角色', 2, 7, NULL, '/role/add', 'POST', NULL, 2, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(10, '角色修改', 'system:role:edit', '修改角色信息', 2, 7, NULL, '/role/edit', 'PUT', NULL, 3, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(11, '角色删除', 'system:role:delete', '删除角色', 2, 7, NULL, '/role/delete', 'DELETE', NULL, 4, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 权限管理
(12, '权限管理', 'system:permission', '权限管理功能', 1, 1, '/system/permission', '/permission', 'GET', 'permission', 3, 2, 0, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(13, '权限查询', 'system:permission:list', '查询权限列表', 2, 12, NULL, '/permission/list', 'GET', NULL, 1, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(14, '权限新增', 'system:permission:add', '新增权限', 2, 12, NULL, '/permission/add', 'POST', NULL, 2, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(15, '权限修改', 'system:permission:edit', '修改权限信息', 2, 12, NULL, '/permission/edit', 'PUT', NULL, 3, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(16, '权限删除', 'system:permission:delete', '删除权限', 2, 12, NULL, '/permission/delete', 'DELETE', NULL, 4, 3, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 系统监控模块
(17, '系统监控', 'monitor', '系统监控模块', 1, 0, '/monitor', NULL, NULL, 'monitor', 2, 1, 0, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(18, '在线用户', 'monitor:online', '在线用户监控', 1, 17, '/monitor/online', '/monitor/online', 'GET', 'online', 1, 2, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(19, '登录日志', 'monitor:loginlog', '登录日志查看', 1, 17, '/monitor/loginlog', '/monitor/loginlog', 'GET', 'loginlog', 2, 2, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(20, '操作日志', 'monitor:operlog', '操作日志查看', 1, 17, '/monitor/operlog', '/monitor/operlog', 'GET', 'operlog', 3, 2, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),

-- 个人中心
(21, '个人中心', 'profile', '个人中心功能', 1, 0, '/profile', '/profile', 'GET', 'profile', 3, 1, 0, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(22, '个人信息', 'profile:info', '查看个人信息', 2, 21, NULL, '/profile/info', 'GET', NULL, 1, 2, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
(23, '修改密码', 'profile:password', '修改个人密码', 2, 21, NULL, '/profile/password', 'PUT', NULL, 2, 2, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1);

-- ===== 插入用户角色关联数据 =====
INSERT INTO `user_role` (`user_id`, `role_id`, `create_time`, `create_user`) VALUES 
(1, 1, '2024-01-01 00:00:00', 1), -- admin -> 超级管理员
(2, 2, '2024-01-01 00:00:00', 1), -- manager -> 系统管理员
(2, 3, '2024-01-01 00:00:00', 1), -- manager -> 部门经理
(3, 4, '2024-01-01 00:00:00', 1), -- user1 -> 普通用户
(4, 4, '2024-01-01 00:00:00', 1), -- user2 -> 普通用户
(5, 4, '2024-01-01 00:00:00', 1), -- user3 -> 普通用户
(6, 6, '2024-01-01 00:00:00', 1); -- test -> 测试角色

-- ===== 插入角色权限关联数据 =====
INSERT INTO `role_permission` (`role_id`, `permission_id`, `create_time`, `create_user`) VALUES 
-- 超级管理员拥有所有权限
(1, 1, '2024-01-01 00:00:00', 1), (1, 2, '2024-01-01 00:00:00', 1), (1, 3, '2024-01-01 00:00:00', 1),
(1, 4, '2024-01-01 00:00:00', 1), (1, 5, '2024-01-01 00:00:00', 1), (1, 6, '2024-01-01 00:00:00', 1),
(1, 7, '2024-01-01 00:00:00', 1), (1, 8, '2024-01-01 00:00:00', 1), (1, 9, '2024-01-01 00:00:00', 1),
(1, 10, '2024-01-01 00:00:00', 1), (1, 11, '2024-01-01 00:00:00', 1), (1, 12, '2024-01-01 00:00:00', 1),
(1, 13, '2024-01-01 00:00:00', 1), (1, 14, '2024-01-01 00:00:00', 1), (1, 15, '2024-01-01 00:00:00', 1),
(1, 16, '2024-01-01 00:00:00', 1), (1, 17, '2024-01-01 00:00:00', 1), (1, 18, '2024-01-01 00:00:00', 1),
(1, 19, '2024-01-01 00:00:00', 1), (1, 20, '2024-01-01 00:00:00', 1), (1, 21, '2024-01-01 00:00:00', 1),
(1, 22, '2024-01-01 00:00:00', 1), (1, 23, '2024-01-01 00:00:00', 1),

-- 系统管理员权限
(2, 1, '2024-01-01 00:00:00', 1), (2, 2, '2024-01-01 00:00:00', 1), (2, 3, '2024-01-01 00:00:00', 1),
(2, 4, '2024-01-01 00:00:00', 1), (2, 5, '2024-01-01 00:00:00', 1), (2, 7, '2024-01-01 00:00:00', 1),
(2, 8, '2024-01-01 00:00:00', 1), (2, 9, '2024-01-01 00:00:00', 1), (2, 10, '2024-01-01 00:00:00', 1),
(2, 17, '2024-01-01 00:00:00', 1), (2, 18, '2024-01-01 00:00:00', 1), (2, 19, '2024-01-01 00:00:00', 1),
(2, 20, '2024-01-01 00:00:00', 1), (2, 21, '2024-01-01 00:00:00', 1), (2, 22, '2024-01-01 00:00:00', 1),
(2, 23, '2024-01-01 00:00:00', 1),

-- 部门经理权限
(3, 2, '2024-01-01 00:00:00', 1), (3, 3, '2024-01-01 00:00:00', 1), (3, 5, '2024-01-01 00:00:00', 1),
(3, 7, '2024-01-01 00:00:00', 1), (3, 8, '2024-01-01 00:00:00', 1), (3, 17, '2024-01-01 00:00:00', 1),
(3, 18, '2024-01-01 00:00:00', 1), (3, 19, '2024-01-01 00:00:00', 1), (3, 20, '2024-01-01 00:00:00', 1),
(3, 21, '2024-01-01 00:00:00', 1), (3, 22, '2024-01-01 00:00:00', 1), (3, 23, '2024-01-01 00:00:00', 1),

-- 普通用户权限
(4, 21, '2024-01-01 00:00:00', 1), (4, 22, '2024-01-01 00:00:00', 1), (4, 23, '2024-01-01 00:00:00', 1),

-- 访客用户权限
(5, 21, '2024-01-01 00:00:00', 1), (5, 22, '2024-01-01 00:00:00', 1),

-- 测试角色权限
(6, 21, '2024-01-01 00:00:00', 1), (6, 22, '2024-01-01 00:00:00', 1), (6, 23, '2024-01-01 00:00:00', 1);

-- ===== 插入系统配置数据 =====
INSERT INTO `system_config` (
    `config_key`, `config_value`, `config_desc`, `config_type`, `config_group`, 
    `is_system`, `sort_order`, `status`, `create_time`, `update_time`, `create_user`, `update_user`
) VALUES 
('system.name', 'MyBatis实战项目', '系统名称', 'string', 'system', 1, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('system.version', '1.0.0', '系统版本', 'string', 'system', 1, 2, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('system.copyright', '© 2024 MyBatis Demo', '版权信息', 'string', 'system', 1, 3, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('user.default.password', 'user123', '用户默认密码', 'string', 'user', 1, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('user.password.min.length', '6', '密码最小长度', 'number', 'user', 1, 2, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('user.login.max.attempts', '5', '登录最大尝试次数', 'number', 'security', 1, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('user.session.timeout', '30', '会话超时时间(分钟)', 'number', 'security', 1, 2, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('file.upload.max.size', '10485760', '文件上传最大大小(字节)', 'number', 'file', 1, 1, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1),
('file.upload.allowed.types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx', '允许上传的文件类型', 'string', 'file', 1, 2, 1, 
 '2024-01-01 00:00:00', '2024-01-01 00:00:00', 1, 1);

-- 提交事务
COMMIT;
