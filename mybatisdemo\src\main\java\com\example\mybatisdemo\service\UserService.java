package com.example.mybatisdemo.service;

import com.example.mybatisdemo.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 根据ID查询用户
     * @param id 用户ID
     * @return 用户信息
     */
    User getUserById(Long id);
    
    /**
     * 根据用户名查询用户
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);
    
    /**
     * 查询所有用户
     * @return 用户列表
     */
    List<User> getAllUsers();
    
    /**
     * 根据状态查询用户
     * @param status 用户状态
     * @return 用户列表
     */
    List<User> getUsersByStatus(Integer status);
    
    /**
     * 分页查询用户
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 用户列表
     */
    List<User> getUsersByPage(Integer page, Integer size);
    
    /**
     * 根据关键字搜索用户
     * @param keyword 关键字
     * @return 用户列表
     */
    List<User> searchUsers(String keyword);
    
    /**
     * 创建用户
     * @param user 用户信息
     * @return 创建的用户
     */
    User createUser(User user);
    
    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新的用户
     */
    User updateUser(User user);
    
    /**
     * 根据ID删除用户
     * @param id 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long id);
    
    /**
     * 批量删除用户
     * @param ids 用户ID列表
     * @return 删除的用户数量
     */
    int deleteUsers(List<Long> ids);
    
    /**
     * 统计用户总数
     * @return 用户总数
     */
    Long getTotalUserCount();
    
    /**
     * 根据状态统计用户数
     * @param status 用户状态
     * @return 用户数量
     */
    Long getUserCountByStatus(Integer status);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean isUsernameExists(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean isEmailExists(String email);
    
    /**
     * 启用用户
     * @param id 用户ID
     * @return 是否成功
     */
    boolean enableUser(Long id);
    
    /**
     * 禁用用户
     * @param id 用户ID
     * @return 是否成功
     */
    boolean disableUser(Long id);
}
