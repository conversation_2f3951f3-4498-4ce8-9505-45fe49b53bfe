package opptest;

public class Dog extends  Animal {
    private final String breed;
    public Dog(String name, int age, String breed) {
        super(name, age);
        this.breed = breed;
    }
    @Override
    public void eat() {
        System.out.println(name + "正在吃狗粮");
    }
    public void bark() {
        System.out.println(name + "正在汪汪叫");
    }
    public String getBreed() {
        return breed;
    }
    public void setBreed(String breed) {
        System.out.println(name +"breed");
    }
}
