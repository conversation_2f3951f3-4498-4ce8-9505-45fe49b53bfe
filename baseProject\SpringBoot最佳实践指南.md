# Spring Boot 最佳实践指南

## 📚 目录

1. [项目结构最佳实践](#项目结构最佳实践)
2. [配置管理最佳实践](#配置管理最佳实践)
3. [依赖注入最佳实践](#依赖注入最佳实践)
4. [异常处理最佳实践](#异常处理最佳实践)
5. [日志管理最佳实践](#日志管理最佳实践)
6. [数据访问最佳实践](#数据访问最佳实践)
7. [安全最佳实践](#安全最佳实践)
8. [性能优化最佳实践](#性能优化最佳实践)
9. [测试最佳实践](#测试最佳实践)
10. [部署最佳实践](#部署最佳实践)

---

## 项目结构最佳实践

### 1. 标准项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/
│   │       └── example/
│   │           ├── Application.java                 # 主启动类
│   │           ├── config/                          # 配置类
│   │           │   ├── WebConfig.java
│   │           │   ├── SecurityConfig.java
│   │           │   └── DatabaseConfig.java
│   │           ├── controller/                      # 控制层
│   │           │   ├── UserController.java
│   │           │   └── ProductController.java
│   │           ├── service/                         # 业务逻辑层
│   │           │   ├── UserService.java
│   │           │   └── impl/
│   │           │       └── UserServiceImpl.java
│   │           ├── repository/                      # 数据访问层
│   │           │   ├── UserRepository.java
│   │           │   └── ProductRepository.java
│   │           ├── entity/                          # 实体类
│   │           │   ├── User.java
│   │           │   └── Product.java
│   │           ├── dto/                             # 数据传输对象
│   │           │   ├── UserDTO.java
│   │           │   └── CreateUserRequest.java
│   │           ├── vo/                              # 视图对象
│   │           │   └── UserVO.java
│   │           ├── enums/                           # 枚举类
│   │           │   └── UserStatus.java
│   │           ├── exception/                       # 异常类
│   │           │   ├── BusinessException.java
│   │           │   └── GlobalExceptionHandler.java
│   │           ├── util/                            # 工具类
│   │           │   ├── DateUtil.java
│   │           │   └── StringUtil.java
│   │           ├── aspect/                          # 切面类
│   │           │   └── LoggingAspect.java
│   │           └── annotation/                      # 自定义注解
│   │               └── Log.java
│   └── resources/
│       ├── application.yml                          # 主配置文件
│       ├── application-dev.yml                      # 开发环境配置
│       ├── application-prod.yml                     # 生产环境配置
│       ├── static/                                  # 静态资源
│       ├── templates/                               # 模板文件
│       └── db/
│           └── migration/                           # 数据库迁移脚本
└── test/
    └── java/
        └── com/
            └── example/
                ├── ApplicationTests.java
                ├── controller/
                ├── service/
                └── repository/
```

### 2. 包命名规范

```java
// 基础包名：公司域名倒序 + 项目名
com.company.projectname

// 具体示例
com.example.usermanagement.controller
com.example.usermanagement.service
com.example.usermanagement.repository
```

### 3. 类命名规范

```java
// 控制器
@RestController
public class UserController {
    // RESTful API控制器
}

@Controller
public class UserViewController {
    // 视图控制器
}

// 服务类
public interface UserService {
    // 服务接口
}

@Service
public class UserServiceImpl implements UserService {
    // 服务实现类
}

// 数据访问层
public interface UserRepository extends JpaRepository<User, Long> {
    // JPA Repository
}

@Repository
public class UserRepositoryImpl {
    // 自定义Repository实现
}

// 实体类
@Entity
@Table(name = "users")
public class User {
    // 实体类
}

// DTO类
public class UserDTO {
    // 数据传输对象
}

public class CreateUserRequest {
    // 请求对象
}

public class UserResponse {
    // 响应对象
}
```

---

## 配置管理最佳实践

### 1. 配置文件组织

#### 主配置文件 (application.yml)

```yaml
# 应用基本信息
spring:
  application:
    name: user-management-system
  profiles:
    active: @spring.profiles.active@  # 使用Maven Profile

# 通用配置
server:
  port: 8080
  servlet:
    context-path: /api

# 日志配置
logging:
  level:
    com.example: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

#### 环境特定配置

```yaml
# application-dev.yml (开发环境)
spring:
  datasource:
    url: ************************************
    username: dev_user
    password: dev_password
  
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

logging:
  level:
    com.example: DEBUG
    org.springframework.web: DEBUG

---
# application-test.yml (测试环境)
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop

---
# application-prod.yml (生产环境)
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:demo}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.example: WARN
  file:
    name: logs/application.log
```

### 2. 配置属性类

```java
@ConfigurationProperties(prefix = "app")
@Component
@Data
@Validated
public class AppProperties {
    
    @NotBlank
    private String name;
    
    @NotBlank
    private String version;
    
    @Valid
    private Security security = new Security();
    
    @Valid
    private Cache cache = new Cache();
    
    @Data
    @Validated
    public static class Security {
        
        @NotBlank
        private String secretKey;
        
        @Min(300)
        @Max(86400)
        private int tokenExpiration = 3600; // 默认1小时
        
        private boolean enabled = true;
    }
    
    @Data
    @Validated
    public static class Cache {
        
        @NotBlank
        private String type = "redis";
        
        @Min(60)
        private int defaultExpiration = 3600; // 默认1小时
        
        private Map<String, Integer> expirations = new HashMap<>();
    }
}
```

### 3. 配置类最佳实践

```java
@Configuration
@EnableConfigurationProperties(AppProperties.class)
@ConditionalOnProperty(name = "app.cache.enabled", havingValue = "true", matchIfMissing = true)
public class CacheConfig {
    
    private final AppProperties appProperties;
    
    public CacheConfig(AppProperties appProperties) {
        this.appProperties = appProperties;
    }
    
    @Bean
    @ConditionalOnMissingBean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofSeconds(appProperties.getCache().getDefaultExpiration()))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new Jackson2JsonRedisSerializer<>(Object.class)));
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        appProperties.getCache().getExpirations().forEach((cacheName, expiration) -> {
            cacheConfigurations.put(cacheName, defaultConfig.entryTtl(Duration.ofSeconds(expiration)));
        });
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
}
```

---

## 依赖注入最佳实践

### 1. 构造器注入（推荐）

```java
@Service
public class UserService {
    
    // 使用final确保依赖不可变
    private final UserRepository userRepository;
    private final EmailService emailService;
    private final PasswordEncoder passwordEncoder;
    
    // 构造器注入，Spring 4.3+可以省略@Autowired
    public UserService(UserRepository userRepository, 
                      EmailService emailService, 
                      PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.emailService = emailService;
        this.passwordEncoder = passwordEncoder;
    }
    
    public User createUser(CreateUserRequest request) {
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        
        User savedUser = userRepository.save(user);
        emailService.sendWelcomeEmail(savedUser.getEmail());
        
        return savedUser;
    }
}
```

### 2. 避免循环依赖

```java
// 错误示例：循环依赖
@Service
public class UserService {
    @Autowired
    private OrderService orderService; // UserService依赖OrderService
}

@Service
public class OrderService {
    @Autowired
    private UserService userService; // OrderService依赖UserService
}

// 正确示例：通过事件解耦
@Service
public class UserService {
    
    private final ApplicationEventPublisher eventPublisher;
    
    public UserService(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }
    
    public User createUser(CreateUserRequest request) {
        User user = userRepository.save(new User(request));
        
        // 发布用户创建事件，而不是直接调用OrderService
        eventPublisher.publishEvent(new UserCreatedEvent(user));
        
        return user;
    }
}

@Component
public class UserEventListener {
    
    private final OrderService orderService;
    
    public UserEventListener(OrderService orderService) {
        this.orderService = orderService;
    }
    
    @EventListener
    public void handleUserCreated(UserCreatedEvent event) {
        orderService.initializeUserOrders(event.getUser());
    }
}
```

### 3. 可选依赖处理

```java
@Service
public class UserService {
    
    private final UserRepository userRepository;
    private final Optional<NotificationService> notificationService;
    
    public UserService(UserRepository userRepository, 
                      @Autowired(required = false) NotificationService notificationService) {
        this.userRepository = userRepository;
        this.notificationService = Optional.ofNullable(notificationService);
    }
    
    public User createUser(CreateUserRequest request) {
        User user = userRepository.save(new User(request));
        
        // 安全地使用可选依赖
        notificationService.ifPresent(service -> 
            service.sendNotification(user.getEmail(), "Welcome!"));
        
        return user;
    }
}
```

---

## 异常处理最佳实践

### 1. 全局异常处理器

```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {} - URI: {}", e.getMessage(), request.getRequestURI());
        
        ErrorResponse error = ErrorResponse.builder()
            .code(e.getErrorCode())
            .message(e.getMessage())
            .timestamp(LocalDateTime.now())
            .path(request.getRequestURI())
            .build();
        
        return ResponseEntity.badRequest().body(error);
    }
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(MethodArgumentNotValidException e, HttpServletRequest request) {
        log.warn("参数验证失败: {} - URI: {}", e.getMessage(), request.getRequestURI());
        
        Map<String, String> fieldErrors = new HashMap<>();
        e.getBindingResult().getFieldErrors().forEach(error -> 
            fieldErrors.put(error.getField(), error.getDefaultMessage()));
        
        ErrorResponse error = ErrorResponse.builder()
            .code("VALIDATION_ERROR")
            .message("参数验证失败")
            .details(fieldErrors)
            .timestamp(LocalDateTime.now())
            .path(request.getRequestURI())
            .build();
        
        return ResponseEntity.badRequest().body(error);
    }
    
    /**
     * 处理资源不存在异常
     */
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleEntityNotFoundException(EntityNotFoundException e, HttpServletRequest request) {
        log.warn("资源不存在: {} - URI: {}", e.getMessage(), request.getRequestURI());
        
        ErrorResponse error = ErrorResponse.builder()
            .code("RESOURCE_NOT_FOUND")
            .message(e.getMessage())
            .timestamp(LocalDateTime.now())
            .path(request.getRequestURI())
            .build();
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }
    
    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception e, HttpServletRequest request) {
        log.error("系统异常: {} - URI: {}", e.getMessage(), request.getRequestURI(), e);
        
        ErrorResponse error = ErrorResponse.builder()
            .code("INTERNAL_SERVER_ERROR")
            .message("系统内部错误，请稍后重试")
            .timestamp(LocalDateTime.now())
            .path(request.getRequestURI())
            .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
```

### 2. 自定义业务异常

```java
public class BusinessException extends RuntimeException {
    
    private final String errorCode;
    
    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public BusinessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    // 静态工厂方法
    public static BusinessException userNotFound(Long userId) {
        return new BusinessException("USER_NOT_FOUND", "用户不存在: " + userId);
    }
    
    public static BusinessException userAlreadyExists(String username) {
        return new BusinessException("USER_ALREADY_EXISTS", "用户名已存在: " + username);
    }
    
    public static BusinessException invalidPassword() {
        return new BusinessException("INVALID_PASSWORD", "密码格式不正确");
    }
}
```

### 3. 错误响应对象

```java
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {
    
    private String code;
    private String message;
    private Object details;
    private LocalDateTime timestamp;
    private String path;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
}
```
