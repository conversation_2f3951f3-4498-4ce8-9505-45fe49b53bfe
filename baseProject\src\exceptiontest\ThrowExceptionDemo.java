package exceptiontest;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;

public class ThrowExceptionDemo {
    public static  void validateAge(int age) throws IllegalArgumentException{
        if (age < 0 || age > 150) {
            throw new IllegalArgumentException("年龄必须在0-150之间，当前值: " + age);
        }
        System.out.println("年龄验证通过: " + age);
    }
    public static void readFile(String filename) throws IOException {
        if (filename == null || filename.trim().isEmpty()) {
            throw new IOException("文件名不能为空");
        }

        File file = new File(filename);
        if (!file.exists()) {
            throw new FileNotFoundException("文件不存在: " + filename);
        }

        // 文件读取逻辑...
    }
    public static void main(String[] args) {
        try {
            validateAge(-5);
        } catch (IllegalArgumentException e) {
            System.out.println("参数异常: " + e.getMessage());
        }

        try {
            readFile("nonexistent.txt");
        } catch (IOException e) {
            System.out.println("IO异常: " + e.getMessage());
        }
    }
}
