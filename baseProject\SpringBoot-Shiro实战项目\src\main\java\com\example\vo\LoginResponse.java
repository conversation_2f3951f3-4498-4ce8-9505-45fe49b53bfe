package com.example.vo;

import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import lombok.Data;

import java.util.List;

/**
 * 登录响应对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class LoginResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 用户角色列表
     */
    private List<SysRole> roles;
    
    /**
     * 用户权限列表
     */
    private List<SysPermission> permissions;
    
    /**
     * 会话ID
     */
    private String sessionId;
}
