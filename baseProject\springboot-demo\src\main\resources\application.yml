# Spring Boot 主配置文件
# 使用YAML格式，层次清晰，易于维护

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# Spring 配置
spring:
  # 应用信息
  application:
    name: springboot-demo
    
  # 环境配置
  profiles:
    active: dev
    
  # 数据源配置
  datasource:
    url: *********************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      pool-name: SpringBootDemoHikariCP
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update  # 开发环境使用update，生产环境使用validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
        
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600000  # 10分钟
      cache-null-values: false
      
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
      
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
      
  # 邮件配置
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-app-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# 日志配置
logging:
  level:
    com.example.demo: DEBUG
    org.springframework.web: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/springboot-demo.log
    max-size: 10MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,beans,configprops
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  info:
    env:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 应用信息配置
info:
  app:
    name: '@project.name@'
    description: '@project.description@'
    version: '@project.version@'
    encoding: '@project.build.sourceEncoding@'
    java:
      version: '@java.version@'

# 自定义配置
app:
  name: Spring Boot Demo
  version: 1.0.0
  author: Developer
  description: Spring Boot学习演示项目
  features:
    - user-management
    - role-based-security
    - data-validation
    - exception-handling
    - caching
    - monitoring
  jwt:
    secret: mySecretKey
    expiration: 86400  # 24小时
  upload:
    path: /uploads
    allowed-types:
      - image/jpeg
      - image/png
      - image/gif
      - application/pdf
  pagination:
    default-page-size: 10
    max-page-size: 100

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ********************************************************************************************************************************
    username: dev_user
    password: dev_pass
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  redis:
    database: 1

logging:
  level:
    com.example.demo: DEBUG
    
---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    
---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:springboot_demo_prod}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:prod_user}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 50
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  redis:
    host: ${REDIS_HOST:localhost}
    password: ${REDIS_PASSWORD:}
    
logging:
  level:
    com.example.demo: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
  file:
    name: /var/log/springboot-demo/application.log
