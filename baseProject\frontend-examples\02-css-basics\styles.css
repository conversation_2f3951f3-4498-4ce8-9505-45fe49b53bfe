/* CSS基础示例样式文件 */

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* ===== 页面头部 ===== */
.header {
    text-align: center;
    padding: 40px 0;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    border-radius: 10px;
    margin-bottom: 30px;
}

.main-title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* ===== 导航菜单 ===== */
.navigation {
    background: #2c3e50;
    border-radius: 8px;
    margin-bottom: 30px;
    overflow: hidden;
}

.nav-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
}

.nav-list li {
    flex: 1;
    min-width: 120px;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    text-align: center;
    transition: background-color 0.3s ease;
    border-right: 1px solid #34495e;
}

.nav-link:hover {
    background-color: #3498db;
    transform: translateY(-2px);
}

.nav-list li:last-child .nav-link {
    border-right: none;
}

/* ===== 章节样式 ===== */
.section {
    margin-bottom: 50px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 5px solid #3498db;
}

.section-title {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 2rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #3498db;
}

/* ===== 选择器示例 ===== */
.example-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.example-item {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.example-item:hover {
    transform: translateY(-5px);
}

.example-item h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.example-item code {
    background: #f1f2f6;
    padding: 5px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #e74c3c;
    display: block;
    margin-top: 10px;
}

/* 类选择器示例 */
.highlight {
    background-color: #fff3cd !important;
    border: 2px solid #ffc107;
}

/* ID选择器示例 */
#unique-item {
    border: 2px solid #e74c3c;
    background: #ffebee;
}

/* 属性选择器示例 */
[data-info] {
    font-style: italic;
    color: #8e44ad;
}

/* 伪类和伪元素示例 */
.pseudo-examples {
    margin-top: 30px;
}

.hover-button {
    padding: 12px 24px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    margin: 10px;
}

.hover-button:hover {
    background: #2980b9;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.first-letter-demo::first-letter {
    font-size: 3em;
    color: #e74c3c;
    font-weight: bold;
    float: left;
    line-height: 1;
    margin-right: 5px;
}

.nth-child-demo {
    list-style: none;
    display: flex;
    gap: 10px;
}

.nth-child-demo li {
    padding: 10px 15px;
    background: #ecf0f1;
    border-radius: 5px;
}

.nth-child-demo li:nth-child(odd) {
    background: #3498db;
    color: white;
}

.nth-child-demo li:nth-child(even) {
    background: #e74c3c;
    color: white;
}

/* ===== 盒模型示例 ===== */
.box-model-demo {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.box-example {
    position: relative;
    margin: 40px;
    border: 20px solid #e74c3c;
    padding: 30px;
    background-color: #3498db;
    background-clip: content-box;
}

.box-example::before {
    content: '';
    position: absolute;
    top: -40px;
    left: -40px;
    right: -40px;
    bottom: -40px;
    background: #f39c12;
    z-index: -1;
}

.content {
    background: #2ecc71;
    padding: 20px;
    color: white;
    text-align: center;
    font-weight: bold;
}

.box-labels {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.label {
    padding: 8px 12px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
}

.margin-label { background: #f39c12; }
.border-label { background: #e74c3c; }
.padding-label { background: #3498db; }
.content-label { background: #2ecc71; }

.box-sizing-demo {
    margin-top: 30px;
}

.box-sizing-examples {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.box {
    width: 200px;
    padding: 20px;
    border: 10px solid #3498db;
    background: #ecf0f1;
    text-align: center;
}

.content-box {
    box-sizing: content-box;
}

.border-box {
    box-sizing: border-box;
}

/* ===== 布局示例 ===== */
.layout-examples {
    margin-bottom: 30px;
}

.position-demo {
    margin-bottom: 30px;
}

.position-container {
    position: relative;
    height: 200px;
    background: #ecf0f1;
    border: 2px dashed #bdc3c7;
    margin: 20px 0;
}

.position-item {
    width: 80px;
    height: 60px;
    background: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border-radius: 4px;
}

.static {
    position: static;
    background: #95a5a6;
}

.relative {
    position: relative;
    top: 20px;
    left: 20px;
    background: #e74c3c;
}

.absolute {
    position: absolute;
    top: 50px;
    right: 20px;
    background: #f39c12;
}

.fixed {
    position: fixed;
    top: 10px;
    right: 10px;
    background: #8e44ad;
    z-index: 1000;
}

.float-demo {
    background: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
}

.float-item {
    width: 150px;
    height: 100px;
    background: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px;
    border-radius: 4px;
}

.left { float: left; }
.right { float: right; }

.clearfix::after {
    content: '';
    display: table;
    clear: both;
}

/* ===== Flexbox示例 ===== */
.flexbox-examples {
    margin-bottom: 30px;
}

.flex-container {
    display: flex;
    background: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    margin: 15px 0;
    min-height: 80px;
}

.flex-item {
    background: #3498db;
    color: white;
    padding: 15px;
    margin: 5px;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
}

.flex-item.small {
    min-width: 60px;
    padding: 10px;
}

/* Justify-content 示例 */
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* Align-items 示例 */
.tall { height: 120px; }
.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }

.flex-demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.flex-demo h4 {
    margin-bottom: 10px;
    color: #2c3e50;
}

/* Flex项目属性 */
.flex-properties {
    background: #ecf0f1;
}

.flex-grow-1 { flex-grow: 1; background: #e74c3c; }
.flex-grow-2 { flex-grow: 2; background: #f39c12; }
.flex-shrink-0 { flex-shrink: 0; background: #2ecc71; min-width: 150px; }

/* ===== Grid示例 ===== */
.grid-examples {
    margin-bottom: 30px;
}

.grid-container {
    display: grid;
    gap: 15px;
    background: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.grid-item {
    background: #3498db;
    color: white;
    padding: 20px;
    border-radius: 4px;
    text-align: center;
    font-weight: bold;
}

.basic-grid {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 100px);
}

.named-grid {
    grid-template-areas: 
        "header header header"
        "sidebar main main"
        "footer footer footer";
    grid-template-columns: 200px 1fr 1fr;
    grid-template-rows: 80px 200px 60px;
}

.named-grid .header { grid-area: header; background: #e74c3c; }
.named-grid .sidebar { grid-area: sidebar; background: #f39c12; }
.named-grid .main { grid-area: main; background: #2ecc71; }
.named-grid .footer { grid-area: footer; background: #8e44ad; }

.responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* ===== 动画示例 ===== */
.animation-examples {
    margin-bottom: 30px;
}

/* Transition 过渡 */
.transition-demos {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.transition-item {
    padding: 20px;
    background: #3498db;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 120px;
}

.hover-scale:hover { transform: scale(1.2); }
.hover-rotate:hover { transform: rotate(15deg); }
.hover-color:hover { background: #e74c3c; }

/* Transform 变换 */
.transform-demos {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.transform-item {
    padding: 20px;
    background: #2ecc71;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
    text-align: center;
    min-width: 100px;
}

.translate:hover { transform: translateX(20px); }
.rotate:hover { transform: rotate(45deg); }
.scale:hover { transform: scale(1.3); }
.skew:hover { transform: skew(15deg, 5deg); }

/* Keyframe 关键帧动画 */
.keyframe-demos {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.keyframe-item {
    padding: 20px;
    background: #f39c12;
    color: white;
    border-radius: 8px;
    text-align: center;
    min-width: 100px;
    cursor: pointer;
}

.bounce:hover { animation: bounce 1s infinite; }
.pulse:hover { animation: pulse 2s infinite; }
.slide:hover { animation: slide 1s ease-in-out; }
.fade:hover { animation: fade 2s ease-in-out; }

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-20px); }
    60% { transform: translateY(-10px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes slide {
    0% { transform: translateX(0); }
    50% { transform: translateX(50px); }
    100% { transform: translateX(0); }
}

@keyframes fade {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* 加载动画 */
.loading-demos {
    display: flex;
    gap: 40px;
    align-items: center;
    margin: 30px 0;
    flex-wrap: wrap;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dots {
    display: flex;
    gap: 5px;
}

.dot {
    width: 12px;
    height: 12px;
    background: #3498db;
    border-radius: 50%;
    animation: dotPulse 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes dotPulse {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.progress-bar {
    width: 200px;
    height: 20px;
    background: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: linear-gradient(45deg, #3498db, #2ecc71);
    border-radius: 10px;
    animation: progressMove 2s ease-in-out infinite;
}

@keyframes progressMove {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* ===== 响应式设计 ===== */
.responsive-demo {
    margin-bottom: 30px;
}

.responsive-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.responsive-item {
    padding: 20px;
    background: #3498db;
    color: white;
    border-radius: 8px;
    text-align: center;
}

.breakpoint-info {
    background: #2c3e50;
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin-top: 20px;
}

.current-breakpoint {
    font-weight: bold;
    color: #f39c12;
}

/* ===== 页面底部 ===== */
.footer {
    background: #2c3e50;
    color: white;
    padding: 40px;
    border-radius: 10px;
    text-align: center;
}

.footer h2 {
    margin-bottom: 20px;
    color: #f39c12;
}

.next-steps {
    margin-top: 30px;
    text-align: left;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.next-steps h3 {
    color: #3498db;
    margin-bottom: 15px;
}

.next-steps ul {
    list-style-position: inside;
}

.next-steps li {
    margin-bottom: 8px;
    padding-left: 10px;
}

/* ===== 媒体查询 (响应式设计) ===== */

/* 平板设备 */
@media (max-width: 992px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .nav-list {
        flex-direction: column;
    }
    
    .nav-link {
        border-right: none;
        border-bottom: 1px solid #34495e;
    }
    
    .responsive-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .flex-demo-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .example-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    .main-title {
        font-size: 1.8rem;
    }
    
    .section {
        padding: 20px 15px;
    }
    
    .responsive-grid {
        grid-template-columns: 1fr;
    }
    
    .flex-demo-grid {
        grid-template-columns: 1fr;
    }
    
    .example-grid {
        grid-template-columns: 1fr;
    }
    
    .box-model-demo {
        flex-direction: column;
        text-align: center;
    }
    
    .transition-demos,
    .transform-demos,
    .keyframe-demos {
        justify-content: center;
    }
    
    .loading-demos {
        justify-content: center;
    }
    
    .named-grid {
        grid-template-areas: 
            "header"
            "sidebar"
            "main"
            "footer";
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }
}

/* 小屏手机 */
@media (max-width: 480px) {
    .container {
        margin: 5px;
        padding: 10px;
    }
    
    .header {
        padding: 20px 10px;
    }
    
    .main-title {
        font-size: 1.5rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .box-example {
        margin: 20px;
        border-width: 10px;
        padding: 15px;
    }
    
    .box-example::before {
        top: -20px;
        left: -20px;
        right: -20px;
        bottom: -20px;
    }
}
