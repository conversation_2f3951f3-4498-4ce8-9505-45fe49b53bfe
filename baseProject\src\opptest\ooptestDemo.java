package opptest;

public class ooptestDemo {
    public static void main(String[] args) {
        Student student = new Student("张三", 20, "2023001");
        student.study();
        student.showInfo();
        Student student2 = new Student();
        student2.setAge(18);
        student2.setName("李四");
        student2.study();
        /*继承*/
        Dog dog = new Dog("旺财", 3, "金毛");
        dog.eat();
        dog.sleep();
        dog.bark();
        Cat cat = new Cat("咪咪", 2);
        cat.eat();
        cat.sleep();
        /*多态*/
        Animal dog1 = new Dog("汪财产2", 3, "金毛");
        dog1.eat();
        dog1.sleep();
        Animal cat1 = new Cat("咪咪2", 2);
        cat1.eat();
        cat1.sleep();
        Animal[] animals = {
                new Dog("小黑", 4, "拉布拉多"),
                new Cat("小白", 1),
                new Dog("大黄", 5, "德牧")
        };
        for (Animal animal : animals) {
            animal.eat();
            animal.sleep();
        }
        /*抽象类*/
        Shape circle = new Circle("红色", 5);
        circle.displayColor();
        System.out.println("圆的面积：" + circle.getArea());
        System.out.println("圆的周长：" + circle.getPerimeter());
        /*接口*/
        Rectangle rectangle = new Rectangle("红", 2.0, 3.0);
        rectangle.draw();
        rectangle.move(1, 2);

    }
}
