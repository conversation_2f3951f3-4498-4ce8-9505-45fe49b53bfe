package com.example.mybatisdemo.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 手机号校验器
 */
public class PhoneNumberValidator implements ConstraintValidator<PhoneNumber, String> {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    private boolean allowEmpty;
    
    @Override
    public void initialize(PhoneNumber constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果允许为空且值为空，则校验通过
        if (allowEmpty && !StringUtils.hasText(value)) {
            return true;
        }
        
        // 如果不允许为空且值为空，则校验失败
        if (!allowEmpty && !StringUtils.hasText(value)) {
            return false;
        }
        
        // 校验手机号格式
        return PHONE_PATTERN.matcher(value).matches();
    }
}
