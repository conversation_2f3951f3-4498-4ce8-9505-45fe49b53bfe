<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mybatisdemo.mapper.ArticleMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="Article">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="author_id" property="authorId" jdbcType="BIGINT"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="tags" property="tags" jdbcType="VARCHAR"/>
        <result column="view_count" property="viewCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, title, content, author_id, category, tags, view_count, status, create_time, update_time
    </sql>

    <!-- 根据ID查询文章 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        WHERE id = #{id}
    </select>

    <!-- 查询所有文章 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        ORDER BY create_time DESC
    </select>

    <!-- 根据作者ID查询文章 -->
    <select id="selectByAuthorId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        WHERE author_id = #{authorId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据分类查询文章 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        WHERE category = #{category}
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询文章 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询文章 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据关键字搜索文章 -->
    <select id="searchArticles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        WHERE title LIKE CONCAT('%', #{keyword}, '%')
           OR content LIKE CONCAT('%', #{keyword}, '%')
           OR tags LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 查询热门文章（按浏览量排序） -->
    <select id="selectPopularArticles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM article
        WHERE status = 1
        ORDER BY view_count DESC, create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 插入文章 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO article (title, content, author_id, category, tags, view_count, status, create_time, update_time)
        VALUES (#{title}, #{content}, #{authorId}, #{category}, #{tags}, #{viewCount}, #{status}, 
                #{createTime}, #{updateTime})
    </insert>

    <!-- 更新文章信息 -->
    <update id="update">
        UPDATE article
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="tags != null">
                tags = #{tags},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount">
        UPDATE article SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 根据ID删除文章 -->
    <delete id="deleteById">
        DELETE FROM article WHERE id = #{id}
    </delete>

    <!-- 批量删除文章 -->
    <delete id="deleteBatch">
        DELETE FROM article WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计文章总数 -->
    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*) FROM article
    </select>

    <!-- 根据作者统计文章数 -->
    <select id="countByAuthorId" resultType="java.lang.Long">
        SELECT COUNT(*) FROM article WHERE author_id = #{authorId}
    </select>

    <!-- 根据分类统计文章数 -->
    <select id="countByCategory" resultType="java.lang.Long">
        SELECT COUNT(*) FROM article WHERE category = #{category}
    </select>

</mapper>
