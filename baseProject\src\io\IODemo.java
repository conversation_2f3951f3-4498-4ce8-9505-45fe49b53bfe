package io;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;

/**
 * IO操作示例类
 * 演示Java中各种IO操作
 */
public class IODemo {
    
    public static void main(String[] args) {
        IODemo demo = new IODemo();
        
        System.out.println("=== 字节流操作 ===");
        demo.byteStreamDemo();
        
        System.out.println("\n=== 字符流操作 ===");
        demo.characterStreamDemo();
        
        System.out.println("\n=== 缓冲流操作 ===");
        demo.bufferedStreamDemo();
        
        System.out.println("\n=== 对象序列化 ===");
        demo.serializationDemo();
        
        System.out.println("\n=== NIO操作 ===");
        demo.nioDemo();
    }
    
    /**
     * 字节流操作示例
     */
    public void byteStreamDemo() {
        String sourceFile = "source.txt";
        String destFile = "byte_copy.txt";
        
        // 创建源文件
        createSourceFile(sourceFile);
        
        // 使用字节流复制文件
        try (FileInputStream fis = new FileInputStream(sourceFile);
             FileOutputStream fos = new FileOutputStream(destFile)) {
            
            byte[] buffer = new byte[1024];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
            
            System.out.println("字节流文件复制完成: " + sourceFile + " -> " + destFile);
            
        } catch (IOException e) {
            System.out.println("字节流操作异常: " + e.getMessage());
        }
        
        // 读取并显示文件内容
        readFileBytes(destFile);
    }
    
    /**
     * 字符流操作示例
     */
    public void characterStreamDemo() {
        String fileName = "character_test.txt";
        
        // 写入文本文件
        try (FileWriter writer = new FileWriter(fileName, StandardCharsets.UTF_8)) {
            writer.write("这是字符流测试文件\n");
            writer.write("支持中文字符\n");
            writer.write("English text is also supported\n");
            writer.write("特殊字符: ©®™€\n");
            
            System.out.println("字符流写入完成");
            
        } catch (IOException e) {
            System.out.println("字符流写入异常: " + e.getMessage());
        }
        
        // 读取文本文件
        try (FileReader reader = new FileReader(fileName, StandardCharsets.UTF_8)) {
            int charData;
            System.out.println("文件内容:");
            
            while ((charData = reader.read()) != -1) {
                System.out.print((char) charData);
            }
            
        } catch (IOException e) {
            System.out.println("字符流读取异常: " + e.getMessage());
        }
    }
    
    /**
     * 缓冲流操作示例
     */
    public void bufferedStreamDemo() {
        String fileName = "buffered_test.txt";
        
        // 使用BufferedWriter写入多行文本
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(fileName))) {
            writer.write("第一行内容");
            writer.newLine();
            writer.write("第二行内容");
            writer.newLine();
            writer.write("第三行内容");
            writer.newLine();
            
            System.out.println("缓冲流写入完成");
            
        } catch (IOException e) {
            System.out.println("缓冲流写入异常: " + e.getMessage());
        }
        
        // 使用BufferedReader逐行读取
        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
            String line;
            int lineNumber = 1;
            
            System.out.println("逐行读取文件:");
            while ((line = reader.readLine()) != null) {
                System.out.println("第" + lineNumber + "行: " + line);
                lineNumber++;
            }
            
        } catch (IOException e) {
            System.out.println("缓冲流读取异常: " + e.getMessage());
        }
    }
    
    /**
     * 对象序列化示例
     */
    public void serializationDemo() {
        String fileName = "students.ser";
        
        // 创建学生对象列表
        List<Student> students = new ArrayList<>();
        students.add(new Student("张三", 20, "计算机科学"));
        students.add(new Student("李四", 21, "软件工程"));
        students.add(new Student("王五", 19, "信息安全"));
        
        // 序列化对象
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(fileName))) {
            oos.writeObject(students);
            System.out.println("对象序列化完成");
            
        } catch (IOException e) {
            System.out.println("序列化异常: " + e.getMessage());
        }
        
        // 反序列化对象
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(fileName))) {
            @SuppressWarnings("unchecked")
            List<Student> deserializedStudents = (List<Student>) ois.readObject();
            
            System.out.println("反序列化完成，学生列表:");
            for (Student student : deserializedStudents) {
                System.out.println(student);
            }
            
        } catch (IOException | ClassNotFoundException e) {
            System.out.println("反序列化异常: " + e.getMessage());
        }
    }
    
    /**
     * NIO操作示例
     */
    public void nioDemo() {
        try {
            // 创建目录
            Path dirPath = Paths.get("nio_test");
            if (!Files.exists(dirPath)) {
                Files.createDirectory(dirPath);
                System.out.println("NIO目录创建成功: " + dirPath);
            }
            
            // 创建文件并写入内容
            Path filePath = dirPath.resolve("nio_test.txt");
            List<String> lines = List.of(
                "这是NIO测试文件",
                "使用Files类操作",
                "支持现代化的文件操作"
            );
            
            Files.write(filePath, lines, StandardCharsets.UTF_8);
            System.out.println("NIO文件写入完成");
            
            // 读取文件内容
            List<String> readLines = Files.readAllLines(filePath, StandardCharsets.UTF_8);
            System.out.println("NIO文件读取内容:");
            readLines.forEach(line -> System.out.println("  " + line));
            
            // 文件属性信息
            System.out.println("文件信息:");
            System.out.println("  文件大小: " + Files.size(filePath) + " 字节");
            System.out.println("  是否为目录: " + Files.isDirectory(filePath));
            System.out.println("  是否为常规文件: " + Files.isRegularFile(filePath));
            System.out.println("  是否可读: " + Files.isReadable(filePath));
            System.out.println("  是否可写: " + Files.isWritable(filePath));
            
        } catch (IOException e) {
            System.out.println("NIO操作异常: " + e.getMessage());
        }
    }
    
    /**
     * 创建源文件
     */
    private void createSourceFile(String fileName) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(fileName))) {
            writer.println("这是源文件内容");
            writer.println("用于测试字节流复制");
            writer.println("包含多行文本");
        } catch (IOException e) {
            System.out.println("创建源文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 读取文件字节内容
     */
    private void readFileBytes(String fileName) {
        try (FileInputStream fis = new FileInputStream(fileName)) {
            System.out.println("文件字节内容:");
            int byteData;
            while ((byteData = fis.read()) != -1) {
                System.out.print((char) byteData);
            }
            System.out.println();
            
        } catch (IOException e) {
            System.out.println("读取文件字节异常: " + e.getMessage());
        }
    }
}

/**
 * 可序列化的学生类
 */
class Student implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String name;
    private int age;
    private String major;
    private transient String password = "secret"; // transient字段不会被序列化
    
    public Student(String name, int age, String major) {
        this.name = name;
        this.age = age;
        this.major = major;
    }
    
    // getter方法
    public String getName() { return name; }
    public int getAge() { return age; }
    public String getMajor() { return major; }
    public String getPassword() { return password; }
    
    @Override
    public String toString() {
        return "Student{name='" + name + "', age=" + age + 
               ", major='" + major + "', password='" + password + "'}";
    }
}
