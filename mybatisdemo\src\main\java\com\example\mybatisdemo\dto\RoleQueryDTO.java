package com.example.mybatisdemo.dto;

import com.example.mybatisdemo.common.page.BasePageQuery;
import com.example.mybatisdemo.common.page.PageUtils;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色查询DTO - 企业级查询条件
 *
 * 支持复杂的查询条件：
 * - 基础字段模糊查询
 * - 状态筛选
 * - 时间范围查询
 * - 层级查询
 * - 权限关联查询
 * - 使用情况查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleQueryDTO extends BasePageQuery {

    // ==================== 基础查询条件 ====================

    /**
     * 角色名称（模糊查询）
     */
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;

    /**
     * 角色编码（模糊查询）
     */
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    private String roleCode;

    /**
     * 角色描述（模糊查询）
     */
    @Size(max = 200, message = "角色描述长度不能超过200个字符")
    private String roleDesc;

    /**
     * 角色状态：0-禁用，1-启用，null-全部
     */
    @Min(value = 0, message = "状态值不正确")
    @Max(value = 1, message = "状态值不正确")
    private Integer status;

    /**
     * 关键字搜索（同时搜索角色名称、编码、描述）
     */
    @Size(max = 50, message = "关键字长度不能超过50个字符")
    private String keyword;

    // ==================== 高级查询条件 ====================

    /**
     * 父角色ID（查询子角色）
     */
    private Long parentId;

    /**
     * 角色层级
     */
    @Min(value = 1, message = "角色层级必须大于0")
    @Max(value = 10, message = "角色层级不能超过10")
    private Integer level;

    /**
     * 是否包含子角色
     */
    private Boolean includeChildren;

    /**
     * 创建人ID
     */
    private Long createUser;

    /**
     * 更新人ID
     */
    private Long updateUser;

    // ==================== 时间范围查询 ====================

    /**
     * 创建时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTimeEnd;

    // ==================== 权限相关查询 ====================

    /**
     * 包含指定权限的角色
     */
    private String permissionCode;

    /**
     * 包含任一权限的角色
     */
    private List<String> permissionCodes;

    /**
     * 是否查询有权限的角色
     */
    private Boolean hasPermissions;

    // ==================== 使用情况查询 ====================

    /**
     * 是否查询正在使用的角色（有用户关联）
     */
    private Boolean inUse;

    /**
     * 用户数量范围 - 最小值
     */
    @Min(value = 0, message = "用户数量不能为负数")
    private Integer minUserCount;

    /**
     * 用户数量范围 - 最大值
     */
    @Min(value = 0, message = "用户数量不能为负数")
    private Integer maxUserCount;

    // ==================== 排序和分组 ====================

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortDirection = "desc";

    /**
     * 是否按层级分组
     */
    private Boolean groupByLevel;

    /**
     * 是否按状态分组
     */
    private Boolean groupByStatus;

    // ==================== 导出相关 ====================

    /**
     * 是否导出模式（不分页，返回所有数据）
     */
    private Boolean exportMode = false;

    /**
     * 导出字段列表
     */
    private List<String> exportFields;

    // ==================== 缓存控制 ====================

    /**
     * 是否使用缓存
     */
    private Boolean useCache = true;

    /**
     * 缓存过期时间（秒）
     */
    private Integer cacheExpire;

    // ==================== 方法 ====================

    /**
     * 获取排序字符串（用于PageHelper.orderBy()）
     */
    public String getOrderByClause() {
        if (!StringUtils.hasText(sortField)) {
            return "create_time desc";
        }

        // 转换驼峰命名为下划线命名
        String dbField = camelToUnderscore(sortField);
        String direction = "desc";

        if (StringUtils.hasText(sortDirection)) {
            direction = "asc".equalsIgnoreCase(sortDirection) ? "asc" : "desc";
        }

        return dbField + " " + direction;
    }

    /**
     * 驼峰命名转下划线命名
     */
    private String camelToUnderscore(String camelCase) {
        if (!StringUtils.hasText(camelCase)) {
            return "create_time";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 是否有时间范围查询条件
     */
    public boolean hasTimeRangeCondition() {
        return createTimeStart != null || createTimeEnd != null ||
               updateTimeStart != null || updateTimeEnd != null;
    }

    /**
     * 是否有权限相关查询条件
     */
    public boolean hasPermissionCondition() {
        return StringUtils.hasText(permissionCode) ||
               (permissionCodes != null && !permissionCodes.isEmpty()) ||
               hasPermissions != null;
    }

    /**
     * 是否有使用情况查询条件
     */
    public boolean hasUsageCondition() {
        return inUse != null || minUserCount != null || maxUserCount != null;
    }

    /**
     * 是否为复杂查询（需要关联查询）
     */
    public boolean isComplexQuery() {
        return hasPermissionCondition() || hasUsageCondition() || includeChildren != null;
    }

    /**
     * 获取缓存键
     */
    public String getCacheKey() {
        return "role:query:" + this.hashCode();
    }
}
