@echo off
echo 正在设置环境变量...
set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo 正在清理项目...
call E:\softInstall\apache-maven-3.9.10\bin\mvn clean

echo 正在编译项目...
call E:\softInstall\apache-maven-3.9.10\bin\mvn compile

echo 正在启动应用程序...
call E:\softInstall\apache-maven-3.9.10\bin\mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dfile.encoding=UTF-8" 