-- =============================================
-- 完整数据库设置脚本
-- 一键执行所有初始化操作
-- =============================================

-- 设置SQL模式和字符集
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- 1. 创建数据库
SOURCE 01_database_init.sql;

-- 2. 创建表结构
SOURCE 02_create_tables.sql;

-- 3. 插入测试数据
SOURCE 03_insert_test_data.sql;

-- 提交事务
COMMIT;

-- 显示设置完成信息
SELECT 
    '数据库设置完成!' as message,
    DATABASE() as current_database,
    (SELECT COUNT(*) FROM users) as total_users,
    NOW() as setup_time;
