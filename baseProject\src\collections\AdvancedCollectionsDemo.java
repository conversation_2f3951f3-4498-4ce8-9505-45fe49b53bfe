package collections;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 高级集合演示
 * 展示集合的高级用法和最佳实践
 */
public class AdvancedCollectionsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 高级集合演示 ===");
        
        // 1. 自定义排序
        customSortingDemo();
        
        // 2. 集合操作
        collectionOperationsDemo();
        
        // 3. 性能比较
        performanceComparisonDemo();
        
        // 4. 最佳实践
        bestPracticesDemo();
    }
    
    /**
     * 自定义排序演示
     */
    public static void customSortingDemo() {
        System.out.println("\n--- 自定义排序演示 ---");
        
        List<Person> people = Arrays.asList(
            new Person("张三", 25, 5000),
            new Person("李四", 30, 8000),
            new Person("王五", 25, 6000),
            new Person("赵六", 35, 10000)
        );
        
        System.out.println("原始列表:");
        people.forEach(System.out::println);
        
        // 按年龄排序
        List<Person> sortedByAge = people.stream()
            .sorted(Comparator.comparing(Person::getAge))
            .collect(Collectors.toList());
        System.out.println("\n按年龄排序:");
        sortedByAge.forEach(System.out::println);
        
        // 按薪资降序排序
        List<Person> sortedBySalary = people.stream()
            .sorted(Comparator.comparing(Person::getSalary).reversed())
            .collect(Collectors.toList());
        System.out.println("\n按薪资降序排序:");
        sortedBySalary.forEach(System.out::println);
        
        // 多级排序：先按年龄，再按薪资
        List<Person> multiSort = people.stream()
            .sorted(Comparator.comparing(Person::getAge)
                    .thenComparing(Person::getSalary))
            .collect(Collectors.toList());
        System.out.println("\n多级排序（年龄+薪资）:");
        multiSort.forEach(System.out::println);
        
        // 使用TreeSet自动排序
        Set<Person> sortedSet = new TreeSet<>(Comparator.comparing(Person::getName));
        sortedSet.addAll(people);
        System.out.println("\nTreeSet按姓名排序:");
        sortedSet.forEach(System.out::println);
    }
    
    /**
     * 集合操作演示
     */
    public static void collectionOperationsDemo() {
        System.out.println("\n--- 集合操作演示 ---");
        
        Set<String> set1 = new HashSet<>(Arrays.asList("A", "B", "C", "D"));
        Set<String> set2 = new HashSet<>(Arrays.asList("C", "D", "E", "F"));
        
        System.out.println("集合1: " + set1);
        System.out.println("集合2: " + set2);
        
        // 并集
        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);
        System.out.println("并集: " + union);
        
        // 交集
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        System.out.println("交集: " + intersection);
        
        // 差集
        Set<String> difference = new HashSet<>(set1);
        difference.removeAll(set2);
        System.out.println("差集(1-2): " + difference);
        
        // 对称差集
        Set<String> symmetricDiff = new HashSet<>(union);
        symmetricDiff.removeAll(intersection);
        System.out.println("对称差集: " + symmetricDiff);
        
        // 使用Stream API进行集合操作
        System.out.println("\n使用Stream API:");
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        
        // 过滤偶数
        List<Integer> evenNumbers = numbers.stream()
            .filter(n -> n % 2 == 0)
            .collect(Collectors.toList());
        System.out.println("偶数: " + evenNumbers);
        
        // 平方后求和
        int sumOfSquares = numbers.stream()
            .mapToInt(n -> n * n)
            .sum();
        System.out.println("平方和: " + sumOfSquares);
        
        // 分组
        Map<Boolean, List<Integer>> partitioned = numbers.stream()
            .collect(Collectors.partitioningBy(n -> n % 2 == 0));
        System.out.println("按奇偶分组: " + partitioned);
    }
    
    /**
     * 性能比较演示
     */
    public static void performanceComparisonDemo() {
        System.out.println("\n--- 性能比较演示 ---");
        
        int size = 100000;
        
        // List性能比较
        System.out.println("List性能比较 (大小: " + size + "):");
        
        // ArrayList vs LinkedList 添加性能
        long start = System.currentTimeMillis();
        List<Integer> arrayList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            arrayList.add(i);
        }
        long arrayListAddTime = System.currentTimeMillis() - start;
        
        start = System.currentTimeMillis();
        List<Integer> linkedList = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            linkedList.add(i);
        }
        long linkedListAddTime = System.currentTimeMillis() - start;
        
        System.out.println("ArrayList尾部添加: " + arrayListAddTime + "ms");
        System.out.println("LinkedList尾部添加: " + linkedListAddTime + "ms");
        
        // 随机访问性能
        Random random = new Random();
        int accessCount = 10000;
        
        start = System.currentTimeMillis();
        for (int i = 0; i < accessCount; i++) {
            arrayList.get(random.nextInt(size));
        }
        long arrayListAccessTime = System.currentTimeMillis() - start;
        
        start = System.currentTimeMillis();
        for (int i = 0; i < accessCount; i++) {
            linkedList.get(random.nextInt(size));
        }
        long linkedListAccessTime = System.currentTimeMillis() - start;
        
        System.out.println("ArrayList随机访问: " + arrayListAccessTime + "ms");
        System.out.println("LinkedList随机访问: " + linkedListAccessTime + "ms");
        
        // Set性能比较
        System.out.println("\nSet性能比较:");
        
        start = System.currentTimeMillis();
        Set<Integer> hashSet = new HashSet<>();
        for (int i = 0; i < size; i++) {
            hashSet.add(i);
        }
        long hashSetAddTime = System.currentTimeMillis() - start;
        
        start = System.currentTimeMillis();
        Set<Integer> treeSet = new TreeSet<>();
        for (int i = 0; i < size; i++) {
            treeSet.add(i);
        }
        long treeSetAddTime = System.currentTimeMillis() - start;
        
        System.out.println("HashSet添加: " + hashSetAddTime + "ms");
        System.out.println("TreeSet添加: " + treeSetAddTime + "ms");
    }
    
    /**
     * 最佳实践演示
     */
    public static void bestPracticesDemo() {
        System.out.println("\n--- 最佳实践演示 ---");
        
        // 1. 初始化容量
        System.out.println("1. 初始化容量优化:");
        List<String> efficientList = new ArrayList<>(1000); // 预设容量
        Map<String, Object> efficientMap = new HashMap<>(1000); // 预设容量
        System.out.println("预设容量可以减少扩容开销");
        
        // 2. 使用合适的集合类型
        System.out.println("\n2. 选择合适的集合类型:");
        
        // 需要快速查找 -> HashSet
        Set<String> fastLookup = new HashSet<>();
        
        // 需要排序 -> TreeSet
        Set<String> sorted = new TreeSet<>();
        
        // 需要保持插入顺序 -> LinkedHashSet
        Set<String> ordered = new LinkedHashSet<>();
        
        System.out.println("根据需求选择合适的集合类型");
        
        // 3. 避免在迭代时修改集合
        System.out.println("\n3. 安全的集合修改:");
        List<String> list = new ArrayList<>(Arrays.asList("A", "B", "C", "D"));
        
        // 错误的做法（会抛出异常）
        // for (String item : list) {
        //     if ("B".equals(item)) {
        //         list.remove(item); // ConcurrentModificationException
        //     }
        // }
        
        // 正确的做法
        list.removeIf(item -> "B".equals(item));
        System.out.println("安全删除后: " + list);
        
        // 4. 使用不可变集合
        System.out.println("\n4. 不可变集合:");
        List<String> immutableList = Collections.unmodifiableList(
            Arrays.asList("不可变", "列表")
        );
        System.out.println("不可变列表: " + immutableList);
        
        // 5. 空值处理
        System.out.println("\n5. 空值处理:");
        List<String> nullableList = getNullableList();
        if (nullableList != null && !nullableList.isEmpty()) {
            System.out.println("安全访问列表: " + nullableList.size());
        } else {
            System.out.println("列表为空或null");
        }
        
        // 6. 使用Stream API进行函数式编程
        System.out.println("\n6. Stream API使用:");
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        
        List<Integer> result = numbers.stream()
            .filter(n -> n % 2 == 0)  // 过滤偶数
            .map(n -> n * n)          // 平方
            .sorted()                 // 排序
            .collect(Collectors.toList());
        
        System.out.println("偶数平方排序: " + result);
    }
    
    /**
     * 模拟可能返回null的方法
     */
    private static List<String> getNullableList() {
        return Math.random() > 0.5 ? Arrays.asList("有", "数据") : null;
    }
}

/**
 * 人员类，用于演示自定义排序
 */
class Person {
    private String name;
    private int age;
    private double salary;
    
    public Person(String name, int age, double salary) {
        this.name = name;
        this.age = age;
        this.salary = salary;
    }
    
    // Getters
    public String getName() { return name; }
    public int getAge() { return age; }
    public double getSalary() { return salary; }
    
    @Override
    public String toString() {
        return String.format("Person{name='%s', age=%d, salary=%.0f}", name, age, salary);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Person person = (Person) obj;
        return age == person.age &&
               Double.compare(person.salary, salary) == 0 &&
               Objects.equals(name, person.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, age, salary);
    }
}

/**
 * 集合陷阱演示
 */
class CollectionPitfallsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 集合常见陷阱演示 ===");
        
        // 1. equals和hashCode的重要性
        equalsHashCodeDemo();
        
        // 2. 并发修改异常
        concurrentModificationDemo();
        
        // 3. 内存泄漏风险
        memoryLeakDemo();
    }
    
    /**
     * equals和hashCode演示
     */
    public static void equalsHashCodeDemo() {
        System.out.println("\n--- equals和hashCode重要性 ---");
        
        // 没有正确实现equals和hashCode的类
        class BadStudent {
            String name;
            int age;
            
            BadStudent(String name, int age) {
                this.name = name;
                this.age = age;
            }
            
            @Override
            public String toString() {
                return name + "(" + age + ")";
            }
        }
        
        Set<BadStudent> badSet = new HashSet<>();
        BadStudent student1 = new BadStudent("张三", 20);
        BadStudent student2 = new BadStudent("张三", 20);
        
        badSet.add(student1);
        badSet.add(student2);
        
        System.out.println("没有重写equals/hashCode的集合大小: " + badSet.size()); // 2
        System.out.println("集合内容: " + badSet);
        
        // 正确实现equals和hashCode的类
        Set<Person> goodSet = new HashSet<>();
        Person person1 = new Person("张三", 20, 5000);
        Person person2 = new Person("张三", 20, 5000);
        
        goodSet.add(person1);
        goodSet.add(person2);
        
        System.out.println("正确实现equals/hashCode的集合大小: " + goodSet.size()); // 1
        System.out.println("集合内容: " + goodSet);
    }
    
    /**
     * 并发修改异常演示
     */
    public static void concurrentModificationDemo() {
        System.out.println("\n--- 并发修改异常演示 ---");
        
        List<String> list = new ArrayList<>(Arrays.asList("A", "B", "C", "D", "E"));
        
        System.out.println("原始列表: " + list);
        
        // 正确的删除方式1：使用Iterator
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()) {
            String item = iterator.next();
            if ("C".equals(item)) {
                iterator.remove();
                System.out.println("使用Iterator删除: " + item);
            }
        }
        
        // 正确的删除方式2：使用removeIf
        list.removeIf(item -> "B".equals(item));
        System.out.println("使用removeIf删除B后: " + list);
        
        // 正确的删除方式3：倒序删除
        for (int i = list.size() - 1; i >= 0; i--) {
            if ("A".equals(list.get(i))) {
                list.remove(i);
                System.out.println("倒序删除A");
            }
        }
        
        System.out.println("最终列表: " + list);
    }
    
    /**
     * 内存泄漏演示
     */
    public static void memoryLeakDemo() {
        System.out.println("\n--- 内存泄漏风险演示 ---");
        
        // 使用WeakHashMap避免内存泄漏
        Map<String, Object> cache = new WeakHashMap<>();
        
        String key = new String("缓存键"); // 使用new确保不是字符串常量池中的
        cache.put(key, "缓存值");
        
        System.out.println("缓存大小: " + cache.size());
        
        key = null; // 移除强引用
        System.gc(); // 建议垃圾回收
        
        try {
            Thread.sleep(100); // 等待GC
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("GC后缓存大小: " + cache.size());
        System.out.println("WeakHashMap可以自动清理无强引用的键值对");
    }
}
