package com.example;

import cn.hutool.crypto.digest.DigestUtil;
import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import com.example.service.SysUserService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * Shiro系统测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ShiroApplicationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ShiroApplicationTest.class);
    
    @Autowired
    private SysUserService userService;
    
    @Autowired
    private SecurityManager securityManager;
    
    /**
     * 测试用户查询
     */
    @Test
    public void testUserQuery() {
        logger.info("=== 测试用户查询 ===");
        
        // 查询所有用户
        List<SysUser> allUsers = userService.getAllUsers();
        logger.info("所有用户数量: {}", allUsers.size());
        
        for (SysUser user : allUsers) {
            logger.info("用户: {} - {} - {}", user.getId(), user.getUsername(), user.getRealName());
        }
        
        // 根据用户名查询
        SysUser admin = userService.getUserByUsername("admin");
        if (admin != null) {
            logger.info("管理员用户: {}", admin);
        }
    }
    
    /**
     * 测试用户角色权限查询
     */
    @Test
    public void testUserRolesAndPermissions() {
        logger.info("=== 测试用户角色权限查询 ===");
        
        // 查询管理员用户的角色和权限
        SysUser admin = userService.getUserByUsername("admin");
        if (admin != null) {
            // 查询用户详细信息（包含角色和权限）
            SysUser userWithDetails = userService.getUserWithRolesAndPermissions(admin.getId());
            
            if (userWithDetails != null) {
                logger.info("用户: {}", userWithDetails.getUsername());
                
                // 输出角色信息
                List<SysRole> roles = userWithDetails.getRoles();
                if (roles != null && !roles.isEmpty()) {
                    logger.info("用户角色:");
                    for (SysRole role : roles) {
                        logger.info("  - {} ({}): {}", role.getRoleName(), role.getRoleCode(), role.getDescription());
                    }
                } else {
                    logger.info("用户没有分配角色");
                }
                
                // 输出权限信息
                List<SysPermission> permissions = userWithDetails.getPermissions();
                if (permissions != null && !permissions.isEmpty()) {
                    logger.info("用户权限:");
                    for (SysPermission permission : permissions) {
                        logger.info("  - {} ({}): {} - {}", 
                            permission.getPermissionName(), 
                            permission.getPermissionCode(),
                            permission.getPermissionType(),
                            permission.getUrl());
                    }
                } else {
                    logger.info("用户没有权限");
                }
            }
        }
    }
    
    /**
     * 测试密码加密
     */
    @Test
    public void testPasswordEncryption() {
        logger.info("=== 测试密码加密 ===");
        
        String rawPassword = "123456";
        String username = "testuser";
        String salt = "testsalt";
        
        // 使用MD5加密
        String encryptedPassword = DigestUtil.md5Hex(rawPassword + username + salt);
        
        logger.info("原始密码: {}", rawPassword);
        logger.info("用户名: {}", username);
        logger.info("盐值: {}", salt);
        logger.info("加密密码: {}", encryptedPassword);
        
        // 验证密码
        String verifyPassword = DigestUtil.md5Hex(rawPassword + username + salt);
        boolean matches = encryptedPassword.equals(verifyPassword);
        logger.info("密码验证结果: {}", matches);
    }
    
    /**
     * 测试Shiro认证
     */
    @Test
    public void testShiroAuthentication() {
        logger.info("=== 测试Shiro认证 ===");
        
        // 设置SecurityManager
        SecurityUtils.setSecurityManager(securityManager);
        
        // 获取Subject
        Subject subject = SecurityUtils.getSubject();
        
        // 创建认证令牌
        UsernamePasswordToken token = new UsernamePasswordToken("admin", "123456");
        
        try {
            // 执行登录
            subject.login(token);
            
            if (subject.isAuthenticated()) {
                logger.info("用户认证成功");
                
                // 获取用户信息
                SysUser user = (SysUser) subject.getPrincipal();
                logger.info("当前用户: {}", user.getUsername());
                
                // 测试角色权限
                boolean hasAdminRole = subject.hasRole("admin");
                logger.info("是否有admin角色: {}", hasAdminRole);
                
                boolean hasUserViewPermission = subject.isPermitted("system:user:view");
                logger.info("是否有用户查看权限: {}", hasUserViewPermission);
                
                // 登出
                subject.logout();
                logger.info("用户已登出");
            } else {
                logger.error("用户认证失败");
            }
            
        } catch (Exception e) {
            logger.error("认证过程中发生异常", e);
        }
    }
    
    /**
     * 测试用户创建
     */
    @Test
    public void testUserCreation() {
        logger.info("=== 测试用户创建 ===");
        
        // 创建测试用户
        SysUser testUser = new SysUser();
        testUser.setUsername("testuser");
        testUser.setPassword("123456");
        testUser.setEmail("<EMAIL>");
        testUser.setRealName("测试用户");
        testUser.setPhone("13800000000");
        testUser.setStatus(1);
        
        try {
            boolean success = userService.createUser(testUser);
            if (success) {
                logger.info("用户创建成功: {}", testUser);
                
                // 查询创建的用户
                SysUser createdUser = userService.getUserByUsername("testuser");
                if (createdUser != null) {
                    logger.info("查询到创建的用户: {}", createdUser);
                    
                    // 删除测试用户
                    boolean deleted = userService.deleteUser(createdUser.getId());
                    logger.info("测试用户删除结果: {}", deleted);
                }
            } else {
                logger.error("用户创建失败");
            }
        } catch (Exception e) {
            logger.error("用户创建异常", e);
        }
    }
    
    /**
     * 测试用户状态修改
     */
    @Test
    public void testUserStatusChange() {
        logger.info("=== 测试用户状态修改 ===");
        
        // 查询普通用户
        SysUser user = userService.getUserByUsername("user");
        if (user != null) {
            logger.info("用户当前状态: {}", user.getStatus());
            
            // 禁用用户
            boolean disabled = userService.changeUserStatus(user.getId(), 0);
            logger.info("禁用用户结果: {}", disabled);
            
            // 查询用户状态
            SysUser disabledUser = userService.getUserById(user.getId());
            logger.info("禁用后用户状态: {}", disabledUser.getStatus());
            
            // 启用用户
            boolean enabled = userService.changeUserStatus(user.getId(), 1);
            logger.info("启用用户结果: {}", enabled);
            
            // 查询用户状态
            SysUser enabledUser = userService.getUserById(user.getId());
            logger.info("启用后用户状态: {}", enabledUser.getStatus());
        }
    }
    
    /**
     * 测试用户名和邮箱唯一性检查
     */
    @Test
    public void testUniqueCheck() {
        logger.info("=== 测试用户名和邮箱唯一性检查 ===");
        
        // 检查已存在的用户名
        boolean adminExists = userService.isUsernameExists("admin");
        logger.info("用户名'admin'是否存在: {}", adminExists);
        
        // 检查不存在的用户名
        boolean notExists = userService.isUsernameExists("notexistuser");
        logger.info("用户名'notexistuser'是否存在: {}", notExists);
        
        // 检查已存在的邮箱
        boolean emailExists = userService.isEmailExists("<EMAIL>");
        logger.info("邮箱'<EMAIL>'是否存在: {}", emailExists);
        
        // 检查不存在的邮箱
        boolean emailNotExists = userService.isEmailExists("<EMAIL>");
        logger.info("邮箱'<EMAIL>'是否存在: {}", emailNotExists);
    }
    
    /**
     * 测试用户统计
     */
    @Test
    public void testUserStatistics() {
        logger.info("=== 测试用户统计 ===");
        
        int totalUsers = userService.getTotalUserCount();
        logger.info("用户总数: {}", totalUsers);
        
        int activeUsers = userService.getActiveUserCount();
        logger.info("活跃用户数: {}", activeUsers);
        
        List<SysUser> enabledUsers = userService.getUsersByStatus(1);
        logger.info("启用状态用户数: {}", enabledUsers.size());
        
        List<SysUser> disabledUsers = userService.getUsersByStatus(0);
        logger.info("禁用状态用户数: {}", disabledUsers.size());
    }
}
