package com.example.mybatisrbac.vo;

import com.example.mybatisrbac.entity.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户视图对象
 * 用于前端展示的用户信息
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户视图对象")
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long id;

    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "admin")
    private String username;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    /**
     * 头像URL
     */
    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 状态描述
     */
    @Schema(description = "状态描述", example = "启用")
    private String statusText;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "系统管理员")
    private String createByName;

    /**
     * 更新人姓名
     */
    @Schema(description = "更新人姓名", example = "系统管理员")
    private String updateByName;

    /**
     * 用户角色列表
     */
    @Schema(description = "用户角色列表")
    private List<Role> roles;

    /**
     * 角色名称列表（逗号分隔）
     */
    @Schema(description = "角色名称列表", example = "管理员,普通用户")
    private String roleNames;

    /**
     * 获取状态文本
     */
    public String getStatusText() {
        if (status == null) {
            return "未知";
        }
        return status == 1 ? "启用" : "禁用";
    }

    /**
     * 获取角色名称列表
     */
    public String getRoleNames() {
        if (roles == null || roles.isEmpty()) {
            return "无角色";
        }
        return roles.stream()
                .map(Role::getRoleName)
                .reduce((a, b) -> a + "," + b)
                .orElse("无角色");
    }
}
