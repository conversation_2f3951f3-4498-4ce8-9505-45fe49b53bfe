package springcontainer;

import java.lang.annotation.*;
import java.lang.reflect.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简化版Spring容器实现演示
 * 展示IoC容器和依赖注入的基本原理
 */
public class SimpleSpringContainerDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 简化版Spring容器演示 ===");
        
        try {
            // 创建简单容器
            SimpleContainer container = new SimpleContainer();
            
            // 注册Bean类
            container.registerBean("userRepository", UserRepositoryImpl.class);
            container.registerBean("emailService", EmailServiceImpl.class);
            container.registerBean("userService", UserServiceImpl.class);
            
            // 初始化容器（创建所有Bean并注入依赖）
            container.initialize();
            
            // 获取Bean并使用
            UserService userService = (UserService) container.getBean("userService");
            
            // 测试功能
            System.out.println("\n--- 测试用户服务 ---");
            User user = userService.createUser("Alice", "<EMAIL>");
            System.out.println("创建的用户: " + user);
            
            User foundUser = userService.findById(1L);
            System.out.println("查找的用户: " + foundUser);
            
            // 显示容器信息
            container.showContainerInfo();
            
        } catch (Exception e) {
            System.err.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

/**
 * 简化版IoC容器
 */
class SimpleContainer {
    
    // Bean定义注册表
    private final Map<String, Class<?>> beanDefinitions = new HashMap<>();
    
    // Bean实例缓存（单例）
    private final Map<String, Object> singletonBeans = new ConcurrentHashMap<>();
    
    // 正在创建的Bean（用于检测循环依赖）
    private final Set<String> beansCurrentlyInCreation = new HashSet<>();
    
    /**
     * 注册Bean定义
     */
    public void registerBean(String beanName, Class<?> beanClass) {
        beanDefinitions.put(beanName, beanClass);
        System.out.println("注册Bean: " + beanName + " -> " + beanClass.getSimpleName());
    }
    
    /**
     * 初始化容器
     */
    public void initialize() throws Exception {
        System.out.println("\n开始初始化容器...");
        
        // 创建所有Bean
        for (String beanName : beanDefinitions.keySet()) {
            getBean(beanName);
        }
        
        System.out.println("容器初始化完成");
    }
    
    /**
     * 获取Bean实例
     */
    public Object getBean(String beanName) throws Exception {
        // 1. 检查单例缓存
        Object bean = singletonBeans.get(beanName);
        if (bean != null) {
            return bean;
        }
        
        // 2. 检查循环依赖
        if (beansCurrentlyInCreation.contains(beanName)) {
            throw new RuntimeException("检测到循环依赖: " + beanName);
        }
        
        // 3. 创建Bean
        return createBean(beanName);
    }
    
    /**
     * 创建Bean实例
     */
    private Object createBean(String beanName) throws Exception {
        Class<?> beanClass = beanDefinitions.get(beanName);
        if (beanClass == null) {
            throw new RuntimeException("未找到Bean定义: " + beanName);
        }
        
        System.out.println("创建Bean: " + beanName);
        
        // 标记正在创建
        beansCurrentlyInCreation.add(beanName);
        
        try {
            // 1. 实例化Bean
            Object bean = instantiateBean(beanClass);
            
            // 2. 依赖注入
            injectDependencies(bean);
            
            // 3. 缓存Bean
            singletonBeans.put(beanName, bean);
            
            System.out.println("Bean创建完成: " + beanName);
            return bean;
            
        } finally {
            // 移除创建标记
            beansCurrentlyInCreation.remove(beanName);
        }
    }
    
    /**
     * 实例化Bean
     */
    private Object instantiateBean(Class<?> beanClass) throws Exception {
        // 查找构造函数
        Constructor<?>[] constructors = beanClass.getConstructors();
        
        for (Constructor<?> constructor : constructors) {
            if (constructor.isAnnotationPresent(Autowired.class) || 
                constructor.getParameterCount() == 0) {
                
                // 获取构造函数参数
                Class<?>[] parameterTypes = constructor.getParameterTypes();
                Object[] args = new Object[parameterTypes.length];
                
                // 注入构造函数参数
                for (int i = 0; i < parameterTypes.length; i++) {
                    String dependencyBeanName = findBeanNameByType(parameterTypes[i]);
                    if (dependencyBeanName != null) {
                        args[i] = getBean(dependencyBeanName);
                    }
                }
                
                return constructor.newInstance(args);
            }
        }
        
        // 默认使用无参构造函数
        return beanClass.newInstance();
    }
    
    /**
     * 依赖注入
     */
    private void injectDependencies(Object bean) throws Exception {
        Class<?> beanClass = bean.getClass();
        Field[] fields = beanClass.getDeclaredFields();
        
        for (Field field : fields) {
            if (field.isAnnotationPresent(Autowired.class)) {
                // 查找依赖Bean
                String dependencyBeanName = findBeanNameByType(field.getType());
                if (dependencyBeanName != null) {
                    Object dependency = getBean(dependencyBeanName);
                    
                    // 注入依赖
                    field.setAccessible(true);
                    field.set(bean, dependency);
                    
                    System.out.println("  注入依赖: " + field.getName() + " -> " + dependencyBeanName);
                }
            }
        }
    }
    
    /**
     * 根据类型查找Bean名称
     */
    private String findBeanNameByType(Class<?> type) {
        for (Map.Entry<String, Class<?>> entry : beanDefinitions.entrySet()) {
            if (type.isAssignableFrom(entry.getValue())) {
                return entry.getKey();
            }
        }
        return null;
    }
    
    /**
     * 显示容器信息
     */
    public void showContainerInfo() {
        System.out.println("\n=== 容器信息 ===");
        System.out.println("Bean定义数量: " + beanDefinitions.size());
        System.out.println("Bean实例数量: " + singletonBeans.size());
        
        System.out.println("\nBean定义:");
        beanDefinitions.forEach((name, clazz) -> 
            System.out.println("  " + name + " -> " + clazz.getSimpleName()));
        
        System.out.println("\nBean实例:");
        singletonBeans.forEach((name, bean) -> 
            System.out.println("  " + name + " -> " + bean.getClass().getSimpleName() + "@" + 
                             Integer.toHexString(bean.hashCode())));
    }
}

// ==================== 注解定义 ====================

@Target({ElementType.CONSTRUCTOR, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@interface Autowired {
}

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@interface Component {
    String value() default "";
}

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@interface Service {
    String value() default "";
}

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@interface Repository {
    String value() default "";
}

// ==================== 业务类 ====================

/**
 * 用户实体
 */
class User {
    private Long id;
    private String name;
    private String email;
    
    public User() {}
    
    public User(String name, String email) {
        this.name = name;
        this.email = email;
    }
    
    // getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    @Override
    public String toString() {
        return "User{id=" + id + ", name='" + name + "', email='" + email + "'}";
    }
}

/**
 * 用户仓库接口
 */
interface UserRepository {
    User save(User user);
    User findById(Long id);
}

/**
 * 用户仓库实现
 */
@Repository
class UserRepositoryImpl implements UserRepository {
    private final Map<Long, User> users = new HashMap<>();
    private Long nextId = 1L;
    
    @Override
    public User save(User user) {
        if (user.getId() == null) {
            user.setId(nextId++);
        }
        users.put(user.getId(), user);
        System.out.println("  UserRepository: 保存用户 " + user);
        return user;
    }
    
    @Override
    public User findById(Long id) {
        User user = users.get(id);
        System.out.println("  UserRepository: 查找用户 " + id + " -> " + user);
        return user;
    }
}

/**
 * 邮件服务接口
 */
interface EmailService {
    void sendWelcomeEmail(String email);
}

/**
 * 邮件服务实现
 */
@Service
class EmailServiceImpl implements EmailService {
    
    @Override
    public void sendWelcomeEmail(String email) {
        System.out.println("  EmailService: 发送欢迎邮件到 " + email);
    }
}

/**
 * 用户服务接口
 */
interface UserService {
    User createUser(String name, String email);
    User findById(Long id);
}

/**
 * 用户服务实现
 */
@Service
class UserServiceImpl implements UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private EmailService emailService;
    
    // 也可以使用构造函数注入
    // @Autowired
    // public UserServiceImpl(UserRepository userRepository, EmailService emailService) {
    //     this.userRepository = userRepository;
    //     this.emailService = emailService;
    // }
    
    @Override
    public User createUser(String name, String email) {
        System.out.println("UserService: 创建用户 " + name);
        
        User user = new User(name, email);
        User savedUser = userRepository.save(user);
        emailService.sendWelcomeEmail(email);
        
        return savedUser;
    }
    
    @Override
    public User findById(Long id) {
        System.out.println("UserService: 查找用户 " + id);
        return userRepository.findById(id);
    }
}
