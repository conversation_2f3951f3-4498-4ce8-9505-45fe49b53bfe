<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mybatisdemo.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, phone, nickname, status, create_time, update_time
    </sql>

    <!-- 根据ID查询用户 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE username = #{username}
    </select>

    <!-- 查询所有用户 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询用户 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询用户 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据关键字搜索用户 -->
    <select id="searchUsers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE username LIKE CONCAT('%', #{keyword}, '%')
           OR nickname LIKE CONCAT('%', #{keyword}, '%')
           OR email LIKE CONCAT('%', #{keyword}, '%')
        ORDER BY create_time DESC
    </select>

    <!-- 插入用户 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (username, password, email, phone, nickname, status, create_time, update_time)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{nickname}, #{status},
                #{createTime}, #{updateTime})
    </insert>

    <!-- 更新用户信息 -->
    <update id="update">
        UPDATE user
        <set>
            <if test="username != null and username != ''">
                username = #{username},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="nickname != null and nickname != ''">
                nickname = #{nickname},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除用户 -->
    <delete id="deleteById">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 批量删除用户 -->
    <delete id="deleteBatch">
        DELETE FROM user WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 统计用户总数 -->
    <select id="countAll" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user
    </select>

    <!-- 根据状态统计用户数 -->
    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(*) FROM user WHERE status = #{status}
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0 FROM user WHERE username = #{username}
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0 FROM user WHERE email = #{email}
    </select>

</mapper>
