package com.example.service;

import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;

import java.util.List;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public interface SysUserService {
    
    /**
     * 根据ID查询用户
     */
    SysUser getUserById(Long id);
    
    /**
     * 根据用户名查询用户
     */
    SysUser getUserByUsername(String username);
    
    /**
     * 根据邮箱查询用户
     */
    SysUser getUserByEmail(String email);
    
    /**
     * 查询所有用户
     */
    List<SysUser> getAllUsers();
    
    /**
     * 根据状态查询用户
     */
    List<SysUser> getUsersByStatus(Integer status);
    
    /**
     * 创建用户
     */
    boolean createUser(SysUser user);
    
    /**
     * 更新用户
     */
    boolean updateUser(SysUser user);
    
    /**
     * 删除用户
     */
    boolean deleteUser(Long id);
    
    /**
     * 修改用户密码
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 重置用户密码
     */
    boolean resetPassword(Long userId, String newPassword);
    
    /**
     * 启用/禁用用户
     */
    boolean changeUserStatus(Long userId, Integer status);
    
    /**
     * 检查用户名是否存在
     */
    boolean isUsernameExists(String username);
    
    /**
     * 检查邮箱是否存在
     */
    boolean isEmailExists(String email);
    
    /**
     * 查询用户的角色列表
     */
    List<SysRole> getUserRoles(Long userId);
    
    /**
     * 查询用户的权限列表
     */
    List<SysPermission> getUserPermissions(Long userId);
    
    /**
     * 查询用户详细信息（包含角色和权限）
     */
    SysUser getUserWithRolesAndPermissions(Long userId);
    
    /**
     * 为用户分配角色
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds);
    
    /**
     * 移除用户的角色
     */
    boolean removeUserRole(Long userId, Long roleId);
    
    /**
     * 移除用户的所有角色
     */
    boolean removeAllUserRoles(Long userId);
    
    /**
     * 更新用户最后登录信息
     */
    boolean updateLastLogin(Long userId, String loginIp);
    
    /**
     * 统计用户总数
     */
    int getTotalUserCount();
    
    /**
     * 统计启用的用户数
     */
    int getActiveUserCount();
}
