package com.example.mybatisdemo.service.impl;

import com.example.mybatisdemo.common.enums.ResultCode;
import com.example.mybatisdemo.common.exception.BusinessException;
import com.example.mybatisdemo.entity.User;
import com.example.mybatisdemo.mapper.UserMapper;
import com.example.mybatisdemo.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    @Transactional(readOnly = true)
    public User getUserById(Long id) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户ID不能为空");
        }
        
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        return user;
    }
    
    @Override
    @Transactional(readOnly = true)
    public User getUserByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户名不能为空");
        }
        
        return userMapper.selectByUsername(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        return userMapper.selectAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "状态不能为空");
        }
        
        return userMapper.selectByStatus(status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getUsersByPage(Integer page, Integer size) {
        if (page == null || page < 1) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "页码必须大于0");
        }
        if (size == null || size < 1) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "每页大小必须大于0");
        }
        
        int offset = (page - 1) * size;
        return userMapper.selectByPage(offset, size);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> searchUsers(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return getAllUsers();
        }
        
        return userMapper.searchUsers(keyword);
    }
    
    @Override
    public User createUser(User user) {
        validateUser(user, true);
        
        // 检查用户名是否已存在
        if (userMapper.existsByUsername(user.getUsername())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && userMapper.existsByEmail(user.getEmail())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "邮箱已存在");
        }
        
        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATA_OPERATION_ERROR, "用户创建失败");
        }
        
        logger.info("用户创建成功，ID: {}, 用户名: {}", user.getId(), user.getUsername());
        return user;
    }
    
    @Override
    public User updateUser(User user) {
        if (user.getId() == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        User existingUser = userMapper.selectById(user.getId());
        if (existingUser == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        // 如果更新用户名，检查是否重复
        if (StringUtils.hasText(user.getUsername()) && 
            !user.getUsername().equals(existingUser.getUsername()) &&
            userMapper.existsByUsername(user.getUsername())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "用户名已存在");
        }
        
        // 如果更新邮箱，检查是否重复
        if (StringUtils.hasText(user.getEmail()) && 
            !user.getEmail().equals(existingUser.getEmail()) &&
            userMapper.existsByEmail(user.getEmail())) {
            throw new BusinessException(ResultCode.USER_ALREADY_EXISTS, "邮箱已存在");
        }
        
        user.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.update(user);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATA_OPERATION_ERROR, "用户更新失败");
        }
        
        logger.info("用户更新成功，ID: {}", user.getId());
        return userMapper.selectById(user.getId());
    }
    
    @Override
    public boolean deleteUser(Long id) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        int result = userMapper.deleteById(id);
        if (result > 0) {
            logger.info("用户删除成功，ID: {}", id);
            return true;
        }
        
        return false;
    }
    
    @Override
    public int deleteUsers(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户ID列表不能为空");
        }
        
        int result = userMapper.deleteBatch(ids);
        logger.info("批量删除用户成功，删除数量: {}", result);
        return result;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long getTotalUserCount() {
        return userMapper.countAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public Long getUserCountByStatus(Integer status) {
        if (status == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "状态不能为空");
        }
        
        return userMapper.countByStatus(status);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameExists(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        
        return userMapper.existsByUsername(username);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean isEmailExists(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        
        return userMapper.existsByEmail(email);
    }
    
    @Override
    public boolean enableUser(Long id) {
        return updateUserStatus(id, 1);
    }
    
    @Override
    public boolean disableUser(Long id) {
        return updateUserStatus(id, 0);
    }
    
    /**
     * 更新用户状态
     */
    private boolean updateUserStatus(Long id, Integer status) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户ID不能为空");
        }
        
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        
        user.setStatus(status);
        user.setUpdateTime(LocalDateTime.now());
        
        int result = userMapper.update(user);
        if (result > 0) {
            logger.info("用户状态更新成功，ID: {}, 状态: {}", id, status);
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证用户信息
     */
    private void validateUser(User user, boolean isCreate) {
        if (user == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "用户信息不能为空");
        }
        
        if (isCreate || StringUtils.hasText(user.getUsername())) {
            if (!StringUtils.hasText(user.getUsername())) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "用户名不能为空");
            }
            if (user.getUsername().length() < 3 || user.getUsername().length() > 50) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "用户名长度必须在3-50个字符之间");
            }
        }
        
        if (isCreate || StringUtils.hasText(user.getPassword())) {
            if (!StringUtils.hasText(user.getPassword())) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "密码不能为空");
            }
            if (user.getPassword().length() < 6) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "密码长度不能少于6个字符");
            }
        }
        
        if (StringUtils.hasText(user.getEmail())) {
            if (!user.getEmail().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "邮箱格式不正确");
            }
        }
        
        if (StringUtils.hasText(user.getPhone())) {
            if (!user.getPhone().matches("^1[3-9]\\d{9}$")) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "手机号格式不正确");
            }
        }
    }
}
