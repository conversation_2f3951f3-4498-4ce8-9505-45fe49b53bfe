package opptest;

public class Rectangle extends Shape implements Drawable, Movable {
    private double width;
    private double height;
    private int x, y;
    public Rectangle(String color, double width, double height) {
        super(color);
        this.width = width;
        this.height = height;
    }

    @Override
    public void draw() {
        System.out.println("正在绘制矩形");
    }

    @Override
    public void move(int x, int y) {
        System.out.println("正在移动矩形"+x+","+y);
    }

    @Override
    public double getArea() {
        return width * height;
    }

    @Override
    public double getPerimeter() {
        return 2 * (width + height);
    }
}
