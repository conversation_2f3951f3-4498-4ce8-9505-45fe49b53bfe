package com.example.mybatisrbac.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 状态值验证注解
 * 验证状态值只能为0或1
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidStatusValidator.class)
@Documented
public @interface ValidStatus {
    
    String message() default "状态值只能为0或1";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
