import exception.ExceptionDemo;
import io.IODemo;

/**
 * 异常处理和IO操作测试运行器
 * 用于演示Java异常处理和IO操作的各种功能
 */
public class ExceptionIOTestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    Java异常处理和IO操作演示程序");
        System.out.println("========================================");
        
        // 运行异常处理演示
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           异常处理演示");
        System.out.println("=".repeat(50));
        
        try {
            ExceptionDemo exceptionDemo = new ExceptionDemo();
            exceptionDemo.main(new String[]{});
        } catch (Exception e) {
            System.out.println("异常处理演示出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 等待一下，让输出更清晰
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 运行IO操作演示
        System.out.println("\n" + "=".repeat(50));
        System.out.println("             IO操作演示");
        System.out.println("=".repeat(50));
        
        try {
            IODemo ioDemo = new IODemo();
            ioDemo.main(new String[]{});
        } catch (Exception e) {
            System.out.println("IO操作演示出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           演示程序结束");
        System.out.println("=".repeat(50));
        
        // 显示生成的文件
        showGeneratedFiles();
    }
    
    /**
     * 显示演示程序生成的文件
     */
    private static void showGeneratedFiles() {
        System.out.println("\n演示程序生成的文件:");
        
        String[] files = {
            "test_exception.txt",
            "source.txt", 
            "byte_copy.txt",
            "character_test.txt",
            "buffered_test.txt",
            "students.ser",
            "nio_test/nio_test.txt"
        };
        
        for (String file : files) {
            System.out.println("  - " + file);
        }
        
        System.out.println("\n注意事项:");
        System.out.println("1. 某些文件可能因为异常而未能创建");
        System.out.println("2. .ser文件是二进制序列化文件，无法直接查看");
        System.out.println("3. 可以查看其他文本文件来了解IO操作结果");
        System.out.println("4. 运行程序前请确保有文件写入权限");
    }
}
