# Java多线程编程详解

## 目录
1. [多线程基础概念](#多线程基础概念)
2. [线程创建方式](#线程创建方式)
3. [线程生命周期](#线程生命周期)
4. [线程同步机制](#线程同步机制)
5. [线程通信](#线程通信)
6. [线程池](#线程池)
7. [并发工具类](#并发工具类)
8. [原子类](#原子类)
9. [并发集合](#并发集合)
10. [实际应用场景](#实际应用场景)
11. [最佳实践](#最佳实践)

## 多线程基础概念

### 什么是线程
线程是程序执行的最小单位，是进程中的一个执行路径。一个进程可以包含多个线程，这些线程共享进程的内存空间和系统资源。

### 进程 vs 线程
| 特性 | 进程 | 线程 |
|------|------|------|
| 定义 | 程序的一次执行 | 进程中的执行单元 |
| 内存空间 | 独立的内存空间 | 共享进程内存空间 |
| 创建开销 | 较大 | 较小 |
| 通信方式 | IPC（进程间通信） | 共享内存 |
| 崩溃影响 | 不影响其他进程 | 可能影响整个进程 |

### 并发 vs 并行
- **并发（Concurrency）**: 多个任务在同一时间段内交替执行
- **并行（Parallelism）**: 多个任务在同一时刻同时执行

### 多线程的优势
1. **提高程序响应性** - 避免阻塞用户界面
2. **提高资源利用率** - 充分利用多核CPU
3. **提高程序吞吐量** - 同时处理多个任务
4. **模块化设计** - 将复杂任务分解为独立的线程

### 多线程的挑战
1. **线程安全问题** - 数据竞争和不一致性
2. **死锁问题** - 线程相互等待资源
3. **性能开销** - 线程创建和上下文切换
4. **调试困难** - 并发bug难以重现

## 线程创建方式

### 1. 继承Thread类

```java
public class ThreadDemo extends Thread {
    private String threadName;
    
    public ThreadDemo(String name) {
        this.threadName = name;
    }
    
    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(threadName + " - 计数: " + i);
            try {
                Thread.sleep(1000); // 暂停1秒
            } catch (InterruptedException e) {
                System.out.println(threadName + " 被中断");
                return;
            }
        }
        System.out.println(threadName + " 执行完成");
    }
    
    public static void main(String[] args) {
        ThreadDemo thread1 = new ThreadDemo("线程1");
        ThreadDemo thread2 = new ThreadDemo("线程2");
        
        thread1.start(); // 启动线程
        thread2.start();
        
        System.out.println("主线程继续执行");
    }
}
```

### 2. 实现Runnable接口

```java
public class RunnableDemo implements Runnable {
    private String taskName;
    
    public RunnableDemo(String name) {
        this.taskName = name;
    }
    
    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(taskName + " - 执行步骤: " + i);
            try {
                Thread.sleep(800);
            } catch (InterruptedException e) {
                System.out.println(taskName + " 被中断");
                return;
            }
        }
        System.out.println(taskName + " 任务完成");
    }
    
    public static void main(String[] args) {
        // 创建Runnable对象
        RunnableDemo task1 = new RunnableDemo("任务A");
        RunnableDemo task2 = new RunnableDemo("任务B");
        
        // 创建Thread对象并启动
        Thread thread1 = new Thread(task1);
        Thread thread2 = new Thread(task2);
        
        thread1.start();
        thread2.start();
        
        // 使用Lambda表达式创建线程
        Thread thread3 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                System.out.println("Lambda线程 - " + i);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        thread3.start();
    }
}
```

### 3. 实现Callable接口

```java
import java.util.concurrent.*;

public class CallableDemo implements Callable<String> {
    private String taskName;
    private int duration;
    
    public CallableDemo(String name, int duration) {
        this.taskName = name;
        this.duration = duration;
    }
    
    @Override
    public String call() throws Exception {
        System.out.println(taskName + " 开始执行");
        
        for (int i = 1; i <= 5; i++) {
            System.out.println(taskName + " - 进度: " + (i * 20) + "%");
            Thread.sleep(duration);
        }
        
        return taskName + " 执行完成，耗时: " + (duration * 5) + "ms";
    }
    
    public static void main(String[] args) {
        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        // 创建Callable任务
        CallableDemo task1 = new CallableDemo("计算任务1", 300);
        CallableDemo task2 = new CallableDemo("计算任务2", 500);
        
        try {
            // 提交任务并获取Future对象
            Future<String> future1 = executor.submit(task1);
            Future<String> future2 = executor.submit(task2);
            
            // 获取任务结果（会阻塞直到任务完成）
            String result1 = future1.get();
            String result2 = future2.get();
            
            System.out.println("结果1: " + result1);
            System.out.println("结果2: " + result2);
            
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
    }
}
```

## 线程生命周期

### 线程状态
Java线程有以下几种状态：

1. **NEW** - 新建状态，线程被创建但未启动
2. **RUNNABLE** - 可运行状态，包括运行中和就绪状态
3. **BLOCKED** - 阻塞状态，等待获取锁
4. **WAITING** - 等待状态，无限期等待其他线程的特定操作
5. **TIMED_WAITING** - 超时等待状态，在指定时间内等待
6. **TERMINATED** - 终止状态，线程执行完成

### 线程状态转换图

```
    NEW
     ↓ start()
  RUNNABLE ←→ BLOCKED (等待锁)
     ↓
  WAITING/TIMED_WAITING (wait/sleep/join)
     ↓
  TERMINATED
```

### 线程状态示例

```java
public class ThreadStateDemo {
    public static void main(String[] args) throws InterruptedException {
        Thread thread = new Thread(() -> {
            try {
                System.out.println("线程开始执行");
                Thread.sleep(2000); // TIMED_WAITING状态
                System.out.println("线程执行完成");
            } catch (InterruptedException e) {
                System.out.println("线程被中断");
            }
        });
        
        System.out.println("创建后状态: " + thread.getState()); // NEW
        
        thread.start();
        System.out.println("启动后状态: " + thread.getState()); // RUNNABLE
        
        Thread.sleep(500);
        System.out.println("运行中状态: " + thread.getState()); // TIMED_WAITING
        
        thread.join(); // 等待线程完成
        System.out.println("完成后状态: " + thread.getState()); // TERMINATED
    }
}
```

## 线程同步机制

### 1. synchronized关键字

#### 同步方法
```java
public class SynchronizedMethodDemo {
    private int count = 0;
    
    // 同步实例方法
    public synchronized void increment() {
        count++;
        System.out.println(Thread.currentThread().getName() + " - count: " + count);
    }
    
    // 同步静态方法
    public static synchronized void staticMethod() {
        System.out.println("静态同步方法");
    }
    
    public int getCount() {
        return count;
    }
}
```

#### 同步代码块
```java
public class SynchronizedBlockDemo {
    private int count = 0;
    private final Object lock = new Object();
    
    public void increment() {
        synchronized (lock) { // 同步代码块
            count++;
            System.out.println(Thread.currentThread().getName() + " - count: " + count);
        }
    }
    
    public void decrement() {
        synchronized (this) { // 使用this作为锁
            count--;
            System.out.println(Thread.currentThread().getName() + " - count: " + count);
        }
    }
}
```

### 2. Lock接口

```java
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class LockDemo {
    private int count = 0;
    private final Lock lock = new ReentrantLock();

    public void increment() {
        lock.lock(); // 获取锁
        try {
            count++;
            System.out.println(Thread.currentThread().getName() + " - count: " + count);
        } finally {
            lock.unlock(); // 释放锁
        }
    }

    public void tryLockExample() {
        if (lock.tryLock()) { // 尝试获取锁
            try {
                // 执行临界区代码
                System.out.println("获取锁成功");
            } finally {
                lock.unlock();
            }
        } else {
            System.out.println("获取锁失败");
        }
    }
}
```

### 3. volatile关键字

```java
public class VolatileDemo {
    private volatile boolean flag = false;
    private int count = 0;

    public void writer() {
        count = 42;
        flag = true; // volatile写操作
    }

    public void reader() {
        if (flag) { // volatile读操作
            System.out.println("count = " + count); // 保证能看到42
        }
    }
}
```

### 4. 读写锁

```java
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

public class ReadWriteLockDemo {
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private String data = "初始数据";

    public String read() {
        lock.readLock().lock(); // 获取读锁
        try {
            System.out.println(Thread.currentThread().getName() + " 读取: " + data);
            Thread.sleep(1000); // 模拟读取耗时
            return data;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        } finally {
            lock.readLock().unlock(); // 释放读锁
        }
    }

    public void write(String newData) {
        lock.writeLock().lock(); // 获取写锁
        try {
            System.out.println(Thread.currentThread().getName() + " 写入: " + newData);
            this.data = newData;
            Thread.sleep(1000); // 模拟写入耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.writeLock().unlock(); // 释放写锁
        }
    }
}
```

## 线程通信

### 1. wait/notify机制

```java
public class WaitNotifyDemo {
    private final Object lock = new Object();
    private boolean condition = false;

    public void waitingMethod() {
        synchronized (lock) {
            while (!condition) {
                try {
                    System.out.println(Thread.currentThread().getName() + " 开始等待");
                    lock.wait(); // 等待条件满足
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
            System.out.println(Thread.currentThread().getName() + " 条件满足，继续执行");
        }
    }

    public void notifyingMethod() {
        synchronized (lock) {
            condition = true;
            System.out.println(Thread.currentThread().getName() + " 设置条件为true");
            lock.notifyAll(); // 唤醒所有等待的线程
        }
    }
}
```

### 2. Condition接口

```java
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class ConditionDemo {
    private final Lock lock = new ReentrantLock();
    private final Condition condition = lock.newCondition();
    private boolean ready = false;

    public void waitForCondition() {
        lock.lock();
        try {
            while (!ready) {
                System.out.println(Thread.currentThread().getName() + " 等待条件");
                condition.await(); // 等待条件
            }
            System.out.println(Thread.currentThread().getName() + " 条件满足");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
    }

    public void signalCondition() {
        lock.lock();
        try {
            ready = true;
            System.out.println(Thread.currentThread().getName() + " 发送信号");
            condition.signalAll(); // 唤醒所有等待的线程
        } finally {
            lock.unlock();
        }
    }
}
```

## 线程池

### 1. Executor框架

```java
import java.util.concurrent.*;

public class ExecutorDemo {
    public static void main(String[] args) {
        // 1. 固定大小线程池
        ExecutorService fixedPool = Executors.newFixedThreadPool(3);

        // 2. 缓存线程池
        ExecutorService cachedPool = Executors.newCachedThreadPool();

        // 3. 单线程池
        ExecutorService singlePool = Executors.newSingleThreadExecutor();

        // 4. 定时任务线程池
        ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(2);

        // 提交任务
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            fixedPool.submit(() -> {
                System.out.println("任务" + taskId + " 在 " +
                    Thread.currentThread().getName() + " 中执行");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }

        // 定时任务
        scheduledPool.scheduleAtFixedRate(() -> {
            System.out.println("定时任务执行: " + System.currentTimeMillis());
        }, 0, 3, TimeUnit.SECONDS);

        // 关闭线程池
        fixedPool.shutdown();
        cachedPool.shutdown();
        singlePool.shutdown();

        // 等待5秒后关闭定时任务
        try {
            Thread.sleep(15000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        scheduledPool.shutdown();
    }
}
```

### 2. ThreadPoolExecutor详解

```java
import java.util.concurrent.*;

public class ThreadPoolExecutorDemo {
    public static void main(String[] args) {
        // 自定义线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            2,                      // 核心线程数
            4,                      // 最大线程数
            60L,                    // 空闲线程存活时间
            TimeUnit.SECONDS,       // 时间单位
            new LinkedBlockingQueue<>(10), // 工作队列
            new ThreadFactory() {   // 线程工厂
                private int count = 1;
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "自定义线程-" + count++);
                    t.setDaemon(false);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );

        // 监控线程池状态
        ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();
        monitor.scheduleAtFixedRate(() -> {
            System.out.println("线程池状态 - 活跃线程: " + executor.getActiveCount() +
                ", 完成任务: " + executor.getCompletedTaskCount() +
                ", 队列大小: " + executor.getQueue().size());
        }, 0, 1, TimeUnit.SECONDS);

        // 提交任务
        for (int i = 1; i <= 15; i++) {
            final int taskId = i;
            executor.submit(() -> {
                System.out.println("任务" + taskId + " 开始执行 - " +
                    Thread.currentThread().getName());
                try {
                    Thread.sleep(3000); // 模拟任务执行
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.println("任务" + taskId + " 执行完成");
            });
        }

        // 等待任务完成
        try {
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        executor.shutdown();
        monitor.shutdown();
    }
}
```

## 并发工具类

### 1. CountDownLatch

```java
import java.util.concurrent.CountDownLatch;

public class CountDownLatchDemo {
    public static void main(String[] args) throws InterruptedException {
        int workerCount = 3;
        CountDownLatch latch = new CountDownLatch(workerCount);

        // 启动工作线程
        for (int i = 1; i <= workerCount; i++) {
            final int workerId = i;
            new Thread(() -> {
                try {
                    System.out.println("工作者" + workerId + " 开始工作");
                    Thread.sleep(2000 + workerId * 1000); // 模拟不同的工作时间
                    System.out.println("工作者" + workerId + " 完成工作");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown(); // 计数器减1
                }
            }).start();
        }

        System.out.println("主线程等待所有工作者完成...");
        latch.await(); // 等待计数器归零
        System.out.println("所有工作者完成，主线程继续执行");
    }
}
```

### 2. CyclicBarrier

```java
import java.util.concurrent.CyclicBarrier;

public class CyclicBarrierDemo {
    public static void main(String[] args) {
        int playerCount = 3;

        CyclicBarrier barrier = new CyclicBarrier(playerCount, () -> {
            System.out.println("所有玩家准备就绪，游戏开始！");
        });

        // 启动玩家线程
        for (int i = 1; i <= playerCount; i++) {
            final int playerId = i;
            new Thread(() -> {
                try {
                    System.out.println("玩家" + playerId + " 正在准备...");
                    Thread.sleep(1000 + playerId * 500); // 模拟准备时间
                    System.out.println("玩家" + playerId + " 准备完成，等待其他玩家");

                    barrier.await(); // 等待所有玩家准备完成

                    System.out.println("玩家" + playerId + " 开始游戏");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }).start();
        }
    }
}
```

### 3. Semaphore

```java
import java.util.concurrent.Semaphore;

public class SemaphoreDemo {
    public static void main(String[] args) {
        // 停车场有3个车位
        Semaphore parkingLot = new Semaphore(3);

        // 5辆车尝试停车
        for (int i = 1; i <= 5; i++) {
            final int carId = i;
            new Thread(() -> {
                try {
                    System.out.println("车辆" + carId + " 尝试进入停车场");
                    parkingLot.acquire(); // 获取许可证

                    System.out.println("车辆" + carId + " 成功停车");
                    Thread.sleep(3000); // 停车时间
                    System.out.println("车辆" + carId + " 离开停车场");

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    parkingLot.release(); // 释放许可证
                }
            }).start();
        }
    }
}
```

## 原子类

### AtomicInteger示例

```java
import java.util.concurrent.atomic.AtomicInteger;

public class AtomicDemo {
    private AtomicInteger count = new AtomicInteger(0);

    public void increment() {
        int newValue = count.incrementAndGet(); // 原子性递增
        System.out.println(Thread.currentThread().getName() + " - count: " + newValue);
    }

    public void compareAndSet() {
        int expectedValue = 5;
        int newValue = 10;
        boolean success = count.compareAndSet(expectedValue, newValue);
        System.out.println("CAS操作" + (success ? "成功" : "失败") +
            ", 当前值: " + count.get());
    }

    public static void main(String[] args) throws InterruptedException {
        AtomicDemo demo = new AtomicDemo();

        // 启动多个线程进行递增操作
        for (int i = 0; i < 5; i++) {
            new Thread(demo::increment).start();
        }

        Thread.sleep(1000);
        demo.compareAndSet();
    }
}
```

## 并发集合

### ConcurrentHashMap示例

```java
import java.util.concurrent.ConcurrentHashMap;

public class ConcurrentCollectionDemo {
    public static void main(String[] args) {
        ConcurrentHashMap<String, Integer> map = new ConcurrentHashMap<>();

        // 多线程安全的操作
        for (int i = 0; i < 5; i++) {
            final int threadId = i;
            new Thread(() -> {
                for (int j = 0; j < 3; j++) {
                    String key = "key" + (threadId * 3 + j);
                    map.put(key, threadId * 3 + j);
                    System.out.println(Thread.currentThread().getName() +
                        " 添加: " + key + " = " + (threadId * 3 + j));
                }
            }).start();
        }

        // 等待所有线程完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        System.out.println("最终结果: " + map);
    }
}
```

## 实际应用场景

### 1. 生产者消费者模式

```java
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class ProducerConsumerDemo {
    private static final int BUFFER_SIZE = 5;
    private static final BlockingQueue<Integer> buffer = new LinkedBlockingQueue<>(BUFFER_SIZE);

    static class Producer implements Runnable {
        @Override
        public void run() {
            try {
                for (int i = 1; i <= 10; i++) {
                    buffer.put(i); // 阻塞式添加
                    System.out.println("生产者生产: " + i + ", 缓冲区大小: " + buffer.size());
                    Thread.sleep(500);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    static class Consumer implements Runnable {
        @Override
        public void run() {
            try {
                while (true) {
                    Integer item = buffer.take(); // 阻塞式获取
                    System.out.println("消费者消费: " + item + ", 缓冲区大小: " + buffer.size());
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    public static void main(String[] args) {
        Thread producer = new Thread(new Producer(), "生产者");
        Thread consumer = new Thread(new Consumer(), "消费者");

        producer.start();
        consumer.start();

        try {
            producer.join(); // 等待生产者完成
            Thread.sleep(5000); // 让消费者消费完剩余产品
            consumer.interrupt(); // 中断消费者
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
```

### 2. 线程安全的单例模式

```java
public class ThreadSafeSingleton {
    // 使用volatile确保可见性
    private static volatile ThreadSafeSingleton instance;

    private ThreadSafeSingleton() {
        // 私有构造函数
    }

    // 双重检查锁定
    public static ThreadSafeSingleton getInstance() {
        if (instance == null) {
            synchronized (ThreadSafeSingleton.class) {
                if (instance == null) {
                    instance = new ThreadSafeSingleton();
                }
            }
        }
        return instance;
    }

    // 静态内部类方式（推荐）
    private static class SingletonHolder {
        private static final ThreadSafeSingleton INSTANCE = new ThreadSafeSingleton();
    }

    public static ThreadSafeSingleton getInstanceByInnerClass() {
        return SingletonHolder.INSTANCE;
    }
}
```

## 最佳实践

### 1. 线程安全编程原则

```java
public class ThreadSafetyPrinciples {

    // ✅ 好的做法：使用不可变对象
    private final String immutableField = "不可变";

    // ✅ 好的做法：使用volatile确保可见性
    private volatile boolean flag = false;

    // ✅ 好的做法：使用线程安全的集合
    private final ConcurrentHashMap<String, String> safeMap = new ConcurrentHashMap<>();

    // ❌ 避免：直接使用非线程安全的集合
    // private final HashMap<String, String> unsafeMap = new HashMap<>();

    // ✅ 好的做法：最小化同步范围
    private final Object lock = new Object();
    private int count = 0;

    public void goodSynchronization() {
        // 只同步必要的代码块
        synchronized (lock) {
            count++;
        }
        // 非临界区代码不在同步块内
        System.out.println("当前计数: " + count);
    }

    // ❌ 避免：过大的同步范围
    public synchronized void badSynchronization() {
        count++;
        // 大量非临界区代码
        try {
            Thread.sleep(1000); // 不应该在同步方法中
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        System.out.println("当前计数: " + count);
    }
}
```

### 2. 避免死锁

```java
public class DeadlockPrevention {
    private final Object lock1 = new Object();
    private final Object lock2 = new Object();

    // ❌ 可能导致死锁的代码
    public void method1() {
        synchronized (lock1) {
            System.out.println("方法1获取锁1");
            synchronized (lock2) {
                System.out.println("方法1获取锁2");
            }
        }
    }

    public void method2() {
        synchronized (lock2) {
            System.out.println("方法2获取锁2");
            synchronized (lock1) {
                System.out.println("方法2获取锁1");
            }
        }
    }

    // ✅ 避免死锁：统一锁的获取顺序
    public void safeMethod1() {
        synchronized (lock1) {
            System.out.println("安全方法1获取锁1");
            synchronized (lock2) {
                System.out.println("安全方法1获取锁2");
            }
        }
    }

    public void safeMethod2() {
        synchronized (lock1) { // 与safeMethod1相同的顺序
            System.out.println("安全方法2获取锁1");
            synchronized (lock2) {
                System.out.println("安全方法2获取锁2");
            }
        }
    }

    // ✅ 使用tryLock避免死锁
    private final Lock reentrantLock1 = new ReentrantLock();
    private final Lock reentrantLock2 = new ReentrantLock();

    public void tryLockMethod() {
        boolean acquired1 = false;
        boolean acquired2 = false;

        try {
            acquired1 = reentrantLock1.tryLock(1, TimeUnit.SECONDS);
            if (acquired1) {
                acquired2 = reentrantLock2.tryLock(1, TimeUnit.SECONDS);
                if (acquired2) {
                    // 执行业务逻辑
                    System.out.println("成功获取所有锁");
                } else {
                    System.out.println("获取锁2失败");
                }
            } else {
                System.out.println("获取锁1失败");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            if (acquired2) reentrantLock2.unlock();
            if (acquired1) reentrantLock1.unlock();
        }
    }
}
```

### 3. 线程池最佳实践

```java
public class ThreadPoolBestPractices {

    // ✅ 好的做法：自定义线程池参数
    public static ThreadPoolExecutor createCustomThreadPool() {
        return new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(), // 核心线程数
            Runtime.getRuntime().availableProcessors() * 2, // 最大线程数
            60L, TimeUnit.SECONDS, // 空闲线程存活时间
            new LinkedBlockingQueue<>(1000), // 有界队列
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "CustomPool-" + threadNumber.getAndIncrement());
                    t.setDaemon(false);
                    t.setPriority(Thread.NORM_PRIORITY);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }

    // ✅ 好的做法：正确关闭线程池
    public static void shutdownThreadPoolGracefully(ExecutorService executor) {
        executor.shutdown(); // 不再接受新任务

        try {
            // 等待已提交任务完成
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow(); // 强制关闭

                // 等待任务响应中断
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    System.err.println("线程池未能正常关闭");
                }
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    // ✅ 好的做法：监控线程池状态
    public static void monitorThreadPool(ThreadPoolExecutor executor) {
        ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();

        monitor.scheduleAtFixedRate(() -> {
            System.out.println("=== 线程池监控 ===");
            System.out.println("核心线程数: " + executor.getCorePoolSize());
            System.out.println("最大线程数: " + executor.getMaximumPoolSize());
            System.out.println("当前线程数: " + executor.getPoolSize());
            System.out.println("活跃线程数: " + executor.getActiveCount());
            System.out.println("已完成任务数: " + executor.getCompletedTaskCount());
            System.out.println("队列中任务数: " + executor.getQueue().size());
            System.out.println("==================");
        }, 0, 5, TimeUnit.SECONDS);

        // 在适当时候关闭监控
        // monitor.shutdown();
    }
}
```

### 4. 常见错误和解决方案

```java
public class CommonMistakes {

    // ❌ 错误：在循环中创建线程
    public void badThreadCreation() {
        for (int i = 0; i < 1000; i++) {
            new Thread(() -> {
                // 执行任务
            }).start();
        }
    }

    // ✅ 正确：使用线程池
    public void goodThreadCreation() {
        ExecutorService executor = Executors.newFixedThreadPool(10);

        for (int i = 0; i < 1000; i++) {
            executor.submit(() -> {
                // 执行任务
            });
        }

        executor.shutdown();
    }

    // ❌ 错误：忽略InterruptedException
    public void badInterruptHandling() {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            // 什么都不做，忽略中断
        }
    }

    // ✅ 正确：正确处理中断
    public void goodInterruptHandling() {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            return; // 或者抛出异常
        }
    }

    // ❌ 错误：在finally块中不释放锁
    private final Lock lock = new ReentrantLock();

    public void badLockUsage() {
        lock.lock();
        try {
            // 业务逻辑
        } finally {
            // 忘记释放锁
        }
    }

    // ✅ 正确：在finally块中释放锁
    public void goodLockUsage() {
        lock.lock();
        try {
            // 业务逻辑
        } finally {
            lock.unlock(); // 确保释放锁
        }
    }
}
```

## 总结

Java多线程编程是一个复杂但强大的特性，掌握以下要点：

### 核心概念
- **线程创建**: Thread类、Runnable接口、Callable接口
- **线程同步**: synchronized、Lock、volatile
- **线程通信**: wait/notify、Condition
- **线程池**: Executor框架、ThreadPoolExecutor

### 并发工具
- **同步工具**: CountDownLatch、CyclicBarrier、Semaphore
- **原子类**: AtomicInteger、AtomicReference等
- **并发集合**: ConcurrentHashMap、BlockingQueue等

### 最佳实践
1. **优先使用线程池**而不是直接创建线程
2. **最小化同步范围**，避免不必要的锁竞争
3. **统一锁的获取顺序**，避免死锁
4. **正确处理中断**，保持线程的响应性
5. **使用不可变对象**，减少同步需求
6. **监控线程池状态**，及时发现问题

### 性能考虑
- 线程创建和上下文切换的开销
- 锁竞争对性能的影响
- 内存可见性和缓存一致性
- 合理设置线程池参数

掌握这些知识将帮助您编写高效、安全的多线程程序。
```
