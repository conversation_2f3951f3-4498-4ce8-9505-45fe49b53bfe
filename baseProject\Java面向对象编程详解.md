# Java面向对象编程详解

## 目录
1. [面向对象编程概述](#面向对象编程概述)
2. [类和对象](#类和对象)
3. [封装](#封装)
4. [继承](#继承)
5. [多态](#多态)
6. [抽象类和接口](#抽象类和接口)
7. [实际项目示例](#实际项目示例)
8. [最佳实践](#最佳实践)

## 面向对象编程概述

面向对象编程（Object-Oriented Programming，OOP）是一种编程范式，它将现实世界中的事物抽象为对象，通过对象之间的交互来解决问题。

### 面向对象的四大特性
1. **封装（Encapsulation）** - 隐藏对象的内部实现细节
2. **继承（Inheritance）** - 子类继承父类的属性和方法
3. **多态（Polymorphism）** - 同一个接口，不同的实现
4. **抽象（Abstraction）** - 提取事物的共同特征

## 类和对象

### 什么是类？
类是对象的模板或蓝图，定义了对象的属性和行为。

### 什么是对象？
对象是类的实例，是具体存在的实体。

### 基本语法
```java
// 定义类
public class Student {
    // 属性（成员变量）
    private String name;
    private int age;
    private String studentId;
    
    // 构造方法
    public Student(String name, int age, String studentId) {
        this.name = name;
        this.age = age;
        this.studentId = studentId;
    }
    
    // 方法（行为）
    public void study() {
        System.out.println(name + "正在学习");
    }
    
    public void showInfo() {
        System.out.println("姓名：" + name + "，年龄：" + age + "，学号：" + studentId);
    }
}

// 创建对象
Student student1 = new Student("张三", 20, "2023001");
student1.study();
student1.showInfo();
```

## 封装

封装是将数据和操作数据的方法绑定在一起，并隐藏对象的内部实现细节。

### 访问修饰符
- **private** - 只能在本类中访问
- **default**（无修饰符） - 在同一包中可访问
- **protected** - 在同一包或子类中可访问
- **public** - 在任何地方都可访问

### 封装示例
```java
public class BankAccount {
    private double balance;  // 私有属性，外部无法直接访问
    private String accountNumber;
    
    public BankAccount(String accountNumber, double initialBalance) {
        this.accountNumber = accountNumber;
        this.balance = initialBalance;
    }
    
    // 提供公共方法来访问私有属性
    public double getBalance() {
        return balance;
    }
    
    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            System.out.println("存款成功，当前余额：" + balance);
        } else {
            System.out.println("存款金额必须大于0");
        }
    }
    
    public void withdraw(double amount) {
        if (amount > 0 && amount <= balance) {
            balance -= amount;
            System.out.println("取款成功，当前余额：" + balance);
        } else {
            System.out.println("取款失败：金额无效或余额不足");
        }
    }
}
```

## 继承

继承允许一个类（子类）继承另一个类（父类）的属性和方法。

### 继承的语法
```java
// 父类
public class Animal {
    protected String name;
    protected int age;
    
    public Animal(String name, int age) {
        this.name = name;
        this.age = age;
    }
    
    public void eat() {
        System.out.println(name + "正在吃东西");
    }
    
    public void sleep() {
        System.out.println(name + "正在睡觉");
    }
}

// 子类继承父类
public class Dog extends Animal {
    private String breed;
    
    public Dog(String name, int age, String breed) {
        super(name, age);  // 调用父类构造方法
        this.breed = breed;
    }
    
    // 子类特有的方法
    public void bark() {
        System.out.println(name + "正在汪汪叫");
    }
    
    // 重写父类方法
    @Override
    public void eat() {
        System.out.println(name + "正在吃狗粮");
    }
}
```

### 方法重写（Override）
子类可以重写父类的方法，提供自己的实现。

```java
public class Cat extends Animal {
    public Cat(String name, int age) {
        super(name, age);
    }
    
    @Override
    public void eat() {
        System.out.println(name + "正在吃鱼");
    }
    
    public void meow() {
        System.out.println(name + "正在喵喵叫");
    }
}
```

## 多态

多态是指同一个方法调用，在不同的对象上会有不同的行为。

### 多态的实现方式
1. **方法重写** - 子类重写父类方法
2. **接口实现** - 不同类实现同一接口
3. **方法重载** - 同一类中方法名相同，参数不同

### 多态示例
```java
public class PolymorphismDemo {
    public static void main(String[] args) {
        // 多态：父类引用指向子类对象
        Animal animal1 = new Dog("旺财", 3, "金毛");
        Animal animal2 = new Cat("咪咪", 2);
        
        // 同样的方法调用，不同的行为
        animal1.eat();  // 输出：旺财正在吃狗粮
        animal2.eat();  // 输出：咪咪正在吃鱼
        
        // 使用多态处理不同类型的动物
        Animal[] animals = {
            new Dog("小黑", 4, "拉布拉多"),
            new Cat("小白", 1),
            new Dog("大黄", 5, "德牧")
        };
        
        for (Animal animal : animals) {
            animal.eat();  // 每个动物都会调用自己的eat方法
        }
    }
}
```

## 抽象类和接口

### 抽象类
抽象类是不能被实例化的类，通常包含抽象方法。

```java
public abstract class Shape {
    protected String color;
    
    public Shape(String color) {
        this.color = color;
    }
    
    // 抽象方法，子类必须实现
    public abstract double getArea();
    public abstract double getPerimeter();
    
    // 具体方法，子类可以直接使用
    public void displayColor() {
        System.out.println("颜色：" + color);
    }
}

public class Circle extends Shape {
    private double radius;
    
    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }
    
    @Override
    public double getArea() {
        return Math.PI * radius * radius;
    }
    
    @Override
    public double getPerimeter() {
        return 2 * Math.PI * radius;
    }
}
```

### 接口
接口定义了类必须实现的方法契约。

```java
public interface Drawable {
    void draw();  // 接口中的方法默认是public abstract
    
    // Java 8开始支持默认方法
    default void print() {
        System.out.println("正在打印图形");
    }
}

public interface Movable {
    void move(int x, int y);
}

// 一个类可以实现多个接口
public class Rectangle extends Shape implements Drawable, Movable {
    private double width;
    private double height;
    private int x, y;
    
    public Rectangle(String color, double width, double height) {
        super(color);
        this.width = width;
        this.height = height;
    }
    
    @Override
    public double getArea() {
        return width * height;
    }
    
    @Override
    public double getPerimeter() {
        return 2 * (width + height);
    }
    
    @Override
    public void draw() {
        System.out.println("绘制矩形：宽=" + width + "，高=" + height);
    }
    
    @Override
    public void move(int x, int y) {
        this.x = x;
        this.y = y;
        System.out.println("矩形移动到位置：(" + x + ", " + y + ")");
    }
}
```

## 实际项目示例：学生管理系统

让我们通过一个完整的学生管理系统来演示面向对象编程的应用。

### 系统设计
- **Person类** - 人员基类
- **Student类** - 学生类，继承Person
- **Teacher类** - 教师类，继承Person
- **Course类** - 课程类
- **School类** - 学校类，管理学生和教师

## 最佳实践

### 1. 设计原则
- **单一职责原则** - 一个类只负责一个功能
- **开闭原则** - 对扩展开放，对修改关闭
- **里氏替换原则** - 子类可以替换父类
- **接口隔离原则** - 使用多个专门的接口
- **依赖倒置原则** - 依赖抽象而不是具体实现

### 2. 编码规范
- 类名使用大驼峰命名法（PascalCase）
- 方法名和变量名使用小驼峰命名法（camelCase）
- 常量使用全大写字母，单词间用下划线分隔
- 合理使用访问修饰符
- 为类和方法编写清晰的注释

### 3. 常见错误避免
- 避免过度继承，优先使用组合
- 不要在构造方法中调用可重写的方法
- 正确实现equals()和hashCode()方法
- 避免在循环中创建大量对象

### 4. 性能优化
- 合理使用static关键字
- 避免不必要的对象创建
- 使用StringBuilder处理字符串拼接
- 及时释放不再使用的对象引用

## 总结

Java面向对象编程是一种强大的编程范式，通过封装、继承、多态和抽象四大特性，我们可以：

1. **提高代码复用性** - 通过继承和多态
2. **增强代码可维护性** - 通过封装和模块化
3. **提升开发效率** - 通过抽象和接口设计
4. **降低系统复杂度** - 通过合理的类层次结构

掌握面向对象编程思想，不仅能帮助你写出更好的Java代码，也为学习其他面向对象语言打下坚实基础。

---

*本文档包含了Java面向对象编程的核心概念和实际应用示例，建议结合代码实践来加深理解。*
