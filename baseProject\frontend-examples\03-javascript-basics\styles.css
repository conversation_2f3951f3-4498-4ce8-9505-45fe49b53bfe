/* JavaScript基础示例样式文件 */

/* ===== 全局样式 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* ===== 页面头部 ===== */
.header {
    text-align: center;
    padding: 40px 0;
    background: linear-gradient(45deg, #f39c12, #e74c3c);
    color: white;
    border-radius: 10px;
    margin-bottom: 30px;
}

.main-title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* ===== 导航菜单 ===== */
.navigation {
    background: #2c3e50;
    border-radius: 8px;
    margin-bottom: 30px;
    overflow: hidden;
}

.nav-list {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
}

.nav-list li {
    flex: 1;
    min-width: 150px;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    border-right: 1px solid #34495e;
}

.nav-link:hover {
    background-color: #f39c12;
    transform: translateY(-2px);
}

/* ===== 章节样式 ===== */
.section {
    margin-bottom: 50px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 5px solid #f39c12;
}

.section-title {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 2rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #f39c12;
}

/* ===== 演示容器 ===== */
.demo-container {
    margin-bottom: 30px;
}

.demo-container h3 {
    color: #2c3e50;
    margin: 30px 0 15px 0;
    font-size: 1.5rem;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

/* ===== 代码演示 ===== */
.code-demo {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.code-block, .output-block {
    padding: 20px;
}

.code-block {
    background: #2c3e50;
    color: white;
}

.code-block h4 {
    color: #f39c12;
    margin-bottom: 15px;
}

.code-block pre {
    background: #34495e;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.4;
}

.output-block {
    background: #ecf0f1;
}

.output-block h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.output {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #bdc3c7;
    margin-bottom: 15px;
    min-height: 60px;
    font-family: monospace;
    white-space: pre-wrap;
}

/* ===== 按钮样式 ===== */
.demo-btn {
    background: #f39c12;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: 5px;
}

.demo-btn:hover {
    background: #e67e22;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.demo-btn:active {
    transform: translateY(0);
}

/* ===== 表单样式 ===== */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #2c3e50;
}

input, select, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #bdc3c7;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #f39c12;
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.3);
}

/* ===== 数组演示 ===== */
.array-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.array-display {
    margin-top: 15px;
    padding: 15px;
    background: #ecf0f1;
    border-radius: 5px;
    min-height: 50px;
}

.array-item {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 5px 10px;
    margin: 3px;
    border-radius: 3px;
    font-size: 14px;
}

/* ===== 对象演示 ===== */
.object-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.person-display {
    margin-top: 15px;
    padding: 15px;
    background: #ecf0f1;
    border-radius: 5px;
    min-height: 50px;
}

.person-card {
    background: #2ecc71;
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
}

/* ===== 计算器演示 ===== */
.calculator-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.calculator {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.calculator input, .calculator select {
    width: auto;
    min-width: 100px;
}

.calc-result {
    margin-top: 15px;
    padding: 15px;
    background: #ecf0f1;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== DOM演示 ===== */
.dom-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.demo-box {
    background: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.demo-box.highlight {
    background: #f1c40f;
    color: white;
    transform: scale(1.05);
}

.controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* ===== 动态列表 ===== */
.create-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dynamic-list {
    list-style: none;
    margin-top: 15px;
}

.dynamic-list li {
    background: #3498db;
    color: white;
    padding: 10px 15px;
    margin: 5px 0;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease;
}

.dynamic-list li button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== 表单演示 ===== */
.form-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* ===== 事件演示 ===== */
.mouse-events {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.event-box {
    background: #ecf0f1;
    padding: 40px;
    border-radius: 8px;
    text-align: center;
    cursor: crosshair;
    transition: background-color 0.3s ease;
}

.event-box:hover {
    background: #d5dbdb;
}

.keyboard-events {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* ===== 拖拽演示 ===== */
.drag-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.drag-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.drag-item {
    padding: 15px 20px;
    background: #3498db;
    color: white;
    border-radius: 5px;
    cursor: move;
    user-select: none;
    transition: transform 0.2s ease;
}

.drag-item:hover {
    transform: scale(1.05);
}

.drag-item[data-color="red"] { background: #e74c3c; }
.drag-item[data-color="blue"] { background: #3498db; }
.drag-item[data-color="green"] { background: #2ecc71; }

.drop-zone {
    min-height: 100px;
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ecf0f1;
    transition: all 0.3s ease;
}

.drop-zone.drag-over {
    border-color: #f39c12;
    background: #fef9e7;
}

/* ===== 事件委托演示 ===== */
.delegation-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.button-container {
    margin: 15px 0;
    padding: 15px;
    background: #ecf0f1;
    border-radius: 5px;
    min-height: 60px;
}

.dynamic-button {
    background: #9b59b6;
    color: white;
    border: none;
    padding: 8px 15px;
    margin: 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dynamic-button:hover {
    background: #8e44ad;
}

/* ===== 定时器演示 ===== */
.timer-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 20px;
}

.timer-display {
    font-size: 3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
}

.timer-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

/* ===== Promise演示 ===== */
.promise-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.loading {
    text-align: center;
    padding: 20px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #f39c12;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Fetch演示 ===== */
.fetch-demo, .async-demo {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* ===== 待办事项应用 ===== */
.todo-app {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.todo-input {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.todo-input input {
    flex: 1;
}

.todo-filters {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.filter-btn {
    background: #ecf0f1;
    color: #2c3e50;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active, .filter-btn:hover {
    background: #f39c12;
    color: white;
}

.todo-list {
    list-style: none;
    margin-bottom: 15px;
}

.todo-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #ecf0f1;
    margin: 5px 0;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.todo-item.completed {
    opacity: 0.6;
    text-decoration: line-through;
}

.todo-item input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}

.todo-item span {
    flex: 1;
}

.todo-item button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin-left: 10px;
}

.todo-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #ecf0f1;
}

/* ===== 猜数字游戏 ===== */
.guess-game {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.game-info {
    text-align: center;
    margin-bottom: 20px;
}

.game-input {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.game-input input {
    width: 150px;
}

.game-feedback {
    text-align: center;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    font-weight: bold;
}

.game-feedback.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.game-feedback.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.game-feedback.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.guess-history {
    background: #ecf0f1;
    padding: 15px;
    border-radius: 5px;
    max-height: 150px;
    overflow-y: auto;
}

/* ===== 颜色选择器 ===== */
.color-picker {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.color-controls label {
    display: block;
    margin-bottom: 10px;
    color: #2c3e50;
}

.color-controls input[type="range"] {
    width: 100%;
    margin-bottom: 15px;
}

.color-display {
    text-align: center;
}

.color-preview {
    width: 200px;
    height: 200px;
    border-radius: 10px;
    margin: 0 auto 15px;
    border: 2px solid #bdc3c7;
    transition: all 0.3s ease;
}

.color-info p {
    margin: 5px 0;
    font-family: monospace;
    font-size: 14px;
}

/* ===== 页面底部 ===== */
.footer {
    background: #2c3e50;
    color: white;
    padding: 40px;
    border-radius: 10px;
    text-align: center;
}

.footer h2 {
    margin-bottom: 20px;
    color: #f39c12;
}

.next-steps {
    margin-top: 30px;
    text-align: left;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.next-steps h3 {
    color: #3498db;
    margin-bottom: 15px;
}

.next-steps ul {
    list-style-position: inside;
}

.next-steps li {
    margin-bottom: 8px;
    padding-left: 10px;
}

/* ===== 响应式设计 ===== */
@media (max-width: 992px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .nav-list {
        flex-direction: column;
    }
    
    .nav-link {
        border-right: none;
        border-bottom: 1px solid #34495e;
    }
    
    .code-demo {
        grid-template-columns: 1fr;
    }
    
    .color-picker {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-title {
        font-size: 1.8rem;
    }
    
    .section {
        padding: 20px 15px;
    }
    
    .calculator {
        flex-direction: column;
        align-items: stretch;
    }
    
    .calculator input, .calculator select {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .controls {
        justify-content: center;
    }
    
    .timer-display {
        font-size: 2rem;
    }
    
    .game-input {
        flex-direction: column;
        align-items: center;
    }
    
    .todo-input {
        flex-direction: column;
    }
    
    .todo-stats {
        flex-direction: column;
        gap: 10px;
    }
}
