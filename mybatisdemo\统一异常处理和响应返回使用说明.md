# 统一异常处理和响应返回使用说明

## 概述

本项目实现了统一的异常处理和响应返回机制，包含以下组件：

1. **ResultCode** - 统一响应状态码枚举
2. **Result<T>** - 统一响应结果类
3. **BusinessException** - 自定义业务异常
4. **GlobalExceptionHandler** - 全局异常处理器

## 1. 统一响应格式

所有API响应都使用统一的格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2025-07-21 12:00:00"
}
```

## 2. 使用方式

### 2.1 成功响应

```java
// 无数据的成功响应
return Result.success();

// 带数据的成功响应
return Result.success(data);

// 自定义消息的成功响应
return Result.success("操作完成", data);
```

### 2.2 错误响应

```java
// 使用预定义的错误码
return Result.error(ResultCode.USER_NOT_FOUND);

// 自定义错误消息
return Result.error("自定义错误消息");

// 自定义错误码和消息
return Result.error(9999, "自定义错误");
```

### 2.3 抛出业务异常

```java
// 简单业务异常
throw new BusinessException("用户不存在");

// 使用预定义错误码
throw new BusinessException(ResultCode.USER_NOT_FOUND);

// 自定义错误码
throw new BusinessException(9999, "自定义业务异常");
```

## 3. 测试API

启动应用后，可以测试以下API：

### 3.1 成功响应测试

```bash
# 无数据成功响应
GET http://localhost:8080/api/demo/success

# 带数据成功响应
GET http://localhost:8080/api/demo/success-with-data

# 自定义消息成功响应
GET http://localhost:8080/api/demo/success-custom-message
```

### 3.2 异常处理测试

```bash
# 业务异常
GET http://localhost:8080/api/demo/business-error

# 使用错误码的业务异常
GET http://localhost:8080/api/demo/business-error-with-code

# 自定义错误码异常
GET http://localhost:8080/api/demo/business-error-custom

# 系统异常
GET http://localhost:8080/api/demo/system-error
```

### 3.3 参数校验测试

```bash
# 参数校验异常（POST请求，body为空或格式错误）
POST http://localhost:8080/api/demo/validate-error
Content-Type: application/json

{
  "name": "",
  "age": null
}

# 缺少参数异常
GET http://localhost:8080/api/demo/missing-param

# 参数类型不匹配异常
GET http://localhost:8080/api/demo/type-mismatch/abc
```

## 4. 响应示例

### 4.1 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>"
  },
  "timestamp": "2025-07-21 12:00:00"
}
```

### 4.2 业务异常响应

```json
{
  "code": 2001,
  "message": "用户不存在",
  "data": null,
  "timestamp": "2025-07-21 12:00:00"
}
```

### 4.3 参数校验异常响应

```json
{
  "code": 422,
  "message": "name: 姓名不能为空; age: 年龄不能为空; ",
  "data": null,
  "timestamp": "2025-07-21 12:00:00"
}
```

### 4.4 系统异常响应

```json
{
  "code": 500,
  "message": "服务器内部错误",
  "data": null,
  "timestamp": "2025-07-21 12:00:00"
}
```

## 5. 扩展说明

### 5.1 添加新的错误码

在 `ResultCode` 枚举中添加新的错误码：

```java
// 新的业务错误码
ORDER_NOT_FOUND(5001, "订单不存在"),
ORDER_STATUS_ERROR(5002, "订单状态错误");
```

### 5.2 处理特定异常

在 `GlobalExceptionHandler` 中添加新的异常处理方法：

```java
@ExceptionHandler(CustomException.class)
@ResponseStatus(HttpStatus.BAD_REQUEST)
public Result<Object> handleCustomException(CustomException e, HttpServletRequest request) {
    logger.warn("自定义异常: {} - {}", request.getRequestURI(), e.getMessage());
    return Result.error(e.getCode(), e.getMessage());
}
```

## 6. 注意事项

1. 所有Controller方法都应该返回 `Result<T>` 类型
2. 业务逻辑中的错误应该抛出 `BusinessException`
3. 系统异常会被自动捕获并返回统一格式
4. 日志会记录异常信息，便于调试和监控
5. 参数校验使用 `@Valid` 注解，校验失败会自动处理

## 7. 最佳实践

1. **统一使用Result包装返回值**
2. **合理使用错误码分类**
3. **业务异常使用BusinessException**
4. **重要异常记录详细日志**
5. **前端根据code字段判断处理逻辑**
