package com.example.mybatisrbac.controller;

import com.example.mybatisrbac.common.Result;
import com.example.mybatisrbac.exception.BusinessException;
import com.example.mybatisrbac.util.ResponseUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 测试控制器
 * 用于测试数据库连接和Druid配置
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Tag(name = "系统测试", description = "数据库连接和系统功能测试接口")
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private DataSource dataSource;

    /**
     * 测试控制器根路径
     *
     * @return 测试信息
     */
    @Operation(summary = "测试接口列表", description = "获取所有可用的测试接口信息")
    @GetMapping
    public Result<String> index() {
        String testInfo = "测试控制器运行正常！可用的测试接口：\n" +
               "- GET /test/health - 健康检查\n" +
               "- GET /test/db - 数据库连接测试\n" +
               "- GET /test/datasource - 数据源信息\n" +
               "- GET /test/exception - 异常测试";

        return ResponseUtil.success("测试接口信息获取成功", testInfo);
    }

    /**
     * 测试数据库连接
     *
     * @return 连接测试结果
     */
    @Operation(summary = "数据库连接测试", description = "测试数据库连接是否正常")
    @GetMapping("/db")
    public Result<String> testDatabase() {
        try (Connection connection = dataSource.getConnection()) {
            String connectionInfo = "数据库连接成功！连接信息：" + connection.toString();
            return ResponseUtil.success("数据库连接测试成功", connectionInfo);
        } catch (SQLException e) {
            throw new BusinessException("数据库连接失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据源信息
     *
     * @return 数据源信息
     */
    @Operation(summary = "数据源信息", description = "获取当前数据源配置信息")
    @GetMapping("/datasource")
    public Result<String> getDataSourceInfo() {
        String dataSourceInfo = "数据源类型：" + dataSource.getClass().getName();
        return ResponseUtil.success("数据源信息获取成功", dataSourceInfo);
    }

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    @Operation(summary = "测试健康检查", description = "检查测试模块运行状态")
    @GetMapping("/health")
    public Result<String> health() {
        return ResponseUtil.success("健康检查成功", "测试模块运行正常！");
    }

    /**
     * 异常测试
     *
     * @return 异常信息
     */
    @Operation(summary = "异常测试", description = "测试全局异常处理机制")
    @GetMapping("/exception")
    public Result<String> testException() {
        throw new BusinessException("这是一个测试异常，用于验证全局异常处理器是否正常工作");
    }
}
