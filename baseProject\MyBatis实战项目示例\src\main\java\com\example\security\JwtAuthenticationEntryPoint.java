package com.example.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证入口点
 * 当用户访问需要认证的资源但未提供有效JWT令牌时，返回401错误
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationEntryPoint.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        
        logger.error("未认证访问: {} {}", request.getMethod(), request.getRequestURI());
        logger.error("认证异常: {}", authException.getMessage());
        
        // 设置响应状态码和内容类型
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        // 构建错误响应
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("code", 401);
        errorResponse.put("message", "未认证，请先登录");
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("path", request.getRequestURI());
        errorResponse.put("timestamp", System.currentTimeMillis());
        
        // 根据不同的异常类型返回不同的错误信息
        if (authException.getMessage().contains("JWT")) {
            errorResponse.put("message", "JWT令牌无效或已过期，请重新登录");
        } else if (authException.getMessage().contains("expired")) {
            errorResponse.put("message", "登录已过期，请重新登录");
        } else if (authException.getMessage().contains("malformed")) {
            errorResponse.put("message", "JWT令牌格式错误");
        }
        
        // 写入响应
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
    }
}
