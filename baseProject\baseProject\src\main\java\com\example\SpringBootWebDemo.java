package com.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Spring Boot Web应用程序主启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootApplication
@EnableTransactionManagement  // 启用事务管理
@EnableCaching               // 启用缓存
@EnableAsync                 // 启用异步处理
@EnableScheduling           // 启用定时任务
public class SpringBootWebDemo {
    
    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统属性
        System.setProperty("spring.devtools.restart.enabled", "true");
        System.setProperty("spring.devtools.livereload.enabled", "true");
        
        // 启动Spring Boot应用
        SpringApplication app = new SpringApplication(SpringBootWebDemo.class);
        
        // 设置默认配置文件
        app.setDefaultProperties(getDefaultProperties());
        
        // 运行应用
        app.run(args);
        
        // 输出启动信息
        System.out.println("========================================");
        System.out.println("🚀 Spring Boot Web应用启动成功！");
        System.out.println("📱 访问地址: http://localhost:8080");
        System.out.println("📚 API文档: http://localhost:8080/swagger-ui.html");
        System.out.println("📊 监控面板: http://localhost:8080/actuator");
        System.out.println("🔧 Druid监控: http://localhost:8080/druid");
        System.out.println("========================================");
    }
    
    /**
     * 获取默认配置属性
     * 
     * @return 默认配置属性
     */
    private static java.util.Properties getDefaultProperties() {
        java.util.Properties properties = new java.util.Properties();
        
        // 应用基本配置
        properties.setProperty("spring.application.name", "spring-boot-web-demo");
        properties.setProperty("server.port", "8080");
        properties.setProperty("server.servlet.context-path", "/");
        
        // 开发环境配置
        properties.setProperty("spring.profiles.active", "dev");
        
        // 日志配置
        properties.setProperty("logging.level.com.example", "DEBUG");
        properties.setProperty("logging.level.org.springframework.web", "DEBUG");
        
        // 数据源配置
        properties.setProperty("spring.datasource.url", 
            "***********************************************************************************************************");
        properties.setProperty("spring.datasource.username", "root");
        properties.setProperty("spring.datasource.password", "123456");
        properties.setProperty("spring.datasource.driver-class-name", "com.mysql.cj.jdbc.Driver");
        
        // MyBatis配置
        properties.setProperty("mybatis.mapper-locations", "classpath:mapper/*.xml");
        properties.setProperty("mybatis.type-aliases-package", "com.example.entity");
        properties.setProperty("mybatis.configuration.map-underscore-to-camel-case", "true");
        
        // Jackson配置
        properties.setProperty("spring.jackson.date-format", "yyyy-MM-dd HH:mm:ss");
        properties.setProperty("spring.jackson.time-zone", "GMT+8");
        
        // 文件上传配置
        properties.setProperty("spring.servlet.multipart.max-file-size", "10MB");
        properties.setProperty("spring.servlet.multipart.max-request-size", "100MB");
        
        return properties;
    }
}
