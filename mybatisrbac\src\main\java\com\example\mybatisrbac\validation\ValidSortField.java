package com.example.mybatisrbac.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

/**
 * 排序字段验证注解
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidSortFieldValidator.class)
@Documented
public @interface ValidSortField {
    
    String message() default "无效的排序字段";
    
    /**
     * 允许的字段值
     */
    String[] allowedFields() default {};
    
    /**
     * 实体类型，用于确定允许的字段
     */
    EntityType entityType() default EntityType.ROLE;
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 实体类型枚举
     */
    enum EntityType {
        ROLE, USER, PERMISSION
    }
}
