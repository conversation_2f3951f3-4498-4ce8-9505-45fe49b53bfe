import reflection.*;
import proxy.*;
import proxy.ProxyFactory;
import java.lang.reflect.Constructor;

/**
 * Java反射和代理测试运行器
 * 用于演示和测试反射和代理的各种功能
 */
public class ReflectionProxyTestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    Java反射和代理演示程序");
        System.out.println("========================================");
        
        try {
            // 1. 反射演示
            runDemo("Java反射演示", () -> ReflectionDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 2. 动态代理演示
            runDemo("Java动态代理演示", () -> ProxyDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 3. 综合应用演示
            runDemo("综合应用演示", () -> comprehensiveDemo());
            
        } catch (Exception e) {
            System.out.println("演示程序出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           演示程序结束");
        System.out.println("=".repeat(50));
        
        showSummary();
    }
    
    /**
     * 运行演示
     */
    private static void runDemo(String demoName, Runnable demo) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           " + demoName);
        System.out.println("=".repeat(50));
        
        try {
            demo.run();
        } catch (Exception e) {
            System.out.println(demoName + "出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示之间的等待
     */
    private static void waitBetweenDemos() {
        try {
            System.out.println("\n等待3秒后继续下一个演示...");
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 综合应用演示
     */
    private static void comprehensiveDemo() {
        System.out.println("=== 综合应用演示 ===");
        
        // 1. 反射 + 代理的组合应用
        reflectionWithProxyDemo();
        
        // 2. 动态配置和调用
        dynamicConfigurationDemo();
        
        // 3. 插件化架构演示
        pluginArchitectureDemo();
    }
    
    /**
     * 反射与代理组合演示
     */
    private static void reflectionWithProxyDemo() {
        System.out.println("\n--- 反射与代理组合演示 ---");
        
        try {
            // 使用反射创建服务实例
            Class<?> serviceClass = Class.forName("proxy.UserServiceImpl");
            Constructor<?> constructor = serviceClass.getDeclaredConstructor();
            constructor.setAccessible(true);
            Object serviceInstance = constructor.newInstance();
            
            // 使用反射获取接口
            Class<?>[] interfaces = serviceClass.getInterfaces();
            System.out.println("服务实现的接口: ");
            for (Class<?> intf : interfaces) {
                System.out.println("  " + intf.getName());
            }
            
            // 创建代理
            if (interfaces.length > 0) {
                Object proxy = java.lang.reflect.Proxy.newProxyInstance(
                    serviceClass.getClassLoader(),
                    interfaces,
                    new ReflectionBasedHandler(serviceInstance)
                );
                
                // 使用反射调用代理方法
                java.lang.reflect.Method createMethod = interfaces[0].getMethod("createUser", String.class);
                createMethod.invoke(proxy, "ReflectionUser");
                
                java.lang.reflect.Method getMethod = interfaces[0].getMethod("getUserById", Long.class);
                Object result = getMethod.invoke(proxy, 1L);
                System.out.println("反射调用结果: " + result);
            }
            
        } catch (Exception e) {
            System.out.println("反射与代理组合演示失败: " + e.getMessage());
        }
    }
    
    /**
     * 动态配置演示
     */
    private static void dynamicConfigurationDemo() {
        System.out.println("\n--- 动态配置演示 ---");
        
        // 模拟配置信息
        String[] serviceConfigs = {
            "proxy.UserServiceImpl",
            "proxy.ProductServiceImpl"
        };
        
        String[] proxyTypes = {
            "logging",
            "performance",
            "caching"
        };
        
        for (String serviceConfig : serviceConfigs) {
            try {
                System.out.println("动态创建服务: " + serviceConfig);
                
                // 使用反射创建服务
                Class<?> serviceClass = Class.forName(serviceConfig);
                Constructor<?> constructor = serviceClass.getDeclaredConstructor();
                constructor.setAccessible(true);
                Object service = constructor.newInstance();
                
                // 动态应用代理
                for (String proxyType : proxyTypes) {
                    Object proxy = createProxyByType(service, proxyType);
                    if (proxy != null) {
                        System.out.println("  应用了 " + proxyType + " 代理");
                    }
                }
                
            } catch (Exception e) {
                System.out.println("动态配置失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 插件化架构演示
     */
    private static void pluginArchitectureDemo() {
        System.out.println("\n--- 插件化架构演示 ---");
        
        // 模拟插件管理器
        PluginManager pluginManager = new PluginManager();
        
        // 注册插件
        pluginManager.registerPlugin("userService", "proxy.UserServiceImpl");
        pluginManager.registerPlugin("productService", "proxy.ProductServiceImpl");
        
        // 使用插件
        try {
            Object userService = pluginManager.getPlugin("userService");
            if (userService != null) {
                // 使用反射调用方法
                java.lang.reflect.Method method = userService.getClass().getMethod("createUser", String.class);
                method.invoke(userService, "PluginUser");
            }
            
            Object productService = pluginManager.getPlugin("productService");
            if (productService != null) {
                java.lang.reflect.Method method = productService.getClass().getMethod("addProduct", String.class, double.class);
                method.invoke(productService, "PluginProduct", 99.99);
            }
            
        } catch (Exception e) {
            System.out.println("插件调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据类型创建代理
     */
    private static Object createProxyByType(Object target, String proxyType) {
        try {
            switch (proxyType) {
                case "logging":
                    return ProxyFactory.createLoggingProxy(target);
                case "performance":
                    return ProxyFactory.createPerformanceProxy(target);
                case "caching":
                    return ProxyFactory.createCachingProxy(target);
                default:
                    return null;
            }
        } catch (Exception e) {
            System.out.println("创建代理失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 显示总结信息
     */
    private static void showSummary() {
        System.out.println("\n演示内容总结:");
        
        System.out.println("\n1. Java反射:");
        System.out.println("   - Class对象的获取和使用");
        System.out.println("   - 字段反射：获取、读取、设置字段值");
        System.out.println("   - 方法反射：获取、调用方法");
        System.out.println("   - 构造函数反射：获取、创建实例");
        System.out.println("   - 注解反射：读取和处理注解信息");
        System.out.println("   - 实际应用：属性复制、依赖注入、对象序列化");
        
        System.out.println("\n2. Java动态代理:");
        System.out.println("   - JDK动态代理的基本使用");
        System.out.println("   - 日志代理：记录方法调用信息");
        System.out.println("   - 性能代理：监控方法执行时间");
        System.out.println("   - 缓存代理：缓存方法返回结果");
        System.out.println("   - 事务代理：管理事务的开始、提交、回滚");
        System.out.println("   - 代理链：组合多个代理处理器");
        
        System.out.println("\n3. 综合应用:");
        System.out.println("   - 反射与代理的组合使用");
        System.out.println("   - 动态配置和服务创建");
        System.out.println("   - 插件化架构实现");
        System.out.println("   - 框架开发的基础技术");
        
        System.out.println("\n4. 应用场景:");
        System.out.println("   - 框架开发：Spring、Hibernate等");
        System.out.println("   - 依赖注入：IoC容器实现");
        System.out.println("   - AOP编程：横切关注点处理");
        System.out.println("   - 序列化：JSON、XML处理");
        System.out.println("   - 单元测试：测试私有方法");
        System.out.println("   - 代码生成：动态创建类和方法");
        System.out.println("   - 插件系统：动态加载和执行");
        
        System.out.println("\n5. 性能考虑:");
        System.out.println("   - 反射操作比直接调用慢10-100倍");
        System.out.println("   - 缓存Class、Method、Field对象");
        System.out.println("   - 避免在性能敏感的代码中频繁使用");
        System.out.println("   - 代理对象的创建和调用有额外开销");
        
        System.out.println("\n6. 最佳实践:");
        System.out.println("   - 合理使用反射，避免过度使用");
        System.out.println("   - 缓存反射获取的对象");
        System.out.println("   - 妥善处理反射可能抛出的异常");
        System.out.println("   - 选择合适的代理方式（JDK vs CGLIB）");
        System.out.println("   - 使用代理链模式组合多个处理器");
        System.out.println("   - 注意代理对象的生命周期管理");
        
        System.out.println("\n注意事项:");
        System.out.println("- 反射会破坏封装性，需要谨慎使用");
        System.out.println("- SecurityManager可能限制反射操作");
        System.out.println("- 反射代码难以调试和维护");
        System.out.println("- JDK代理只能代理接口，CGLIB可以代理类");
        System.out.println("- 代理对象的调试比较困难");
        System.out.println("- 在Spring等框架中，反射和代理是核心机制");
    }
}

/**
 * 基于反射的调用处理器
 */
class ReflectionBasedHandler implements java.lang.reflect.InvocationHandler {
    private final Object target;
    
    public ReflectionBasedHandler(Object target) {
        this.target = target;
    }
    
    @Override
    public Object invoke(Object proxy, java.lang.reflect.Method method, Object[] args) throws Throwable {
        System.out.println("反射代理调用: " + method.getName());
        
        // 使用反射调用目标方法
        try {
            java.lang.reflect.Method targetMethod = target.getClass().getMethod(
                method.getName(), method.getParameterTypes());
            
            Object result = targetMethod.invoke(target, args);
            System.out.println("反射代理返回: " + result);
            return result;
            
        } catch (java.lang.reflect.InvocationTargetException e) {
            throw e.getCause();
        }
    }
}

/**
 * 简单的插件管理器
 */
class PluginManager {
    private final java.util.Map<String, String> pluginClasses = new java.util.HashMap<>();
    private final java.util.Map<String, Object> pluginInstances = new java.util.HashMap<>();
    
    /**
     * 注册插件
     */
    public void registerPlugin(String name, String className) {
        pluginClasses.put(name, className);
        System.out.println("注册插件: " + name + " -> " + className);
    }
    
    /**
     * 获取插件实例
     */
    public Object getPlugin(String name) {
        // 检查是否已经创建实例
        Object instance = pluginInstances.get(name);
        if (instance != null) {
            return instance;
        }
        
        // 使用反射创建实例
        String className = pluginClasses.get(name);
        if (className != null) {
            try {
                Class<?> clazz = Class.forName(className);
                Constructor<?> constructor = clazz.getDeclaredConstructor();
                constructor.setAccessible(true);
                instance = constructor.newInstance();
                pluginInstances.put(name, instance);
                System.out.println("创建插件实例: " + name);
                return instance;
            } catch (Exception e) {
                System.out.println("创建插件实例失败: " + e.getMessage());
            }
        }
        
        return null;
    }
    
    /**
     * 卸载插件
     */
    public void unloadPlugin(String name) {
        pluginClasses.remove(name);
        pluginInstances.remove(name);
        System.out.println("卸载插件: " + name);
    }
    
    /**
     * 列出所有插件
     */
    public void listPlugins() {
        System.out.println("已注册的插件:");
        for (java.util.Map.Entry<String, String> entry : pluginClasses.entrySet()) {
            boolean loaded = pluginInstances.containsKey(entry.getKey());
            System.out.println("  " + entry.getKey() + " -> " + entry.getValue() + 
                             " (已加载: " + loaded + ")");
        }
    }
}
