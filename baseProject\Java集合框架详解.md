# Java集合框架详解

## 目录
1. [集合框架概述](#集合框架概述)
2. [Collection接口](#collection接口)
3. [List接口及实现类](#list接口及实现类)
4. [Set接口及实现类](#set接口及实现类)
5. [Queue接口及实现类](#queue接口及实现类)
6. [Map接口及实现类](#map接口及实现类)
7. [迭代器和增强for循环](#迭代器和增强for循环)
8. [集合工具类](#集合工具类)
9. [实际项目示例](#实际项目示例)
10. [性能对比和选择指南](#性能对比和选择指南)

## 集合框架概述

Java集合框架（Java Collections Framework）是一套用来存储和操作对象集合的统一架构。它提供了接口、实现类和算法，让开发者能够高效地处理数据集合。

### 集合框架的核心接口层次结构

```
Collection (接口)
├── List (接口) - 有序、可重复
│   ├── ArrayList (类)
│   ├── LinkedList (类)
│   └── Vector (类)
├── Set (接口) - 无序、不可重复
│   ├── HashSet (类)
│   ├── LinkedHashSet (类)
│   └── TreeSet (类)
└── Queue (接口) - 队列
    ├── LinkedList (类)
    ├── PriorityQueue (类)
    └── ArrayDeque (类)

Map (接口) - 键值对映射
├── HashMap (类)
├── LinkedHashMap (类)
├── TreeMap (类)
└── Hashtable (类)
```

### 集合 vs 数组

| 特性 | 数组 | 集合 |
|------|------|------|
| 长度 | 固定 | 动态 |
| 类型 | 可存储基本类型和对象 | 只能存储对象 |
| 性能 | 访问速度快 | 功能丰富，操作方便 |
| 内存 | 连续内存空间 | 根据实现而定 |

## Collection接口

Collection是所有集合类的根接口，定义了集合的基本操作。

### 常用方法
```java
// 基本操作
boolean add(E e)              // 添加元素
boolean remove(Object o)      // 删除元素
boolean contains(Object o)    // 检查是否包含元素
int size()                   // 获取元素个数
boolean isEmpty()            // 检查是否为空
void clear()                 // 清空集合

// 批量操作
boolean addAll(Collection<? extends E> c)    // 添加另一个集合的所有元素
boolean removeAll(Collection<?> c)           // 删除指定集合中的所有元素
boolean retainAll(Collection<?> c)           // 保留指定集合中的元素

// 转换操作
Object[] toArray()                          // 转换为数组
<T> T[] toArray(T[] a)                     // 转换为指定类型数组
```

## List接口及实现类

List是有序的集合，允许重复元素，可以通过索引访问元素。

### ArrayList - 动态数组

**特点：**
- 基于动态数组实现
- 随机访问速度快 O(1)
- 插入和删除操作较慢 O(n)
- 线程不安全
- 默认初始容量为10

**使用场景：**
- 频繁随机访问元素
- 较少插入和删除操作
- 不需要线程安全

```java
import java.util.ArrayList;
import java.util.List;

// 创建ArrayList
List<String> list = new ArrayList<>();

// 添加元素
list.add("Apple");
list.add("Banana");
list.add("Cherry");
list.add(1, "Orange");  // 在指定位置插入

// 访问元素
String first = list.get(0);        // 获取第一个元素
String last = list.get(list.size() - 1);  // 获取最后一个元素

// 修改元素
list.set(0, "Grape");              // 修改第一个元素

// 删除元素
list.remove(0);                    // 删除第一个元素
list.remove("Banana");             // 删除指定元素

// 查找元素
int index = list.indexOf("Cherry"); // 查找元素索引
boolean exists = list.contains("Apple"); // 检查是否存在
```

### LinkedList - 双向链表

**特点：**
- 基于双向链表实现
- 插入和删除操作快 O(1)
- 随机访问较慢 O(n)
- 实现了List和Deque接口
- 线程不安全

**使用场景：**
- 频繁插入和删除操作
- 较少随机访问
- 需要队列或栈功能

```java
import java.util.LinkedList;

LinkedList<String> linkedList = new LinkedList<>();

// 添加元素
linkedList.add("First");
linkedList.addFirst("Zero");       // 添加到开头
linkedList.addLast("Last");        // 添加到末尾

// 队列操作
linkedList.offer("Queue Element"); // 入队
String head = linkedList.poll();   // 出队

// 栈操作
linkedList.push("Stack Element");  // 入栈
String top = linkedList.pop();     // 出栈
```

### Vector - 同步的动态数组

**特点：**
- 类似ArrayList但线程安全
- 性能较ArrayList差
- 扩容时增长100%（ArrayList增长50%）
- 已较少使用，推荐使用ArrayList + 同步机制

## Set接口及实现类

Set是不允许重复元素的集合。

### HashSet - 哈希表实现

**特点：**
- 基于HashMap实现
- 无序存储
- 不允许重复元素
- 允许null值
- 查找、插入、删除操作平均时间复杂度O(1)

```java
import java.util.HashSet;
import java.util.Set;

Set<String> hashSet = new HashSet<>();

// 添加元素
hashSet.add("Apple");
hashSet.add("Banana");
hashSet.add("Apple");  // 重复元素，不会被添加

// 检查元素
boolean exists = hashSet.contains("Apple");

// 删除元素
hashSet.remove("Banana");

// 遍历
for (String item : hashSet) {
    System.out.println(item);
}
```

### LinkedHashSet - 保持插入顺序的HashSet

**特点：**
- 继承自HashSet
- 维护插入顺序
- 性能略低于HashSet

### TreeSet - 有序集合

**特点：**
- 基于红黑树实现
- 自动排序
- 不允许null值
- 查找、插入、删除操作时间复杂度O(log n)

```java
import java.util.TreeSet;

TreeSet<Integer> treeSet = new TreeSet<>();

// 添加元素（自动排序）
treeSet.add(5);
treeSet.add(2);
treeSet.add(8);
treeSet.add(1);

// 输出：[1, 2, 5, 8]
System.out.println(treeSet);

// 范围操作
TreeSet<Integer> subset = (TreeSet<Integer>) treeSet.subSet(2, 8);
Integer first = treeSet.first();  // 最小值
Integer last = treeSet.last();    // 最大值
```

## Queue接口及实现类

Queue表示队列，遵循FIFO（先进先出）原则。

### PriorityQueue - 优先队列

**特点：**
- 基于堆实现
- 元素按优先级排序
- 不是线程安全的

```java
import java.util.PriorityQueue;
import java.util.Comparator;

// 默认最小堆
PriorityQueue<Integer> minHeap = new PriorityQueue<>();
minHeap.offer(5);
minHeap.offer(2);
minHeap.offer(8);
System.out.println(minHeap.poll()); // 输出：2

// 最大堆
PriorityQueue<Integer> maxHeap = new PriorityQueue<>(Comparator.reverseOrder());
maxHeap.offer(5);
maxHeap.offer(2);
maxHeap.offer(8);
System.out.println(maxHeap.poll()); // 输出：8
```

### ArrayDeque - 双端队列

**特点：**
- 基于数组实现的双端队列
- 可以在两端添加和删除元素
- 性能优于LinkedList

```java
import java.util.ArrayDeque;
import java.util.Deque;

Deque<String> deque = new ArrayDeque<>();

// 队列操作
deque.offerFirst("First");
deque.offerLast("Last");

// 栈操作
deque.push("Top");
String top = deque.pop();
```

## Map接口及实现类

Map存储键值对映射关系，键不能重复。

### HashMap - 哈希表实现

**特点：**
- 基于哈希表实现
- 允许null键和null值
- 无序存储
- 线程不安全
- 查找、插入、删除平均时间复杂度O(1)

```java
import java.util.HashMap;
import java.util.Map;

Map<String, Integer> hashMap = new HashMap<>();

// 添加键值对
hashMap.put("Apple", 10);
hashMap.put("Banana", 20);
hashMap.put("Cherry", 15);

// 获取值
Integer appleCount = hashMap.get("Apple");
Integer defaultValue = hashMap.getOrDefault("Orange", 0);

// 检查键或值
boolean hasKey = hashMap.containsKey("Apple");
boolean hasValue = hashMap.containsValue(10);

// 删除
hashMap.remove("Banana");

// 遍历
for (Map.Entry<String, Integer> entry : hashMap.entrySet()) {
    System.out.println(entry.getKey() + ": " + entry.getValue());
}

// 只遍历键
for (String key : hashMap.keySet()) {
    System.out.println(key);
}

// 只遍历值
for (Integer value : hashMap.values()) {
    System.out.println(value);
}
```

### LinkedHashMap - 保持插入顺序的HashMap

**特点：**
- 继承自HashMap
- 维护插入顺序或访问顺序
- 性能略低于HashMap

### TreeMap - 有序映射

**特点：**
- 基于红黑树实现
- 键自动排序
- 不允许null键
- 操作时间复杂度O(log n)

```java
import java.util.TreeMap;

TreeMap<String, Integer> treeMap = new TreeMap<>();
treeMap.put("Charlie", 25);
treeMap.put("Alice", 30);
treeMap.put("Bob", 20);

// 输出按键排序：{Alice=30, Bob=20, Charlie=25}
System.out.println(treeMap);

// 范围操作
Map<String, Integer> subMap = treeMap.subMap("Alice", "Charlie");
```

## 迭代器和增强for循环

### Iterator迭代器

```java
import java.util.Iterator;

List<String> list = new ArrayList<>();
list.add("A");
list.add("B");
list.add("C");

// 使用Iterator
Iterator<String> iterator = list.iterator();
while (iterator.hasNext()) {
    String element = iterator.next();
    System.out.println(element);
    // 安全删除
    if ("B".equals(element)) {
        iterator.remove();
    }
}
```

### ListIterator双向迭代器

```java
ListIterator<String> listIterator = list.listIterator();

// 向前遍历
while (listIterator.hasNext()) {
    System.out.println(listIterator.next());
}

// 向后遍历
while (listIterator.hasPrevious()) {
    System.out.println(listIterator.previous());
}
```

### 增强for循环

```java
// 遍历List
for (String item : list) {
    System.out.println(item);
}

// 遍历Map
for (Map.Entry<String, Integer> entry : map.entrySet()) {
    System.out.println(entry.getKey() + ": " + entry.getValue());
}
```

## 集合工具类

### Collections工具类

Collections类提供了许多静态方法来操作集合。

```java
import java.util.Collections;

List<Integer> numbers = new ArrayList<>();
numbers.add(3);
numbers.add(1);
numbers.add(4);
numbers.add(2);

// 排序
Collections.sort(numbers);                    // 升序排序
Collections.sort(numbers, Collections.reverseOrder()); // 降序排序

// 查找
int index = Collections.binarySearch(numbers, 3);      // 二分查找

// 最值
Integer max = Collections.max(numbers);
Integer min = Collections.min(numbers);

// 反转
Collections.reverse(numbers);

// 打乱
Collections.shuffle(numbers);

// 填充
Collections.fill(numbers, 0);                 // 用0填充所有元素

// 复制
List<Integer> copy = new ArrayList<>(Collections.nCopies(5, 10)); // 创建5个10的列表

// 不可变集合
List<String> immutableList = Collections.unmodifiableList(Arrays.asList("A", "B", "C"));

// 同步集合
List<String> syncList = Collections.synchronizedList(new ArrayList<>());
Map<String, Integer> syncMap = Collections.synchronizedMap(new HashMap<>());
```

### Arrays工具类

```java
import java.util.Arrays;

// 数组转List
String[] array = {"A", "B", "C"};
List<String> list = Arrays.asList(array);

// List转数组
String[] newArray = list.toArray(new String[0]);

// 数组操作
int[] nums = {3, 1, 4, 1, 5};
Arrays.sort(nums);                            // 排序
int index = Arrays.binarySearch(nums, 4);     // 二分查找
Arrays.fill(nums, 0);                         // 填充
```

## 实际项目示例

### 学生管理系统

```java
import java.util.*;

public class StudentManagementSystem {
    // 使用Map存储学生信息，学号作为键
    private Map<String, Student> students = new HashMap<>();

    // 使用Set存储课程信息，避免重复
    private Set<String> courses = new HashSet<>();

    // 使用List存储成绩记录，保持顺序
    private List<Grade> grades = new ArrayList<>();

    // 添加学生
    public void addStudent(Student student) {
        students.put(student.getStudentId(), student);
    }

    // 查找学生
    public Student findStudent(String studentId) {
        return students.get(studentId);
    }

    // 获取所有学生，按姓名排序
    public List<Student> getAllStudentsSorted() {
        List<Student> studentList = new ArrayList<>(students.values());
        Collections.sort(studentList, Comparator.comparing(Student::getName));
        return studentList;
    }

    // 添加课程
    public void addCourse(String course) {
        courses.add(course);
    }

    // 添加成绩
    public void addGrade(String studentId, String course, double score) {
        if (students.containsKey(studentId) && courses.contains(course)) {
            grades.add(new Grade(studentId, course, score));
        }
    }

    // 计算学生平均分
    public double calculateAverageScore(String studentId) {
        return grades.stream()
                .filter(grade -> grade.getStudentId().equals(studentId))
                .mapToDouble(Grade::getScore)
                .average()
                .orElse(0.0);
    }

    // 获取课程排行榜
    public List<Student> getCourseRanking(String course) {
        Map<String, Double> courseScores = new HashMap<>();

        grades.stream()
                .filter(grade -> grade.getCourse().equals(course))
                .forEach(grade -> courseScores.put(grade.getStudentId(), grade.getScore()));

        return courseScores.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .map(entry -> students.get(entry.getKey()))
                .collect(Collectors.toList());
    }
}

class Student {
    private String studentId;
    private String name;
    private int age;

    public Student(String studentId, String name, int age) {
        this.studentId = studentId;
        this.name = name;
        this.age = age;
    }

    // getters and setters
    public String getStudentId() { return studentId; }
    public String getName() { return name; }
    public int getAge() { return age; }
}

class Grade {
    private String studentId;
    private String course;
    private double score;

    public Grade(String studentId, String course, double score) {
        this.studentId = studentId;
        this.course = course;
        this.score = score;
    }

    // getters
    public String getStudentId() { return studentId; }
    public String getCourse() { return course; }
    public double getScore() { return score; }
}
```

### 购物车系统

```java
import java.util.*;

public class ShoppingCart {
    // 使用LinkedHashMap保持添加顺序
    private Map<Product, Integer> items = new LinkedHashMap<>();

    // 添加商品
    public void addItem(Product product, int quantity) {
        items.merge(product, quantity, Integer::sum);
    }

    // 移除商品
    public void removeItem(Product product) {
        items.remove(product);
    }

    // 更新数量
    public void updateQuantity(Product product, int quantity) {
        if (quantity <= 0) {
            removeItem(product);
        } else {
            items.put(product, quantity);
        }
    }

    // 计算总价
    public double getTotalPrice() {
        return items.entrySet().stream()
                .mapToDouble(entry -> entry.getKey().getPrice() * entry.getValue())
                .sum();
    }

    // 获取商品数量
    public int getTotalItems() {
        return items.values().stream().mapToInt(Integer::intValue).sum();
    }

    // 按价格排序显示商品
    public List<Map.Entry<Product, Integer>> getItemsSortedByPrice() {
        return items.entrySet().stream()
                .sorted(Map.Entry.comparingByKey(Comparator.comparing(Product::getPrice)))
                .collect(Collectors.toList());
    }

    // 清空购物车
    public void clear() {
        items.clear();
    }

    // 检查是否为空
    public boolean isEmpty() {
        return items.isEmpty();
    }
}

class Product {
    private String id;
    private String name;
    private double price;
    private String category;

    public Product(String id, String name, double price, String category) {
        this.id = id;
        this.name = name;
        this.price = price;
        this.category = category;
    }

    // getters
    public String getId() { return id; }
    public String getName() { return name; }
    public double getPrice() { return price; }
    public String getCategory() { return category; }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Product product = (Product) obj;
        return Objects.equals(id, product.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
```

## 性能对比和选择指南

### 时间复杂度对比

| 操作 | ArrayList | LinkedList | HashSet | TreeSet | HashMap | TreeMap |
|------|-----------|------------|---------|---------|---------|---------|
| 添加 | O(1)* | O(1) | O(1)* | O(log n) | O(1)* | O(log n) |
| 删除 | O(n) | O(1)** | O(1)* | O(log n) | O(1)* | O(log n) |
| 查找 | O(1) | O(n) | O(1)* | O(log n) | O(1)* | O(log n) |
| 遍历 | O(n) | O(n) | O(n) | O(n) | O(n) | O(n) |

*平均情况，最坏情况可能是O(n)
**如果已知节点位置

### 选择指南

#### List选择
- **ArrayList**: 频繁随机访问，较少插入删除
- **LinkedList**: 频繁插入删除，较少随机访问
- **Vector**: 需要线程安全的ArrayList（不推荐，建议用Collections.synchronizedList）

#### Set选择
- **HashSet**: 不需要排序，追求最快性能
- **LinkedHashSet**: 需要保持插入顺序
- **TreeSet**: 需要自动排序

#### Map选择
- **HashMap**: 不需要排序，追求最快性能
- **LinkedHashMap**: 需要保持插入顺序或访问顺序
- **TreeMap**: 需要按键排序
- **ConcurrentHashMap**: 需要线程安全的HashMap

### 内存使用对比

```java
// 内存效率测试示例
public class MemoryUsageComparison {
    public static void main(String[] args) {
        int size = 100000;

        // ArrayList vs LinkedList 内存使用
        List<Integer> arrayList = new ArrayList<>();
        List<Integer> linkedList = new LinkedList<>();

        // ArrayList: 连续内存，开销小
        // LinkedList: 每个节点额外存储前后指针，内存开销大

        for (int i = 0; i < size; i++) {
            arrayList.add(i);
            linkedList.add(i);
        }

        // HashSet vs TreeSet 内存使用
        Set<Integer> hashSet = new HashSet<>();
        Set<Integer> treeSet = new TreeSet<>();

        // HashSet: 哈希表 + 链表/红黑树，内存开销中等
        // TreeSet: 红黑树，每个节点存储颜色和父子指针，内存开销较大
    }
}
```

### 性能测试示例

```java
import java.util.*;

public class PerformanceTest {
    private static final int SIZE = 100000;

    public static void main(String[] args) {
        testListPerformance();
        testSetPerformance();
        testMapPerformance();
    }

    public static void testListPerformance() {
        System.out.println("=== List性能测试 ===");

        // ArrayList vs LinkedList 添加性能
        List<Integer> arrayList = new ArrayList<>();
        List<Integer> linkedList = new LinkedList<>();

        // 尾部添加测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < SIZE; i++) {
            arrayList.add(i);
        }
        System.out.println("ArrayList尾部添加: " + (System.currentTimeMillis() - start) + "ms");

        start = System.currentTimeMillis();
        for (int i = 0; i < SIZE; i++) {
            linkedList.add(i);
        }
        System.out.println("LinkedList尾部添加: " + (System.currentTimeMillis() - start) + "ms");

        // 随机访问测试
        Random random = new Random();
        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            arrayList.get(random.nextInt(SIZE));
        }
        System.out.println("ArrayList随机访问: " + (System.currentTimeMillis() - start) + "ms");

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            linkedList.get(random.nextInt(SIZE));
        }
        System.out.println("LinkedList随机访问: " + (System.currentTimeMillis() - start) + "ms");
    }

    public static void testSetPerformance() {
        System.out.println("\n=== Set性能测试 ===");

        Set<Integer> hashSet = new HashSet<>();
        Set<Integer> treeSet = new TreeSet<>();

        // 添加性能测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < SIZE; i++) {
            hashSet.add(i);
        }
        System.out.println("HashSet添加: " + (System.currentTimeMillis() - start) + "ms");

        start = System.currentTimeMillis();
        for (int i = 0; i < SIZE; i++) {
            treeSet.add(i);
        }
        System.out.println("TreeSet添加: " + (System.currentTimeMillis() - start) + "ms");

        // 查找性能测试
        Random random = new Random();
        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            hashSet.contains(random.nextInt(SIZE));
        }
        System.out.println("HashSet查找: " + (System.currentTimeMillis() - start) + "ms");

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            treeSet.contains(random.nextInt(SIZE));
        }
        System.out.println("TreeSet查找: " + (System.currentTimeMillis() - start) + "ms");
    }

    public static void testMapPerformance() {
        System.out.println("\n=== Map性能测试 ===");

        Map<Integer, String> hashMap = new HashMap<>();
        Map<Integer, String> treeMap = new TreeMap<>();

        // 添加性能测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < SIZE; i++) {
            hashMap.put(i, "Value" + i);
        }
        System.out.println("HashMap添加: " + (System.currentTimeMillis() - start) + "ms");

        start = System.currentTimeMillis();
        for (int i = 0; i < SIZE; i++) {
            treeMap.put(i, "Value" + i);
        }
        System.out.println("TreeMap添加: " + (System.currentTimeMillis() - start) + "ms");

        // 查找性能测试
        Random random = new Random();
        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            hashMap.get(random.nextInt(SIZE));
        }
        System.out.println("HashMap查找: " + (System.currentTimeMillis() - start) + "ms");

        start = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            treeMap.get(random.nextInt(SIZE));
        }
        System.out.println("TreeMap查找: " + (System.currentTimeMillis() - start) + "ms");
    }
}
```

## 集合框架最佳实践

### 1. 选择合适的集合类型

```java
public class CollectionBestPractices {

    // ✅ 好的做法：根据需求选择合适的集合
    public void goodPractices() {
        // 需要快速随机访问
        List<String> randomAccessList = new ArrayList<>();

        // 需要频繁插入删除
        List<String> frequentModificationList = new LinkedList<>();

        // 需要去重且不关心顺序
        Set<String> uniqueItems = new HashSet<>();

        // 需要去重且保持排序
        Set<String> sortedUniqueItems = new TreeSet<>();

        // 需要键值映射且追求性能
        Map<String, Object> fastMap = new HashMap<>();

        // 需要键值映射且保持排序
        Map<String, Object> sortedMap = new TreeMap<>();
    }

    // ❌ 避免的做法
    public void badPractices() {
        // 不要为了排序而使用TreeSet，如果只需要一次排序
        Set<String> items = new TreeSet<>(); // 每次插入都排序，浪费性能

        // 应该使用
        List<String> itemsList = new ArrayList<>();
        // 添加完所有元素后再排序
        Collections.sort(itemsList);
    }
}
```

### 2. 初始化容量优化

```java
public class CapacityOptimization {

    public void optimizeCapacity() {
        // ✅ 如果知道大概大小，设置初始容量
        List<String> largeList = new ArrayList<>(10000);
        Map<String, Object> largeMap = new HashMap<>(10000);
        Set<String> largeSet = new HashSet<>(10000);

        // ✅ 对于HashMap，考虑负载因子
        // 如果知道要存储1000个元素，设置容量为1000/0.75 ≈ 1334
        Map<String, Object> efficientMap = new HashMap<>(1334);
    }
}
```

### 3. 线程安全考虑

```java
import java.util.concurrent.*;

public class ThreadSafetyConsiderations {

    public void threadSafeCollections() {
        // ✅ 使用线程安全的集合
        List<String> safeList = new CopyOnWriteArrayList<>();
        Set<String> safeSet = ConcurrentHashMap.newKeySet();
        Map<String, Object> safeMap = new ConcurrentHashMap<>();
        Queue<String> safeQueue = new ConcurrentLinkedQueue<>();

        // ✅ 或者使用同步包装器
        List<String> syncList = Collections.synchronizedList(new ArrayList<>());
        Map<String, Object> syncMap = Collections.synchronizedMap(new HashMap<>());

        // ⚠️ 注意：同步包装器在迭代时仍需要手动同步
        synchronized (syncList) {
            for (String item : syncList) {
                // 安全的迭代
            }
        }
    }
}
```

### 4. 内存泄漏预防

```java
public class MemoryLeakPrevention {

    // ❌ 可能导致内存泄漏
    private static final Map<String, Object> cache = new HashMap<>();

    public void badCaching(String key, Object value) {
        cache.put(key, value); // 缓存永远不会清理
    }

    // ✅ 使用WeakHashMap或定期清理
    private static final Map<String, Object> weakCache = new WeakHashMap<>();

    public void goodCaching(String key, Object value) {
        weakCache.put(key, value); // 当key没有强引用时会自动清理
    }

    // ✅ 或者使用有界缓存
    private static final Map<String, Object> boundedCache = new LinkedHashMap<String, Object>(100, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, Object> eldest) {
            return size() > 100; // 限制缓存大小
        }
    };
}
```

### 5. 集合的不可变性

```java
import java.util.*;

public class ImmutableCollections {

    public void createImmutableCollections() {
        // ✅ 创建不可变集合
        List<String> immutableList = Collections.unmodifiableList(
            Arrays.asList("A", "B", "C")
        );

        Set<String> immutableSet = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList("X", "Y", "Z"))
        );

        Map<String, Integer> mutableMap = new HashMap<>();
        mutableMap.put("one", 1);
        mutableMap.put("two", 2);
        Map<String, Integer> immutableMap = Collections.unmodifiableMap(mutableMap);

        // Java 9+ 的便捷方法
        // List<String> modernList = List.of("A", "B", "C");
        // Set<String> modernSet = Set.of("X", "Y", "Z");
        // Map<String, Integer> modernMap = Map.of("one", 1, "two", 2);
    }
}
```

### 6. 集合的复制

```java
public class CollectionCopying {

    public void copyCollections() {
        List<String> original = new ArrayList<>();
        original.add("A");
        original.add("B");

        // ✅ 浅拷贝
        List<String> shallowCopy1 = new ArrayList<>(original);
        List<String> shallowCopy2 = original.stream().collect(Collectors.toList());

        // ✅ 深拷贝（对于包含可变对象的集合）
        List<StringBuilder> originalMutable = new ArrayList<>();
        originalMutable.add(new StringBuilder("Hello"));

        List<StringBuilder> deepCopy = originalMutable.stream()
            .map(sb -> new StringBuilder(sb.toString()))
            .collect(Collectors.toList());
    }
}
```

## 常见陷阱和解决方案

### 1. ConcurrentModificationException

```java
public class ConcurrentModificationSolution {

    // ❌ 错误的做法
    public void badIteration() {
        List<String> list = new ArrayList<>();
        list.add("A");
        list.add("B");
        list.add("C");

        // 这会抛出ConcurrentModificationException
        for (String item : list) {
            if ("B".equals(item)) {
                list.remove(item); // 在迭代过程中修改集合
            }
        }
    }

    // ✅ 正确的做法
    public void goodIteration() {
        List<String> list = new ArrayList<>();
        list.add("A");
        list.add("B");
        list.add("C");

        // 方法1：使用Iterator
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()) {
            String item = iterator.next();
            if ("B".equals(item)) {
                iterator.remove(); // 使用Iterator的remove方法
            }
        }

        // 方法2：使用removeIf（Java 8+）
        list.removeIf(item -> "B".equals(item));

        // 方法3：收集要删除的元素，然后批量删除
        List<String> toRemove = new ArrayList<>();
        for (String item : list) {
            if ("B".equals(item)) {
                toRemove.add(item);
            }
        }
        list.removeAll(toRemove);
    }
}
```

### 2. equals和hashCode的重要性

```java
public class EqualsHashCodeImportance {

    // ❌ 没有正确实现equals和hashCode的类
    static class BadStudent {
        private String name;
        private int age;

        public BadStudent(String name, int age) {
            this.name = name;
            this.age = age;
        }
        // 没有重写equals和hashCode
    }

    // ✅ 正确实现equals和hashCode的类
    static class GoodStudent {
        private String name;
        private int age;

        public GoodStudent(String name, int age) {
            this.name = name;
            this.age = age;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            GoodStudent student = (GoodStudent) obj;
            return age == student.age && Objects.equals(name, student.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, age);
        }
    }

    public void demonstrateProblem() {
        // 使用BadStudent
        Set<BadStudent> badSet = new HashSet<>();
        BadStudent student1 = new BadStudent("张三", 20);
        BadStudent student2 = new BadStudent("张三", 20);

        badSet.add(student1);
        badSet.add(student2);
        System.out.println("BadStudent集合大小: " + badSet.size()); // 输出2，应该是1

        // 使用GoodStudent
        Set<GoodStudent> goodSet = new HashSet<>();
        GoodStudent student3 = new GoodStudent("张三", 20);
        GoodStudent student4 = new GoodStudent("张三", 20);

        goodSet.add(student3);
        goodSet.add(student4);
        System.out.println("GoodStudent集合大小: " + goodSet.size()); // 输出1，正确
    }
}
```

### 3. 空值处理

```java
public class NullHandling {

    public void handleNulls() {
        // ✅ 检查集合本身是否为null
        List<String> list = getList(); // 可能返回null
        if (list != null && !list.isEmpty()) {
            // 安全操作
        }

        // ✅ 使用Optional避免null
        Optional<List<String>> optionalList = Optional.ofNullable(getList());
        optionalList.ifPresent(l -> {
            // 安全操作
        });

        // ✅ 初始化为空集合而不是null
        List<String> safeList = getList();
        if (safeList == null) {
            safeList = Collections.emptyList();
        }
    }

    private List<String> getList() {
        // 模拟可能返回null的方法
        return Math.random() > 0.5 ? Arrays.asList("A", "B") : null;
    }
}
```

## 总结

Java集合框架是Java编程的核心组件，掌握以下要点：

### 核心概念
- **Collection**: List、Set、Queue的父接口
- **Map**: 键值对映射，独立于Collection体系
- **Iterator**: 统一的遍历接口
- **Comparable/Comparator**: 排序接口

### 选择指南
1. **需要索引访问**: 选择List（ArrayList/LinkedList）
2. **需要唯一性**: 选择Set（HashSet/TreeSet/LinkedHashSet）
3. **需要队列操作**: 选择Queue（ArrayDeque/PriorityQueue）
4. **需要键值映射**: 选择Map（HashMap/TreeMap/LinkedHashMap）

### 性能考虑
- **ArrayList**: 随机访问快，插入删除慢
- **LinkedList**: 插入删除快，随机访问慢
- **HashMap**: 平均O(1)操作，无序
- **TreeMap**: O(log n)操作，有序

### 最佳实践
1. **合理选择集合类型**
2. **设置合适的初始容量**
3. **注意线程安全问题**
4. **正确实现equals和hashCode**
5. **避免在迭代时修改集合**
6. **使用泛型确保类型安全**

掌握这些知识将帮助您编写高效、安全的Java程序！
