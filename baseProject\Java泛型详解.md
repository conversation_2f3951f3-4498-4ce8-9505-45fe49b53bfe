# Java泛型详解

## 目录
1. [泛型简介](#泛型简介)
2. [泛型基础](#泛型基础)
3. [泛型类](#泛型类)
4. [泛型接口](#泛型接口)
5. [泛型方法](#泛型方法)
6. [通配符](#通配符)
7. [类型擦除](#类型擦除)
8. [泛型约束](#泛型约束)
9. [实际应用](#实际应用)
10. [最佳实践](#最佳实践)

## 泛型简介

### 什么是泛型
泛型（Generics）是Java 5引入的一个重要特性，它允许在定义类、接口和方法时使用类型参数。泛型提供了编译时类型安全检测机制，允许程序员在编译时检测到非法的类型。

### 泛型的优势
- **类型安全** - 在编译时检查类型错误
- **消除强制类型转换** - 减少ClassCastException的风险
- **提高代码重用性** - 一套代码可以处理多种类型
- **提高性能** - 避免装箱和拆箱操作

### 没有泛型的问题
```java
// Java 5之前的代码
List list = new ArrayList();
list.add("Hello");
list.add(123);  // 可以添加不同类型

String str = (String) list.get(0);  // 需要强制转换
String str2 = (String) list.get(1); // 运行时ClassCastException!
```

### 使用泛型的解决方案
```java
// 使用泛型
List<String> list = new ArrayList<String>();
list.add("Hello");
// list.add(123);  // 编译错误！类型不匹配

String str = list.get(0);  // 不需要强制转换
```

## 泛型基础

### 类型参数命名约定
- **T** - Type（类型）
- **E** - Element（元素）
- **K** - Key（键）
- **V** - Value（值）
- **N** - Number（数字）
- **S, U, V** - 第二、第三、第四个类型

### 基本语法
```java
// 泛型类定义
class ClassName<T> {
    private T data;
    
    public void setData(T data) {
        this.data = data;
    }
    
    public T getData() {
        return data;
    }
}

// 泛型接口定义
interface InterfaceName<T> {
    void method(T param);
    T getResult();
}

// 泛型方法定义
public <T> T methodName(T param) {
    return param;
}
```

## 泛型类

### 单个类型参数
```java
/**
 * 泛型容器类
 */
public class Container<T> {
    private T item;
    
    public Container() {}
    
    public Container(T item) {
        this.item = item;
    }
    
    public void setItem(T item) {
        this.item = item;
    }
    
    public T getItem() {
        return item;
    }
    
    public boolean isEmpty() {
        return item == null;
    }
    
    @Override
    public String toString() {
        return "Container{item=" + item + "}";
    }
}

// 使用示例
Container<String> stringContainer = new Container<>("Hello");
Container<Integer> intContainer = new Container<>(42);
Container<List<String>> listContainer = new Container<>(Arrays.asList("A", "B"));
```

### 多个类型参数
```java
/**
 * 键值对泛型类
 */
public class Pair<K, V> {
    private K key;
    private V value;
    
    public Pair() {}
    
    public Pair(K key, V value) {
        this.key = key;
        this.value = value;
    }
    
    public K getKey() {
        return key;
    }
    
    public void setKey(K key) {
        this.key = key;
    }
    
    public V getValue() {
        return value;
    }
    
    public void setValue(V value) {
        this.value = value;
    }
    
    @Override
    public String toString() {
        return "Pair{key=" + key + ", value=" + value + "}";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Pair<?, ?> pair = (Pair<?, ?>) obj;
        return Objects.equals(key, pair.key) && Objects.equals(value, pair.value);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(key, value);
    }
}

// 使用示例
Pair<String, Integer> nameAge = new Pair<>("Alice", 25);
Pair<Integer, String> idName = new Pair<>(1001, "Bob");
Pair<String, List<String>> categoryItems = new Pair<>("Fruits", Arrays.asList("Apple", "Banana"));
```

### 泛型类继承
```java
/**
 * 泛型基类
 */
public abstract class Animal<T> {
    protected T data;
    
    public Animal(T data) {
        this.data = data;
    }
    
    public abstract void makeSound();
    
    public T getData() {
        return data;
    }
}

/**
 * 具体实现类 - 指定具体类型
 */
public class Dog extends Animal<String> {
    public Dog(String name) {
        super(name);
    }
    
    @Override
    public void makeSound() {
        System.out.println(data + " says: Woof!");
    }
}

/**
 * 泛型子类 - 保持泛型
 */
public class Cat<T> extends Animal<T> {
    public Cat(T data) {
        super(data);
    }
    
    @Override
    public void makeSound() {
        System.out.println(data + " says: Meow!");
    }
}

// 使用示例
Dog dog = new Dog("Buddy");
dog.makeSound();  // Buddy says: Woof!

Cat<String> cat = new Cat<>("Whiskers");
cat.makeSound();  // Whiskers says: Meow!

Cat<Integer> numberedCat = new Cat<>(007);
numberedCat.makeSound();  // 007 says: Meow!
```

## 泛型接口

### 基本泛型接口
```java
/**
 * 泛型比较器接口
 */
public interface Comparator<T> {
    int compare(T o1, T o2);
    
    default boolean equals(T o1, T o2) {
        return compare(o1, o2) == 0;
    }
}

/**
 * 泛型转换器接口
 */
public interface Converter<F, T> {
    T convert(F from);
    
    default <R> Converter<F, R> andThen(Converter<T, R> after) {
        return from -> after.convert(convert(from));
    }
}

// 实现示例
public class StringToIntegerConverter implements Converter<String, Integer> {
    @Override
    public Integer convert(String from) {
        try {
            return Integer.parseInt(from);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}

public class IntegerToStringConverter implements Converter<Integer, String> {
    @Override
    public String convert(Integer from) {
        return String.valueOf(from);
    }
}
```

### 泛型接口的实现方式
```java
/**
 * 泛型仓库接口
 */
public interface Repository<T, ID> {
    void save(T entity);
    T findById(ID id);
    List<T> findAll();
    void deleteById(ID id);
    boolean existsById(ID id);
}

/**
 * 方式1：实现时指定具体类型
 */
public class UserRepository implements Repository<User, Long> {
    private Map<Long, User> users = new HashMap<>();
    
    @Override
    public void save(User entity) {
        users.put(entity.getId(), entity);
    }
    
    @Override
    public User findById(Long id) {
        return users.get(id);
    }
    
    @Override
    public List<User> findAll() {
        return new ArrayList<>(users.values());
    }
    
    @Override
    public void deleteById(Long id) {
        users.remove(id);
    }
    
    @Override
    public boolean existsById(Long id) {
        return users.containsKey(id);
    }
}

/**
 * 方式2：实现时保持泛型
 */
public class MemoryRepository<T extends BaseEntity<ID>, ID> implements Repository<T, ID> {
    private Map<ID, T> entities = new HashMap<>();
    
    @Override
    public void save(T entity) {
        entities.put(entity.getId(), entity);
    }
    
    @Override
    public T findById(ID id) {
        return entities.get(id);
    }
    
    @Override
    public List<T> findAll() {
        return new ArrayList<>(entities.values());
    }
    
    @Override
    public void deleteById(ID id) {
        entities.remove(id);
    }
    
    @Override
    public boolean existsById(ID id) {
        return entities.containsKey(id);
    }
}
```

## 泛型方法

### 基本泛型方法
```java
public class GenericMethods {

    /**
     * 泛型方法 - 交换数组中两个元素的位置
     */
    public static <T> void swap(T[] array, int i, int j) {
        if (i >= 0 && i < array.length && j >= 0 && j < array.length) {
            T temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
    }

    /**
     * 泛型方法 - 查找数组中的元素
     */
    public static <T> int indexOf(T[] array, T target) {
        for (int i = 0; i < array.length; i++) {
            if (Objects.equals(array[i], target)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 泛型方法 - 创建列表
     */
    public static <T> List<T> createList(T... elements) {
        List<T> list = new ArrayList<>();
        for (T element : elements) {
            list.add(element);
        }
        return list;
    }

    /**
     * 泛型方法 - 转换列表
     */
    public static <F, T> List<T> map(List<F> source, Function<F, T> mapper) {
        List<T> result = new ArrayList<>();
        for (F item : source) {
            result.add(mapper.apply(item));
        }
        return result;
    }

    /**
     * 泛型方法 - 过滤列表
     */
    public static <T> List<T> filter(List<T> source, Predicate<T> predicate) {
        List<T> result = new ArrayList<>();
        for (T item : source) {
            if (predicate.test(item)) {
                result.add(item);
            }
        }
        return result;
    }
}

// 使用示例
String[] names = {"Alice", "Bob", "Charlie"};
GenericMethods.swap(names, 0, 2);  // 交换第一个和第三个元素

Integer[] numbers = {1, 2, 3, 4, 5};
int index = GenericMethods.indexOf(numbers, 3);  // 查找元素3的位置

List<String> fruits = GenericMethods.createList("Apple", "Banana", "Orange");
List<Integer> lengths = GenericMethods.map(fruits, String::length);
List<String> longFruits = GenericMethods.filter(fruits, s -> s.length() > 5);
```

### 有界类型参数的泛型方法
```java
public class BoundedGenericMethods {

    /**
     * 限制类型参数必须是Number的子类
     */
    public static <T extends Number> double sum(List<T> numbers) {
        double total = 0.0;
        for (T number : numbers) {
            total += number.doubleValue();
        }
        return total;
    }

    /**
     * 限制类型参数必须实现Comparable接口
     */
    public static <T extends Comparable<T>> T max(T a, T b) {
        return a.compareTo(b) > 0 ? a : b;
    }

    /**
     * 多重边界 - 必须继承某个类并实现某个接口
     */
    public static <T extends Number & Comparable<T>> T findMax(List<T> numbers) {
        if (numbers.isEmpty()) {
            return null;
        }

        T max = numbers.get(0);
        for (T number : numbers) {
            if (number.compareTo(max) > 0) {
                max = number;
            }
        }
        return max;
    }
}

// 使用示例
List<Integer> integers = Arrays.asList(1, 2, 3, 4, 5);
double sum = BoundedGenericMethods.sum(integers);  // 15.0

String maxString = BoundedGenericMethods.max("apple", "banana");  // "banana"
Integer maxNumber = BoundedGenericMethods.findMax(integers);  // 5
```

## 通配符

### 上界通配符（? extends T）
```java
public class UpperBoundedWildcard {

    /**
     * 上界通配符 - 只能读取，不能写入（除了null）
     */
    public static double sumOfNumbers(List<? extends Number> numbers) {
        double sum = 0.0;
        for (Number number : numbers) {
            sum += number.doubleValue();
        }
        // numbers.add(new Integer(1));  // 编译错误！不能添加
        return sum;
    }

    /**
     * 复制列表 - 从源列表复制到目标列表
     */
    public static <T> void copy(List<? super T> dest, List<? extends T> src) {
        for (T item : src) {
            dest.add(item);
        }
    }
}

// 使用示例
List<Integer> integers = Arrays.asList(1, 2, 3);
List<Double> doubles = Arrays.asList(1.1, 2.2, 3.3);
List<Number> numbers = new ArrayList<>();

double sum1 = UpperBoundedWildcard.sumOfNumbers(integers);  // 可以传入Integer列表
double sum2 = UpperBoundedWildcard.sumOfNumbers(doubles);   // 可以传入Double列表

UpperBoundedWildcard.copy(numbers, integers);  // 从Integer列表复制到Number列表
```

### 下界通配符（? super T）
```java
public class LowerBoundedWildcard {

    /**
     * 下界通配符 - 可以写入，读取时只能当作Object
     */
    public static void addNumbers(List<? super Integer> numbers) {
        numbers.add(1);
        numbers.add(2);
        numbers.add(3);

        // Integer num = numbers.get(0);  // 编译错误！不能确定具体类型
        Object obj = numbers.get(0);      // 只能当作Object读取
    }

    /**
     * 比较器示例
     */
    public static <T> void sort(List<T> list, Comparator<? super T> comparator) {
        // 使用比较器对列表进行排序
        list.sort(comparator);
    }
}

// 使用示例
List<Number> numbers = new ArrayList<>();
LowerBoundedWildcard.addNumbers(numbers);  // 可以向Number列表添加Integer

List<Object> objects = new ArrayList<>();
LowerBoundedWildcard.addNumbers(objects);  // 可以向Object列表添加Integer

List<String> strings = Arrays.asList("banana", "apple", "cherry");
Comparator<Object> objectComparator = (o1, o2) -> o1.toString().compareTo(o2.toString());
LowerBoundedWildcard.sort(strings, objectComparator);  // 使用Object比较器比较String
```

### 无界通配符（?）
```java
public class UnboundedWildcard {

    /**
     * 无界通配符 - 表示未知类型
     */
    public static void printList(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
        // list.add("hello");  // 编译错误！不能添加任何元素（除了null）
        list.add(null);        // 可以添加null
    }

    /**
     * 检查列表是否为空
     */
    public static boolean isEmpty(List<?> list) {
        return list.isEmpty();
    }

    /**
     * 获取列表大小
     */
    public static int getSize(List<?> list) {
        return list.size();
    }

    /**
     * 清空列表
     */
    public static void clear(List<?> list) {
        list.clear();
    }
}

// 使用示例
List<String> strings = Arrays.asList("A", "B", "C");
List<Integer> integers = Arrays.asList(1, 2, 3);

UnboundedWildcard.printList(strings);   // 可以打印任何类型的列表
UnboundedWildcard.printList(integers);

boolean empty1 = UnboundedWildcard.isEmpty(strings);
boolean empty2 = UnboundedWildcard.isEmpty(integers);
```

### PECS原则（Producer Extends, Consumer Super）
```java
public class PECSExample {

    /**
     * Producer Extends - 如果你需要从集合中读取T类型的数据，使用? extends T
     * Consumer Super - 如果你需要向集合中写入T类型的数据，使用? super T
     */

    // Producer - 生产者，从集合中读取数据
    public static <T> void copyAll(Collection<? super T> dest, Collection<? extends T> src) {
        for (T item : src) {  // 从src读取（Producer Extends）
            dest.add(item);   // 向dest写入（Consumer Super）
        }
    }

    // 实际应用示例
    public static void demonstratePECS() {
        // 设置数据
        List<Integer> integers = Arrays.asList(1, 2, 3, 4, 5);
        List<Number> numbers = new ArrayList<>();
        List<Object> objects = new ArrayList<>();

        // Producer Extends - 可以从Integer列表读取到Number
        copyAll(numbers, integers);  // Integer extends Number

        // Consumer Super - 可以向Object列表写入Number
        copyAll(objects, numbers);   // Object super Number

        System.out.println("Numbers: " + numbers);
        System.out.println("Objects: " + objects);
    }
}
```

## 类型擦除

### 什么是类型擦除
Java泛型是通过类型擦除来实现的。在编译时，所有的泛型信息都会被擦除，替换为原始类型（Raw Type）或Object类型。

```java
// 编译前的泛型代码
List<String> stringList = new ArrayList<String>();
List<Integer> integerList = new ArrayList<Integer>();

// 编译后的字节码（类型擦除后）
List stringList = new ArrayList();
List integerList = new ArrayList();

// 运行时检查
System.out.println(stringList.getClass() == integerList.getClass()); // true
```

### 类型擦除的规则
```java
public class TypeErasureExample<T extends Number> {
    private T value;

    // 编译后变成：
    // private Number value;

    public void setValue(T value) {
        this.value = value;
    }

    // 编译后变成：
    // public void setValue(Number value) {
    //     this.value = value;
    // }

    public T getValue() {
        return value;
    }

    // 编译后变成：
    // public Number getValue() {
    //     return value;
    // }
}

// 无界类型参数擦除为Object
public class UnboundedExample<T> {
    private T data;  // 编译后变成 private Object data;
}
```

### 类型擦除的限制
```java
public class TypeErasureLimitations {

    // ❌ 不能创建泛型数组
    // T[] array = new T[10];  // 编译错误

    // ❌ 不能使用instanceof检查泛型类型
    public static <T> boolean isInstance(Object obj) {
        // return obj instanceof T;  // 编译错误
        return false;
    }

    // ❌ 不能创建泛型类的实例
    public static <T> T createInstance() {
        // return new T();  // 编译错误
        return null;
    }

    // ❌ 不能在静态上下文中使用类型参数
    public class GenericClass<T> {
        // private static T staticField;  // 编译错误

        // public static void staticMethod(T param) {}  // 编译错误
    }

    // ✅ 解决方案：使用Class对象
    public static <T> T createInstance(Class<T> clazz) throws Exception {
        return clazz.newInstance();
    }

    public static <T> T[] createArray(Class<T> clazz, int size) {
        return (T[]) Array.newInstance(clazz, size);
    }
}
```

## 泛型约束

### 边界约束
```java
// 上界约束 - extends
public class UpperBounded<T extends Number> {
    private T value;

    public void setValue(T value) {
        this.value = value;
    }

    public double getDoubleValue() {
        return value.doubleValue();  // 可以调用Number的方法
    }
}

// 多重约束
public interface Drawable {
    void draw();
}

public class Shape implements Drawable {
    @Override
    public void draw() {
        System.out.println("Drawing shape");
    }
}

public class BoundedShape<T extends Shape & Drawable> {
    private T shape;

    public void setShape(T shape) {
        this.shape = shape;
    }

    public void drawShape() {
        shape.draw();  // 可以调用Drawable的方法
    }
}
```

### 递归类型约束
```java
// 递归类型约束 - 常用于比较操作
public class RecursiveBound<T extends Comparable<T>> {

    public T max(T a, T b) {
        return a.compareTo(b) > 0 ? a : b;
    }

    public List<T> sort(List<T> list) {
        List<T> sorted = new ArrayList<>(list);
        Collections.sort(sorted);
        return sorted;
    }
}

// 实际应用：枚举类型
public enum Priority implements Comparable<Priority> {
    LOW, MEDIUM, HIGH, URGENT;
}

// 使用递归约束
RecursiveBound<Priority> priorityBound = new RecursiveBound<>();
Priority maxPriority = priorityBound.max(Priority.LOW, Priority.HIGH);  // HIGH
```

## 实际应用

### 泛型工具类
```java
/**
 * 泛型工具类 - 常用操作封装
 */
public class GenericUtils {

    /**
     * 安全的类型转换
     */
    @SuppressWarnings("unchecked")
    public static <T> T safeCast(Object obj, Class<T> clazz) {
        if (clazz.isInstance(obj)) {
            return (T) obj;
        }
        return null;
    }

    /**
     * 创建不可变列表
     */
    @SafeVarargs
    public static <T> List<T> immutableList(T... elements) {
        return Collections.unmodifiableList(Arrays.asList(elements));
    }

    /**
     * 合并多个列表
     */
    @SafeVarargs
    public static <T> List<T> merge(List<T>... lists) {
        List<T> result = new ArrayList<>();
        for (List<T> list : lists) {
            result.addAll(list);
        }
        return result;
    }

    /**
     * 分组操作
     */
    public static <T, K> Map<K, List<T>> groupBy(List<T> list, Function<T, K> keyExtractor) {
        Map<K, List<T>> groups = new HashMap<>();
        for (T item : list) {
            K key = keyExtractor.apply(item);
            groups.computeIfAbsent(key, k -> new ArrayList<>()).add(item);
        }
        return groups;
    }

    /**
     * 查找第一个匹配的元素
     */
    public static <T> Optional<T> findFirst(List<T> list, Predicate<T> predicate) {
        for (T item : list) {
            if (predicate.test(item)) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }
}
```

### 泛型建造者模式
```java
/**
 * 泛型建造者模式
 */
public class GenericBuilder<T> {
    private final Class<T> clazz;
    private final Map<String, Object> properties = new HashMap<>();

    public GenericBuilder(Class<T> clazz) {
        this.clazz = clazz;
    }

    public GenericBuilder<T> set(String property, Object value) {
        properties.put(property, value);
        return this;
    }

    public T build() {
        try {
            T instance = clazz.newInstance();
            for (Map.Entry<String, Object> entry : properties.entrySet()) {
                setProperty(instance, entry.getKey(), entry.getValue());
            }
            return instance;
        } catch (Exception e) {
            throw new RuntimeException("Failed to build instance", e);
        }
    }

    private void setProperty(T instance, String property, Object value) {
        try {
            Field field = clazz.getDeclaredField(property);
            field.setAccessible(true);
            field.set(instance, value);
        } catch (Exception e) {
            // 忽略不存在的属性
        }
    }

    public static <T> GenericBuilder<T> of(Class<T> clazz) {
        return new GenericBuilder<>(clazz);
    }
}

// 使用示例
public class Person {
    private String name;
    private int age;
    private String email;

    // getters and setters...
}

Person person = GenericBuilder.of(Person.class)
    .set("name", "Alice")
    .set("age", 25)
    .set("email", "<EMAIL>")
    .build();
```

### 泛型缓存系统
```java
/**
 * 泛型缓存系统
 */
public class GenericCache<K, V> {
    private final Map<K, CacheEntry<V>> cache = new ConcurrentHashMap<>();
    private final long defaultTtl;

    public GenericCache(long defaultTtl) {
        this.defaultTtl = defaultTtl;
    }

    public void put(K key, V value) {
        put(key, value, defaultTtl);
    }

    public void put(K key, V value, long ttl) {
        long expireTime = System.currentTimeMillis() + ttl;
        cache.put(key, new CacheEntry<>(value, expireTime));
    }

    public Optional<V> get(K key) {
        CacheEntry<V> entry = cache.get(key);
        if (entry == null) {
            return Optional.empty();
        }

        if (entry.isExpired()) {
            cache.remove(key);
            return Optional.empty();
        }

        return Optional.of(entry.getValue());
    }

    public void remove(K key) {
        cache.remove(key);
    }

    public void clear() {
        cache.clear();
    }

    public int size() {
        cleanExpired();
        return cache.size();
    }

    private void cleanExpired() {
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    private static class CacheEntry<V> {
        private final V value;
        private final long expireTime;

        public CacheEntry(V value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }

        public V getValue() {
            return value;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
    }
}

// 使用示例
GenericCache<String, User> userCache = new GenericCache<>(60000); // 1分钟TTL
userCache.put("user1", new User("Alice"));
Optional<User> user = userCache.get("user1");
```

## 最佳实践

### 1. 命名约定
```java
// ✅ 好的命名
public class Repository<T, ID> {}           // T=Type, ID=Identifier
public interface Converter<F, T> {}         // F=From, T=To
public class Cache<K, V> {}                 // K=Key, V=Value

// ❌ 避免的命名
public class MyClass<A, B, C> {}            // 无意义的命名
public interface Handler<X> {}              // 不清晰的命名
```

### 2. 优先使用泛型
```java
// ✅ 使用泛型
List<String> names = new ArrayList<>();
Map<String, Integer> scores = new HashMap<>();

// ❌ 使用原始类型
List names = new ArrayList();               // 编译器警告
Map scores = new HashMap();                 // 类型不安全
```

### 3. 合理使用通配符
```java
// ✅ 合理使用通配符
public void processNumbers(List<? extends Number> numbers) {
    // 只读操作，使用上界通配符
}

public void addToCollection(Collection<? super Integer> collection) {
    // 写入操作，使用下界通配符
}

// ❌ 过度使用通配符
public List<?> confusingMethod(List<?> input) {
    // 返回类型不明确，难以使用
    return input;
}
```

### 4. 避免泛型数组
```java
// ❌ 避免泛型数组
// List<String>[] arrays = new List<String>[10];  // 编译错误

// ✅ 使用集合替代
List<List<String>> listOfLists = new ArrayList<>();

// ✅ 或使用通配符
List<?>[] arrays = new List<?>[10];
```

### 5. 正确处理类型擦除
```java
public class TypeToken<T> {
    private final Class<T> type;

    // ✅ 通过构造函数传递类型信息
    public TypeToken(Class<T> type) {
        this.type = type;
    }

    public Class<T> getType() {
        return type;
    }

    public T createInstance() throws Exception {
        return type.newInstance();
    }
}

// 使用示例
TypeToken<String> stringToken = new TypeToken<>(String.class);
TypeToken<List> listToken = new TypeToken<>(List.class);
```

### 6. 泛型方法vs泛型类
```java
// ✅ 当只有方法需要泛型时，使用泛型方法
public class Utility {
    public static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
}

// ✅ 当整个类都需要泛型时，使用泛型类
public class Container<T> {
    private T item;

    public void setItem(T item) { this.item = item; }
    public T getItem() { return item; }
}
```

### 7. 异常处理中的泛型
```java
public class GenericException<T> extends Exception {
    private final T data;

    public GenericException(String message, T data) {
        super(message);
        this.data = data;
    }

    public T getData() {
        return data;
    }
}

// 使用示例
try {
    // 某些操作
} catch (GenericException<ValidationError> e) {
    ValidationError error = e.getData();
    // 处理验证错误
}
```

Java泛型是一个强大的特性，它提供了类型安全、代码重用和更好的API设计。掌握泛型的使用，包括泛型类、泛型接口、泛型方法、通配符和边界约束，对于编写高质量的Java代码至关重要。记住PECS原则，合理使用通配符，并注意类型擦除的限制，这样就能充分发挥泛型的优势。
