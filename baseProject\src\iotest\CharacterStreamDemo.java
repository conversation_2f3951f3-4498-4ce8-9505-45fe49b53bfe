package iotest;

public class CharacterStreamDemo {
    public static void main(String[] args) {
        String fileName = "text_file.txt";

        // 写入文本文件
        writeTextFile(fileName);

        // 读取文本文件
        readTextFile(fileName);
    }

    public static void writeTextFile(String fileName) {
        try (java.io.FileWriter writer = new java.io.FileWriter(fileName)) {
            writer.write("这是第一行文本\n");
            writer.write("这是第二行文本\n");
        }
        catch (java.io.IOException e) {
            System.out.println("写入文件异常: " + e.getMessage());
        }
    }

    public static void readTextFile(String fileName) {
        try (java.io.FileReader reader = new java.io.FileReader(fileName)) {
            int charData;
            while ((charData = reader.read()) != -1) {
                System.out.print((char) charData);
            }
        }
        catch (java.io.IOException e) {
            System.out.println("读取文件异常: " + e.getMessage());
        }
    }
}
