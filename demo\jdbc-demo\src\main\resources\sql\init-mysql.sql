-- =============================================
-- JDBC Demo MySQL 初始化脚本
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS jdbc_demo_db;
CREATE DATABASE jdbc_demo_db 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

USE jdbc_demo_db;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    age INT COMMENT '年龄',
    address VARCHAR(200) COMMENT '地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建用户账户表（用于事务演示）
CREATE TABLE IF NOT EXISTS user_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表';

-- 创建用户日志表
CREATE TABLE IF NOT EXISTS user_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作日志表';

-- 插入初始测试数据
INSERT INTO users (username, email, phone, age, address) VALUES
('admin', '<EMAIL>', '***********', 30, '北京市朝阳区'),
('test_user', '<EMAIL>', '***********', 25, '上海市浦东新区'),
('demo_user', '<EMAIL>', '***********', 28, '广州市天河区');

-- 为用户创建账户
INSERT INTO user_account (user_id, balance) VALUES
(1, 1000.00),
(2, 500.00),
(3, 800.00);

-- 插入一些日志记录
INSERT INTO user_logs (user_id, action, description, ip_address) VALUES
(1, 'LOGIN', '管理员登录系统', '*************'),
(2, 'REGISTER', '用户注册', '*************'),
(3, 'LOGIN', '用户登录', '*************'),
(1, 'UPDATE_PROFILE', '更新个人信息', '*************');

-- 创建存储过程示例
DELIMITER //

CREATE PROCEDURE GetUserStatistics()
BEGIN
    SELECT 
        COUNT(*) as total_users,
        AVG(age) as avg_age,
        COUNT(CASE WHEN age < 25 THEN 1 END) as young_users,
        COUNT(CASE WHEN age >= 25 AND age < 35 THEN 1 END) as middle_users,
        COUNT(CASE WHEN age >= 35 THEN 1 END) as senior_users
    FROM users;
END //

DELIMITER ;

-- 创建函数示例
DELIMITER //

CREATE FUNCTION GetUserBalance(userId BIGINT) 
RETURNS DECIMAL(10,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE userBalance DECIMAL(10,2) DEFAULT 0.00;
    
    SELECT balance INTO userBalance 
    FROM user_account 
    WHERE user_id = userId;
    
    RETURN IFNULL(userBalance, 0.00);
END //

DELIMITER ;

-- 创建视图示例
CREATE VIEW user_summary AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.age,
    u.address,
    ua.balance,
    u.created_at
FROM users u
LEFT JOIN user_account ua ON u.id = ua.user_id;

-- 显示创建结果
SELECT 'MySQL数据库初始化完成' as status;
SELECT TABLE_NAME, TABLE_COMMENT 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'jdbc_demo_db';

-- 显示初始数据
SELECT '=== 初始用户数据 ===' as info;
SELECT * FROM users;

SELECT '=== 初始账户数据 ===' as info;
SELECT * FROM user_account;

SELECT '=== 用户汇总视图 ===' as info;
SELECT * FROM user_summary;
