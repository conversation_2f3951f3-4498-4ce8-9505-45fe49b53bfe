package thread;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池演示
 * 包括各种类型的线程池和自定义线程池
 */
public class ThreadPoolDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 线程池演示 ===");
        
        // 1. 基本线程池类型
        basicThreadPoolDemo();
        
        // 2. 自定义线程池
        customThreadPoolDemo();
        
        // 3. 定时任务线程池
        scheduledThreadPoolDemo();
        
        // 4. 线程池监控
        threadPoolMonitoringDemo();
    }
    
    /**
     * 基本线程池类型演示
     */
    public static void basicThreadPoolDemo() {
        System.out.println("\n--- 基本线程池演示 ---");
        
        // 1. 固定大小线程池
        ExecutorService fixedPool = Executors.newFixedThreadPool(3);
        System.out.println("固定大小线程池 (大小: 3)");
        
        for (int i = 1; i <= 6; i++) {
            final int taskId = i;
            fixedPool.submit(() -> {
                System.out.println("固定池任务" + taskId + " 在 " + 
                    Thread.currentThread().getName() + " 中执行");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.println("固定池任务" + taskId + " 完成");
            });
        }
        
        // 2. 缓存线程池
        ExecutorService cachedPool = Executors.newCachedThreadPool();
        System.out.println("\n缓存线程池");
        
        for (int i = 1; i <= 3; i++) {
            final int taskId = i;
            cachedPool.submit(() -> {
                System.out.println("缓存池任务" + taskId + " 在 " + 
                    Thread.currentThread().getName() + " 中执行");
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.println("缓存池任务" + taskId + " 完成");
            });
        }
        
        // 3. 单线程池
        ExecutorService singlePool = Executors.newSingleThreadExecutor();
        System.out.println("\n单线程池");
        
        for (int i = 1; i <= 3; i++) {
            final int taskId = i;
            singlePool.submit(() -> {
                System.out.println("单线程池任务" + taskId + " 在 " + 
                    Thread.currentThread().getName() + " 中执行");
                try {
                    Thread.sleep(800);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.println("单线程池任务" + taskId + " 完成");
            });
        }
        
        // 等待任务完成并关闭线程池
        shutdownAndAwait(fixedPool, "固定线程池");
        shutdownAndAwait(cachedPool, "缓存线程池");
        shutdownAndAwait(singlePool, "单线程池");
    }
    
    /**
     * 自定义线程池演示
     */
    public static void customThreadPoolDemo() {
        System.out.println("\n--- 自定义线程池演示 ---");
        
        // 自定义线程工厂
        ThreadFactory customThreadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "自定义线程-" + threadNumber.getAndIncrement());
                t.setDaemon(false);
                t.setPriority(Thread.NORM_PRIORITY);
                return t;
            }
        };
        
        // 创建自定义线程池
        ThreadPoolExecutor customExecutor = new ThreadPoolExecutor(
            2,                              // 核心线程数
            4,                              // 最大线程数
            60L,                            // 空闲线程存活时间
            TimeUnit.SECONDS,               // 时间单位
            new LinkedBlockingQueue<>(5),   // 工作队列
            customThreadFactory,            // 线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
        
        System.out.println("自定义线程池配置:");
        System.out.println("核心线程数: " + customExecutor.getCorePoolSize());
        System.out.println("最大线程数: " + customExecutor.getMaximumPoolSize());
        System.out.println("队列容量: " + customExecutor.getQueue().remainingCapacity());
        
        // 提交任务
        for (int i = 1; i <= 10; i++) {
            final int taskId = i;
            try {
                customExecutor.submit(() -> {
                    System.out.println("自定义池任务" + taskId + " 开始执行 - " + 
                        Thread.currentThread().getName());
                    try {
                        Thread.sleep(3000); // 模拟长时间任务
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    System.out.println("自定义池任务" + taskId + " 执行完成");
                });
            } catch (RejectedExecutionException e) {
                System.out.println("任务" + taskId + " 被拒绝执行: " + e.getMessage());
            }
        }
        
        // 监控线程池状态
        for (int i = 0; i < 5; i++) {
            System.out.println("线程池状态 - 活跃线程: " + customExecutor.getActiveCount() +
                ", 队列大小: " + customExecutor.getQueue().size() +
                ", 完成任务: " + customExecutor.getCompletedTaskCount());
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        shutdownAndAwait(customExecutor, "自定义线程池");
    }
    
    /**
     * 定时任务线程池演示
     */
    public static void scheduledThreadPoolDemo() {
        System.out.println("\n--- 定时任务线程池演示 ---");
        
        ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(2);
        
        // 1. 延迟执行任务
        System.out.println("提交延迟任务 (3秒后执行)");
        scheduledPool.schedule(() -> {
            System.out.println("延迟任务执行 - " + Thread.currentThread().getName());
        }, 3, TimeUnit.SECONDS);
        
        // 2. 固定频率执行任务
        System.out.println("提交固定频率任务 (每2秒执行一次)");
        ScheduledFuture<?> fixedRateTask = scheduledPool.scheduleAtFixedRate(() -> {
            System.out.println("固定频率任务执行 - " + System.currentTimeMillis() + 
                " - " + Thread.currentThread().getName());
        }, 1, 2, TimeUnit.SECONDS);
        
        // 3. 固定延迟执行任务
        System.out.println("提交固定延迟任务 (上次执行完成后延迟1秒)");
        ScheduledFuture<?> fixedDelayTask = scheduledPool.scheduleWithFixedDelay(() -> {
            System.out.println("固定延迟任务开始 - " + System.currentTimeMillis());
            try {
                Thread.sleep(1500); // 模拟任务执行时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            System.out.println("固定延迟任务完成 - " + System.currentTimeMillis());
        }, 0, 1, TimeUnit.SECONDS);
        
        // 运行10秒后取消定时任务
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("取消定时任务");
        fixedRateTask.cancel(false);
        fixedDelayTask.cancel(false);
        
        shutdownAndAwait(scheduledPool, "定时任务线程池");
    }
    
    /**
     * 线程池监控演示
     */
    public static void threadPoolMonitoringDemo() {
        System.out.println("\n--- 线程池监控演示 ---");
        
        ThreadPoolExecutor monitoredPool = new ThreadPoolExecutor(
            2, 4, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(3),
            new ThreadFactory() {
                private int count = 1;
                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "监控线程-" + count++);
                }
            }
        );
        
        // 启动监控线程
        ScheduledExecutorService monitor = Executors.newSingleThreadScheduledExecutor();
        monitor.scheduleAtFixedRate(() -> {
            System.out.println("=== 线程池监控信息 ===");
            System.out.println("核心线程数: " + monitoredPool.getCorePoolSize());
            System.out.println("最大线程数: " + monitoredPool.getMaximumPoolSize());
            System.out.println("当前线程数: " + monitoredPool.getPoolSize());
            System.out.println("活跃线程数: " + monitoredPool.getActiveCount());
            System.out.println("已完成任务数: " + monitoredPool.getCompletedTaskCount());
            System.out.println("总任务数: " + monitoredPool.getTaskCount());
            System.out.println("队列中任务数: " + monitoredPool.getQueue().size());
            System.out.println("是否正在关闭: " + monitoredPool.isShutdown());
            System.out.println("是否已终止: " + monitoredPool.isTerminated());
            System.out.println("========================");
        }, 0, 2, TimeUnit.SECONDS);
        
        // 提交一些任务
        for (int i = 1; i <= 8; i++) {
            final int taskId = i;
            monitoredPool.submit(() -> {
                System.out.println("监控任务" + taskId + " 开始 - " + 
                    Thread.currentThread().getName());
                try {
                    Thread.sleep(4000); // 长时间任务
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.println("监控任务" + taskId + " 完成");
            });
        }
        
        // 运行15秒后关闭
        try {
            Thread.sleep(15000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        monitor.shutdown();
        shutdownAndAwait(monitoredPool, "监控线程池");
    }
    
    /**
     * 优雅关闭线程池
     */
    private static void shutdownAndAwait(ExecutorService executor, String poolName) {
        System.out.println("\n关闭" + poolName + "...");
        executor.shutdown();
        
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                System.out.println(poolName + "未能在5秒内关闭，强制关闭");
                executor.shutdownNow();
                
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    System.err.println(poolName + "未能正常关闭");
                }
            } else {
                System.out.println(poolName + "已正常关闭");
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}

/**
 * 线程池最佳实践示例
 */
class ThreadPoolBestPractices {
    
    /**
     * 创建合适的线程池
     */
    public static ThreadPoolExecutor createOptimalThreadPool() {
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        int maximumPoolSize = corePoolSize * 2;
        long keepAliveTime = 60L;
        TimeUnit unit = TimeUnit.SECONDS;
        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(1000);
        
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger threadNumber = new AtomicInteger(1);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "OptimalPool-" + threadNumber.getAndIncrement());
                t.setDaemon(false);
                t.setPriority(Thread.NORM_PRIORITY);
                return t;
            }
        };
        
        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        
        return new ThreadPoolExecutor(
            corePoolSize, maximumPoolSize, keepAliveTime, unit,
            workQueue, threadFactory, handler
        );
    }
    
    public static void main(String[] args) {
        System.out.println("=== 线程池最佳实践 ===");
        
        ThreadPoolExecutor executor = createOptimalThreadPool();
        
        System.out.println("最优线程池配置:");
        System.out.println("核心线程数: " + executor.getCorePoolSize());
        System.out.println("最大线程数: " + executor.getMaximumPoolSize());
        System.out.println("队列类型: " + executor.getQueue().getClass().getSimpleName());
        
        // 提交任务测试
        for (int i = 1; i <= 5; i++) {
            final int taskId = i;
            executor.submit(() -> {
                System.out.println("最优池任务" + taskId + " - " + 
                    Thread.currentThread().getName());
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        
        // 优雅关闭
        executor.shutdown();
        try {
            if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        System.out.println("最优线程池演示完成");
    }
}
