<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML基础示例 - 我的第一个网页</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #007bff;
            margin-top: 30px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #007bff;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .image-gallery {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .image-item {
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <header>
            <h1>🌟 HTML基础学习示例</h1>
            <p><strong>欢迎来到前端开发的世界！</strong>这个页面展示了HTML的各种基础元素和用法。</p>
        </header>

        <!-- 导航菜单 -->
        <nav>
            <h2>📋 页面导航</h2>
            <ul>
                <li><a href="#text-elements">文本元素</a></li>
                <li><a href="#lists">列表</a></li>
                <li><a href="#tables">表格</a></li>
                <li><a href="#forms">表单</a></li>
                <li><a href="#media">媒体元素</a></li>
                <li><a href="#semantic">语义化标签</a></li>
            </ul>
        </nav>

        <!-- 文本元素示例 -->
        <section id="text-elements">
            <h2>📝 文本元素</h2>
            
            <h3>标题层级</h3>
            <h1>一级标题 (h1)</h1>
            <h2>二级标题 (h2)</h2>
            <h3>三级标题 (h3)</h3>
            <h4>四级标题 (h4)</h4>
            <h5>五级标题 (h5)</h5>
            <h6>六级标题 (h6)</h6>

            <h3>文本格式</h3>
            <p>这是一个普通段落。HTML中的段落使用 <code>&lt;p&gt;</code> 标签。</p>
            <p>文本可以是 <strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>、<del>删除线</del>、<mark>高亮</mark>。</p>
            <p>还可以使用 <small>小字体</small> 和 <big>大字体</big>。</p>
            
            <blockquote>
                <p>"学习HTML是前端开发的第一步，也是最重要的基础。"</p>
                <cite>— 前端开发者</cite>
            </blockquote>

            <div class="highlight">
                <p><strong>💡 提示：</strong>HTML是超文本标记语言，用于创建网页的结构和内容。</p>
            </div>
        </section>

        <!-- 列表示例 -->
        <section id="lists">
            <h2>📋 列表</h2>
            
            <h3>无序列表 (ul)</h3>
            <ul>
                <li>HTML - 网页结构</li>
                <li>CSS - 样式设计</li>
                <li>JavaScript - 交互逻辑</li>
                <li>前端框架
                    <ul>
                        <li>Vue.js</li>
                        <li>React</li>
                        <li>Angular</li>
                    </ul>
                </li>
            </ul>

            <h3>有序列表 (ol)</h3>
            <ol>
                <li>学习HTML基础</li>
                <li>掌握CSS样式</li>
                <li>学习JavaScript</li>
                <li>选择前端框架</li>
                <li>项目实战练习</li>
            </ol>

            <h3>定义列表 (dl)</h3>
            <dl>
                <dt>HTML</dt>
                <dd>超文本标记语言，用于创建网页结构</dd>
                
                <dt>CSS</dt>
                <dd>层叠样式表，用于设计网页外观</dd>
                
                <dt>JavaScript</dt>
                <dd>编程语言，用于实现网页交互功能</dd>
            </dl>
        </section>

        <!-- 表格示例 -->
        <section id="tables">
            <h2>📊 表格</h2>
            <p>表格用于展示结构化数据：</p>
            
            <table>
                <caption>前端技术学习进度表</caption>
                <thead>
                    <tr>
                        <th>技术</th>
                        <th>难度</th>
                        <th>学习时间</th>
                        <th>重要程度</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>HTML</td>
                        <td>⭐⭐</td>
                        <td>1-2周</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>✅ 已完成</td>
                    </tr>
                    <tr>
                        <td>CSS</td>
                        <td>⭐⭐⭐</td>
                        <td>2-3周</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>🔄 学习中</td>
                    </tr>
                    <tr>
                        <td>JavaScript</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>4-6周</td>
                        <td>⭐⭐⭐⭐⭐</td>
                        <td>⏳ 计划中</td>
                    </tr>
                    <tr>
                        <td>Vue.js</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>3-4周</td>
                        <td>⭐⭐⭐⭐</td>
                        <td>⏳ 计划中</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- 表单示例 -->
        <section id="forms">
            <h2>📝 表单</h2>
            <p>表单用于收集用户输入：</p>
            
            <form action="#" method="post">
                <fieldset>
                    <legend>个人信息</legend>
                    
                    <div class="form-group">
                        <label for="name">姓名：</label>
                        <input type="text" id="name" name="name" placeholder="请输入您的姓名" required>
                    </div>

                    <div class="form-group">
                        <label for="email">邮箱：</label>
                        <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                    </div>

                    <div class="form-group">
                        <label for="age">年龄：</label>
                        <input type="number" id="age" name="age" min="1" max="120">
                    </div>

                    <div class="form-group">
                        <label for="gender">性别：</label>
                        <select id="gender" name="gender">
                            <option value="">请选择</option>
                            <option value="male">男</option>
                            <option value="female">女</option>
                            <option value="other">其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>兴趣爱好：</label>
                        <input type="checkbox" id="coding" name="hobbies" value="coding">
                        <label for="coding">编程</label>
                        
                        <input type="checkbox" id="reading" name="hobbies" value="reading">
                        <label for="reading">阅读</label>
                        
                        <input type="checkbox" id="music" name="hobbies" value="music">
                        <label for="music">音乐</label>
                    </div>

                    <div class="form-group">
                        <label>学习目标：</label>
                        <input type="radio" id="frontend" name="goal" value="frontend">
                        <label for="frontend">前端开发</label>
                        
                        <input type="radio" id="backend" name="goal" value="backend">
                        <label for="backend">后端开发</label>
                        
                        <input type="radio" id="fullstack" name="goal" value="fullstack">
                        <label for="fullstack">全栈开发</label>
                    </div>

                    <div class="form-group">
                        <label for="message">留言：</label>
                        <textarea id="message" name="message" rows="4" placeholder="请输入您的留言..."></textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit">提交表单</button>
                        <button type="reset">重置表单</button>
                    </div>
                </fieldset>
            </form>
        </section>

        <!-- 媒体元素示例 -->
        <section id="media">
            <h2>🎨 媒体元素</h2>
            
            <h3>图片</h3>
            <div class="image-gallery">
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=HTML" alt="HTML示例图片">
                    <p>HTML结构</p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/28a745/ffffff?text=CSS" alt="CSS示例图片">
                    <p>CSS样式</p>
                </div>
                <div class="image-item">
                    <img src="https://via.placeholder.com/300x200/ffc107/000000?text=JS" alt="JavaScript示例图片">
                    <p>JavaScript交互</p>
                </div>
            </div>

            <h3>链接</h3>
            <p>学习资源链接：</p>
            <ul>
                <li><a href="https://developer.mozilla.org/zh-CN/" target="_blank">MDN Web文档</a></li>
                <li><a href="https://www.w3school.com.cn/" target="_blank">W3School</a></li>
                <li><a href="https://www.runoob.com/" target="_blank">菜鸟教程</a></li>
            </ul>
        </section>

        <!-- 语义化标签示例 -->
        <section id="semantic">
            <h2>🏗️ 语义化标签</h2>
            <p>HTML5引入了许多语义化标签，让网页结构更清晰：</p>
            
            <article>
                <header>
                    <h3>文章标题</h3>
                    <p>发布时间：<time datetime="2024-01-01">2024年1月1日</time></p>
                </header>
                
                <p>这是一篇关于HTML语义化标签的文章内容...</p>
                
                <aside>
                    <h4>相关链接</h4>
                    <ul>
                        <li><a href="#">HTML5新特性</a></li>
                        <li><a href="#">语义化的重要性</a></li>
                    </ul>
                </aside>
                
                <footer>
                    <p>作者：前端开发者 | 标签：HTML, 语义化</p>
                </footer>
            </article>
        </section>

        <!-- 页面底部 -->
        <footer>
            <h2>🎉 恭喜！</h2>
            <p>您已经学习了HTML的基础知识！接下来可以学习CSS来美化这个页面。</p>
            <p><strong>下一步：</strong>学习CSS样式设计，让网页更加美观。</p>
            
            <div class="highlight">
                <p><strong>💡 学习建议：</strong></p>
                <ul>
                    <li>多练习编写HTML代码</li>
                    <li>尝试创建不同类型的网页</li>
                    <li>学习HTML5的新特性</li>
                    <li>注重代码的语义化和可访问性</li>
                </ul>
            </div>
        </footer>
    </div>

    <script>
        // 简单的JavaScript交互
        document.addEventListener('DOMContentLoaded', function() {
            // 表单提交处理
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('表单提交成功！这只是一个演示。');
            });

            // 平滑滚动到锚点
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });

            console.log('🎉 HTML基础示例页面加载完成！');
        });
    </script>
</body>
</html>
