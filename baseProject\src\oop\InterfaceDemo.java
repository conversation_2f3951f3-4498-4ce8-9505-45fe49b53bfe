package oop;

/**
 * 接口演示程序
 * 展示接口的使用和多接口实现
 */
public class InterfaceDemo {
    public static void main(String[] args) {
        System.out.println("=== Java接口演示 ===\n");
        
        // 1. 创建SchoolManager对象
        System.out.println("1. 创建学校管理员");
        SchoolManager manager = new SchoolManager("M001", "王管理员");
        
        // 2. 演示接口方法调用
        System.out.println("\n2. 演示管理功能");
        manager.create();
        manager.update();
        manager.find("M001");
        manager.find("M002");
        
        // 3. 演示打印功能
        System.out.println("\n3. 演示打印功能");
        manager.print();
        manager.printWithTimestamp();
        
        // 4. 演示默认方法
        System.out.println("\n4. 演示默认方法");
        manager.showManagementStatus();
        
        // 5. 演示静态方法
        System.out.println("\n5. 演示静态方法");
        System.out.println("系统版本：" + Manageable.getManagementSystemVersion());
        
        // 6. 演示多态 - 接口引用
        System.out.println("\n6. 演示接口多态");
        Manageable manageableRef = manager;
        Printable printableRef = manager;
        
        System.out.println("通过Manageable接口调用：");
        manageableRef.showManagementStatus();
        
        System.out.println("通过Printable接口调用：");
        printableRef.print();
        
        // 7. 删除管理员
        System.out.println("\n7. 删除管理员");
        manager.delete();
        manager.update();  // 删除后无法更新
        
        System.out.println("\n=== 接口演示结束 ===");
    }
}
