# Spring Boot详解

## 目录
1. [Spring Boot简介](#spring-boot简介)
2. [环境搭建](#环境搭建)
3. [项目结构](#项目结构)
4. [核心注解](#核心注解)
5. [配置管理](#配置管理)
6. [Web开发](#web开发)
7. [数据访问](#数据访问)
8. [安全控制](#安全控制)
9. [测试](#测试)
10. [部署](#部署)

## Spring Boot简介

### 什么是Spring Boot
Spring Boot是Spring团队提供的全新框架，其设计目的是用来简化Spring应用的初始搭建以及开发过程。它使用了特定的方式来进行配置，从而使开发人员不再需要定义样板化的配置。

### Spring Boot的优势
- **快速创建独立的Spring应用**
- **直接嵌入Tomcat、Jetty或Undertow**（无需部署WAR文件）
- **提供starter依赖简化构建配置**
- **自动配置Spring和第三方库**
- **提供生产就绪的功能**（如指标、健康检查和外部化配置）
- **无代码生成和XML配置要求**

### Spring Boot vs Spring Framework
| 特性 | Spring Framework | Spring Boot |
|------|------------------|-------------|
| 配置 | 需要大量XML或Java配置 | 自动配置，约定优于配置 |
| 依赖管理 | 手动管理版本兼容性 | Starter依赖自动管理 |
| 服务器 | 需要外部服务器 | 内嵌服务器 |
| 部署 | WAR包部署 | JAR包独立运行 |
| 开发效率 | 配置复杂，开发较慢 | 快速开发，开箱即用 |

## 环境搭建

### 系统要求
- **Java**: JDK 8 或更高版本
- **Maven**: 3.6+ 或 **Gradle**: 6.8+
- **IDE**: IntelliJ IDEA、Eclipse、VS Code等

### 创建Spring Boot项目

#### 方式一：使用Spring Initializr
1. 访问 https://start.spring.io/
2. 选择项目配置：
   - Project: Maven Project
   - Language: Java
   - Spring Boot: 2.7.x 或 3.x
   - Group: com.example
   - Artifact: demo
   - Packaging: Jar
   - Java: 8/11/17
3. 添加依赖：Spring Web、Spring Data JPA、MySQL Driver等
4. 生成并下载项目

#### 方式二：使用IDE创建
```xml
<!-- pom.xml 基本配置 -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.14</version>
        <relativePath/>
    </parent>
    
    <groupId>com.example</groupId>
    <artifactId>springboot-demo</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>springboot-demo</name>
    <description>Spring Boot Demo Project</description>
    
    <properties>
        <java.version>8</java.version>
    </properties>
    
    <dependencies>
        <!-- Spring Boot Web Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- Spring Boot Test Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

### 第一个Spring Boot应用

```java
// Application.java - 主启动类
package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

```java
// HelloController.java - 控制器
package com.example.demo.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HelloController {
    
    @GetMapping("/hello")
    public String hello() {
        return "Hello, Spring Boot!";
    }
    
    @GetMapping("/")
    public String home() {
        return "Welcome to Spring Boot Demo!";
    }
}
```

## 项目结构

### 标准项目结构
```
src/
├── main/
│   ├── java/
│   │   └── com/example/demo/
│   │       ├── Application.java          # 主启动类
│   │       ├── controller/               # 控制器层
│   │       │   └── UserController.java
│   │       ├── service/                  # 服务层
│   │       │   ├── UserService.java
│   │       │   └── impl/
│   │       │       └── UserServiceImpl.java
│   │       ├── repository/               # 数据访问层
│   │       │   └── UserRepository.java
│   │       ├── entity/                   # 实体类
│   │       │   └── User.java
│   │       ├── dto/                      # 数据传输对象
│   │       │   └── UserDTO.java
│   │       ├── config/                   # 配置类
│   │       │   └── WebConfig.java
│   │       └── util/                     # 工具类
│   │           └── DateUtil.java
│   └── resources/
│       ├── application.yml               # 配置文件
│       ├── static/                       # 静态资源
│       │   ├── css/
│       │   ├── js/
│       │   └── images/
│       └── templates/                    # 模板文件
│           └── index.html
└── test/
    └── java/
        └── com/example/demo/
            └── ApplicationTests.java
```

### 包命名规范
- **controller**: 控制器层，处理HTTP请求
- **service**: 业务逻辑层，处理业务逻辑
- **repository/dao**: 数据访问层，与数据库交互
- **entity/model**: 实体类，对应数据库表
- **dto**: 数据传输对象，用于层间数据传输
- **vo**: 视图对象，用于前端展示
- **config**: 配置类
- **util**: 工具类
- **exception**: 异常处理类

## 核心注解

### 启动类注解
```java
@SpringBootApplication
public class Application {
    // @SpringBootApplication 等价于以下三个注解的组合：
    // @Configuration: 标识配置类
    // @EnableAutoConfiguration: 启用自动配置
    // @ComponentScan: 启用组件扫描
}
```

### 控制器注解
```java
@RestController  // @Controller + @ResponseBody
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping("/{id}")           // GET请求
    @PostMapping                   // POST请求
    @PutMapping("/{id}")          // PUT请求
    @DeleteMapping("/{id}")       // DELETE请求
    @PatchMapping("/{id}")        // PATCH请求
    
    // 参数绑定注解
    public User getUser(@PathVariable Long id,           // 路径变量
                       @RequestParam String name,        // 请求参数
                       @RequestBody UserDTO userDTO,     // 请求体
                       @RequestHeader String token) {    // 请求头
        return userService.findById(id);
    }
}
```

### 依赖注入注解
```java
@Component      // 通用组件
@Service        // 服务层组件
@Repository     // 数据访问层组件
@Controller     // 控制器组件
@Configuration  // 配置类

@Autowired      // 自动装配
@Qualifier      // 指定装配的Bean
@Resource       // JSR-250标准注解
@Value          // 注入配置值
```

### 配置注解
```java
@ConfigurationProperties(prefix = "app")
public class AppProperties {
    private String name;
    private String version;
    // getters and setters
}

@EnableConfigurationProperties(AppProperties.class)
@Configuration
public class AppConfig {
    
    @Bean
    @ConditionalOnProperty(name = "feature.enabled", havingValue = "true")
    public FeatureService featureService() {
        return new FeatureServiceImpl();
    }
}
```

## 配置管理

### application.yml配置文件
```yaml
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

# 数据源配置
spring:
  datasource:
    url: ***********************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 2000ms

# 日志配置
logging:
  level:
    com.example.demo: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 自定义配置
app:
  name: Spring Boot Demo
  version: 1.0.0
  author: Developer
  features:
    - user-management
    - data-analytics
    - security
```

### 多环境配置
```yaml
# application.yml - 主配置文件
spring:
  profiles:
    active: dev  # 激活开发环境

---
# application-dev.yml - 开发环境
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ************************************
    username: dev_user
    password: dev_pass

---
# application-prod.yml - 生产环境
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ***************************************
    username: prod_user
    password: ${DB_PASSWORD}  # 环境变量
```

### 配置属性类
```java
@ConfigurationProperties(prefix = "app")
@Component
public class AppProperties {
    private String name;
    private String version;
    private String author;
    private List<String> features;

    // 构造函数、getter和setter
    public AppProperties() {}

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public String getAuthor() { return author; }
    public void setAuthor(String author) { this.author = author; }

    public List<String> getFeatures() { return features; }
    public void setFeatures(List<String> features) { this.features = features; }
}
```

### 使用配置
```java
@RestController
public class ConfigController {

    @Autowired
    private AppProperties appProperties;

    @Value("${server.port}")
    private int serverPort;

    @Value("${app.name:Default App}")  // 默认值
    private String appName;

    @GetMapping("/config")
    public Map<String, Object> getConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("appName", appProperties.getName());
        config.put("version", appProperties.getVersion());
        config.put("author", appProperties.getAuthor());
        config.put("features", appProperties.getFeatures());
        config.put("serverPort", serverPort);
        return config;
    }
}
```

## Web开发

### RESTful API设计
```java
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")  // 跨域支持
public class UserController {

    @Autowired
    private UserService userService;

    // 获取所有用户
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy) {

        Pageable pageable = PageRequest.of(page, size, Sort.by(sortBy));
        Page<User> users = userService.findAll(pageable);

        return ResponseEntity.ok()
                .header("X-Total-Count", String.valueOf(users.getTotalElements()))
                .body(users.getContent());
    }

    // 根据ID获取用户
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        return userService.findById(id)
                .map(user -> ResponseEntity.ok().body(user))
                .orElse(ResponseEntity.notFound().build());
    }

    // 创建用户
    @PostMapping
    public ResponseEntity<User> createUser(@Valid @RequestBody UserDTO userDTO) {
        User user = userService.create(userDTO);
        return ResponseEntity.status(HttpStatus.CREATED).body(user);
    }

    // 更新用户
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id,
                                          @Valid @RequestBody UserDTO userDTO) {
        return userService.update(id, userDTO)
                .map(user -> ResponseEntity.ok().body(user))
                .orElse(ResponseEntity.notFound().build());
    }

    // 删除用户
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        if (userService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // 搜索用户
    @GetMapping("/search")
    public ResponseEntity<List<User>> searchUsers(@RequestParam String keyword) {
        List<User> users = userService.searchByKeyword(keyword);
        return ResponseEntity.ok(users);
    }
}
```

### 数据传输对象(DTO)
```java
// UserDTO.java
public class UserDTO {
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, message = "密码长度不能少于6位")
    private String password;

    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Min(value = 18, message = "年龄不能小于18岁")
    @Max(value = 100, message = "年龄不能大于100岁")
    private Integer age;

    // 构造函数、getter和setter
    public UserDTO() {}

    public UserDTO(String username, String password, String email, String phone, Integer age) {
        this.username = username;
        this.password = password;
        this.email = email;
        this.phone = phone;
        this.age = age;
    }

    // getters and setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }
}
```

### 全局异常处理
```java
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    // 处理参数验证异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error ->
            errors.put(error.getField(), error.getDefaultMessage())
        );

        ErrorResponse errorResponse = new ErrorResponse(
            "VALIDATION_ERROR",
            "参数验证失败",
            errors
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    // 处理业务异常
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ErrorResponse> handleBusinessException(BusinessException ex) {
        logger.warn("业务异常: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            ex.getCode(),
            ex.getMessage(),
            null
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    // 处理系统异常
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleException(Exception ex) {
        logger.error("系统异常", ex);

        ErrorResponse errorResponse = new ErrorResponse(
            "SYSTEM_ERROR",
            "系统内部错误",
            null
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}

// 错误响应类
public class ErrorResponse {
    private String code;
    private String message;
    private Object details;
    private LocalDateTime timestamp;

    public ErrorResponse(String code, String message, Object details) {
        this.code = code;
        this.message = message;
        this.details = details;
        this.timestamp = LocalDateTime.now();
    }

    // getters and setters
}
```

## 数据访问

### JPA实体类
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false, length = 50)
    private String username;

    @Column(nullable = false)
    private String password;

    @Column(unique = true, nullable = false)
    private String email;

    @Column(length = 11)
    private String phone;

    @Column
    private Integer age;

    @Enumerated(EnumType.STRING)
    private UserStatus status = UserStatus.ACTIVE;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // 一对多关系
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Order> orders = new ArrayList<>();

    // 多对多关系
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();

    // 构造函数
    public User() {}

    public User(String username, String password, String email) {
        this.username = username;
        this.password = password;
        this.email = email;
    }

    // getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }

    public UserStatus getStatus() { return status; }
    public void setStatus(UserStatus status) { this.status = status; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public List<Order> getOrders() { return orders; }
    public void setOrders(List<Order> orders) { this.orders = orders; }

    public Set<Role> getRoles() { return roles; }
    public void setRoles(Set<Role> roles) { this.roles = roles; }
}

// 用户状态枚举
public enum UserStatus {
    ACTIVE, INACTIVE, SUSPENDED
}
```

### Repository接口
```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    // 根据用户名查找
    Optional<User> findByUsername(String username);

    // 根据邮箱查找
    Optional<User> findByEmail(String email);

    // 根据状态查找
    List<User> findByStatus(UserStatus status);

    // 根据年龄范围查找
    List<User> findByAgeBetween(Integer minAge, Integer maxAge);

    // 自定义查询
    @Query("SELECT u FROM User u WHERE u.username LIKE %:keyword% OR u.email LIKE %:keyword%")
    List<User> searchByKeyword(@Param("keyword") String keyword);

    // 原生SQL查询
    @Query(value = "SELECT * FROM users WHERE created_at >= :date", nativeQuery = true)
    List<User> findUsersCreatedAfter(@Param("date") LocalDateTime date);

    // 更新操作
    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.id = :id")
    int updateUserStatus(@Param("id") Long id, @Param("status") UserStatus status);

    // 删除操作
    @Modifying
    @Query("DELETE FROM User u WHERE u.status = :status")
    int deleteByStatus(@Param("status") UserStatus status);

    // 统计查询
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = :status")
    long countByStatus(@Param("status") UserStatus status);

    // 分页查询
    Page<User> findByStatusOrderByCreatedAtDesc(UserStatus status, Pageable pageable);
}
```

### Service层实现
```java
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // 查找所有用户（分页）
    @Transactional(readOnly = true)
    public Page<User> findAll(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    // 根据ID查找用户
    @Transactional(readOnly = true)
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    // 根据用户名查找用户
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    // 创建用户
    public User create(UserDTO userDTO) {
        // 检查用户名是否已存在
        if (userRepository.findByUsername(userDTO.getUsername()).isPresent()) {
            throw new BusinessException("USER_EXISTS", "用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.findByEmail(userDTO.getEmail()).isPresent()) {
            throw new BusinessException("EMAIL_EXISTS", "邮箱已存在");
        }

        User user = new User();
        user.setUsername(userDTO.getUsername());
        user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        user.setEmail(userDTO.getEmail());
        user.setPhone(userDTO.getPhone());
        user.setAge(userDTO.getAge());
        user.setStatus(UserStatus.ACTIVE);

        User savedUser = userRepository.save(user);
        logger.info("创建用户成功: {}", savedUser.getUsername());

        return savedUser;
    }

    // 更新用户
    public Optional<User> update(Long id, UserDTO userDTO) {
        return userRepository.findById(id).map(user -> {
            // 检查用户名是否被其他用户使用
            userRepository.findByUsername(userDTO.getUsername())
                .filter(existingUser -> !existingUser.getId().equals(id))
                .ifPresent(existingUser -> {
                    throw new BusinessException("USERNAME_EXISTS", "用户名已被使用");
                });

            user.setUsername(userDTO.getUsername());
            user.setEmail(userDTO.getEmail());
            user.setPhone(userDTO.getPhone());
            user.setAge(userDTO.getAge());

            User updatedUser = userRepository.save(user);
            logger.info("更新用户成功: {}", updatedUser.getUsername());

            return updatedUser;
        });
    }

    // 删除用户
    public boolean delete(Long id) {
        return userRepository.findById(id).map(user -> {
            userRepository.delete(user);
            logger.info("删除用户成功: {}", user.getUsername());
            return true;
        }).orElse(false);
    }

    // 搜索用户
    @Transactional(readOnly = true)
    public List<User> searchByKeyword(String keyword) {
        return userRepository.searchByKeyword(keyword);
    }

    // 更新用户状态
    public boolean updateStatus(Long id, UserStatus status) {
        int updated = userRepository.updateUserStatus(id, status);
        if (updated > 0) {
            logger.info("更新用户状态成功: id={}, status={}", id, status);
            return true;
        }
        return false;
    }

    // 统计用户数量
    @Transactional(readOnly = true)
    public long countByStatus(UserStatus status) {
        return userRepository.countByStatus(status);
    }
}

// 业务异常类
public class BusinessException extends RuntimeException {
    private String code;

    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
```

## 安全控制

### Spring Security配置
```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtRequestFilter jwtRequestFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers(HttpMethod.GET, "/api/users").hasRole("USER")
                .requestMatchers(HttpMethod.POST, "/api/users").hasRole("ADMIN")
                .requestMatchers(HttpMethod.PUT, "/api/users/**").hasRole("ADMIN")
                .requestMatchers(HttpMethod.DELETE, "/api/users/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        http.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

## 测试

### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private UserService userService;

    @Test
    @DisplayName("创建用户 - 成功")
    void createUser_Success() {
        // Given
        UserDTO userDTO = new UserDTO("testuser", "password123", "<EMAIL>", "***********", 25);
        User savedUser = new User("testuser", "encodedPassword", "<EMAIL>");
        savedUser.setId(1L);

        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.empty());
        when(passwordEncoder.encode("password123")).thenReturn("encodedPassword");
        when(userRepository.save(any(User.class))).thenReturn(savedUser);

        // When
        User result = userService.create(userDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getUsername()).isEqualTo("testuser");
        assertThat(result.getEmail()).isEqualTo("<EMAIL>");

        verify(userRepository).findByUsername("testuser");
        verify(userRepository).findByEmail("<EMAIL>");
        verify(passwordEncoder).encode("password123");
        verify(userRepository).save(any(User.class));
    }

    @Test
    @DisplayName("创建用户 - 用户名已存在")
    void createUser_UsernameExists() {
        // Given
        UserDTO userDTO = new UserDTO("existinguser", "password123", "<EMAIL>", "***********", 25);
        User existingUser = new User("existinguser", "password", "<EMAIL>");

        when(userRepository.findByUsername("existinguser")).thenReturn(Optional.of(existingUser));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            userService.create(userDTO);
        });

        assertThat(exception.getCode()).isEqualTo("USER_EXISTS");
        assertThat(exception.getMessage()).isEqualTo("用户名已存在");

        verify(userRepository).findByUsername("existinguser");
        verify(userRepository, never()).save(any(User.class));
    }
}
```

### 集成测试
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
@Transactional
class UserControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserRepository userRepository;

    @Test
    @DisplayName("获取所有用户 - 成功")
    void getAllUsers_Success() {
        // Given
        User user1 = new User("user1", "password1", "<EMAIL>");
        User user2 = new User("user2", "password2", "<EMAIL>");
        userRepository.saveAll(Arrays.asList(user1, user2));

        // When
        ResponseEntity<User[]> response = restTemplate.getForEntity("/api/users", User[].class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).hasSize(2);
    }

    @Test
    @DisplayName("创建用户 - 成功")
    void createUser_Success() {
        // Given
        UserDTO userDTO = new UserDTO("newuser", "password123", "<EMAIL>", "***********", 25);
        HttpEntity<UserDTO> request = new HttpEntity<>(userDTO);

        // When
        ResponseEntity<User> response = restTemplate.postForEntity("/api/users", request, User.class);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getUsername()).isEqualTo("newuser");

        // 验证数据库中确实创建了用户
        Optional<User> savedUser = userRepository.findByUsername("newuser");
        assertThat(savedUser).isPresent();
    }
}
```

### Web层测试
```java
@WebMvcTest(UserController.class)
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("获取用户详情 - 成功")
    void getUserById_Success() throws Exception {
        // Given
        User user = new User("testuser", "password", "<EMAIL>");
        user.setId(1L);

        when(userService.findById(1L)).thenReturn(Optional.of(user));

        // When & Then
        mockMvc.perform(get("/api/users/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.username").value("testuser"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));

        verify(userService).findById(1L);
    }

    @Test
    @DisplayName("创建用户 - 参数验证失败")
    void createUser_ValidationFailed() throws Exception {
        // Given
        UserDTO invalidUserDTO = new UserDTO("", "", "invalid-email", "", 0);

        // When & Then
        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidUserDTO)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value("VALIDATION_ERROR"));

        verify(userService, never()).create(any(UserDTO.class));
    }
}
```

## 部署

### 打包应用
```bash
# Maven打包
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# Gradle打包
./gradlew build
```

### Docker部署
```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine

VOLUME /tmp

COPY target/springboot-demo-0.0.1-SNAPSHOT.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java","-jar","/app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - DB_PASSWORD=password123
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password123
      MYSQL_DATABASE: demo_prod
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

### 生产环境配置
```yaml
# application-prod.yml
server:
  port: 8080

spring:
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:3306/demo_prod?useSSL=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

  redis:
    host: ${REDIS_HOST:localhost}
    password: ${REDIS_PASSWORD:}

logging:
  level:
    com.example.demo: INFO
  file:
    name: logs/application.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
```

## 最佳实践

### 1. 项目结构规范
- 按功能模块划分包结构
- 遵循分层架构原则
- 使用统一的命名规范

### 2. 配置管理
- 使用配置文件管理不同环境
- 敏感信息使用环境变量
- 使用@ConfigurationProperties管理复杂配置

### 3. 异常处理
- 使用全局异常处理器
- 定义业务异常类
- 返回统一的错误响应格式

### 4. 数据访问
- 合理使用事务注解
- 使用分页查询处理大量数据
- 注意N+1查询问题

### 5. 安全考虑
- 使用Spring Security保护API
- 实现JWT认证授权
- 对敏感数据进行加密

### 6. 性能优化
- 使用缓存减少数据库访问
- 合理配置连接池
- 使用异步处理提高响应速度

### 7. 测试策略
- 编写单元测试和集成测试
- 使用测试配置文件
- 保持高测试覆盖率

Spring Boot以其简化配置、快速开发的特点，已成为Java企业级开发的首选框架。掌握这些核心概念和最佳实践，将帮助您构建高质量的Spring Boot应用！
