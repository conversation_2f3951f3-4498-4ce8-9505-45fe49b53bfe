package exceptiontest;

public class ExceptionDemo {
    public static void main(String[] args) {
        try {
            int result = 10 / 0; // 会抛出ArithmeticException
            System.out.println("结果: " + result);
        } catch (ArithmeticException e) {
            System.out.println("捕获到算术异常: " + e.getMessage());
        } finally {
            System.out.println("finally块总是执行");
        }
    }
}
