# 企业级角色管理系统使用指南

## 系统概述

本系统提供了完整的企业级角色管理解决方案，支持复杂的角色层次结构、权限管理、批量操作、数据统计等功能。

## 核心特性

### 🎯 功能特性
- **高级分页查询** - 支持复杂条件组合查询
- **角色层次管理** - 支持多级角色树形结构
- **权限精细控制** - 角色权限分配和管理
- **批量操作** - 批量创建、更新、删除角色
- **数据统计分析** - 角色使用情况统计
- **操作审计** - 完整的操作日志记录
- **缓存优化** - 多级缓存提升性能
- **数据导出** - 支持Excel等格式导出

### 🔒 安全特性
- **权限控制** - 基于注解的方法级权限控制
- **操作审计** - 记录所有关键操作
- **数据校验** - 完整的输入参数校验
- **SQL注入防护** - 参数化查询防止注入

### ⚡ 性能特性
- **分页优化** - 基于PageHelper的高效分页
- **缓存策略** - Redis缓存热点数据
- **批量处理** - 支持批量操作减少数据库交互
- **索引优化** - 数据库索引优化查询性能

## API接口文档

### 基础URL
```
http://localhost:8080/api/enterprise/roles
```

### 1. 查询功能

#### 1.1 分页查询角色
```http
GET /api/enterprise/roles
```

**支持的查询参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 | 1 |
| pageSize | Integer | 否 | 每页大小，默认10 | 10 |
| roleName | String | 否 | 角色名称（模糊） | 管理员 |
| roleCode | String | 否 | 角色编码（模糊） | ADMIN |
| roleDesc | String | 否 | 角色描述（模糊） | 系统管理 |
| status | Integer | 否 | 状态：0-禁用，1-启用 | 1 |
| keyword | String | 否 | 关键字搜索 | 管理 |
| parentId | Long | 否 | 父角色ID | 1 |
| level | Integer | 否 | 角色层级 | 2 |
| includeChildren | Boolean | 否 | 是否包含子角色 | true |
| createUser | Long | 否 | 创建人ID | 1001 |
| createTimeStart | DateTime | 否 | 创建时间开始 | 2025-01-01 00:00:00 |
| createTimeEnd | DateTime | 否 | 创建时间结束 | 2025-12-31 23:59:59 |
| permissionCode | String | 否 | 包含指定权限 | user:read |
| inUse | Boolean | 否 | 是否正在使用 | true |
| minUserCount | Integer | 否 | 最小用户数 | 1 |
| maxUserCount | Integer | 否 | 最大用户数 | 100 |
| sortField | String | 否 | 排序字段 | createTime |
| sortDirection | String | 否 | 排序方向 | desc |
| useCache | Boolean | 否 | 是否使用缓存 | true |

**查询示例：**

```bash
# 基础分页查询
GET /api/enterprise/roles?pageNum=1&pageSize=10

# 条件查询
GET /api/enterprise/roles?status=1&roleName=管理员&pageNum=1&pageSize=10

# 关键字搜索
GET /api/enterprise/roles?keyword=管理&pageNum=1&pageSize=10

# 时间范围查询
GET /api/enterprise/roles?createTimeStart=2025-01-01&createTimeEnd=2025-12-31

# 权限相关查询
GET /api/enterprise/roles?permissionCode=user:read&pageNum=1&pageSize=10

# 使用情况查询
GET /api/enterprise/roles?inUse=true&minUserCount=1&pageNum=1&pageSize=10

# 层级查询
GET /api/enterprise/roles?parentId=1&includeChildren=true

# 复合查询
GET /api/enterprise/roles?keyword=管理&status=1&inUse=true&sortField=createTime&sortDirection=desc&pageNum=1&pageSize=10
```

**响应示例：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "roleName": "系统管理员",
        "roleCode": "ADMIN",
        "roleDesc": "系统管理员角色",
        "status": 1,
        "parentId": null,
        "level": 1,
        "sortOrder": 1,
        "createUser": 1001,
        "updateUser": 1001,
        "createTime": "2025-07-22 10:00:00",
        "updateTime": "2025-07-22 10:00:00"
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 5,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "isFirstPage": true,
    "isLastPage": false,
    "navigatepageNums": [1, 2, 3, 4, 5]
  },
  "timestamp": "2025-07-22 10:00:00"
}
```

#### 1.2 获取角色树形结构
```http
GET /api/enterprise/roles/tree?includeDisabled=false
```

#### 1.3 获取启用的角色列表
```http
GET /api/enterprise/roles/active
```

#### 1.4 查询子角色
```http
GET /api/enterprise/roles/children/{parentId}?includeDisabled=false
```

#### 1.5 根据用户查询角色
```http
GET /api/enterprise/roles/user/{userId}
```

### 2. 基础CRUD操作

#### 2.1 查询角色详情
```http
GET /api/enterprise/roles/{id}
```

#### 2.2 根据编码查询角色
```http
GET /api/enterprise/roles/code/{roleCode}
```

#### 2.3 创建角色
```http
POST /api/enterprise/roles
Content-Type: application/json
X-Operator-Id: 1001

{
  "roleName": "部门管理员",
  "roleCode": "DEPT_ADMIN",
  "roleDesc": "部门管理员角色",
  "parentId": 1,
  "level": 2,
  "sortOrder": 10,
  "status": 1
}
```

#### 2.4 更新角色
```http
PUT /api/enterprise/roles/{id}
Content-Type: application/json
X-Operator-Id: 1001

{
  "roleName": "高级管理员",
  "roleDesc": "高级管理员角色",
  "sortOrder": 5
}
```

#### 2.5 删除角色
```http
DELETE /api/enterprise/roles/{id}
X-Operator-Id: 1001
```

### 3. 批量操作

#### 3.1 批量删除角色
```http
DELETE /api/enterprise/roles/batch
Content-Type: application/json
X-Operator-Id: 1001

[1, 2, 3, 4, 5]
```

#### 3.2 批量更新状态
```http
PUT /api/enterprise/roles/batch/status?status=1
Content-Type: application/json
X-Operator-Id: 1001

[1, 2, 3, 4, 5]
```

### 4. 状态管理

#### 4.1 启用角色
```http
PUT /api/enterprise/roles/{id}/enable
X-Operator-Id: 1001
```

#### 4.2 禁用角色
```http
PUT /api/enterprise/roles/{id}/disable
X-Operator-Id: 1001
```

### 5. 权限管理

#### 5.1 分配权限
```http
POST /api/enterprise/roles/{roleId}/permissions
Content-Type: application/json
X-Operator-Id: 1001

[1001, 1002, 1003]
```

#### 5.2 移除权限
```http
DELETE /api/enterprise/roles/{roleId}/permissions
Content-Type: application/json
X-Operator-Id: 1001

[1001, 1002]
```

#### 5.3 查询角色权限
```http
GET /api/enterprise/roles/{roleId}/permissions
```

### 6. 用户角色关联

#### 6.1 为用户分配角色
```http
POST /api/enterprise/roles/assign/user/{userId}
Content-Type: application/json
X-Operator-Id: 1001

[1, 2, 3]
```

#### 6.2 移除用户角色
```http
DELETE /api/enterprise/roles/remove/user/{userId}
Content-Type: application/json
X-Operator-Id: 1001

[1, 2]
```

### 7. 数据统计

#### 7.1 角色统计信息
```http
GET /api/enterprise/roles/statistics
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "totalRoles": 50,
    "activeRoles": 45,
    "disabledRoles": 5,
    "rolesWithUsers": 30,
    "rolesWithoutUsers": 20,
    "avgUsersPerRole": 15.5,
    "maxLevel": 4,
    "topUsedRoles": [
      {"roleId": 1, "roleName": "普通用户", "userCount": 1000},
      {"roleId": 2, "roleName": "部门管理员", "userCount": 50}
    ]
  }
}
```

#### 7.2 角色使用情况
```http
GET /api/enterprise/roles/{roleId}/usage
```

### 8. 数据验证

#### 8.1 检查角色编码
```http
GET /api/enterprise/roles/check/code/{roleCode}?excludeId=1
```

#### 8.2 检查角色名称
```http
GET /api/enterprise/roles/check/name/{roleName}?excludeId=1
```

#### 8.3 检查是否可删除
```http
GET /api/enterprise/roles/{roleId}/can-delete
```

### 9. 缓存管理

#### 9.1 刷新缓存
```http
POST /api/enterprise/roles/cache/refresh?roleId=1
```

#### 9.2 清空缓存
```http
DELETE /api/enterprise/roles/cache/clear
```

### 10. 数据导出

#### 10.1 导出角色数据
```http
GET /api/enterprise/roles/export?status=1&exportFields=roleName,roleCode,status
```

## 使用场景示例

### 场景1：管理后台角色列表页

```javascript
// 前端查询代码示例
const queryRoles = async (params) => {
  const response = await fetch('/api/enterprise/roles?' + new URLSearchParams({
    pageNum: params.page || 1,
    pageSize: params.size || 10,
    keyword: params.keyword || '',
    status: params.status,
    sortField: 'createTime',
    sortDirection: 'desc'
  }));
  
  return response.json();
};

// 使用示例
const result = await queryRoles({
  page: 1,
  size: 10,
  keyword: '管理',
  status: 1
});
```

### 场景2：角色权限分配

```javascript
// 为角色分配权限
const assignPermissions = async (roleId, permissionIds, operatorId) => {
  const response = await fetch(`/api/enterprise/roles/${roleId}/permissions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Operator-Id': operatorId
    },
    body: JSON.stringify(permissionIds)
  });
  
  return response.json();
};
```

### 场景3：批量操作

```javascript
// 批量启用角色
const batchEnableRoles = async (roleIds, operatorId) => {
  const response = await fetch('/api/enterprise/roles/batch/status?status=1', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Operator-Id': operatorId
    },
    body: JSON.stringify(roleIds)
  });
  
  return response.json();
};
```

## 性能优化建议

### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_role_name ON role(role_name);
CREATE INDEX idx_role_code ON role(role_code);
CREATE INDEX idx_role_status ON role(status);
CREATE INDEX idx_role_parent_id ON role(parent_id);
CREATE INDEX idx_role_create_time ON role(create_time);
CREATE INDEX idx_role_level ON role(level);

-- 复合索引
CREATE INDEX idx_role_status_level ON role(status, level);
CREATE INDEX idx_role_parent_status ON role(parent_id, status);
```

### 2. 缓存策略
- 热点角色数据缓存30分钟
- 角色树结构缓存1小时
- 角色权限关系缓存15分钟
- 统计数据缓存5分钟

### 3. 查询优化
- 使用分页查询避免大数据量
- 复杂查询使用索引覆盖
- 批量操作减少数据库交互
- 合理使用缓存减少查询

## 监控和运维

### 1. 关键指标监控
- API响应时间
- 查询QPS
- 缓存命中率
- 错误率统计

### 2. 日志记录
- 操作审计日志
- 性能监控日志
- 错误异常日志
- 安全访问日志

### 3. 健康检查
```http
GET /api/enterprise/roles/health
```

这个企业级角色管理系统提供了完整的角色管理解决方案，支持复杂的业务场景和高性能要求。
