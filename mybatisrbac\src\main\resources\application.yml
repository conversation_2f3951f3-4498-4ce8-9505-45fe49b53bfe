server:
  port: 8888

spring:
  application:
    name: mybatisrbac
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: 123456

    # Druid 连接池配置
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20

      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j

      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"

      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow: 127.0.0.1,192.168.1.0/24
        # 控制台管理用户名和密码
        login-username: admin
        login-password: 123456
        url-pattern: "/druid/*"
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false

# MyBatis配置
mybatis:
  # 指定mapper文件位置
  mapper-locations: classpath:mapper/*.xml
  # 指定实体类包路径
  type-aliases-package: com.example.mybatisrbac.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 打印SQL语句
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    try-it-out-enabled: true
  packages-to-scan: com.example.mybatisrbac.controller

# 日志配置
logging:
  level:
    # 设置root日志级别
    root: INFO
    # 设置SQL日志级别
    com.example.mybatisrbac.mapper: DEBUG
    # 设置Druid日志级别
    com.alibaba.druid: DEBUG
    # 设置Spring框架日志级别
    org.springframework: INFO
    # 设置MyBatis日志级别
    org.apache.ibatis: DEBUG
  pattern:
    # 控制台日志格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    # 文件日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    # 日志文件路径
    name: logs/mybatisrbac.log
