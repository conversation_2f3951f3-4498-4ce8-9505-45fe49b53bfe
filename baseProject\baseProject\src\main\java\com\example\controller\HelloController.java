package com.example.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Hello控制器 - 演示Spring Boot基本功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api")
@Validated
@CrossOrigin(origins = "*") // 允许跨域访问
public class HelloController {
    
    @Value("${spring.application.name:Spring Boot Demo}")
    private String applicationName;
    
    @Value("${server.port:8080}")
    private String serverPort;
    
    /**
     * 基本的Hello接口
     * 
     * @return 问候信息
     */
    @GetMapping("/hello")
    public ResponseEntity<Map<String, Object>> hello() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Hello, Spring Boot!");
        response.put("application", applicationName);
        response.put("port", serverPort);
        response.put("timestamp", LocalDateTime.now());
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 带参数的问候接口
     * 
     * @param name 姓名
     * @return 个性化问候信息
     */
    @GetMapping("/hello/{name}")
    public ResponseEntity<Map<String, Object>> helloWithName(
            @PathVariable @NotBlank(message = "姓名不能为空") String name) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Hello, " + name + "!");
        response.put("name", name);
        response.put("timestamp", LocalDateTime.now());
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * POST请求示例
     * 
     * @param request 请求体
     * @return 响应信息
     */
    @PostMapping("/hello")
    public ResponseEntity<Map<String, Object>> helloPost(
            @RequestBody Map<String, Object> request) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Hello from POST request!");
        response.put("receivedData", request);
        response.put("timestamp", LocalDateTime.now());
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取请求信息
     * 
     * @param request HTTP请求对象
     * @return 请求信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getRequestInfo(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        // 请求基本信息
        Map<String, Object> requestInfo = new HashMap<>();
        requestInfo.put("method", request.getMethod());
        requestInfo.put("url", request.getRequestURL().toString());
        requestInfo.put("uri", request.getRequestURI());
        requestInfo.put("remoteAddr", request.getRemoteAddr());
        requestInfo.put("userAgent", request.getHeader("User-Agent"));
        
        // 请求头信息
        Map<String, String> headers = new HashMap<>();
        request.getHeaderNames().asIterator().forEachRemaining(headerName -> 
            headers.put(headerName, request.getHeader(headerName)));
        
        // 请求参数
        Map<String, String[]> parameters = request.getParameterMap();
        
        response.put("request", requestInfo);
        response.put("headers", headers);
        response.put("parameters", parameters);
        response.put("timestamp", LocalDateTime.now());
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("application", applicationName);
        response.put("timestamp", LocalDateTime.now());
        
        // 系统信息
        Map<String, Object> system = new HashMap<>();
        system.put("javaVersion", System.getProperty("java.version"));
        system.put("osName", System.getProperty("os.name"));
        system.put("osVersion", System.getProperty("os.version"));
        system.put("maxMemory", Runtime.getRuntime().maxMemory() / 1024 / 1024 + " MB");
        system.put("totalMemory", Runtime.getRuntime().totalMemory() / 1024 / 1024 + " MB");
        system.put("freeMemory", Runtime.getRuntime().freeMemory() / 1024 / 1024 + " MB");
        
        response.put("system", system);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 错误处理示例
     * 
     * @param type 错误类型
     * @return 错误响应
     */
    @GetMapping("/error/{type}")
    public ResponseEntity<Map<String, Object>> simulateError(@PathVariable String type) {
        Map<String, Object> response = new HashMap<>();
        
        switch (type.toLowerCase()) {
            case "400":
                response.put("error", "Bad Request");
                response.put("message", "请求参数错误");
                return ResponseEntity.badRequest().body(response);
                
            case "404":
                response.put("error", "Not Found");
                response.put("message", "资源未找到");
                return ResponseEntity.notFound().build();
                
            case "500":
                throw new RuntimeException("模拟服务器内部错误");
                
            default:
                response.put("message", "未知错误类型: " + type);
                response.put("availableTypes", new String[]{"400", "404", "500"});
                return ResponseEntity.ok(response);
        }
    }
    
    /**
     * 文件上传示例
     * 
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") org.springframework.web.multipart.MultipartFile file) {
        
        Map<String, Object> response = new HashMap<>();
        
        if (file.isEmpty()) {
            response.put("error", "文件不能为空");
            return ResponseEntity.badRequest().body(response);
        }
        
        try {
            // 文件信息
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("originalName", file.getOriginalFilename());
            fileInfo.put("size", file.getSize());
            fileInfo.put("contentType", file.getContentType());
            
            // 这里可以添加文件保存逻辑
            // Files.copy(file.getInputStream(), Paths.get("uploads/" + file.getOriginalFilename()));
            
            response.put("message", "文件上传成功");
            response.put("file", fileInfo);
            response.put("timestamp", LocalDateTime.now());
            response.put("status", "success");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("error", "文件上传失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
