package com.example.mybatisdemo.mapper;

import com.example.mybatisdemo.dto.RoleQueryDTO;
import com.example.mybatisdemo.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口 - 标准PageHelper实现
 */
@Mapper
public interface RoleMapper {

    // ==================== 查询方法 ====================

    /**
     * 查询所有启用 的角色（不分页，用于下拉选择）
     */
    List<Role> selectAllActive();

    /**
     * 根据条件查询角色列表（PageHelper会自动拦截此方法进行分页）
     * 重要：方法名要清晰表明这是一个列表查询，PageHelper会自动处理
     */
    List<Role> selectRoleList(RoleQueryDTO query);

    /**
     * 根据ID查询角色
     */
    Role selectById(@Param("id") Long id);

    /**
     * 根据角色编码查询角色
     */
    Role selectByCode(@Param("roleCode") String roleCode);

    /**
     * 根据角色名称查询角色
     */
    Role selectByName(@Param("roleName") String roleName);

    // ==================== CRUD操作 ====================

    /**
     * 插入角色
     */
    int insert(Role role);

    /**
     * 更新角色
     */
    int updateById(Role role);

    /**
     * 删除角色
     */
    int deleteById(@Param("id") Long id);

    /**
     * 批量删除角色
     */
    int deleteBatchByIds(@Param("ids") List<Long> ids);

    // ==================== 状态管理 ====================

    /**
     * 更新角色状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("updateUser") Long updateUser);

    /**
     * 批量更新角色状态
     */
    int updateStatusBatch(@Param("ids") List<Long> ids, @Param("status") Integer status, @Param("updateUser") Long updateUser);

    // ==================== 数据验证 ====================

    /**
     * 检查角色编码是否存在
     */
    int countByCode(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);

    /**
     * 检查角色名称是否存在
     */
    int countByName(@Param("roleName") String roleName, @Param("excludeId") Long excludeId);
}
