# Spring Boot核心原理详解

## 目录
1. [Spring Boot核心原理概述](#spring-boot核心原理概述)
2. [依赖注入(DI)原理](#依赖注入di原理)
3. [自动配置(Auto Configuration)](#自动配置auto-configuration)
4. [起步依赖(Starter Dependencies)](#起步依赖starter-dependencies)
5. [内嵌服务器原理](#内嵌服务器原理)
6. [配置管理机制](#配置管理机制)
7. [AOP切面编程](#aop切面编程)
8. [事务管理](#事务管理)
9. [缓存机制](#缓存机制)
10. [监控和管理](#监控和管理)

## Spring Boot核心原理概述

### Spring Boot的设计理念

Spring Boot基于"约定优于配置"的理念，通过以下核心机制简化Spring应用开发：

1. **自动配置** - 根据类路径中的依赖自动配置Spring应用
2. **起步依赖** - 提供一站式的依赖管理
3. **内嵌服务器** - 无需外部容器，应用可独立运行
4. **生产就绪** - 提供监控、健康检查等生产环境功能

### Spring Boot启动流程

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

**启动流程详解：**

1. **创建SpringApplication实例**
   - 推断应用类型（Web、Reactive、None）
   - 加载ApplicationContextInitializer
   - 加载ApplicationListener

2. **运行SpringApplication**
   - 创建并配置Environment
   - 创建ApplicationContext
   - 准备ApplicationContext
   - 刷新ApplicationContext
   - 调用ApplicationRunner和CommandLineRunner

3. **自动配置生效**
   - 扫描@EnableAutoConfiguration
   - 加载spring.factories中的配置类
   - 根据条件注解决定是否生效

## 依赖注入(DI)原理

### 什么是依赖注入

依赖注入是一种设计模式，用于实现控制反转(IoC)。Spring容器负责创建对象并注入其依赖，而不是对象自己创建依赖。

### IoC容器原理

```java
/**
 * IoC容器的核心概念演示
 */
public class IoCContainerDemo {
    
    // 传统方式：对象自己创建依赖（紧耦合）
    public class TraditionalUserService {
        private UserRepository userRepository = new UserRepositoryImpl(); // 硬编码依赖
        
        public User findUser(Long id) {
            return userRepository.findById(id);
        }
    }
    
    // IoC方式：依赖由外部注入（松耦合）
    @Service
    public class IoCUserService {
        private final UserRepository userRepository;
        
        // 构造函数注入（推荐）
        public IoCUserService(UserRepository userRepository) {
            this.userRepository = userRepository;
        }
        
        public User findUser(Long id) {
            return userRepository.findById(id);
        }
    }
}
```

### 依赖注入的三种方式

#### 1. 构造函数注入（推荐）

```java
@Service
public class UserService {
    private final UserRepository userRepository;
    private final EmailService emailService;
    
    // Spring 4.3+支持单构造函数自动注入，无需@Autowired
    public UserService(UserRepository userRepository, EmailService emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }
    
    public User createUser(UserDTO userDTO) {
        User user = new User(userDTO.getUsername(), userDTO.getEmail());
        User savedUser = userRepository.save(user);
        emailService.sendWelcomeEmail(savedUser.getEmail());
        return savedUser;
    }
}
```

**优点：**
- 保证依赖不可变（final字段）
- 保证依赖不为null
- 便于单元测试
- 避免循环依赖

#### 2. Setter注入

```java
@Service
public class UserService {
    private UserRepository userRepository;
    private EmailService emailService;
    
    @Autowired
    public void setUserRepository(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Autowired
    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }
}
```

#### 3. 字段注入（不推荐）

```java
@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private EmailService emailService;
}
```

### Bean的生命周期

```java
@Component
public class LifecycleBean implements InitializingBean, DisposableBean {
    
    private String name;
    
    public LifecycleBean() {
        System.out.println("1. 构造函数调用");
    }
    
    @Autowired
    public void setName(@Value("${bean.name:defaultName}") String name) {
        this.name = name;
        System.out.println("2. 属性注入: " + name);
    }
    
    @PostConstruct
    public void init() {
        System.out.println("3. @PostConstruct初始化");
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        System.out.println("4. InitializingBean.afterPropertiesSet()");
    }
    
    @Bean(initMethod = "customInit")
    public void customInit() {
        System.out.println("5. 自定义初始化方法");
    }
    
    @PreDestroy
    public void preDestroy() {
        System.out.println("6. @PreDestroy销毁前");
    }
    
    @Override
    public void destroy() throws Exception {
        System.out.println("7. DisposableBean.destroy()");
    }
}
```

### Bean作用域

```java
@Configuration
public class BeanScopeConfig {
    
    @Bean
    @Scope("singleton") // 默认，单例
    public SingletonBean singletonBean() {
        return new SingletonBean();
    }
    
    @Bean
    @Scope("prototype") // 原型，每次获取创建新实例
    public PrototypeBean prototypeBean() {
        return new PrototypeBean();
    }
    
    @Bean
    @Scope("request") // Web环境，每个HTTP请求一个实例
    public RequestBean requestBean() {
        return new RequestBean();
    }
    
    @Bean
    @Scope("session") // Web环境，每个HTTP会话一个实例
    public SessionBean sessionBean() {
        return new SessionBean();
    }
}
```

### 条件化配置

```java
@Configuration
public class ConditionalConfig {
    
    @Bean
    @ConditionalOnProperty(name = "feature.email.enabled", havingValue = "true")
    public EmailService emailService() {
        return new EmailServiceImpl();
    }
    
    @Bean
    @ConditionalOnMissingBean(EmailService.class)
    public EmailService mockEmailService() {
        return new MockEmailService();
    }
    
    @Bean
    @ConditionalOnClass(RedisTemplate.class)
    public CacheService redisCacheService() {
        return new RedisCacheService();
    }
    
    @Bean
    @ConditionalOnMissingClass("org.springframework.data.redis.core.RedisTemplate")
    public CacheService memoryCacheService() {
        return new MemoryCacheService();
    }
}
```

## 自动配置(Auto Configuration)

### 自动配置原理

Spring Boot通过`@EnableAutoConfiguration`注解启用自动配置：

```java
@SpringBootApplication
// 等价于：
// @Configuration
// @EnableAutoConfiguration  <- 关键注解
// @ComponentScan
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 自动配置工作机制

1. **加载自动配置类**
   - 扫描classpath下的`META-INF/spring.factories`
   - 加载`org.springframework.boot.autoconfigure.EnableAutoConfiguration`对应的配置类

2. **条件化配置**
   - 使用`@Conditional`注解判断是否应该应用配置
   - 常见条件注解：`@ConditionalOnClass`、`@ConditionalOnBean`等

3. **配置优先级**
   - 用户自定义配置 > 自动配置
   - 通过`@ConditionalOnMissingBean`实现

### 自定义自动配置

```java
// 1. 创建自动配置类
@Configuration
@ConditionalOnClass(MyService.class)
@ConditionalOnProperty(prefix = "myservice", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(MyServiceProperties.class)
public class MyServiceAutoConfiguration {
    
    @Autowired
    private MyServiceProperties properties;
    
    @Bean
    @ConditionalOnMissingBean
    public MyService myService() {
        return new MyServiceImpl(properties);
    }
}

// 2. 创建配置属性类
@ConfigurationProperties(prefix = "myservice")
public class MyServiceProperties {
    private boolean enabled = true;
    private String apiUrl = "http://localhost:8080";
    private int timeout = 5000;
    
    // getters and setters
}

// 3. 在META-INF/spring.factories中注册
// org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
// com.example.autoconfigure.MyServiceAutoConfiguration
```

### 常见自动配置示例

```java
// Web自动配置
@Configuration
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@ConditionalOnClass({ Servlet.class, DispatcherServlet.class, WebMvcConfigurer.class })
@ConditionalOnMissingBean(WebMvcConfigurationSupport.class)
public class WebMvcAutoConfiguration {
    // 自动配置DispatcherServlet、ViewResolver等
}

// 数据源自动配置
@Configuration
@ConditionalOnClass({ DataSource.class, EmbeddedDatabaseType.class })
@EnableConfigurationProperties(DataSourceProperties.class)
public class DataSourceAutoConfiguration {
    // 自动配置DataSource
}

// Redis自动配置
@Configuration
@ConditionalOnClass(RedisOperations.class)
@EnableConfigurationProperties(RedisProperties.class)
public class RedisAutoConfiguration {
    // 自动配置RedisTemplate、StringRedisTemplate
}
```

## 起步依赖(Starter Dependencies)

### Starter的作用

Starter是Spring Boot的核心特性之一，它将相关的依赖打包在一起，简化依赖管理：

```xml
<!-- 传统方式：需要手动管理多个依赖 -->
<dependencies>
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>5.3.21</version>
    </dependency>
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>5.3.21</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.13.3</version>
    </dependency>
    <!-- 更多依赖... -->
</dependencies>

<!-- Spring Boot方式：一个starter搞定 -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
</dependencies>
```

### 常用Starter

```xml
<!-- Web开发 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- 数据访问 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency>

<!-- 安全控制 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-security</artifactId>
</dependency>

<!-- 缓存支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-cache</artifactId>
</dependency>

<!-- 测试支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

### 自定义Starter

```java
// 1. 创建starter项目结构
// my-spring-boot-starter/
// ├── pom.xml
// └── src/main/
//     ├── java/
//     │   └── com/example/starter/
//     │       ├── MyStarterAutoConfiguration.java
//     │       ├── MyStarterProperties.java
//     │       └── MyStarterService.java
//     └── resources/
//         └── META-INF/
//             └── spring.factories

// 2. pom.xml
/*
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>
*/

// 3. 自动配置类
@Configuration
@EnableConfigurationProperties(MyStarterProperties.class)
@ConditionalOnProperty(prefix = "mystarter", name = "enabled", havingValue = "true", matchIfMissing = true)
public class MyStarterAutoConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public MyStarterService myStarterService(MyStarterProperties properties) {
        return new MyStarterService(properties);
    }
}

// 4. 配置属性
@ConfigurationProperties(prefix = "mystarter")
public class MyStarterProperties {
    private boolean enabled = true;
    private String message = "Hello from MyStarter";
    
    // getters and setters
}

// 5. 核心服务
public class MyStarterService {
    private final MyStarterProperties properties;
    
    public MyStarterService(MyStarterProperties properties) {
        this.properties = properties;
    }
    
    public String getMessage() {
        return properties.getMessage();
    }
}
```

## 内嵌服务器原理

### 内嵌服务器的优势

Spring Boot内嵌了Tomcat、Jetty、Undertow等服务器，使应用可以独立运行：

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
        // 应用启动后，内嵌的Tomcat服务器也同时启动
    }
}
```

### 服务器自动配置

```java
// Tomcat自动配置
@Configuration
@ConditionalOnClass({ Servlet.class, Tomcat.class, UpgradeProtocol.class })
@ConditionalOnMissingBean(value = ServletWebServerFactory.class, search = SearchStrategy.CURRENT)
public static class EmbeddedTomcat {

    @Bean
    public TomcatServletWebServerFactory tomcatServletWebServerFactory() {
        return new TomcatServletWebServerFactory();
    }
}

// 自定义服务器配置
@Configuration
public class ServerConfig {

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            // 自定义Tomcat配置
            factory.setPort(8080);
            factory.setContextPath("/api");
            factory.addConnectorCustomizers(connector -> {
                connector.setMaxPostSize(10 * 1024 * 1024); // 10MB
            });
        };
    }
}
```

### 切换内嵌服务器

```xml
<!-- 排除默认的Tomcat，使用Jetty -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <exclusions>
        <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
    </exclusions>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-jetty</artifactId>
</dependency>
```

## 配置管理机制

### 配置加载顺序

Spring Boot按以下优先级加载配置（数字越小优先级越高）：

1. 命令行参数
2. JNDI属性
3. Java系统属性
4. 操作系统环境变量
5. application-{profile}.properties/yml
6. application.properties/yml
7. @PropertySource注解
8. 默认属性

### 外部化配置

```java
// 1. 配置属性类
@ConfigurationProperties(prefix = "app")
@Component
@Validated
public class AppProperties {

    @NotBlank
    private String name;

    @Min(1)
    @Max(65535)
    private int port = 8080;

    @Valid
    private Database database = new Database();

    @Valid
    private List<Feature> features = new ArrayList<>();

    // 嵌套配置类
    public static class Database {
        @NotBlank
        private String url;

        @NotBlank
        private String username;

        private String password;

        // getters and setters
    }

    public static class Feature {
        @NotBlank
        private String name;

        private boolean enabled = true;

        // getters and setters
    }

    // getters and setters
}

// 2. 使用配置
@RestController
public class ConfigController {

    private final AppProperties appProperties;

    public ConfigController(AppProperties appProperties) {
        this.appProperties = appProperties;
    }

    @GetMapping("/config")
    public AppProperties getConfig() {
        return appProperties;
    }
}
```

### Profile环境配置

```yaml
# application.yml
spring:
  profiles:
    active: @spring.profiles.active@  # Maven占位符

---
# 开发环境
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: jdbc:h2:mem:devdb
    username: sa
    password:
  h2:
    console:
      enabled: true

---
# 测试环境
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:

---
# 生产环境
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
```

### 配置加密

```java
// 使用Jasypt加密敏感配置
@Configuration
@EnableEncryptableProperties
public class EncryptionConfig {

    @Bean("jasyptStringEncryptor")
    public StringEncryptor stringEncryptor() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        config.setPassword("mySecretKey");
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor;
    }
}

// 配置文件中使用加密值
// spring:
//   datasource:
//     password: ENC(encrypted_password_here)
```

## AOP切面编程

### AOP基本概念

```java
// 切面类
@Aspect
@Component
public class LoggingAspect {

    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);

    // 切点表达式
    @Pointcut("execution(* com.example.service.*.*(..))")
    public void serviceLayer() {}

    @Pointcut("@annotation(com.example.annotation.Loggable)")
    public void loggableMethod() {}

    // 前置通知
    @Before("serviceLayer()")
    public void logBefore(JoinPoint joinPoint) {
        logger.info("调用方法: {}", joinPoint.getSignature().getName());
    }

    // 后置通知
    @After("serviceLayer()")
    public void logAfter(JoinPoint joinPoint) {
        logger.info("方法执行完成: {}", joinPoint.getSignature().getName());
    }

    // 环绕通知
    @Around("loggableMethod()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();

            logger.info("方法 {} 执行时间: {}ms",
                       joinPoint.getSignature().getName(),
                       endTime - startTime);

            return result;
        } catch (Exception e) {
            logger.error("方法 {} 执行异常", joinPoint.getSignature().getName(), e);
            throw e;
        }
    }

    // 异常通知
    @AfterThrowing(pointcut = "serviceLayer()", throwing = "ex")
    public void logException(JoinPoint joinPoint, Exception ex) {
        logger.error("方法 {} 抛出异常: {}",
                    joinPoint.getSignature().getName(),
                    ex.getMessage());
    }
}

// 自定义注解
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Loggable {
    String value() default "";
}

// 使用示例
@Service
public class UserService {

    @Loggable("用户查询")
    public User findById(Long id) {
        // 业务逻辑
        return userRepository.findById(id).orElse(null);
    }
}
```

### 实际应用场景

```java
// 1. 操作日志切面
@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private OperationLogService operationLogService;

    @Around("@annotation(operationLog)")
    public Object logOperation(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        // 获取当前用户
        String username = getCurrentUsername();

        // 记录操作开始
        OperationLogEntity logEntity = new OperationLogEntity();
        logEntity.setUsername(username);
        logEntity.setOperation(operationLog.value());
        logEntity.setStartTime(LocalDateTime.now());

        try {
            Object result = joinPoint.proceed();

            // 记录操作成功
            logEntity.setStatus("SUCCESS");
            logEntity.setEndTime(LocalDateTime.now());

            return result;
        } catch (Exception e) {
            // 记录操作失败
            logEntity.setStatus("FAILED");
            logEntity.setErrorMessage(e.getMessage());
            logEntity.setEndTime(LocalDateTime.now());

            throw e;
        } finally {
            operationLogService.save(logEntity);
        }
    }
}

// 2. 缓存切面
@Aspect
@Component
public class CacheAspect {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Around("@annotation(cacheable)")
    public Object cache(ProceedingJoinPoint joinPoint, Cacheable cacheable) throws Throwable {
        String key = generateKey(joinPoint, cacheable.key());

        // 尝试从缓存获取
        Object cached = redisTemplate.opsForValue().get(key);
        if (cached != null) {
            return cached;
        }

        // 执行方法
        Object result = joinPoint.proceed();

        // 存入缓存
        if (result != null) {
            redisTemplate.opsForValue().set(key, result,
                                          Duration.ofSeconds(cacheable.expireTime()));
        }

        return result;
    }
}
```

## 事务管理

### 声明式事务原理

Spring Boot通过AOP实现声明式事务管理：

```java
@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmailService emailService;

    // 默认事务配置
    public User createUser(UserDTO userDTO) {
        User user = new User(userDTO.getUsername(), userDTO.getEmail());
        User savedUser = userRepository.save(user);

        // 如果发送邮件失败，整个事务回滚
        emailService.sendWelcomeEmail(savedUser.getEmail());

        return savedUser;
    }

    // 只读事务
    @Transactional(readOnly = true)
    public List<User> findAllUsers() {
        return userRepository.findAll();
    }

    // 自定义事务配置
    @Transactional(
        propagation = Propagation.REQUIRED,
        isolation = Isolation.READ_COMMITTED,
        timeout = 30,
        rollbackFor = {Exception.class},
        noRollbackFor = {BusinessException.class}
    )
    public User updateUser(Long id, UserDTO userDTO) {
        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("用户不存在"));

        user.setUsername(userDTO.getUsername());
        user.setEmail(userDTO.getEmail());

        return userRepository.save(user);
    }

    // 新事务
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void logUserOperation(Long userId, String operation) {
        OperationLog log = new OperationLog(userId, operation, LocalDateTime.now());
        operationLogRepository.save(log);
        // 即使外层事务回滚，这个日志记录也会提交
    }
}
```

### 事务传播行为

```java
@Service
public class TransactionPropagationDemo {

    @Autowired
    private UserService userService;

    @Autowired
    private OrderService orderService;

    // REQUIRED（默认）：如果当前存在事务，则加入该事务；如果不存在，则创建新事务
    @Transactional(propagation = Propagation.REQUIRED)
    public void requiredExample() {
        userService.createUser(new UserDTO()); // 加入当前事务
        orderService.createOrder(new OrderDTO()); // 加入当前事务
    }

    // REQUIRES_NEW：总是创建新事务，如果当前存在事务，则挂起当前事务
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void requiresNewExample() {
        // 这个方法总是在新事务中执行
    }

    // SUPPORTS：如果当前存在事务，则加入该事务；如果不存在，则以非事务方式执行
    @Transactional(propagation = Propagation.SUPPORTS)
    public void supportsExample() {
        // 如果调用者有事务，则加入；否则非事务执行
    }

    // NESTED：如果当前存在事务，则在嵌套事务内执行
    @Transactional(propagation = Propagation.NESTED)
    public void nestedExample() {
        try {
            // 嵌套事务，可以独立回滚
            riskyOperation();
        } catch (Exception e) {
            // 嵌套事务回滚，但不影响外层事务
            log.warn("嵌套事务失败", e);
        }
    }
}
```

### 编程式事务

```java
@Service
public class ProgrammaticTransactionService {

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private PlatformTransactionManager transactionManager;

    // 使用TransactionTemplate
    public User createUserWithTemplate(UserDTO userDTO) {
        return transactionTemplate.execute(status -> {
            try {
                User user = new User(userDTO.getUsername(), userDTO.getEmail());
                User savedUser = userRepository.save(user);

                emailService.sendWelcomeEmail(savedUser.getEmail());

                return savedUser;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("创建用户失败", e);
            }
        });
    }

    // 使用PlatformTransactionManager
    public User createUserWithManager(UserDTO userDTO) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        def.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        def.setTimeout(30);

        TransactionStatus status = transactionManager.getTransaction(def);

        try {
            User user = new User(userDTO.getUsername(), userDTO.getEmail());
            User savedUser = userRepository.save(user);

            emailService.sendWelcomeEmail(savedUser.getEmail());

            transactionManager.commit(status);
            return savedUser;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("创建用户失败", e);
        }
    }
}
```

## 缓存机制

### Spring Cache抽象

```java
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());

        return builder.build();
    }

    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(10))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}

@Service
public class UserCacheService {

    // 缓存结果
    @Cacheable(value = "users", key = "#id")
    public User findById(Long id) {
        System.out.println("从数据库查询用户: " + id);
        return userRepository.findById(id).orElse(null);
    }

    // 缓存结果，支持条件
    @Cacheable(value = "users", key = "#username", condition = "#username.length() > 3")
    public User findByUsername(String username) {
        return userRepository.findByUsername(username).orElse(null);
    }

    // 更新缓存
    @CachePut(value = "users", key = "#user.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    // 删除缓存
    @CacheEvict(value = "users", key = "#id")
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }

    // 清空所有缓存
    @CacheEvict(value = "users", allEntries = true)
    public void clearAllUsers() {
        // 清空users缓存的所有条目
    }

    // 多缓存操作
    @Caching(
        cacheable = @Cacheable(value = "users", key = "#id"),
        put = @CachePut(value = "userStats", key = "#id")
    )
    public User findUserWithStats(Long id) {
        User user = userRepository.findById(id).orElse(null);
        if (user != null) {
            // 计算用户统计信息
            user.setLoginCount(calculateLoginCount(id));
        }
        return user;
    }
}
```

### 自定义缓存实现

```java
@Component
public class CustomCacheService {

    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    private final Map<String, Long> expireTime = new ConcurrentHashMap<>();

    public void put(String key, Object value, long ttlSeconds) {
        cache.put(key, value);
        expireTime.put(key, System.currentTimeMillis() + ttlSeconds * 1000);
    }

    public Object get(String key) {
        Long expire = expireTime.get(key);
        if (expire != null && System.currentTimeMillis() > expire) {
            // 缓存过期，删除
            cache.remove(key);
            expireTime.remove(key);
            return null;
        }
        return cache.get(key);
    }

    public void evict(String key) {
        cache.remove(key);
        expireTime.remove(key);
    }

    public void clear() {
        cache.clear();
        expireTime.clear();
    }

    // 定时清理过期缓存
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void cleanExpiredCache() {
        long now = System.currentTimeMillis();
        expireTime.entrySet().removeIf(entry -> {
            if (now > entry.getValue()) {
                cache.remove(entry.getKey());
                return true;
            }
            return false;
        });
    }
}
```

## 监控和管理

### Spring Boot Actuator

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

```yaml
# 配置Actuator
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 暴露所有端点
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    shutdown:
      enabled: true
  info:
    env:
      enabled: true
    git:
      mode: full

# 应用信息
info:
  app:
    name: Spring Boot Demo
    version: 1.0.0
    description: Spring Boot核心原理演示应用
```

### 自定义健康检查

```java
@Component
public class CustomHealthIndicator implements HealthIndicator {

    @Autowired
    private ExternalService externalService;

    @Override
    public Health health() {
        try {
            // 检查外部服务状态
            boolean isHealthy = externalService.isHealthy();

            if (isHealthy) {
                return Health.up()
                    .withDetail("external-service", "Available")
                    .withDetail("response-time", "50ms")
                    .build();
            } else {
                return Health.down()
                    .withDetail("external-service", "Unavailable")
                    .withDetail("error", "Service not responding")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("external-service", "Error")
                .withException(e)
                .build();
        }
    }
}

// 自定义指标
@Component
public class CustomMetrics {

    private final Counter userCreationCounter;
    private final Timer userQueryTimer;
    private final Gauge activeUsersGauge;

    public CustomMetrics(MeterRegistry meterRegistry) {
        this.userCreationCounter = Counter.builder("user.creation")
            .description("用户创建次数")
            .register(meterRegistry);

        this.userQueryTimer = Timer.builder("user.query")
            .description("用户查询耗时")
            .register(meterRegistry);

        this.activeUsersGauge = Gauge.builder("user.active")
            .description("活跃用户数")
            .register(meterRegistry, this, CustomMetrics::getActiveUserCount);
    }

    public void incrementUserCreation() {
        userCreationCounter.increment();
    }

    public Timer.Sample startUserQueryTimer() {
        return Timer.start();
    }

    public void recordUserQueryTime(Timer.Sample sample) {
        sample.stop(userQueryTimer);
    }

    private double getActiveUserCount() {
        // 计算活跃用户数的逻辑
        return userService.getActiveUserCount();
    }
}
```

### 应用事件机制

```java
// 自定义事件
public class UserCreatedEvent extends ApplicationEvent {
    private final User user;

    public UserCreatedEvent(Object source, User user) {
        super(source);
        this.user = user;
    }

    public User getUser() {
        return user;
    }
}

// 事件发布者
@Service
public class UserService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    public User createUser(UserDTO userDTO) {
        User user = new User(userDTO.getUsername(), userDTO.getEmail());
        User savedUser = userRepository.save(user);

        // 发布用户创建事件
        eventPublisher.publishEvent(new UserCreatedEvent(this, savedUser));

        return savedUser;
    }
}

// 事件监听器
@Component
public class UserEventListener {

    @EventListener
    @Async
    public void handleUserCreated(UserCreatedEvent event) {
        User user = event.getUser();

        // 发送欢迎邮件
        emailService.sendWelcomeEmail(user.getEmail());

        // 记录用户创建日志
        logger.info("新用户创建: {}", user.getUsername());

        // 更新统计信息
        statisticsService.incrementUserCount();
    }

    @EventListener
    @Order(1) // 设置执行顺序
    public void handleUserCreatedForAudit(UserCreatedEvent event) {
        // 审计日志
        auditService.logUserCreation(event.getUser());
    }
}
```

这个详细的Spring Boot核心原理文档涵盖了：

1. **依赖注入原理** - IoC容器、Bean生命周期、作用域
2. **自动配置机制** - 条件化配置、自定义自动配置
3. **起步依赖** - Starter原理、自定义Starter
4. **内嵌服务器** - 服务器自动配置、自定义配置
5. **配置管理** - 外部化配置、Profile、加密
6. **AOP切面编程** - 切面原理、实际应用
7. **事务管理** - 声明式事务、传播行为、编程式事务
8. **缓存机制** - Spring Cache、自定义缓存
9. **监控管理** - Actuator、健康检查、指标监控、事件机制

每个部分都包含了详细的原理解释和实际的代码示例，帮助您深入理解Spring Boot的核心机制和最佳实践。
```
```
