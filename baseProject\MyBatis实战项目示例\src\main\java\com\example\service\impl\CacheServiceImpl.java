package com.example.service.impl;

import com.example.entity.User;
import com.example.service.CacheService;
import com.example.service.UserService;
import com.example.vo.UserDetailVO;
import com.example.vo.UserStatsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 缓存服务实现类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class CacheServiceImpl implements CacheService {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheServiceImpl.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    @Cacheable(value = "userDetails", key = "#userId", unless = "#result == null")
    public UserDetailVO getUserDetail(Long userId) {
        logger.info("从数据库构建用户详情: {}", userId);
        
        User user = userService.getUserById(userId);
        if (user == null) {
            return null;
        }
        
        UserDetailVO detail = new UserDetailVO();
        detail.setId(user.getId());
        detail.setUsername(user.getUsername());
        detail.setEmail(user.getEmail());
        detail.setPhone(user.getPhone());
        detail.setRealName(user.getRealName());
        detail.setStatus(user.getStatus());
        detail.setCreateTime(user.getCreateTime());
        detail.setUpdateTime(user.getUpdateTime());
        
        // 获取用户角色和权限
        detail.setRoles(getUserRoles(userId));
        detail.setPermissions(getUserPermissions(userId));
        
        return detail;
    }
    
    @Override
    @Cacheable(value = "userRoles", key = "#userId")
    public List<String> getUserRoles(Long userId) {
        logger.info("从数据库查询用户角色: {}", userId);
        
        // 模拟从数据库查询用户角色
        // 实际项目中应该从角色表查询
        List<String> roles = new ArrayList<>();
        User user = userService.getUserById(userId);
        if (user != null) {
            if (user.getUsername().equals("admin")) {
                roles.add("ADMIN");
                roles.add("USER");
            } else {
                roles.add("USER");
            }
        }
        return roles;
    }
    
    @Override
    @Cacheable(value = "userPermissions", key = "#userId")
    public List<String> getUserPermissions(Long userId) {
        logger.info("从数据库查询用户权限: {}", userId);
        
        // 模拟从数据库查询用户权限
        List<String> permissions = new ArrayList<>();
        List<String> roles = getUserRoles(userId);
        
        if (roles.contains("ADMIN")) {
            permissions.addAll(Arrays.asList(
                "user:read", "user:create", "user:update", "user:delete",
                "system:config", "system:monitor"
            ));
        } else if (roles.contains("USER")) {
            permissions.addAll(Arrays.asList("user:read", "profile:update"));
        }
        
        return permissions;
    }
    
    @Override
    @Cacheable(value = "userStats", key = "'global'")
    public UserStatsVO getUserStats() {
        logger.info("从数据库统计用户信息");
        
        List<User> allUsers = userService.getAllUsers();
        
        UserStatsVO stats = new UserStatsVO();
        stats.setTotalUsers(allUsers.size());
        stats.setActiveUsers((int) allUsers.stream().filter(u -> u.getStatus() == 1).count());
        stats.setInactiveUsers((int) allUsers.stream().filter(u -> u.getStatus() == 0).count());
        stats.setLastUpdateTime(LocalDateTime.now());
        
        return stats;
    }
    
    @Override
    @Cacheable(value = "userList", key = "'active'")
    public List<User> getActiveUsers() {
        logger.info("从数据库查询活跃用户");
        
        return userService.getAllUsers().stream()
            .filter(user -> user.getStatus() == 1)
            .collect(Collectors.toList());
    }
    
    @Override
    @Cacheable(value = "systemConfig", key = "#key")
    public String getSystemConfig(String key) {
        logger.info("从数据库查询系统配置: {}", key);
        
        // 模拟从配置表查询
        Map<String, String> configs = new HashMap<>();
        configs.put("app.name", "MyBatis实战项目");
        configs.put("app.version", "1.0.0");
        configs.put("app.description", "基于Spring Boot + MyBatis的实战项目");
        
        return configs.get(key);
    }
    
    @Override
    @CachePut(value = "systemConfig", key = "#key")
    public void updateSystemConfig(String key, String value) {
        logger.info("更新系统配置: {} = {}", key, value);
        
        // 模拟更新数据库配置
        // configMapper.updateConfig(key, value);
    }
    
    @Override
    public Map<Long, User> batchGetUsers(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        
        Map<Long, User> result = new HashMap<>();
        List<Long> uncachedIds = new ArrayList<>();
        
        // 先从缓存获取
        Cache cache = cacheManager.getCache("users");
        for (Long id : userIds) {
            Cache.ValueWrapper wrapper = cache.get(id);
            if (wrapper != null) {
                User user = (User) wrapper.get();
                if (user != null) {
                    result.put(id, user);
                }
            } else {
                uncachedIds.add(id);
            }
        }
        
        // 批量查询未缓存的数据
        if (!uncachedIds.isEmpty()) {
            logger.info("批量查询未缓存的用户: {}", uncachedIds);
            for (Long id : uncachedIds) {
                User user = userService.getUserById(id);
                if (user != null) {
                    result.put(id, user);
                    // 将查询结果放入缓存
                    cache.put(id, user);
                }
            }
        }
        
        return result;
    }
    
    @Override
    public void warmupUserCache(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        
        logger.info("开始预热用户缓存: {}", userIds);
        
        // 异步预热缓存
        CompletableFuture.runAsync(() -> {
            for (Long userId : userIds) {
                try {
                    // 预热基本信息
                    userService.getUserById(userId);
                    
                    // 预热详情信息
                    getUserDetail(userId);
                    
                    // 预热角色权限
                    getUserRoles(userId);
                    getUserPermissions(userId);
                    
                } catch (Exception e) {
                    logger.warn("预热用户缓存失败: {}", userId, e);
                }
            }
            logger.info("用户缓存预热完成: {}", userIds);
        });
    }
    
    @Override
    @Caching(evict = {
        @CacheEvict(value = "users", key = "#userId"),
        @CacheEvict(value = "userDetails", key = "#userId"),
        @CacheEvict(value = "userRoles", key = "#userId"),
        @CacheEvict(value = "userPermissions", key = "#userId"),
        @CacheEvict(value = "userList", allEntries = true),
        @CacheEvict(value = "userStats", allEntries = true)
    })
    public void evictUserCache(Long userId) {
        logger.info("清除用户相关缓存: {}", userId);
    }
    
    @Override
    @Caching(evict = {
        @CacheEvict(value = "users", allEntries = true),
        @CacheEvict(value = "userDetails", allEntries = true),
        @CacheEvict(value = "userRoles", allEntries = true),
        @CacheEvict(value = "userPermissions", allEntries = true),
        @CacheEvict(value = "userList", allEntries = true),
        @CacheEvict(value = "userStats", allEntries = true),
        @CacheEvict(value = "systemConfig", allEntries = true)
    })
    public void evictAllCache() {
        logger.info("清除所有缓存");
    }
    
    @Override
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        for (String cacheName : cacheManager.getCacheNames()) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Map<String, Object> cacheStats = new HashMap<>();
                
                try {
                    // 获取缓存大小（Redis缓存）
                    String pattern = cacheName + ":*";
                    Set<String> keys = redisTemplate.keys(pattern);
                    cacheStats.put("size", keys != null ? keys.size() : 0);
                    cacheStats.put("type", cache.getClass().getSimpleName());
                    
                } catch (Exception e) {
                    cacheStats.put("error", e.getMessage());
                }
                
                stats.put(cacheName, cacheStats);
            }
        }
        
        return stats;
    }
}
