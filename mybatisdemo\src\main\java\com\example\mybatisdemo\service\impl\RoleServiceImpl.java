package com.example.mybatisdemo.service.impl;

import com.example.mybatisdemo.common.enums.ResultCode;
import com.example.mybatisdemo.common.exception.BusinessException;
import com.example.mybatisdemo.dto.PageResult;
import com.example.mybatisdemo.dto.RoleQueryDTO;
import com.example.mybatisdemo.entity.Role;
import com.example.mybatisdemo.mapper.RoleMapper;
import com.example.mybatisdemo.service.RoleService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 角色服务实现类
 */
@Service
@Transactional
public class RoleServiceImpl implements RoleService {

    private static final Logger logger = LoggerFactory.getLogger(RoleServiceImpl.class);

    @Autowired
    private RoleMapper roleMapper;

    @Override
    @Transactional(readOnly = true)
    public List<Role> getAllRoles() {
        return roleMapper.selectAllActive();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Role> getAllActiveRoles() {
        return roleMapper.selectAllActive();
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<Role> getRolesByPage(RoleQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "查询参数不能为空");
        }

        // 标准PageHelper使用方式
        // 1. 设置分页参数
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 2. 设置排序
        String orderBy = queryDTO.getOrderByClause();
        if (StringUtils.hasText(orderBy)) {
            PageHelper.orderBy(orderBy);
        }

        // 3. 执行查询（PageHelper会自动拦截并添加分页SQL）
        List<Role> roleList = roleMapper.selectRoleList(queryDTO);

        // 4. 获取分页信息
        PageInfo<Role> pageInfo = new PageInfo<>(roleList);

        // 5. 转换为自定义的分页结果
        return PageResult.of(pageInfo);
    }

    @Override
    @Transactional(readOnly = true)
    public Role getRoleById(Long id) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色ID不能为空");
        }

        Role role = roleMapper.selectById(id);
        if (role == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "角色不存在");
        }

        return role;
    }

    @Override
    @Transactional(readOnly = true)
    public Role getRoleByCode(String roleCode) {
        if (roleCode == null || roleCode.trim().isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色编码不能为空");
        }

        return roleMapper.selectByCode(roleCode);
    }

    // ==================== 基础CRUD ====================

    @Override
    public Role createRole(Role role) {
        validateRole(role, true);

        // 设置创建时间和操作人
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
//        role.setCreateUser(operatorId);
//        role.setUpdateUser(operatorId);

        int result = roleMapper.insert(role);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATA_OPERATION_ERROR, "角色创建失败");
        }

//        logger.info("角色创建成功，ID: {}, 编码: {}, 操作人: {}", role.getId(), role.getRoleCode(), operatorId);
        return role;
    }

    @Override
    public Role updateRole(Role role, Long operatorId) {
        if (role.getId() == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色ID不能为空");
        }

        // 检查角色是否存在
        Role existingRole = roleMapper.selectById(role.getId());
        if (existingRole == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "角色不存在");
        }

        // 设置更新时间和操作人
        role.setUpdateTime(LocalDateTime.now());
        role.setUpdateUser(operatorId);

        int result = roleMapper.updateById(role);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATA_OPERATION_ERROR, "角色更新失败");
        }

        logger.info("角色更新成功，ID: {}, 操作人: {}", role.getId(), operatorId);
        return roleMapper.selectById(role.getId());
    }

    @Override
    public boolean deleteRole(Long id, Long operatorId) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色ID不能为空");
        }

        // 检查角色是否存在
        Role role = roleMapper.selectById(id);
        if (role == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "角色不存在");
        }

        int result = roleMapper.deleteById(id);
        if (result > 0) {
            logger.info("角色删除成功，ID: {}, 操作人: {}", id, operatorId);
            return true;
        }

        return false;
    }

    @Override
    public int batchDeleteRoles(List<Long> ids, Long operatorId) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色ID列表不能为空");
        }

        int result = roleMapper.deleteBatchByIds(ids);
        logger.info("批量删除角色完成，删除数量: {}, 操作人: {}", result, operatorId);
        return result;
    }

    // ==================== 状态管理 ====================

    @Override
    public boolean enableRole(Long id, Long operatorId) {
        return updateRoleStatus(id, 1, operatorId);
    }

    @Override
    public boolean disableRole(Long id, Long operatorId) {
        return updateRoleStatus(id, 0, operatorId);
    }

    @Override
    public int batchUpdateRoleStatus(List<Long> ids, Integer status, Long operatorId) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色ID列表不能为空");
        }

        int result = roleMapper.updateStatusBatch(ids, status, operatorId);
        String statusText = status == 1 ? "启用" : "禁用";
        logger.info("批量{}角色完成，数量: {}, 操作人: {}", statusText, result, operatorId);
        return result;
    }

    // ==================== 其他接口方法的简单实现 ====================

    @Override
    public List<Role> getChildRoles(Long parentId, boolean includeDisabled) {
        // 简单实现，实际项目中需要根据具体需求实现
        return List.of();
    }

    @Override
    public List<Role> getRoleTree(boolean includeDisabled) {
        // 简单实现，实际项目中需要根据具体需求实现
        return List.of();
    }

    @Override
    public List<Role> getRolesByUserId(Long userId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return List.of();
    }

    @Override
    public Map<Long, Role> getRolesByIds(List<Long> roleIds) {
        // 简单实现，实际项目中需要根据具体需求实现
        return Map.of();
    }

    @Override
    public boolean assignPermissions(Long roleId, List<Long> permissionIds, Long operatorId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return true;
    }

    @Override
    public boolean removePermissions(Long roleId, List<Long> permissionIds, Long operatorId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return true;
    }

    @Override
    public List<String> getRolePermissions(Long roleId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return List.of();
    }

    @Override
    public boolean assignRolesToUser(Long userId, List<Long> roleIds, Long operatorId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return true;
    }

    @Override
    public boolean removeRolesFromUser(Long userId, List<Long> roleIds, Long operatorId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return true;
    }

    @Override
    public Map<String, Object> getRoleStatistics() {
        // 简单实现，实际项目中需要根据具体需求实现
        return Map.of("totalRoles", 0, "activeRoles", 0);
    }

    @Override
    public Map<String, Object> getRoleUsageStatistics(Long roleId) {
        // 简单实现，实际项目中需要根据具体需求实现
        return Map.of("userCount", 0);
    }

    @Override
    public boolean isRoleCodeExists(String roleCode, Long excludeId) {
        return roleMapper.countByCode(roleCode, excludeId) > 0;
    }

    @Override
    public boolean isRoleNameExists(String roleName, Long excludeId) {
        return roleMapper.countByName(roleName, excludeId) > 0;
    }

    @Override
    public boolean canDeleteRole(Long roleId) {
        // 简单实现，实际项目中需要检查是否有用户在使用
        return true;
    }

    @Override
    public void refreshRoleCache(Long roleId) {
        // 简单实现，实际项目中需要根据缓存策略实现
        logger.info("刷新角色缓存，角色ID: {}", roleId);
    }

    @Override
    public void clearRoleCache() {
        // 简单实现，实际项目中需要根据缓存策略实现
        logger.info("清空角色缓存");
    }

    /**
     * 更新角色状态
     */
    private boolean updateRoleStatus(Long id, Integer status, Long operatorId) {
        if (id == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色ID不能为空");
        }

        int result = roleMapper.updateStatus(id, status, operatorId);
        if (result > 0) {
            logger.info("角色状态更新成功，ID: {}, 状态: {}, 操作人: {}", id, status, operatorId);
            return true;
        }

        return false;
    }

    /**
     * 验证角色信息
     */
    private void validateRole(Role role, boolean isCreate) {
        if (role == null) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色信息不能为空");
        }

        if (role.getRoleName() == null || role.getRoleName().trim().isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色名称不能为空");
        }

        if (role.getRoleCode() == null || role.getRoleCode().trim().isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "角色编码不能为空");
        }

        // 检查角色编码是否重复
        if (isCreate && isRoleCodeExists(role.getRoleCode(), null)) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "角色编码已存在");
        }

        // 检查角色名称是否重复
        if (isCreate && isRoleNameExists(role.getRoleName(), null)) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "角色名称已存在");
        }
    }
}
