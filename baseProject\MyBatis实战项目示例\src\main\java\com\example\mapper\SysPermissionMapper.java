package com.example.mapper;

import com.example.entity.SysPermission;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 系统权限Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SysPermissionMapper {
    
    /**
     * 根据ID查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE id = #{id}")
    SysPermission selectById(Long id);
    
    /**
     * 根据权限编码查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE permission_code = #{permissionCode}")
    SysPermission selectByPermissionCode(String permissionCode);
    
    /**
     * 查询所有权限
     */
    @Select("SELECT * FROM sys_permission ORDER BY parent_id, sort_order")
    List<SysPermission> selectAll();
    
    /**
     * 根据状态查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE status = #{status} ORDER BY parent_id, sort_order")
    List<SysPermission> selectByStatus(Integer status);
    
    /**
     * 根据父ID查询子权限
     */
    @Select("SELECT * FROM sys_permission WHERE parent_id = #{parentId} ORDER BY sort_order")
    List<SysPermission> selectByParentId(Long parentId);
    
    /**
     * 根据资源类型查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE resource_type = #{resourceType} ORDER BY parent_id, sort_order")
    List<SysPermission> selectByResourceType(String resourceType);
    
    /**
     * 查询顶级权限（菜单）
     */
    @Select("SELECT * FROM sys_permission WHERE parent_id = 0 ORDER BY sort_order")
    List<SysPermission> selectTopLevel();
    
    /**
     * 插入权限
     */
    @Insert("INSERT INTO sys_permission(permission_name, permission_code, resource_type, url, method, " +
            "parent_id, sort_order, icon, description, status, create_time, update_time, create_by) " +
            "VALUES(#{permissionName}, #{permissionCode}, #{resourceType}, #{url}, #{method}, " +
            "#{parentId}, #{sortOrder}, #{icon}, #{description}, #{status}, #{createTime}, #{updateTime}, #{createBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SysPermission permission);
    
    /**
     * 更新权限
     */
    @Update("UPDATE sys_permission SET permission_name = #{permissionName}, permission_code = #{permissionCode}, " +
            "resource_type = #{resourceType}, url = #{url}, method = #{method}, parent_id = #{parentId}, " +
            "sort_order = #{sortOrder}, icon = #{icon}, description = #{description}, status = #{status}, " +
            "update_time = #{updateTime}, update_by = #{updateBy} WHERE id = #{id}")
    int update(SysPermission permission);
    
    /**
     * 删除权限
     */
    @Delete("DELETE FROM sys_permission WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查权限名称是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE permission_name = #{permissionName}")
    int countByPermissionName(String permissionName);
    
    /**
     * 检查权限编码是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE permission_code = #{permissionCode}")
    int countByPermissionCode(String permissionCode);
    
    /**
     * 检查权限名称是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE permission_name = #{permissionName} AND id != #{id}")
    int countByPermissionNameExcludeId(@Param("permissionName") String permissionName, @Param("id") Long id);
    
    /**
     * 检查权限编码是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE permission_code = #{permissionCode} AND id != #{id}")
    int countByPermissionCodeExcludeId(@Param("permissionCode") String permissionCode, @Param("id") Long id);
    
    /**
     * 检查是否有子权限
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE parent_id = #{parentId}")
    int countChildren(Long parentId);
    
    /**
     * 检查权限是否被角色使用
     */
    @Select("SELECT COUNT(*) FROM sys_role_permission WHERE permission_id = #{permissionId}")
    int countPermissionRoles(Long permissionId);
    
    /**
     * 查询权限树（递归查询）
     */
    List<SysPermission> selectPermissionTree();
    
    /**
     * 根据用户ID查询权限树
     */
    List<SysPermission> selectUserPermissionTree(Long userId);
    
    /**
     * 根据角色ID查询权限树
     */
    List<SysPermission> selectRolePermissionTree(Long roleId);
    
    /**
     * 分页查询权限
     */
    List<SysPermission> selectByPage(@Param("offset") int offset, @Param("limit") int limit, 
                                    @Param("permissionName") String permissionName, 
                                    @Param("permissionCode") String permissionCode, 
                                    @Param("resourceType") String resourceType, 
                                    @Param("status") Integer status);
    
    /**
     * 统计权限总数
     */
    @Select("SELECT COUNT(*) FROM sys_permission")
    int countTotal();
    
    /**
     * 根据条件统计权限数
     */
    int countByCondition(@Param("permissionName") String permissionName, 
                        @Param("permissionCode") String permissionCode, 
                        @Param("resourceType") String resourceType, 
                        @Param("status") Integer status);
    
    /**
     * 根据URL和方法查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE url = #{url} AND method = #{method} AND status = 1")
    List<SysPermission> selectByUrlAndMethod(@Param("url") String url, @Param("method") String method);
    
    /**
     * 查询用户可访问的菜单权限
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.resource_type = 'MENU' AND p.status = 1 " +
            "ORDER BY p.parent_id, p.sort_order")
    List<SysPermission> selectUserMenus(Long userId);
}
