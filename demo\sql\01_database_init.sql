-- =============================================
-- 数据库初始化脚本
-- 创建数据库和用户
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS demo_db;
CREATE DATABASE demo_db 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE demo_db;

-- 创建数据库用户（可选）
-- CREATE USER IF NOT EXISTS 'demo_user'@'localhost' IDENTIFIED BY 'demo_password';
-- GRANT ALL PRIVILEGES ON demo_db.* TO 'demo_user'@'localhost';
-- FLUSH PRIVILEGES;

-- 显示当前数据库信息
SELECT 
    'Database created successfully' AS status,
    DATABASE() AS current_database,
    @@character_set_database AS charset,
    @@collation_database AS collation;
