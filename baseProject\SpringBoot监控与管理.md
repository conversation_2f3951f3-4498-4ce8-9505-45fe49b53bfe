# Spring Boot 监控与管理

## 📚 目录

1. [Spring Boot Actuator](#spring-boot-actuator)
2. [健康检查](#健康检查)
3. [指标监控](#指标监控)
4. [应用信息](#应用信息)
5. [日志管理](#日志管理)
6. [性能监控](#性能监控)
7. [自定义端点](#自定义端点)
8. [安全配置](#安全配置)
9. [监控集成](#监控集成)
10. [生产环境最佳实践](#生产环境最佳实践)

---

## Spring Boot Actuator

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>

<!-- 可选：添加Micrometer用于指标收集 -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
</dependency>
```

### 2. 基本配置

```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 暴露所有端点（生产环境需要谨慎）
        exclude: "shutdown"  # 排除危险端点
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: "GET,POST"
  
  endpoint:
    health:
      show-details: always  # 显示详细健康信息
      show-components: always
    info:
      enabled: true
    metrics:
      enabled: true
    loggers:
      enabled: true
    shutdown:
      enabled: false  # 生产环境禁用
  
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: Spring Boot应用监控示例
    version: 1.0.0
    encoding: UTF-8
  java:
    version: ${java.version}
  build:
    artifact: ${project.artifactId}
    name: ${project.name}
    time: ${maven.build.timestamp}
    version: ${project.version}
```

### 3. 常用端点

| 端点 | 描述 | HTTP方法 |
|------|------|----------|
| `/actuator/health` | 应用健康状态 | GET |
| `/actuator/info` | 应用信息 | GET |
| `/actuator/metrics` | 应用指标 | GET |
| `/actuator/env` | 环境属性 | GET |
| `/actuator/configprops` | 配置属性 | GET |
| `/actuator/beans` | Spring Bean信息 | GET |
| `/actuator/mappings` | 请求映射信息 | GET |
| `/actuator/loggers` | 日志配置 | GET/POST |
| `/actuator/threaddump` | 线程转储 | GET |
| `/actuator/heapdump` | 堆转储 | GET |
| `/actuator/prometheus` | Prometheus指标 | GET |

---

## 健康检查

### 1. 内置健康指示器

Spring Boot提供了多种内置健康指示器：

```yaml
management:
  health:
    # 数据库健康检查
    db:
      enabled: true
    # Redis健康检查
    redis:
      enabled: true
    # 磁盘空间检查
    diskspace:
      enabled: true
      threshold: 10MB
    # 邮件服务检查
    mail:
      enabled: true
```

### 2. 自定义健康指示器

```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Autowired
    private ExternalService externalService;
    
    @Override
    public Health health() {
        try {
            // 检查外部服务状态
            boolean isServiceUp = externalService.isAvailable();
            
            if (isServiceUp) {
                return Health.up()
                    .withDetail("service", "External Service")
                    .withDetail("status", "Available")
                    .withDetail("responseTime", externalService.getResponseTime())
                    .build();
            } else {
                return Health.down()
                    .withDetail("service", "External Service")
                    .withDetail("status", "Unavailable")
                    .withDetail("error", "Service is not responding")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("service", "External Service")
                .withDetail("error", e.getMessage())
                .withException(e)
                .build();
        }
    }
}
```

### 3. 复合健康指示器

```java
@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(1)) {
                return Health.up()
                    .withDetail("database", "MySQL")
                    .withDetail("validationQuery", "SELECT 1")
                    .build();
            } else {
                return Health.down()
                    .withDetail("database", "MySQL")
                    .withDetail("error", "Connection validation failed")
                    .build();
            }
        } catch (SQLException e) {
            return Health.down()
                .withDetail("database", "MySQL")
                .withDetail("error", e.getMessage())
                .withException(e)
                .build();
        }
    }
}

@Component
public class CacheHealthIndicator implements HealthIndicator {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public Health health() {
        try {
            String pong = redisTemplate.getConnectionFactory()
                .getConnection()
                .ping();
            
            if ("PONG".equals(pong)) {
                return Health.up()
                    .withDetail("cache", "Redis")
                    .withDetail("ping", pong)
                    .build();
            } else {
                return Health.down()
                    .withDetail("cache", "Redis")
                    .withDetail("ping", pong)
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("cache", "Redis")
                .withDetail("error", e.getMessage())
                .withException(e)
                .build();
        }
    }
}
```

---

## 指标监控

### 1. Micrometer集成

```java
@Configuration
public class MetricsConfig {
    
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
    
    @Bean
    public CountedAspect countedAspect(MeterRegistry registry) {
        return new CountedAspect(registry);
    }
    
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config()
            .commonTags("application", "spring-boot-demo")
            .commonTags("environment", getEnvironment());
    }
    
    private String getEnvironment() {
        return System.getProperty("spring.profiles.active", "unknown");
    }
}
```

### 2. 自定义指标

```java
@Service
public class UserService {
    
    private final UserRepository userRepository;
    private final MeterRegistry meterRegistry;
    private final Counter userCreationCounter;
    private final Timer userQueryTimer;
    private final Gauge activeUsersGauge;
    
    public UserService(UserRepository userRepository, MeterRegistry meterRegistry) {
        this.userRepository = userRepository;
        this.meterRegistry = meterRegistry;
        
        // 创建计数器
        this.userCreationCounter = Counter.builder("user.creation")
            .description("用户创建次数")
            .tag("type", "registration")
            .register(meterRegistry);
        
        // 创建计时器
        this.userQueryTimer = Timer.builder("user.query.time")
            .description("用户查询耗时")
            .register(meterRegistry);
        
        // 创建仪表盘
        this.activeUsersGauge = Gauge.builder("user.active.count")
            .description("活跃用户数量")
            .register(meterRegistry, this, UserService::getActiveUserCount);
    }
    
    @Timed(value = "user.creation.time", description = "用户创建耗时")
    @Counted(value = "user.creation.attempts", description = "用户创建尝试次数")
    public User createUser(CreateUserRequest request) {
        try {
            User user = new User();
            // 设置用户属性...
            User savedUser = userRepository.save(user);
            
            // 手动增加计数器
            userCreationCounter.increment();
            
            // 记录自定义指标
            meterRegistry.counter("user.creation.success", "method", "api").increment();
            
            return savedUser;
        } catch (Exception e) {
            meterRegistry.counter("user.creation.failure", 
                "method", "api", 
                "error", e.getClass().getSimpleName()).increment();
            throw e;
        }
    }
    
    public User findUserById(Long id) {
        return Timer.Sample.start(meterRegistry)
            .stop(userQueryTimer)
            .recordCallable(() -> userRepository.findById(id).orElse(null));
    }
    
    private double getActiveUserCount() {
        return userRepository.countByStatus(UserStatus.ACTIVE);
    }
}
```

### 3. 业务指标监控

```java
@Component
public class BusinessMetrics {
    
    private final MeterRegistry meterRegistry;
    private final OrderRepository orderRepository;
    
    public BusinessMetrics(MeterRegistry meterRegistry, OrderRepository orderRepository) {
        this.meterRegistry = meterRegistry;
        this.orderRepository = orderRepository;
        
        // 注册业务指标
        registerBusinessMetrics();
    }
    
    private void registerBusinessMetrics() {
        // 今日订单数量
        Gauge.builder("business.orders.today")
            .description("今日订单数量")
            .register(meterRegistry, this, BusinessMetrics::getTodayOrderCount);
        
        // 今日销售额
        Gauge.builder("business.revenue.today")
            .description("今日销售额")
            .register(meterRegistry, this, BusinessMetrics::getTodayRevenue);
        
        // 平均订单金额
        Gauge.builder("business.order.average.amount")
            .description("平均订单金额")
            .register(meterRegistry, this, BusinessMetrics::getAverageOrderAmount);
    }
    
    private double getTodayOrderCount() {
        LocalDate today = LocalDate.now();
        return orderRepository.countByCreateDateBetween(
            today.atStartOfDay(),
            today.plusDays(1).atStartOfDay()
        );
    }
    
    private double getTodayRevenue() {
        LocalDate today = LocalDate.now();
        return orderRepository.sumAmountByCreateDateBetween(
            today.atStartOfDay(),
            today.plusDays(1).atStartOfDay()
        );
    }
    
    private double getAverageOrderAmount() {
        return orderRepository.getAverageOrderAmount();
    }
    
    // 事件监听器记录业务事件
    @EventListener
    public void handleOrderCreated(OrderCreatedEvent event) {
        meterRegistry.counter("business.order.created",
            "payment.method", event.getOrder().getPaymentMethod(),
            "product.category", event.getOrder().getProductCategory()
        ).increment();
        
        meterRegistry.timer("business.order.processing.time")
            .record(event.getProcessingTime(), TimeUnit.MILLISECONDS);
    }
    
    @EventListener
    public void handlePaymentCompleted(PaymentCompletedEvent event) {
        meterRegistry.counter("business.payment.completed",
            "method", event.getPaymentMethod(),
            "status", event.getStatus()
        ).increment();
        
        meterRegistry.summary("business.payment.amount")
            .record(event.getAmount().doubleValue());
    }
}
```

---

## 应用信息

### 1. 构建信息

```xml
<!-- pom.xml -->
<plugin>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-maven-plugin</artifactId>
    <executions>
        <execution>
            <goals>
                <goal>build-info</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

### 2. Git信息

```xml
<!-- pom.xml -->
<plugin>
    <groupId>pl.project13.maven</groupId>
    <artifactId>git-commit-id-plugin</artifactId>
    <configuration>
        <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
        <generateGitPropertiesFile>true</generateGitPropertiesFile>
        <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
    </configuration>
</plugin>
```

### 3. 自定义信息贡献者

```java
@Component
public class CustomInfoContributor implements InfoContributor {
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public void contribute(Info.Builder builder) {
        Map<String, Object> details = new HashMap<>();
        
        // 添加用户统计信息
        details.put("totalUsers", userRepository.count());
        details.put("activeUsers", userRepository.countByStatus(UserStatus.ACTIVE));
        details.put("lastUserRegistration", userRepository.findTopByOrderByCreateTimeDesc()
            .map(User::getCreateTime)
            .orElse(null));
        
        // 添加系统信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("processors", runtime.availableProcessors());
        systemInfo.put("maxMemory", runtime.maxMemory() / 1024 / 1024 + " MB");
        systemInfo.put("totalMemory", runtime.totalMemory() / 1024 / 1024 + " MB");
        systemInfo.put("freeMemory", runtime.freeMemory() / 1024 / 1024 + " MB");
        
        details.put("system", systemInfo);
        
        builder.withDetail("statistics", details);
    }
}
```
