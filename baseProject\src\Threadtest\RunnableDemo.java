package Threadtest;

public class RunnableDemo implements  Runnable {
    private String taskName;

    public RunnableDemo(String name) {
        this.taskName = name;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(taskName + " - 执行步骤: " + i);
            try {
                Thread.sleep(800);
            } catch (InterruptedException e) {
                System.out.println(taskName + " 被中断");
                return;
            }
        }
        System.out.println(taskName + " 任务完成");
    }
    public static void main(String[] args) {
        RunnableDemo task1 = new RunnableDemo("任务A");
        RunnableDemo task2 = new RunnableDemo("任务B");

        Thread thread1 = new Thread(task1);
        Thread thread2 = new Thread(task2);

        thread1.start();
        thread2.start();
    }
}
