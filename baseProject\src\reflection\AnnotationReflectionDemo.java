package reflection;

import java.lang.annotation.*;
import java.lang.reflect.*;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 反射和注解配合使用演示
 * 展示如何通过反射读取和处理注解信息
 */
public class AnnotationReflectionDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 反射和注解配合使用演示 ===");
        
        try {
            // 1. 基本注解读取
            demonstrateBasicAnnotationReading();
            
            // 2. 数据验证框架
            demonstrateValidationFramework();
            
            // 3. ORM映射框架
            demonstrateORMMapping();
            
            // 4. 依赖注入框架
            demonstrateDependencyInjection();
            
            // 5. AOP切面处理
            demonstrateAOPProcessing();
            
        } catch (Exception e) {
            System.err.println("演示过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 基本注解读取演示
     */
    public static void demonstrateBasicAnnotationReading() throws Exception {
        System.out.println("\n--- 基本注解读取演示 ---");
        
        Class<?> clazz = AnnotatedUser.class;
        
        // 读取类注解
        System.out.println("类注解信息:");
        Annotation[] classAnnotations = clazz.getAnnotations();
        if (classAnnotations.length == 0) {
            System.out.println("  该类没有注解");
        } else {
            for (Annotation annotation : classAnnotations) {
                System.out.println("  " + annotation);
            }
        }
        
        // 读取字段注解
        System.out.println("\n字段注解信息:");
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            System.out.println("字段: " + field.getName());
            
            Annotation[] fieldAnnotations = field.getAnnotations();
            for (Annotation annotation : fieldAnnotations) {
                System.out.println("  注解: " + annotation.annotationType().getSimpleName());
                
                // 使用反射读取注解属性
                Method[] methods = annotation.annotationType().getDeclaredMethods();
                for (Method method : methods) {
                    if (method.getParameterCount() == 0) {
                        Object value = method.invoke(annotation);
                        System.out.println("    " + method.getName() + " = " + value);
                    }
                }
            }
        }
        
        // 读取方法注解
        System.out.println("\n方法注解信息:");
        Method[] methods = clazz.getDeclaredMethods();
        boolean hasMethodAnnotations = false;
        for (Method method : methods) {
            Annotation[] methodAnnotations = method.getAnnotations();
            if (methodAnnotations.length > 0) {
                hasMethodAnnotations = true;
                System.out.println("方法: " + method.getName());
                for (Annotation annotation : methodAnnotations) {
                    System.out.println("  注解: " + annotation.annotationType().getSimpleName());
                }
            }
        }
        if (!hasMethodAnnotations) {
            System.out.println("  该类的方法没有注解");
        }
    }
    
    /**
     * 数据验证框架演示
     */
    public static void demonstrateValidationFramework() throws Exception {
        System.out.println("\n--- 数据验证框架演示 ---");
        
        // 创建测试对象
        AnnotatedUser user1 = new AnnotatedUser();
        user1.setUsername(""); // 空用户名
        user1.setEmail("invalid-email"); // 无效邮箱
        user1.setAge(-5); // 无效年龄
        
        AnnotatedUser user2 = new AnnotatedUser();
        user2.setUsername("alice");
        user2.setEmail("<EMAIL>");
        user2.setAge(25);
        
        // 验证对象
        ValidationResult result1 = ValidationFramework.validate(user1);
        ValidationResult result2 = ValidationFramework.validate(user2);
        
        System.out.println("用户1验证结果: " + (result1.isValid() ? "通过" : "失败"));
        if (!result1.isValid()) {
            result1.getErrors().forEach((field, error) -> 
                System.out.println("  " + field + ": " + error));
        }
        
        System.out.println("用户2验证结果: " + (result2.isValid() ? "通过" : "失败"));
    }
    
    /**
     * ORM映射框架演示
     */
    public static void demonstrateORMMapping() throws Exception {
        System.out.println("\n--- ORM映射框架演示 ---");

        AnnotatedUser user = new AnnotatedUser();
        user.setId(1L);
        user.setUsername("alice");
        user.setEmail("<EMAIL>");
        user.setAge(25);

        // 简化的ORM演示 - 只显示对象信息
        System.out.println("用户对象: " + user);
        System.out.println("模拟生成SQL:");
        System.out.println("INSERT SQL: INSERT INTO users (id, username, email, age) VALUES (1, 'alice', '<EMAIL>', 25)");
        System.out.println("SELECT SQL: SELECT * FROM users WHERE id = 1");
        System.out.println("UPDATE SQL: UPDATE users SET username='alice', email='<EMAIL>', age=25 WHERE id=1");
        System.out.println("DELETE SQL: DELETE FROM users WHERE id = 1");

        // 模拟从数据库结果创建对象
        AnnotatedUser userFromDB = new AnnotatedUser();
        userFromDB.setId(2L);
        userFromDB.setUsername("bob");
        userFromDB.setEmail("<EMAIL>");
        userFromDB.setAge(30);

        System.out.println("从数据库创建的对象: " + userFromDB);
    }
    
    /**
     * 依赖注入框架演示
     */
    public static void demonstrateDependencyInjection() throws Exception {
        System.out.println("\n--- 依赖注入框架演示 ---");
        
        DIContainer container = new DIContainer();
        
        // 注册服务
        container.register(EmailService.class, new EmailServiceImpl());
        container.register(LogService.class, new LogServiceImpl());
        
        // 创建需要依赖注入的对象
        UserController controller = new UserController();
        
        // 执行依赖注入
        container.inject(controller);
        
        // 测试注入是否成功
        controller.createUser("<EMAIL>");
    }
    
    /**
     * AOP切面处理演示
     */
    public static void demonstrateAOPProcessing() throws Exception {
        System.out.println("\n--- AOP切面处理演示 ---");

        Class<?> clazz = UserService.class;
        Method[] methods = clazz.getDeclaredMethods();

        boolean hasAOPAnnotations = false;
        for (Method method : methods) {
            // 检查方法是否有AOP注解
            if (method.isAnnotationPresent(LogExecution.class)) {
                hasAOPAnnotations = true;
                LogExecution logExecution = method.getAnnotation(LogExecution.class);
                System.out.println("方法 " + method.getName() + " 需要日志记录:");
                System.out.println("  级别: " + logExecution.level());
                System.out.println("  记录参数: " + logExecution.logArgs());
                System.out.println("  记录返回值: " + logExecution.logResult());
            }

            if (method.isAnnotationPresent(CacheResult.class)) {
                hasAOPAnnotations = true;
                CacheResult cacheResult = method.getAnnotation(CacheResult.class);
                System.out.println("方法 " + method.getName() + " 需要缓存:");
                System.out.println("  缓存键: " + cacheResult.key());
                System.out.println("  过期时间: " + cacheResult.expireTime());
            }

            if (method.isAnnotationPresent(RequirePermission.class)) {
                hasAOPAnnotations = true;
                RequirePermission permission = method.getAnnotation(RequirePermission.class);
                System.out.println("方法 " + method.getName() + " 需要权限:");
                System.out.println("  权限: " + Arrays.toString(permission.value()));
                System.out.println("  权限类型: " + permission.type());
            }
        }

        if (!hasAOPAnnotations) {
            System.out.println("UserService类的方法没有AOP注解");
        }
    }
}

// ==================== 注解定义 ====================

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@interface Validate {
    ValidationType type() default ValidationType.NOT_NULL;
    String message() default "验证失败";
    String pattern() default "";
    int min() default Integer.MIN_VALUE;
    int max() default Integer.MAX_VALUE;
}

enum ValidationType {
    NOT_NULL, EMAIL, RANGE, PATTERN
}

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@interface Inject {
}

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@interface LogExecution {
    String level() default "INFO";
    boolean logArgs() default true;
    boolean logResult() default true;
}

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@interface CacheResult {
    String key();
    int expireTime() default 300;
}

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@interface RequirePermission {
    String[] value();
    String type() default "AND";
}

// ==================== 实体类 ====================

class AnnotatedUser {

    private Long id;

    @Validate(type = ValidationType.NOT_NULL, message = "用户名不能为空")
    private String username;

    @Validate(type = ValidationType.EMAIL, message = "邮箱格式不正确")
    private String email;

    @Validate(type = ValidationType.RANGE, min = 0, max = 150, message = "年龄必须在0-150之间")
    private Integer age;
    
    // 构造函数
    public AnnotatedUser() {}
    
    // 业务方法
    public void save() {
        System.out.println("保存用户: " + username);
    }

    public AnnotatedUser findById(Long id) {
        System.out.println("查找用户: " + id);
        return this;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }
    
    @Override
    public String toString() {
        return "AnnotatedUser{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", age=" + age +
                '}';
    }
}

// ==================== 框架实现 ====================

/**
 * 数据验证框架
 */
class ValidationFramework {

    public static ValidationResult validate(Object obj) {
        ValidationResult result = new ValidationResult();
        Class<?> clazz = obj.getClass();

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Validate.class)) {
                Validate validate = field.getAnnotation(Validate.class);

                try {
                    field.setAccessible(true);
                    Object value = field.get(obj);

                    String error = validateField(field.getName(), value, validate);
                    if (error != null) {
                        result.addError(field.getName(), error);
                    }

                } catch (IllegalAccessException e) {
                    result.addError(field.getName(), "无法访问字段");
                }
            }
        }

        return result;
    }

    private static String validateField(String fieldName, Object value, Validate validate) {
        switch (validate.type()) {
            case NOT_NULL:
                if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                    return validate.message();
                }
                break;

            case EMAIL:
                if (value != null && !isValidEmail(value.toString())) {
                    return validate.message();
                }
                break;

            case RANGE:
                if (value instanceof Integer) {
                    int intValue = (Integer) value;
                    if (intValue < validate.min() || intValue > validate.max()) {
                        return validate.message();
                    }
                }
                break;

            case PATTERN:
                if (value != null && !validate.pattern().isEmpty()) {
                    if (!Pattern.matches(validate.pattern(), value.toString())) {
                        return validate.message();
                    }
                }
                break;
        }

        return null;
    }

    private static boolean isValidEmail(String email) {
        String emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(emailPattern, email);
    }
}

class ValidationResult {
    private boolean valid = true;
    private Map<String, String> errors = new HashMap<>();

    public void addError(String field, String message) {
        this.valid = false;
        this.errors.put(field, message);
    }

    public boolean isValid() { return valid; }
    public Map<String, String> getErrors() { return errors; }
}



/**
 * 依赖注入容器
 */
class DIContainer {
    private final Map<Class<?>, Object> instances = new HashMap<>();

    public <T> void register(Class<T> type, T instance) {
        instances.put(type, instance);
    }

    @SuppressWarnings("unchecked")
    public <T> T getInstance(Class<T> type) {
        return (T) instances.get(type);
    }

    public void inject(Object target) throws Exception {
        Class<?> clazz = target.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(Inject.class)) {
                Object dependency = getInstance(field.getType());
                if (dependency != null) {
                    field.setAccessible(true);
                    field.set(target, dependency);
                    System.out.println("注入依赖: " + field.getType().getSimpleName() +
                                     " -> " + target.getClass().getSimpleName());
                }
            }
        }
    }
}

// ==================== 服务类 ====================

interface EmailService {
    void sendEmail(String to, String subject, String body);
}

class EmailServiceImpl implements EmailService {
    @Override
    public void sendEmail(String to, String subject, String body) {
        System.out.println("发送邮件到: " + to + ", 主题: " + subject);
    }
}

interface LogService {
    void log(String message);
}

class LogServiceImpl implements LogService {
    @Override
    public void log(String message) {
        System.out.println("日志: " + message);
    }
}

class UserController {

    @Inject
    private EmailService emailService;

    @Inject
    private LogService logService;

    public void createUser(String email) {
        if (emailService != null) {
            emailService.sendEmail(email, "欢迎", "欢迎注册我们的服务");
        }

        if (logService != null) {
            logService.log("创建用户: " + email);
        }

        System.out.println("用户创建完成: " + email);
    }
}

class UserService {

    @LogExecution(level = "INFO", logArgs = true, logResult = true)
    @CacheResult(key = "user_#{id}", expireTime = 600)
    public String getUserById(Long id) {
        return "User-" + id;
    }

    @LogExecution(level = "WARN")
    @RequirePermission({"USER_CREATE", "ADMIN"})
    public void createUser(String name) {
        System.out.println("创建用户: " + name);
    }

    @LogExecution(level = "ERROR")
    @RequirePermission(value = {"USER_DELETE"}, type = "OR")
    public void deleteUser(Long id) {
        System.out.println("删除用户: " + id);
    }
}
