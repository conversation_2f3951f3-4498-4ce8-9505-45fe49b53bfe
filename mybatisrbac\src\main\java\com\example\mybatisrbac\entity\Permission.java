package com.example.mybatisrbac.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 权限实体类
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "权限实体")
public class Permission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    @Schema(description = "权限ID", example = "1")
    private Long id;

    /**
     * 权限名称
     */
    @Schema(description = "权限名称", example = "用户管理")
    private String permissionName;

    /**
     * 权限编码
     */
    @Schema(description = "权限编码", example = "user:manage")
    private String permissionCode;

    /**
     * 权限类型：1-菜单，2-按钮，3-接口
     */
    @Schema(description = "权限类型：1-菜单，2-按钮，3-接口", example = "1")
    private Integer permissionType;

    /**
     * 父权限ID
     */
    @Schema(description = "父权限ID", example = "0")
    private Long parentId;

    /**
     * 路径
     */
    @Schema(description = "路径", example = "/user")
    private String path;

    /**
     * 组件
     */
    @Schema(description = "组件", example = "UserManage")
    private String component;

    /**
     * 图标
     */
    @Schema(description = "图标", example = "user")
    private String icon;

    /**
     * 排序
     */
    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID", example = "1")
    private Long createBy;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID", example = "1")
    private Long updateBy;
}
