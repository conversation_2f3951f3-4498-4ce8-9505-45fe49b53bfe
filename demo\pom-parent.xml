<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.3</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	
	<groupId>com.example</groupId>
	<artifactId>java-learning-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>Java Learning Parent</name>
	<description>Java学习项目 - 多模块父项目</description>
	
	<properties>
		<java.version>17</java.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	
	<!-- 子模块列表 -->
	<modules>
		<module>mysql-demo</module>
		<module>redis-demo</module>
		<module>mongodb-demo</module>
		<module>security-demo</module>
		<module>web-demo</module>
		<module>microservice-demo</module>
	</modules>
	
	<!-- 依赖管理 - 统一管理所有子模块的依赖版本 -->
	<dependencyManagement>
		<dependencies>
			<!-- Spring Boot BOM -->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>3.5.3</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			
			<!-- 数据库相关 -->
			<dependency>
				<groupId>com.mysql</groupId>
				<artifactId>mysql-connector-j</artifactId>
				<version>8.0.33</version>
			</dependency>
			
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>4.4.3</version>
			</dependency>
			
			<dependency>
				<groupId>org.mongodb</groupId>
				<artifactId>mongodb-driver-sync</artifactId>
				<version>4.10.2</version>
			</dependency>
			
			<!-- 工具类 -->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>3.12.0</version>
			</dependency>
			
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson2</artifactId>
				<version>2.0.40</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
	
	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
				</plugin>
				
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>3.11.0</version>
					<configuration>
						<source>17</source>
						<target>17</target>
						<encoding>UTF-8</encoding>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>
