package com.example.mybatisrbac.util;

import com.example.mybatisrbac.common.ResultCode;
import com.example.mybatisrbac.exception.BusinessException;

import java.util.Arrays;
import java.util.List;

/**
 * 参数验证工具类
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public class ValidationUtil {

    /**
     * 有效的角色排序字段
     */
    private static final List<String> VALID_ROLE_SORT_FIELDS = Arrays.asList(
            "id", "roleName", "roleCode", "status", "createTime", "updateTime"
    );

    /**
     * 有效的用户排序字段
     */
    private static final List<String> VALID_USER_SORT_FIELDS = Arrays.asList(
            "id", "username", "email", "phone", "realName", "status", "createTime", "updateTime", "lastLoginTime"
    );

    /**
     * 有效的排序方向
     */
    private static final List<String> VALID_SORT_ORDERS = Arrays.asList("asc", "desc");

    /**
     * 验证分页参数
     * 
     * @param current 当前页码
     * @param size 每页大小
     */
    public static void validatePageParams(Long current, Long size) {
        if (current == null || current < 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "当前页码必须大于0");
        }
        if (current > 10000) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "当前页码不能超过10000");
        }
        if (size == null || size < 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "每页大小必须大于0");
        }
        if (size > 100) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "每页大小不能超过100");
        }
    }

    /**
     * 验证角色排序字段
     * 
     * @param sortField 排序字段
     */
    public static void validateRoleSortField(String sortField) {
        if (sortField != null && !VALID_ROLE_SORT_FIELDS.contains(sortField)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, 
                    "排序字段只能为：" + String.join(",", VALID_ROLE_SORT_FIELDS));
        }
    }

    /**
     * 验证用户排序字段
     * 
     * @param sortField 排序字段
     */
    public static void validateUserSortField(String sortField) {
        if (sortField != null && !VALID_USER_SORT_FIELDS.contains(sortField)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, 
                    "排序字段只能为：" + String.join(",", VALID_USER_SORT_FIELDS));
        }
    }

    /**
     * 验证排序方向
     * 
     * @param sortOrder 排序方向
     */
    public static void validateSortOrder(String sortOrder) {
        if (sortOrder != null && !VALID_SORT_ORDERS.contains(sortOrder.toLowerCase())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "排序方向只能为asc或desc");
        }
    }

    /**
     * 验证状态值
     * 
     * @param status 状态值
     */
    public static void validateStatus(Integer status) {
        if (status != null && (status < 0 || status > 1)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "状态值只能为0或1");
        }
    }

    /**
     * 验证字符串长度
     * 
     * @param value 字符串值
     * @param maxLength 最大长度
     * @param fieldName 字段名
     */
    public static void validateStringLength(String value, int maxLength, String fieldName) {
        if (value != null && value.length() > maxLength) {
            throw new BusinessException(ResultCode.BAD_REQUEST, 
                    fieldName + "长度不能超过" + maxLength);
        }
    }

    /**
     * 验证角色编码格式
     * 
     * @param roleCode 角色编码
     */
    public static void validateRoleCode(String roleCode) {
        if (roleCode != null && !roleCode.matches("^[A-Z_]*$")) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "角色编码只能包含大写字母和下划线");
        }
    }

    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱
     */
    public static void validateEmail(String email) {
        if (email != null && !email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "邮箱格式不正确");
        }
    }

    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     */
    public static void validatePhone(String phone) {
        if (phone != null && !phone.matches("^1[3-9]\\d{9}$")) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "手机号格式不正确");
        }
    }

    /**
     * 验证用户名格式
     * 
     * @param username 用户名
     */
    public static void validateUsername(String username) {
        if (username != null && !username.matches("^[a-zA-Z0-9_]{3,20}$")) {
            throw new BusinessException(ResultCode.BAD_REQUEST, 
                    "用户名只能包含字母、数字和下划线，长度3-20位");
        }
    }

    /**
     * 验证密码强度
     * 
     * @param password 密码
     */
    public static void validatePassword(String password) {
        if (password == null || password.length() < 6) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "密码长度不能少于6位");
        }
        if (password.length() > 20) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "密码长度不能超过20位");
        }
        // 可以添加更复杂的密码强度验证
        // if (!password.matches("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$")) {
        //     throw new BusinessException(ResultCode.BAD_REQUEST, "密码必须包含大小写字母和数字");
        // }
    }
}
