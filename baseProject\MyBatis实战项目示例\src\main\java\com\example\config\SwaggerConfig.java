package com.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Swagger API文档配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
public class SwaggerConfig {
    
    /**
     * 创建API文档
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.example.controller"))
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
    }
    
    /**
     * API信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("RBAC权限管理系统 API文档")
                .description("基于Spring Boot + Security + MyBatis + JWT的RBAC权限管理系统")
                .version("1.0.0")
                .contact(new Contact("Example", "https://example.com", "<EMAIL>"))
                .license("MIT License")
                .licenseUrl("https://opensource.org/licenses/MIT")
                .build();
    }
    
    /**
     * 安全方案配置
     */
    private List<SecurityScheme> securitySchemes() {
        return Collections.singletonList(
                new ApiKey("JWT", "Authorization", "header")
        );
    }
    
    /**
     * 安全上下文配置
     */
    private List<SecurityContext> securityContexts() {
        return Collections.singletonList(
                SecurityContext.builder()
                        .securityReferences(defaultAuth())
                        .operationSelector(operationContext -> 
                            operationContext.requestMappingPattern().matches("/api/(?!auth/login|auth/register|public/).*"))
                        .build()
        );
    }
    
    /**
     * 默认认证配置
     */
    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        return Collections.singletonList(
                new SecurityReference("JWT", authorizationScopes)
        );
    }
}
