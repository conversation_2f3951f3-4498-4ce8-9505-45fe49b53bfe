package com.example.mybatisrbac.interceptor;

import com.example.mybatisrbac.common.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 响应拦截器
 * 自动包装响应结果
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
//@RestControllerAdvice  // 临时禁用，避免与 SpringDoc 冲突
public class ResponseInterceptor implements ResponseBodyAdvice<Object> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 排除已经是 Result 类型的响应
        if (returnType.getParameterType() == Result.class) {
            return false;
        }
        
        // 排除 Swagger 相关的响应
        String packageName = returnType.getDeclaringClass().getPackage().getName();
        if (packageName.contains("springdoc") || packageName.contains("swagger")) {
            return false;
        }
        
        // 排除错误页面
        if (packageName.contains("error")) {
            return false;
        }
        
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {
        
        // 如果已经是 Result 类型，直接返回
        if (body instanceof Result) {
            return body;
        }
        
        // 如果是字符串类型，需要特殊处理
        if (body instanceof String) {
            try {
                Result<String> result = Result.success((String) body);
                return objectMapper.writeValueAsString(result);
            } catch (Exception e) {
                log.error("字符串响应包装失败", e);
                return body;
            }
        }
        
        // 包装其他类型的响应
        return Result.success(body);
    }
}
