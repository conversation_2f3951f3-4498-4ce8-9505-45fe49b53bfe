package com.example.security;

import com.example.entity.SysUser;
import com.example.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * 自定义用户详情服务
 * 实现Spring Security的UserDetailsService接口
 * 用于从数据库加载用户信息进行认证和授权
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomUserDetailsService.class);
    
    @Autowired
    private SysUserMapper userMapper;
    
    /**
     * 根据用户名加载用户详情
     * Spring Security会调用此方法进行用户认证
     * 
     * @param username 用户名
     * @return UserDetails 用户详情
     * @throws UsernameNotFoundException 用户不存在异常
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        logger.info("正在加载用户信息: {}", username);
        
        try {
            // 1. 根据用户名查询用户基本信息
            SysUser user = userMapper.selectByUsername(username);
            if (user == null) {
                logger.warn("用户不存在: {}", username);
                throw new UsernameNotFoundException("用户不存在: " + username);
            }
            
            // 2. 检查用户状态
            if (!user.isEnabled()) {
                logger.warn("用户已被禁用: {}", username);
                throw new UsernameNotFoundException("用户已被禁用: " + username);
            }
            
            // 3. 查询用户的角色和权限信息
            SysUser userWithRolesAndPermissions = userMapper.selectUserWithRolesAndPermissions(user.getId());
            if (userWithRolesAndPermissions == null) {
                // 如果关联查询失败，使用基本用户信息
                userWithRolesAndPermissions = user;
            }
            
            // 4. 创建UserPrincipal对象
            UserPrincipal userPrincipal = UserPrincipal.create(userWithRolesAndPermissions);
            
            logger.info("用户信息加载成功: {}, 角色数: {}, 权限数: {}", 
                username, 
                userWithRolesAndPermissions.getRoles() != null ? userWithRolesAndPermissions.getRoles().size() : 0,
                userWithRolesAndPermissions.getPermissions() != null ? userWithRolesAndPermissions.getPermissions().size() : 0
            );
            
            return userPrincipal;
            
        } catch (UsernameNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("加载用户信息时发生异常: {}", username, e);
            throw new UsernameNotFoundException("加载用户信息失败: " + username, e);
        }
    }
    
    /**
     * 根据用户ID加载用户详情
     * 用于JWT令牌验证时快速加载用户信息
     * 
     * @param userId 用户ID
     * @return UserDetails 用户详情
     * @throws UsernameNotFoundException 用户不存在异常
     */
    public UserDetails loadUserById(Long userId) throws UsernameNotFoundException {
        logger.debug("正在根据ID加载用户信息: {}", userId);
        
        try {
            // 1. 根据用户ID查询用户信息（包含角色和权限）
            SysUser user = userMapper.selectUserWithRolesAndPermissions(userId);
            if (user == null) {
                logger.warn("用户不存在: ID={}", userId);
                throw new UsernameNotFoundException("用户不存在: ID=" + userId);
            }
            
            // 2. 检查用户状态
            if (!user.isEnabled()) {
                logger.warn("用户已被禁用: ID={}", userId);
                throw new UsernameNotFoundException("用户已被禁用: ID=" + userId);
            }
            
            // 3. 创建UserPrincipal对象
            UserPrincipal userPrincipal = UserPrincipal.create(user);
            
            logger.debug("用户信息加载成功: ID={}, 用户名={}", userId, user.getUsername());
            
            return userPrincipal;
            
        } catch (UsernameNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("根据ID加载用户信息时发生异常: {}", userId, e);
            throw new UsernameNotFoundException("加载用户信息失败: ID=" + userId, e);
        }
    }
    
    /**
     * 刷新用户权限信息
     * 当用户角色或权限发生变化时，可以调用此方法刷新权限
     * 
     * @param userId 用户ID
     * @return UserDetails 更新后的用户详情
     */
    public UserDetails refreshUserAuthorities(Long userId) {
        logger.info("刷新用户权限信息: {}", userId);
        return loadUserById(userId);
    }
}
