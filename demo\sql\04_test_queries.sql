-- =============================================
-- 数据查询测试脚本
-- 测试各种查询场景，对应Repository方法
-- =============================================

USE demo_db;

-- 1. 基本查询测试
SELECT '=== 基本查询测试 ===' as test_section;

-- 查询所有用户
SELECT * FROM users ORDER BY id;

-- 根据ID查询用户
SELECT * FROM users WHERE id = 1;

-- 根据用户名查询用户
SELECT * FROM users WHERE username = 'admin';

-- 根据邮箱查询用户
SELECT * FROM users WHERE email = '<EMAIL>';

-- 2. 模糊查询测试
SELECT '=== 模糊查询测试 ===' as test_section;

-- 查找用户名包含'dev'的用户
SELECT * FROM users WHERE username LIKE '%dev%';

-- 查找邮箱域名为gmail的用户
SELECT * FROM users WHERE email LIKE '%@gmail.com';

-- 查找用户名包含'user'的用户
SELECT * FROM users WHERE username LIKE '%user%';

-- 3. 日期查询测试
SELECT '=== 日期查询测试 ===' as test_section;

-- 查询今天注册的用户
SELECT * FROM users WHERE DATE(created_at) = CURDATE();

-- 查询最近7天注册的用户
SELECT * FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 查询2024年2月注册的用户
SELECT * FROM users WHERE created_at >= '2024-02-01' AND created_at < '2024-03-01';

-- 4. 统计查询测试
SELECT '=== 统计查询测试 ===' as test_section;

-- 用户总数
SELECT COUNT(*) as total_users FROM users;

-- 按邮箱域名统计用户数
SELECT 
    SUBSTRING_INDEX(email, '@', -1) as email_domain,
    COUNT(*) as user_count
FROM users 
GROUP BY SUBSTRING_INDEX(email, '@', -1)
ORDER BY user_count DESC;

-- 按注册日期统计用户数
SELECT 
    DATE(created_at) as registration_date,
    COUNT(*) as daily_registrations
FROM users 
GROUP BY DATE(created_at)
ORDER BY registration_date DESC;

-- 5. 排序查询测试
SELECT '=== 排序查询测试 ===' as test_section;

-- 按用户名排序
SELECT id, username, email FROM users ORDER BY username;

-- 按注册时间倒序
SELECT id, username, email, created_at FROM users ORDER BY created_at DESC;

-- 按邮箱排序
SELECT id, username, email FROM users ORDER BY email;

-- 6. 分页查询测试
SELECT '=== 分页查询测试 ===' as test_section;

-- 第一页（前5条）
SELECT * FROM users ORDER BY id LIMIT 5 OFFSET 0;

-- 第二页（第6-10条）
SELECT * FROM users ORDER BY id LIMIT 5 OFFSET 5;

-- 第三页（第11-15条）
SELECT * FROM users ORDER BY id LIMIT 5 OFFSET 10;

-- 7. 存在性检查测试
SELECT '=== 存在性检查测试 ===' as test_section;

-- 检查用户名是否存在
SELECT EXISTS(SELECT 1 FROM users WHERE username = 'admin') as username_exists;

-- 检查邮箱是否存在
SELECT EXISTS(SELECT 1 FROM users WHERE email = '<EMAIL>') as email_exists;

-- 8. 复合查询测试
SELECT '=== 复合查询测试 ===' as test_section;

-- 查询用户名或邮箱包含特定关键字的用户
SELECT * FROM users 
WHERE username LIKE '%test%' OR email LIKE '%test%'
ORDER BY created_at DESC;

-- 查询最近注册的前3个用户
SELECT * FROM users 
ORDER BY created_at DESC 
LIMIT 3;
