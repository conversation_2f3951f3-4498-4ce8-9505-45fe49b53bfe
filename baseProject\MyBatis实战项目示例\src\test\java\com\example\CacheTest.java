package com.example;

import com.example.entity.User;
import com.example.service.CacheService;
import com.example.service.UserService;
import com.example.vo.UserDetailVO;
import com.example.vo.UserStatsVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 缓存功能测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CacheTest {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheTest.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private CacheService cacheService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 测试基本缓存功能
     */
    @Test
    public void testBasicCache() {
        logger.info("=== 测试基本缓存功能 ===");
        
        // 创建测试用户
        User user = createTestUser();
        
        // 第一次查询 - 从数据库
        logger.info("第一次查询用户...");
        User user1 = userService.getUserById(user.getId());
        logger.info("查询结果: {}", user1.getUsername());
        
        // 第二次查询 - 从缓存
        logger.info("第二次查询用户...");
        User user2 = userService.getUserById(user.getId());
        logger.info("查询结果: {}", user2.getUsername());
        
        // 验证缓存是否生效
        Cache cache = cacheManager.getCache("users");
        Cache.ValueWrapper wrapper = cache.get(user.getId());
        logger.info("缓存中的用户: {}", wrapper != null ? ((User) wrapper.get()).getUsername() : "null");
    }
    
    /**
     * 测试缓存更新
     */
    @Test
    public void testCacheUpdate() {
        logger.info("=== 测试缓存更新 ===");
        
        // 创建测试用户
        User user = createTestUser();
        
        // 查询用户（加入缓存）
        User cachedUser = userService.getUserById(user.getId());
        logger.info("缓存前用户名: {}", cachedUser.getUsername());
        
        // 更新用户
        user.setUsername("updated_user");
        user.setUpdateTime(LocalDateTime.now());
        userService.updateUser(user);
        logger.info("用户已更新");
        
        // 再次查询（应该从缓存获取更新后的数据）
        User updatedUser = userService.getUserById(user.getId());
        logger.info("缓存后用户名: {}", updatedUser.getUsername());
    }
    
    /**
     * 测试缓存清除
     */
    @Test
    public void testCacheEviction() {
        logger.info("=== 测试缓存清除 ===");
        
        // 创建测试用户
        User user = createTestUser();
        
        // 查询用户（加入缓存）
        userService.getUserById(user.getId());
        
        // 检查缓存是否存在
        Cache cache = cacheManager.getCache("users");
        logger.info("删除前缓存存在: {}", cache.get(user.getId()) != null);
        
        // 删除用户（清除缓存）
        userService.deleteUser(user.getId());
        
        // 检查缓存是否被清除
        logger.info("删除后缓存存在: {}", cache.get(user.getId()) != null);
    }
    
    /**
     * 测试用户详情缓存
     */
    @Test
    public void testUserDetailCache() {
        logger.info("=== 测试用户详情缓存 ===");
        
        // 创建测试用户
        User user = createTestUser();
        
        // 第一次获取用户详情
        logger.info("第一次获取用户详情...");
        UserDetailVO detail1 = cacheService.getUserDetail(user.getId());
        logger.info("用户详情: {}", detail1.getUsername());
        logger.info("用户角色: {}", detail1.getRoles());
        logger.info("用户权限: {}", detail1.getPermissions());
        
        // 第二次获取用户详情（从缓存）
        logger.info("第二次获取用户详情...");
        UserDetailVO detail2 = cacheService.getUserDetail(user.getId());
        logger.info("用户详情: {}", detail2.getUsername());
    }
    
    /**
     * 测试用户统计缓存
     */
    @Test
    public void testUserStatsCache() {
        logger.info("=== 测试用户统计缓存 ===");
        
        // 第一次获取统计信息
        logger.info("第一次获取用户统计...");
        UserStatsVO stats1 = cacheService.getUserStats();
        logger.info("统计信息: 总用户={}, 活跃用户={}, 非活跃用户={}", 
                stats1.getTotalUsers(), stats1.getActiveUsers(), stats1.getInactiveUsers());
        
        // 第二次获取统计信息（从缓存）
        logger.info("第二次获取用户统计...");
        UserStatsVO stats2 = cacheService.getUserStats();
        logger.info("统计信息: 总用户={}, 活跃用户={}, 非活跃用户={}", 
                stats2.getTotalUsers(), stats2.getActiveUsers(), stats2.getInactiveUsers());
    }
    
    /**
     * 测试批量获取用户
     */
    @Test
    public void testBatchGetUsers() {
        logger.info("=== 测试批量获取用户 ===");
        
        // 创建多个测试用户
        User user1 = createTestUser("batch_user1");
        User user2 = createTestUser("batch_user2");
        User user3 = createTestUser("batch_user3");
        
        List<Long> userIds = Arrays.asList(user1.getId(), user2.getId(), user3.getId());
        
        // 批量获取用户
        logger.info("批量获取用户...");
        Map<Long, User> users = cacheService.batchGetUsers(userIds);
        logger.info("获取到{}个用户", users.size());
        
        users.forEach((id, user) -> {
            logger.info("用户: {} - {}", id, user.getUsername());
        });
    }
    
    /**
     * 测试缓存预热
     */
    @Test
    public void testCacheWarmup() {
        logger.info("=== 测试缓存预热 ===");
        
        // 创建多个测试用户
        User user1 = createTestUser("warmup_user1");
        User user2 = createTestUser("warmup_user2");
        
        List<Long> userIds = Arrays.asList(user1.getId(), user2.getId());
        
        // 预热缓存
        logger.info("开始预热缓存...");
        cacheService.warmupUserCache(userIds);
        
        // 等待预热完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 检查缓存是否已预热
        Cache cache = cacheManager.getCache("users");
        for (Long userId : userIds) {
            boolean cached = cache.get(userId) != null;
            logger.info("用户{}缓存状态: {}", userId, cached ? "已缓存" : "未缓存");
        }
    }
    
    /**
     * 测试系统配置缓存
     */
    @Test
    public void testSystemConfigCache() {
        logger.info("=== 测试系统配置缓存 ===");
        
        // 获取系统配置
        String appName = cacheService.getSystemConfig("app.name");
        logger.info("应用名称: {}", appName);
        
        String appVersion = cacheService.getSystemConfig("app.version");
        logger.info("应用版本: {}", appVersion);
        
        // 更新配置
        cacheService.updateSystemConfig("app.name", "更新后的应用名称");
        
        // 再次获取配置
        String updatedAppName = cacheService.getSystemConfig("app.name");
        logger.info("更新后应用名称: {}", updatedAppName);
    }
    
    /**
     * 测试缓存统计
     */
    @Test
    public void testCacheStatistics() {
        logger.info("=== 测试缓存统计 ===");
        
        // 创建一些缓存数据
        createTestUser("stats_user1");
        createTestUser("stats_user2");
        
        // 获取缓存统计
        Map<String, Object> stats = cacheService.getCacheStatistics();
        logger.info("缓存统计信息:");
        stats.forEach((cacheName, cacheStats) -> {
            logger.info("缓存: {} - 统计: {}", cacheName, cacheStats);
        });
    }
    
    /**
     * 测试Redis直接操作
     */
    @Test
    public void testRedisDirectOperation() {
        logger.info("=== 测试Redis直接操作 ===");
        
        // 直接操作Redis
        String key = "test:direct:key";
        String value = "test value";
        
        redisTemplate.opsForValue().set(key, value);
        logger.info("设置Redis值: {} = {}", key, value);
        
        String retrievedValue = (String) redisTemplate.opsForValue().get(key);
        logger.info("获取Redis值: {} = {}", key, retrievedValue);
        
        // 查看所有缓存key
        Set<String> keys = redisTemplate.keys("cache:*");
        logger.info("所有缓存key数量: {}", keys != null ? keys.size() : 0);
        if (keys != null && keys.size() <= 10) {
            keys.forEach(k -> logger.info("缓存key: {}", k));
        }
        
        // 清理测试数据
        redisTemplate.delete(key);
    }
    
    /**
     * 创建测试用户
     */
    private User createTestUser() {
        return createTestUser("test_user_" + System.currentTimeMillis());
    }
    
    /**
     * 创建测试用户
     */
    private User createTestUser(String username) {
        User user = new User();
        user.setUsername(username);
        user.setPassword("123456");
        user.setEmail(username + "@example.com");
        user.setPhone("1380000" + String.format("%04d", (int)(Math.random() * 10000)));
        user.setRealName("测试用户");
        user.setStatus(1);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        
        userService.saveUser(user);
        logger.info("创建测试用户: {} (ID: {})", user.getUsername(), user.getId());
        
        return user;
    }
}
