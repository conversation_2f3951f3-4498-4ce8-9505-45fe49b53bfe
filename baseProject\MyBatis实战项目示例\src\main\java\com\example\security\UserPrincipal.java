package com.example.security;

import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Spring Security用户主体类
 * 实现UserDetails接口，用于Spring Security认证和授权
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class UserPrincipal implements UserDetails {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 用户状态
     */
    private Integer status;
    
    /**
     * 权限列表
     */
    private Collection<? extends GrantedAuthority> authorities;
    
    /**
     * 账户是否未过期
     */
    private boolean accountNonExpired;
    
    /**
     * 账户是否未锁定
     */
    private boolean accountNonLocked;
    
    /**
     * 凭证是否未过期
     */
    private boolean credentialsNonExpired;
    
    /**
     * 账户是否启用
     */
    private boolean enabled;
    
    // 构造方法
    public UserPrincipal(Long id, String username, String password, String email, String realName, 
                        Integer status, Collection<? extends GrantedAuthority> authorities) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.email = email;
        this.realName = realName;
        this.status = status;
        this.authorities = authorities;
        this.accountNonExpired = true;
        this.accountNonLocked = true;
        this.credentialsNonExpired = true;
        this.enabled = (status != null && status == 1);
    }
    
    /**
     * 根据SysUser创建UserPrincipal
     */
    public static UserPrincipal create(SysUser user) {
        // 收集用户的所有权限
        List<GrantedAuthority> authorities = new ArrayList<>();
        
        // 添加角色权限（以ROLE_开头）
        if (user.getRoles() != null) {
            for (SysRole role : user.getRoles()) {
                if (role.isEnabled()) {
                    authorities.add(new SimpleGrantedAuthority(role.getRoleCode()));
                }
            }
        }
        
        // 添加具体权限
        if (user.getPermissions() != null) {
            for (SysPermission permission : user.getPermissions()) {
                if (permission.isEnabled()) {
                    authorities.add(new SimpleGrantedAuthority(permission.getPermissionCode()));
                }
            }
        }
        
        return new UserPrincipal(
            user.getId(),
            user.getUsername(),
            user.getPassword(),
            user.getEmail(),
            user.getRealName(),
            user.getStatus(),
            authorities
        );
    }
    
    // UserDetails接口实现
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    // Getter方法
    public Long getId() {
        return id;
    }
    
    public String getEmail() {
        return email;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    /**
     * 判断用户是否有指定角色
     */
    public boolean hasRole(String roleCode) {
        return authorities.stream()
            .anyMatch(authority -> authority.getAuthority().equals(roleCode));
    }
    
    /**
     * 判断用户是否有指定权限
     */
    public boolean hasPermission(String permissionCode) {
        return authorities.stream()
            .anyMatch(authority -> authority.getAuthority().equals(permissionCode));
    }
    
    /**
     * 判断用户是否有任意一个指定角色
     */
    public boolean hasAnyRole(String... roleCodes) {
        for (String roleCode : roleCodes) {
            if (hasRole(roleCode)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 判断用户是否有任意一个指定权限
     */
    public boolean hasAnyPermission(String... permissionCodes) {
        for (String permissionCode : permissionCodes) {
            if (hasPermission(permissionCode)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public String toString() {
        return "UserPrincipal{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", realName='" + realName + '\'' +
                ", status=" + status +
                ", enabled=" + enabled +
                ", authorities=" + authorities +
                '}';
    }
}
