import thread.*;
import java.util.concurrent.*;

/**
 * 多线程编程测试运行器
 * 用于演示Java多线程编程的各种功能
 */
public class MultiThreadTestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    Java多线程编程演示程序");
        System.out.println("========================================");
        
        try {
            // 1. 基础线程演示
            runDemo("基础线程演示", () -> BasicThreadDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 2. 线程同步演示
            runDemo("线程同步演示", () -> SynchronizationDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 3. 线程池演示
            runDemo("线程池演示", () -> ThreadPoolDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 4. 并发工具类演示
            runDemo("并发工具类演示", () -> ConcurrentUtilsDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 5. 实际应用场景演示
            runDemo("实际应用场景演示", () -> PracticalExamplesDemo.main(new String[]{}));
            
        } catch (Exception e) {
            System.out.println("演示程序出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           演示程序结束");
        System.out.println("=".repeat(50));
        
        showSummary();
    }
    
    /**
     * 运行演示
     */
    private static void runDemo(String demoName, Runnable demo) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           " + demoName);
        System.out.println("=".repeat(50));
        
        try {
            demo.run();
        } catch (Exception e) {
            System.out.println(demoName + "出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示之间的等待
     */
    private static void waitBetweenDemos() {
        try {
            System.out.println("\n等待3秒后继续下一个演示...");
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 显示总结信息
     */
    private static void showSummary() {
        System.out.println("\n演示内容总结:");
        System.out.println("1. 基础线程演示:");
        System.out.println("   - Thread类继承方式");
        System.out.println("   - Runnable接口实现方式");
        System.out.println("   - Callable接口实现方式");
        System.out.println("   - 线程状态和属性");
        
        System.out.println("\n2. 线程同步演示:");
        System.out.println("   - synchronized关键字");
        System.out.println("   - Lock接口和ReentrantLock");
        System.out.println("   - volatile关键字");
        System.out.println("   - 读写锁ReadWriteLock");
        System.out.println("   - 死锁演示和预防");
        
        System.out.println("\n3. 线程池演示:");
        System.out.println("   - 基本线程池类型");
        System.out.println("   - 自定义ThreadPoolExecutor");
        System.out.println("   - 定时任务ScheduledExecutorService");
        System.out.println("   - 线程池监控和最佳实践");
        
        System.out.println("\n4. 并发工具类演示:");
        System.out.println("   - CountDownLatch (等待多个线程完成)");
        System.out.println("   - CyclicBarrier (多个线程相互等待)");
        System.out.println("   - Semaphore (控制资源访问数量)");
        System.out.println("   - AtomicInteger (原子操作)");
        System.out.println("   - wait/notify和Condition (线程通信)");
        
        System.out.println("\n5. 实际应用场景演示:");
        System.out.println("   - 生产者消费者模式");
        System.out.println("   - 线程安全单例模式");
        System.out.println("   - 并发集合ConcurrentHashMap");
        System.out.println("   - 任务分解并行处理");
        
        System.out.println("\n学习建议:");
        System.out.println("1. 先理解基础概念，再学习高级特性");
        System.out.println("2. 重点掌握线程安全和同步机制");
        System.out.println("3. 熟练使用线程池，避免直接创建线程");
        System.out.println("4. 学会使用并发工具类解决实际问题");
        System.out.println("5. 注意性能优化和最佳实践");
        
        System.out.println("\n注意事项:");
        System.out.println("- 多线程程序的输出可能因执行顺序而异");
        System.out.println("- 某些演示可能需要较长时间完成");
        System.out.println("- 建议在IDE中运行以获得更好的调试体验");
        System.out.println("- 实际应用中要考虑异常处理和资源管理");
    }
}

/**
 * 简化版多线程演示
 * 适合快速了解多线程基本概念
 */
class SimpleMultiThreadDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 简化版多线程演示 ===");
        
        // 1. 基本线程创建
        basicThreadCreation();
        
        // 2. 线程同步
        threadSynchronization();
        
        // 3. 线程池使用
        threadPoolUsage();
    }
    
    /**
     * 基本线程创建
     */
    private static void basicThreadCreation() {
        System.out.println("\n1. 基本线程创建:");
        
        // 使用Runnable接口
        Thread thread1 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                System.out.println("线程1 - " + i);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        
        Thread thread2 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                System.out.println("线程2 - " + i);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        });
        
        thread1.start();
        thread2.start();
        
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 线程同步
     */
    private static void threadSynchronization() {
        System.out.println("\n2. 线程同步:");
        
        class Counter {
            private int count = 0;
            
            public synchronized void increment() {
                count++;
                System.out.println(Thread.currentThread().getName() + " - count: " + count);
            }
            
            public int getCount() {
                return count;
            }
        }
        
        Counter counter = new Counter();
        
        Thread[] threads = new Thread[3];
        for (int i = 0; i < threads.length; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < 2; j++) {
                    counter.increment();
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }, "线程-" + i);
        }
        
        for (Thread thread : threads) {
            thread.start();
        }
        
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        System.out.println("最终计数: " + counter.getCount());
    }
    
    /**
     * 线程池使用
     */
    private static void threadPoolUsage() {
        System.out.println("\n3. 线程池使用:");
        
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        for (int i = 1; i <= 4; i++) {
            final int taskId = i;
            executor.submit(() -> {
                System.out.println("任务" + taskId + " 在 " + 
                    Thread.currentThread().getName() + " 中执行");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.out.println("任务" + taskId + " 完成");
            });
        }
        
        executor.shutdown();
        try {
            if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        System.out.println("线程池演示完成");
    }
}
