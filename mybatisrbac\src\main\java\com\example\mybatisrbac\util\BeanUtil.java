package com.example.mybatisrbac.util;

import com.example.mybatisrbac.dto.RoleCreateDTO;
import com.example.mybatisrbac.dto.RoleUpdateDTO;
import com.example.mybatisrbac.dto.UserCreateDTO;
import com.example.mybatisrbac.entity.Role;
import com.example.mybatisrbac.entity.User;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Bean 转换工具类
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
public class BeanUtil {

    /**
     * Role 转 Role（保持原样，不再需要 VO 转换）
     */
    public static Role roleToVO(Role role) {
        return role;
    }

    /**
     * Role 列表转 Role 列表（保持原样，不再需要 VO 转换）
     */
    public static List<Role> roleListToVOList(List<Role> roles) {
        return roles != null ? roles : new ArrayList<>();
    }

    /**
     * RoleCreateDTO 转 Role
     */
    public static Role createDTOToRole(RoleCreateDTO dto) {
        if (dto == null) {
            return null;
        }
        
        Role role = new Role();
        BeanUtils.copyProperties(dto, role);
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        role.setCreateTime(now);
        role.setUpdateTime(now);
        
        // 设置创建人和更新人（实际项目中从当前登录用户获取）
        role.setCreateBy(1L);
        role.setUpdateBy(1L);
        
        return role;
    }

    /**
     * RoleUpdateDTO 更新到 Role
     */
    public static void updateDTOToRole(RoleUpdateDTO dto, Role role) {
        if (dto == null || role == null) {
            return;
        }
        
        // 只更新非空字段
        if (dto.getRoleName() != null) {
            role.setRoleName(dto.getRoleName());
        }
        if (dto.getRoleCode() != null) {
            role.setRoleCode(dto.getRoleCode());
        }
        if (dto.getDescription() != null) {
            role.setDescription(dto.getDescription());
        }
        if (dto.getStatus() != null) {
            role.setStatus(dto.getStatus());
        }
        
        // 设置更新时间和更新人
        role.setUpdateTime(LocalDateTime.now());
        role.setUpdateBy(1L); // 实际项目中从当前登录用户获取
    }

    /**
     * User 转 User（保持原样，不再需要 VO 转换）
     */
    public static User userToVO(User user) {
        return user;
    }

    /**
     * User 列表转 User 列表（保持原样，不再需要 VO 转换）
     */
    public static List<User> userListToVOList(List<User> users) {
        return users != null ? users : new ArrayList<>();
    }

    /**
     * UserCreateDTO 转 User
     */
    public static User createDTOToUser(UserCreateDTO dto) {
        if (dto == null) {
            return null;
        }

        User user = new User();
        BeanUtils.copyProperties(dto, user);

        // 设置创建时间和更新时间
        Date now = new Date();
        user.setCreateTime(now);
        user.setUpdateTime(now);

        // 设置创建人和更新人（实际项目中从当前登录用户获取）
        user.setCreateBy(1L);
        user.setUpdateBy(1L);

        return user;
    }
}
