# Spring Security 权限控制详解

## 📚 目录

1. [权限控制基本原理](#权限控制基本原理)
2. [URL级权限控制](#url级权限控制)
3. [方法级权限控制](#方法级权限控制)
4. [自定义权限评估](#自定义权限评估)
5. [动态权限控制](#动态权限控制)
6. [实际业务场景](#实际业务场景)
7. [权限控制最佳实践](#权限控制最佳实践)

---

## 权限控制基本原理

### 1. RBAC权限模型

Spring Security采用基于角色的访问控制(RBAC)模型：

```
用户(User) ←→ 角色(Role) ←→ 权限(Permission)
```

#### 数据模型设计

```java
// 用户实体
@Entity
public class User {
    @Id
    private Long id;
    private String username;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();
}

// 角色实体
@Entity
public class Role {
    @Id
    private Long id;
    
    @Enumerated(EnumType.STRING)
    private RoleName name; // ROLE_USER, ROLE_ADMIN, ROLE_MODERATOR
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
}

// 权限实体
@Entity
public class Permission {
    @Id
    private Long id;
    private String name; // USER_READ, USER_CREATE, USER_UPDATE, USER_DELETE
    private String description;
}
```

### 2. 权限加载机制

#### UserPrincipal权限加载

```java
public class UserPrincipal implements UserDetails {
    
    public static UserPrincipal create(User user) {
        // 收集用户的所有权限
        List<GrantedAuthority> authorities = user.getRoles().stream()
            .flatMap(role -> {
                List<GrantedAuthority> roleAuthorities = new ArrayList<>();
                
                // 添加角色权限 (以ROLE_开头)
                roleAuthorities.add(new SimpleGrantedAuthority(role.getName().name()));
                
                // 添加具体权限
                role.getPermissions().forEach(permission -> 
                    roleAuthorities.add(new SimpleGrantedAuthority(permission.getName())));
                
                return roleAuthorities.stream();
            })
            .collect(Collectors.toList());
        
        return new UserPrincipal(
            user.getId(),
            user.getUsername(),
            user.getEmail(),
            user.getPassword(),
            authorities, // 这里包含了角色和权限
            user.getEnabled(),
            user.getAccountNonExpired(),
            user.getAccountNonLocked(),
            user.getCredentialsNonExpired()
        );
    }
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities; // 返回用户的所有权限
    }
}
```

---

## URL级权限控制

### 1. SecurityConfig中的URL权限配置

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests(authz -> authz
            // 公开端点 - 任何人都可以访问
            .requestMatchers("/api/auth/**").permitAll()
            .requestMatchers("/api/public/**").permitAll()
            .requestMatchers("/actuator/health").permitAll()
            
            // 基于角色的控制
            .requestMatchers("/api/admin/**").hasRole("ADMIN") // 只有ADMIN角色可以访问
            .requestMatchers("/api/moderator/**").hasAnyRole("ADMIN", "MODERATOR") // ADMIN或MODERATOR角色
            
            // 基于权限的控制
            .requestMatchers(HttpMethod.GET, "/api/users/**").hasAuthority("USER_READ")
            .requestMatchers(HttpMethod.POST, "/api/users/**").hasAuthority("USER_CREATE")
            .requestMatchers(HttpMethod.PUT, "/api/users/**").hasAuthority("USER_UPDATE")
            .requestMatchers(HttpMethod.DELETE, "/api/users/**").hasAuthority("USER_DELETE")
            
            // 复合权限控制
            .requestMatchers("/api/reports/**").hasAnyAuthority("REPORT_READ", "ADMIN_ALL")
            
            // 用户自己的资源访问
            .requestMatchers(HttpMethod.GET, "/api/users/me").hasAnyRole("USER", "ADMIN")
            .requestMatchers(HttpMethod.PUT, "/api/users/me").hasAnyRole("USER", "ADMIN")
            
            // 其他所有请求需要认证
            .anyRequest().authenticated()
        );
        
        return http.build();
    }
}
```

### 2. 权限控制的执行流程

```java
/**
 * Spring Security权限检查流程：
 * 
 * 1. 请求到达 → FilterSecurityInterceptor
 * 2. 获取当前用户的Authentication对象
 * 3. 从Authentication中获取GrantedAuthority集合
 * 4. 根据URL配置检查是否有对应的权限
 * 5. 有权限 → 放行，无权限 → 抛出AccessDeniedException
 */

// 示例：用户访问 GET /api/users/123
// 1. 检查用户是否有 USER_READ 权限
// 2. 如果用户的authorities包含 "USER_READ"，则放行
// 3. 否则返回403 Forbidden
```

---

## 方法级权限控制

### 1. 启用方法级安全

```java
@Configuration
@EnableGlobalMethodSecurity(
    prePostEnabled = true,    // 启用@PreAuthorize和@PostAuthorize
    securedEnabled = true,    // 启用@Secured
    jsr250Enabled = true      // 启用@RolesAllowed
)
public class MethodSecurityConfig {
}
```

### 2. @PreAuthorize - 方法执行前权限检查

```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    // 基本角色检查
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<User> getAllUsers() {
        return userService.getAllUsers();
    }
    
    // 基本权限检查
    @PostMapping
    @PreAuthorize("hasAuthority('USER_CREATE')")
    public User createUser(@RequestBody User user) {
        return userService.createUser(user);
    }
    
    // 多权限检查 - AND关系
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_DELETE') and hasRole('ADMIN')")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
    }
    
    // 多权限检查 - OR关系
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_READ') or hasRole('ADMIN')")
    public User getUser(@PathVariable Long id) {
        return userService.getUserById(id);
    }
    
    // 复杂权限逻辑 - 用户只能访问自己的资源
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('USER_UPDATE') or (#id == authentication.principal.id)")
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        return userService.updateUser(id, user);
    }
    
    // 基于参数的权限检查
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('USER_STATUS_UPDATE') and #status != 'DELETED'")
    public void updateUserStatus(@PathVariable Long id, @RequestParam String status) {
        userService.updateUserStatus(id, status);
    }
    
    // 基于对象属性的权限检查
    @PostMapping("/department/{deptId}/users")
    @PreAuthorize("hasAuthority('USER_CREATE') and @departmentService.isUserDepartmentManager(authentication.principal.id, #deptId)")
    public User createDepartmentUser(@PathVariable Long deptId, @RequestBody User user) {
        return userService.createDepartmentUser(deptId, user);
    }
}
```

### 3. @PostAuthorize - 方法执行后权限检查

```java
@RestController
public class UserController {
    
    // 检查返回结果 - 用户只能查看自己的详细信息
    @GetMapping("/users/{id}/profile")
    @PostAuthorize("hasAuthority('USER_READ') or returnObject.id == authentication.principal.id")
    public UserProfile getUserProfile(@PathVariable Long id) {
        return userService.getUserProfile(id);
    }
    
    // 检查返回集合 - 过滤用户只能看到的数据
    @GetMapping("/users/department/{deptId}")
    @PostAuthorize("hasAuthority('DEPT_USER_READ') or @departmentService.isUserInDepartment(authentication.principal.id, #deptId)")
    public List<User> getDepartmentUsers(@PathVariable Long deptId) {
        return userService.getDepartmentUsers(deptId);
    }
    
    // 基于返回对象的复杂权限检查
    @GetMapping("/orders/{id}")
    @PostAuthorize("hasAuthority('ORDER_READ') or returnObject.userId == authentication.principal.id")
    public Order getOrder(@PathVariable Long id) {
        return orderService.getOrder(id);
    }
}
```

### 4. SpEL表达式详解

```java
// Spring Security支持的SpEL表达式

public class SecurityExpressions {
    
    // 1. 基本权限检查
    @PreAuthorize("hasRole('ADMIN')")                    // 检查角色
    @PreAuthorize("hasAuthority('USER_READ')")           // 检查权限
    @PreAuthorize("hasAnyRole('ADMIN', 'MODERATOR')")    // 检查多个角色
    @PreAuthorize("hasAnyAuthority('READ', 'WRITE')")    // 检查多个权限
    
    // 2. 认证状态检查
    @PreAuthorize("isAuthenticated()")                   // 已认证
    @PreAuthorize("isAnonymous()")                       // 匿名用户
    @PreAuthorize("isRememberMe()")                      // 记住我登录
    @PreAuthorize("isFullyAuthenticated()")              // 完全认证（非记住我）
    
    // 3. 访问当前用户信息
    @PreAuthorize("authentication.name == 'admin'")                    // 用户名检查
    @PreAuthorize("authentication.principal.id == #userId")           // 用户ID检查
    @PreAuthorize("authentication.principal.username == #username")   // 用户名参数检查
    
    // 4. 参数检查
    @PreAuthorize("#user.name == authentication.name")               // 参数对象属性检查
    @PreAuthorize("#id > 0")                                         // 参数值检查
    @PreAuthorize("#user.department == authentication.principal.department") // 部门检查
    
    // 5. 返回值检查（@PostAuthorize）
    @PostAuthorize("returnObject.owner == authentication.name")      // 返回对象属性检查
    @PostAuthorize("returnObject != null")                          // 返回值非空检查
    
    // 6. 调用自定义方法
    @PreAuthorize("@customSecurityService.hasPermission(authentication, #resourceId)")
    @PreAuthorize("@userService.isOwner(authentication.principal.id, #resourceId)")
    
    // 7. 复杂逻辑组合
    @PreAuthorize("(hasRole('ADMIN') and hasAuthority('USER_DELETE')) or " +
                  "(hasRole('MANAGER') and #user.department == authentication.principal.department)")
    
    // 8. 集合操作
    @PreAuthorize("hasRole('ADMIN') or #userIds.contains(authentication.principal.id)")
    
    public void complexMethod() {}
}
```

---

## 自定义权限评估

### 1. 自定义权限评估器

```java
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private ResourceService resourceService;
    
    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        String permissionString = permission.toString();
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        
        // 1. 检查基本权限
        if (hasBasicPermission(userPrincipal, permissionString)) {
            return true;
        }
        
        // 2. 检查资源级权限
        if (targetDomainObject != null) {
            return hasResourcePermission(userPrincipal, targetDomainObject, permissionString);
        }
        
        return false;
    }
    
    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, 
                                String targetType, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        String permissionString = permission.toString();
        
        // 根据资源类型和ID检查权限
        switch (targetType.toLowerCase()) {
            case "user":
                return hasUserPermission(userPrincipal, (Long) targetId, permissionString);
            case "order":
                return hasOrderPermission(userPrincipal, (Long) targetId, permissionString);
            case "document":
                return hasDocumentPermission(userPrincipal, (Long) targetId, permissionString);
            default:
                return false;
        }
    }
    
    private boolean hasBasicPermission(UserPrincipal userPrincipal, String permission) {
        return userPrincipal.getAuthorities().stream()
            .anyMatch(authority -> authority.getAuthority().equals(permission));
    }
    
    private boolean hasResourcePermission(UserPrincipal userPrincipal, Object resource, String permission) {
        // 资源所有者检查
        if (resource instanceof OwnedResource) {
            OwnedResource ownedResource = (OwnedResource) resource;
            if (ownedResource.getOwnerId().equals(userPrincipal.getId())) {
                return true; // 资源所有者拥有所有权限
            }
        }
        
        // 部门资源检查
        if (resource instanceof DepartmentResource) {
            DepartmentResource deptResource = (DepartmentResource) resource;
            User user = userService.getUserById(userPrincipal.getId());
            if (deptResource.getDepartmentId().equals(user.getDepartmentId())) {
                return hasBasicPermission(userPrincipal, permission);
            }
        }
        
        return false;
    }
    
    private boolean hasUserPermission(UserPrincipal userPrincipal, Long userId, String permission) {
        // 用户只能操作自己的资源
        if (userId.equals(userPrincipal.getId())) {
            return true;
        }
        
        // 管理员可以操作所有用户
        if (hasBasicPermission(userPrincipal, "ADMIN_ALL")) {
            return true;
        }
        
        // 部门经理可以操作本部门用户
        if (hasBasicPermission(userPrincipal, "DEPT_MANAGER")) {
            User currentUser = userService.getUserById(userPrincipal.getId());
            User targetUser = userService.getUserById(userId);
            return currentUser.getDepartmentId().equals(targetUser.getDepartmentId());
        }
        
        return hasBasicPermission(userPrincipal, permission);
    }
    
    private boolean hasOrderPermission(UserPrincipal userPrincipal, Long orderId, String permission) {
        Order order = orderService.getOrder(orderId);
        
        // 订单所有者
        if (order.getUserId().equals(userPrincipal.getId())) {
            return true;
        }
        
        // 订单处理员
        if (hasBasicPermission(userPrincipal, "ORDER_PROCESSOR") && 
            order.getStatus().equals("PROCESSING")) {
            return true;
        }
        
        return hasBasicPermission(userPrincipal, permission);
    }
    
    private boolean hasDocumentPermission(UserPrincipal userPrincipal, Long documentId, String permission) {
        Document document = documentService.getDocument(documentId);
        
        // 文档创建者
        if (document.getCreatedBy().equals(userPrincipal.getId())) {
            return true;
        }
        
        // 检查文档共享权限
        DocumentPermission docPermission = documentService.getDocumentPermission(documentId, userPrincipal.getId());
        if (docPermission != null) {
            switch (permission) {
                case "READ":
                    return docPermission.canRead();
                case "WRITE":
                    return docPermission.canWrite();
                case "DELETE":
                    return docPermission.canDelete();
            }
        }
        
        return hasBasicPermission(userPrincipal, permission);
    }
}
```

### 2. 使用自定义权限评估器

```java
@RestController
public class ResourceController {
    
    // 使用hasPermission方法调用自定义权限评估器
    @GetMapping("/users/{id}")
    @PreAuthorize("hasPermission(#id, 'user', 'READ')")
    public User getUser(@PathVariable Long id) {
        return userService.getUserById(id);
    }
    
    @PutMapping("/users/{id}")
    @PreAuthorize("hasPermission(#id, 'user', 'WRITE')")
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        return userService.updateUser(id, user);
    }
    
    @DeleteMapping("/users/{id}")
    @PreAuthorize("hasPermission(#id, 'user', 'DELETE')")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
    }
    
    // 传递对象给权限评估器
    @PostMapping("/documents/{id}/share")
    @PreAuthorize("hasPermission(#document, 'SHARE')")
    public void shareDocument(@PathVariable Long id, @RequestBody Document document) {
        documentService.shareDocument(id, document);
    }
}
```

---

## 动态权限控制

### 1. 基于数据库的动态权限

```java
@Service
public class DynamicPermissionService {

    @Autowired
    private UserPermissionRepository userPermissionRepository;

    @Autowired
    private ResourcePermissionRepository resourcePermissionRepository;

    /**
     * 检查用户对特定资源的权限
     */
    public boolean hasResourcePermission(Long userId, String resourceType, Long resourceId, String action) {
        // 1. 检查用户直接权限
        UserPermission userPermission = userPermissionRepository
            .findByUserIdAndResourceTypeAndResourceIdAndAction(userId, resourceType, resourceId, action);
        if (userPermission != null && userPermission.isGranted()) {
            return true;
        }

        // 2. 检查角色权限
        List<Role> userRoles = getUserRoles(userId);
        for (Role role : userRoles) {
            RolePermission rolePermission = rolePermissionRepository
                .findByRoleIdAndResourceTypeAndResourceIdAndAction(role.getId(), resourceType, resourceId, action);
            if (rolePermission != null && rolePermission.isGranted()) {
                return true;
            }
        }

        // 3. 检查继承权限（如部门、项目组等）
        return checkInheritedPermissions(userId, resourceType, resourceId, action);
    }

    /**
     * 动态分配权限
     */
    @Transactional
    public void grantPermission(Long userId, String resourceType, Long resourceId, String action, Long grantedBy) {
        UserPermission permission = UserPermission.builder()
            .userId(userId)
            .resourceType(resourceType)
            .resourceId(resourceId)
            .action(action)
            .granted(true)
            .grantedBy(grantedBy)
            .grantedAt(LocalDateTime.now())
            .build();

        userPermissionRepository.save(permission);

        // 记录权限变更日志
        logPermissionChange(userId, resourceType, resourceId, action, "GRANTED", grantedBy);
    }

    /**
     * 撤销权限
     */
    @Transactional
    public void revokePermission(Long userId, String resourceType, Long resourceId, String action, Long revokedBy) {
        UserPermission permission = userPermissionRepository
            .findByUserIdAndResourceTypeAndResourceIdAndAction(userId, resourceType, resourceId, action);

        if (permission != null) {
            permission.setGranted(false);
            permission.setRevokedBy(revokedBy);
            permission.setRevokedAt(LocalDateTime.now());
            userPermissionRepository.save(permission);

            // 记录权限变更日志
            logPermissionChange(userId, resourceType, resourceId, action, "REVOKED", revokedBy);
        }
    }

    private boolean checkInheritedPermissions(Long userId, String resourceType, Long resourceId, String action) {
        User user = userService.getUserById(userId);

        // 检查部门权限
        if (user.getDepartmentId() != null) {
            DepartmentPermission deptPermission = departmentPermissionRepository
                .findByDepartmentIdAndResourceTypeAndResourceIdAndAction(
                    user.getDepartmentId(), resourceType, resourceId, action);
            if (deptPermission != null && deptPermission.isGranted()) {
                return true;
            }
        }

        // 检查项目组权限
        List<ProjectMember> projectMemberships = projectMemberRepository.findByUserId(userId);
        for (ProjectMember membership : projectMemberships) {
            ProjectPermission projectPermission = projectPermissionRepository
                .findByProjectIdAndResourceTypeAndResourceIdAndAction(
                    membership.getProjectId(), resourceType, resourceId, action);
            if (projectPermission != null && projectPermission.isGranted()) {
                return true;
            }
        }

        return false;
    }
}
```

### 2. 权限控制的核心机制总结

Spring Security中不同用户不同权限的放行控制主要通过以下几个层面实现：

#### **1. URL级别控制（粗粒度）**
```java
// 在SecurityConfig中配置
.requestMatchers("/api/admin/**").hasRole("ADMIN")           // 只有ADMIN角色可访问
.requestMatchers("/api/user/**").hasAnyRole("USER", "ADMIN") // USER或ADMIN角色可访问
.requestMatchers(HttpMethod.DELETE, "/api/**").hasAuthority("DELETE_PERMISSION") // 需要删除权限
```

#### **2. 方法级别控制（细粒度）**
```java
// 使用注解进行精确控制
@PreAuthorize("hasRole('ADMIN')")                                    // 角色检查
@PreAuthorize("hasAuthority('USER_READ')")                          // 权限检查
@PreAuthorize("#id == authentication.principal.id")                 // 只能操作自己的资源
@PreAuthorize("hasPermission(#id, 'user', 'READ')")                // 自定义权限评估
```

#### **3. 资源级别控制（最细粒度）**
```java
// 通过自定义权限评估器实现
public boolean hasPermission(Authentication auth, Object resource, Object permission) {
    // 检查用户是否有权限访问特定资源
    // 可以基于资源所有权、部门关系、项目成员等进行判断
}
```

#### **4. 动态权限控制**
```java
// 运行时动态分配和检查权限
@PreAuthorize("@dynamicPermissionService.hasResourcePermission(authentication.principal.id, 'DOCUMENT', #id, 'READ')")
public Document getDocument(@PathVariable Long id) {
    // 基于数据库中的权限配置进行动态检查
}
```

这样的多层权限控制机制确保了：
- **安全性**：多重权限检查，防止越权访问
- **灵活性**：支持复杂的业务权限逻辑
- **可维护性**：权限配置清晰，易于管理
- **性能**：通过缓存机制提高权限检查效率

通过这些机制，Spring Security可以实现非常精确和灵活的权限控制，满足各种复杂的业务需求。
```
