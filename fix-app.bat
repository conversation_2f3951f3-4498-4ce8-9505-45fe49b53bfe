@echo off
echo 正在设置环境变量...
set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo 正在创建目录结构...
mkdir target\classes\com\example\demo\controller 2>nul

echo 正在编译Java文件...
javac -encoding UTF-8 -d target\classes -cp "target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.0\spring-boot-starter-web-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.0\spring-boot-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.0\spring-boot-autoconfigure-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.20\spring-web-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.20\spring-beans-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.20\spring-context-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.20\spring-aop-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.20\spring-expression-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.20\spring-core-5.3.20.jar" src\main\java\com\example\demo\DemoApplication.java src\main\java\com\example\demo\controller\HelloController.java

echo 正在检查编译结果...
dir target\classes\com\example\demo\*.class

echo 正在创建清单文件...
echo Manifest-Version: 1.0 > manifest.txt
echo Main-Class: com.example.demo.DemoApplication >> manifest.txt
echo Class-Path: . >> manifest.txt
echo. >> manifest.txt

echo 正在打包应用程序...
jar -cvfm target\demo-app.jar manifest.txt -C target\classes .

echo 正在运行应用程序...
java -cp "target\demo-app.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.0\spring-boot-starter-web-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.0\spring-boot-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.0\spring-boot-autoconfigure-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.20\spring-web-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.20\spring-beans-5.3.20.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.20\spring-context-5.3.20.jar" com.example.demo.DemoApplication 