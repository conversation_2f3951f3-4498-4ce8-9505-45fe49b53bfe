/**
 * 这个程序展示Java中的基本数据类型
 */
public class DataTypes {
    public static void main(String[] args) {
        // 整数类型
        byte byteVar = 100;                  // 8位，范围：-128到127
        short shortVar = 30000;              // 16位，范围：-32,768到32,767
        int intVar = 100000;                 // 32位，范围：-2^31到2^31-1
        long longVar = 9223372036854775807L; // 64位，注意L后缀
        
        // 浮点类型
        float floatVar = 3.14f;              // 32位，注意f后缀
        double doubleVar = 3.14159265359;    // 64位
        
        // 字符类型
        char charVar = '中';                 // 16位Unicode字符
        
        // 布尔类型
        boolean boolVar = true;              // true或false
        
        // 字符串（引用类型，不是基本类型）
        String stringVar = "这是一个字符串";
        
        // 打印所有变量
        System.out.println("byte变量: " + byteVar);
        System.out.println("short变量: " + shortVar);
        System.out.println("int变量: " + intVar);
        System.out.println("long变量: " + longVar);
        System.out.println("float变量: " + floatVar);
        System.out.println("double变量: " + doubleVar);
        System.out.println("char变量: " + charVar);
        System.out.println("boolean变量: " + boolVar);
        System.out.println("String变量: " + stringVar);
    }
} 