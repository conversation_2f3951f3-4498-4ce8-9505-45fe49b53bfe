package com.example.mybatisdemo.dto;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * 用户创建DTO - 用于接收创建用户的请求参数
 */
@Data
public class UserCreateDTO {
    
    /**
     * 用户名 - 必填，长度3-50个字符，只能包含字母、数字和下划线
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;
    
    /**
     * 密码 - 必填，长度6-100个字符
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,}$", 
             message = "密码必须包含至少一个大写字母、一个小写字母和一个数字")
    private String password;
    
    /**
     * 邮箱 - 可选，但格式必须正确
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 手机号 - 可选，但格式必须正确
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 真实姓名 - 可选，长度1-50个字符
     */
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;
    
    /**
     * 昵称 - 可选，长度1-50个字符
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;
    
    /**
     * 年龄 - 可选，范围1-150
     */
    @Min(value = 1, message = "年龄不能小于1岁")
    @Max(value = 150, message = "年龄不能大于150岁")
    private Integer age;
    
    /**
     * 性别 - 可选，0-女，1-男
     */
    @Min(value = 0, message = "性别值不正确，0-女，1-男")
    @Max(value = 1, message = "性别值不正确，0-女，1-男")
    private Integer gender;
    
    /**
     * 生日 - 可选，不能是未来日期
     */
    @Past(message = "生日不能是未来日期")
    private LocalDate birthday;
    
    /**
     * 地址 - 可选，长度不超过200个字符
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 个人简介 - 可选，长度不超过500个字符
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;
    
    /**
     * 头像URL - 可选，长度不超过500个字符
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatar;
}
