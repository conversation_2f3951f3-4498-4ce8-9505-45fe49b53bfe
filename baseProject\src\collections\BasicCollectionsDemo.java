package collections;

import java.util.*;

/**
 * 基础集合演示
 * 展示List、Set、Queue、Map的基本使用
 */
public class BasicCollectionsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java集合框架基础演示 ===");
        
        // 1. List演示
        listDemo();
        
        // 2. Set演示
        setDemo();
        
        // 3. Queue演示
        queueDemo();
        
        // 4. Map演示
        mapDemo();
        
        // 5. 迭代器演示
        iteratorDemo();
    }
    
    /**
     * List集合演示
     */
    public static void listDemo() {
        System.out.println("\n--- List集合演示 ---");
        
        // ArrayList演示
        System.out.println("ArrayList演示:");
        List<String> arrayList = new ArrayList<>();
        arrayList.add("苹果");
        arrayList.add("香蕉");
        arrayList.add("橙子");
        arrayList.add(1, "葡萄"); // 在指定位置插入
        
        System.out.println("ArrayList内容: " + arrayList);
        System.out.println("第一个元素: " + arrayList.get(0));
        System.out.println("列表大小: " + arrayList.size());
        
        // 修改元素
        arrayList.set(0, "草莓");
        System.out.println("修改后: " + arrayList);
        
        // 查找元素
        int index = arrayList.indexOf("香蕉");
        System.out.println("香蕉的索引: " + index);
        
        // LinkedList演示
        System.out.println("\nLinkedList演示:");
        LinkedList<String> linkedList = new LinkedList<>();
        linkedList.add("第一个");
        linkedList.addFirst("新的第一个");
        linkedList.addLast("最后一个");
        
        System.out.println("LinkedList内容: " + linkedList);
        
        // 队列操作
        linkedList.offer("队列元素");
        String head = linkedList.poll();
        System.out.println("出队元素: " + head);
        System.out.println("出队后: " + linkedList);
        
        // 栈操作
        linkedList.push("栈顶元素");
        String top = linkedList.pop();
        System.out.println("出栈元素: " + top);
        System.out.println("出栈后: " + linkedList);
    }
    
    /**
     * Set集合演示
     */
    public static void setDemo() {
        System.out.println("\n--- Set集合演示 ---");
        
        // HashSet演示
        System.out.println("HashSet演示:");
        Set<String> hashSet = new HashSet<>();
        hashSet.add("红色");
        hashSet.add("绿色");
        hashSet.add("蓝色");
        hashSet.add("红色"); // 重复元素，不会被添加
        
        System.out.println("HashSet内容: " + hashSet);
        System.out.println("包含红色: " + hashSet.contains("红色"));
        System.out.println("集合大小: " + hashSet.size());
        
        // LinkedHashSet演示
        System.out.println("\nLinkedHashSet演示:");
        Set<String> linkedHashSet = new LinkedHashSet<>();
        linkedHashSet.add("第一个");
        linkedHashSet.add("第二个");
        linkedHashSet.add("第三个");
        linkedHashSet.add("第一个"); // 重复，不会添加
        
        System.out.println("LinkedHashSet内容: " + linkedHashSet); // 保持插入顺序
        
        // TreeSet演示
        System.out.println("\nTreeSet演示:");
        Set<Integer> treeSet = new TreeSet<>();
        treeSet.add(5);
        treeSet.add(2);
        treeSet.add(8);
        treeSet.add(1);
        treeSet.add(9);
        
        System.out.println("TreeSet内容: " + treeSet); // 自动排序
        
        // TreeSet的范围操作
        TreeSet<Integer> tree = (TreeSet<Integer>) treeSet;
        System.out.println("2到8之间的元素: " + tree.subSet(2, 9));
        System.out.println("最小值: " + tree.first());
        System.out.println("最大值: " + tree.last());
    }
    
    /**
     * Queue集合演示
     */
    public static void queueDemo() {
        System.out.println("\n--- Queue集合演示 ---");
        
        // ArrayDeque演示
        System.out.println("ArrayDeque演示:");
        Deque<String> deque = new ArrayDeque<>();
        
        // 队列操作
        deque.offer("队列1");
        deque.offer("队列2");
        deque.offer("队列3");
        System.out.println("队列内容: " + deque);
        
        String first = deque.poll();
        System.out.println("出队: " + first);
        System.out.println("出队后: " + deque);
        
        // 双端队列操作
        deque.offerFirst("头部元素");
        deque.offerLast("尾部元素");
        System.out.println("双端操作后: " + deque);
        
        // PriorityQueue演示
        System.out.println("\nPriorityQueue演示:");
        PriorityQueue<Integer> priorityQueue = new PriorityQueue<>();
        priorityQueue.offer(5);
        priorityQueue.offer(2);
        priorityQueue.offer(8);
        priorityQueue.offer(1);
        
        System.out.println("优先队列内容: " + priorityQueue);
        
        // 按优先级出队
        while (!priorityQueue.isEmpty()) {
            System.out.println("出队: " + priorityQueue.poll());
        }
        
        // 自定义比较器的优先队列
        PriorityQueue<String> stringPQ = new PriorityQueue<>(
            Comparator.comparing(String::length).reversed()
        );
        stringPQ.offer("a");
        stringPQ.offer("abc");
        stringPQ.offer("ab");
        
        System.out.println("按长度排序的优先队列:");
        while (!stringPQ.isEmpty()) {
            System.out.println("出队: " + stringPQ.poll());
        }
    }
    
    /**
     * Map集合演示
     */
    public static void mapDemo() {
        System.out.println("\n--- Map集合演示 ---");
        
        // HashMap演示
        System.out.println("HashMap演示:");
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("苹果", 10);
        hashMap.put("香蕉", 20);
        hashMap.put("橙子", 15);
        hashMap.put("苹果", 12); // 更新值
        
        System.out.println("HashMap内容: " + hashMap);
        System.out.println("苹果的数量: " + hashMap.get("苹果"));
        System.out.println("葡萄的数量: " + hashMap.getOrDefault("葡萄", 0));
        
        // 检查键和值
        System.out.println("包含苹果: " + hashMap.containsKey("苹果"));
        System.out.println("包含数量15: " + hashMap.containsValue(15));
        
        // 遍历Map
        System.out.println("遍历键值对:");
        for (Map.Entry<String, Integer> entry : hashMap.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }
        
        // LinkedHashMap演示
        System.out.println("\nLinkedHashMap演示:");
        Map<String, String> linkedHashMap = new LinkedHashMap<>();
        linkedHashMap.put("第一", "First");
        linkedHashMap.put("第二", "Second");
        linkedHashMap.put("第三", "Third");
        
        System.out.println("LinkedHashMap内容: " + linkedHashMap); // 保持插入顺序
        
        // TreeMap演示
        System.out.println("\nTreeMap演示:");
        Map<String, Integer> treeMap = new TreeMap<>();
        treeMap.put("Charlie", 25);
        treeMap.put("Alice", 30);
        treeMap.put("Bob", 20);
        treeMap.put("David", 35);
        
        System.out.println("TreeMap内容: " + treeMap); // 按键排序
        
        // TreeMap的范围操作
        TreeMap<String, Integer> tree = (TreeMap<String, Integer>) treeMap;
        System.out.println("A到C之间的条目: " + tree.subMap("A", "D"));
        System.out.println("第一个键: " + tree.firstKey());
        System.out.println("最后一个键: " + tree.lastKey());
    }
    
    /**
     * 迭代器演示
     */
    public static void iteratorDemo() {
        System.out.println("\n--- 迭代器演示 ---");
        
        List<String> list = new ArrayList<>();
        list.add("元素1");
        list.add("元素2");
        list.add("元素3");
        list.add("要删除的元素");
        list.add("元素4");
        
        System.out.println("原始列表: " + list);
        
        // 使用Iterator安全删除元素
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()) {
            String element = iterator.next();
            if (element.contains("删除")) {
                iterator.remove(); // 安全删除
                System.out.println("删除了: " + element);
            }
        }
        
        System.out.println("删除后列表: " + list);
        
        // ListIterator双向遍历
        System.out.println("\nListIterator双向遍历:");
        ListIterator<String> listIterator = list.listIterator();
        
        // 向前遍历
        System.out.println("向前遍历:");
        while (listIterator.hasNext()) {
            System.out.println("  " + listIterator.next());
        }
        
        // 向后遍历
        System.out.println("向后遍历:");
        while (listIterator.hasPrevious()) {
            System.out.println("  " + listIterator.previous());
        }
        
        // 增强for循环
        System.out.println("\n增强for循环遍历:");
        for (String element : list) {
            System.out.println("  " + element);
        }
    }
}

/**
 * 集合工具类演示
 */
class CollectionUtilsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 集合工具类演示 ===");
        
        collectionsDemo();
        arraysDemo();
    }
    
    /**
     * Collections工具类演示
     */
    public static void collectionsDemo() {
        System.out.println("\n--- Collections工具类演示 ---");
        
        List<Integer> numbers = new ArrayList<>();
        numbers.add(3);
        numbers.add(1);
        numbers.add(4);
        numbers.add(1);
        numbers.add(5);
        
        System.out.println("原始列表: " + numbers);
        
        // 排序
        Collections.sort(numbers);
        System.out.println("升序排序: " + numbers);
        
        Collections.sort(numbers, Collections.reverseOrder());
        System.out.println("降序排序: " + numbers);
        
        // 查找
        Collections.sort(numbers); // 二分查找需要有序列表
        int index = Collections.binarySearch(numbers, 4);
        System.out.println("元素4的索引: " + index);
        
        // 最值
        Integer max = Collections.max(numbers);
        Integer min = Collections.min(numbers);
        System.out.println("最大值: " + max + ", 最小值: " + min);
        
        // 反转和打乱
        Collections.reverse(numbers);
        System.out.println("反转后: " + numbers);
        
        Collections.shuffle(numbers);
        System.out.println("打乱后: " + numbers);
        
        // 填充
        Collections.fill(numbers, 0);
        System.out.println("填充0后: " + numbers);
        
        // 创建不可变集合
        List<String> immutableList = Collections.unmodifiableList(
            Arrays.asList("A", "B", "C")
        );
        System.out.println("不可变列表: " + immutableList);
        
        // 创建同步集合
        List<String> syncList = Collections.synchronizedList(new ArrayList<>());
        System.out.println("同步列表创建成功");
    }
    
    /**
     * Arrays工具类演示
     */
    public static void arraysDemo() {
        System.out.println("\n--- Arrays工具类演示 ---");
        
        // 数组转List
        String[] array = {"红", "绿", "蓝"};
        List<String> list = Arrays.asList(array);
        System.out.println("数组转List: " + list);
        
        // List转数组
        String[] newArray = list.toArray(new String[0]);
        System.out.println("List转数组: " + Arrays.toString(newArray));
        
        // 数组操作
        int[] nums = {3, 1, 4, 1, 5, 9, 2, 6};
        System.out.println("原始数组: " + Arrays.toString(nums));
        
        Arrays.sort(nums);
        System.out.println("排序后: " + Arrays.toString(nums));
        
        int index = Arrays.binarySearch(nums, 5);
        System.out.println("元素5的索引: " + index);
        
        Arrays.fill(nums, 0);
        System.out.println("填充0后: " + Arrays.toString(nums));
    }
}
