package com.example.controller;

import com.example.entity.SysUser;
import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import com.example.service.SysUserService;
import com.example.vo.ApiResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统用户控制器
 * 处理用户管理相关的HTTP请求
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/system/users")
public class SysUserController {
    
    private static final Logger logger = LoggerFactory.getLogger(SysUserController.class);
    
    @Autowired
    private SysUserService userService;
    
    /**
     * 查询用户列表
     */
    @GetMapping
    @RequiresPermissions("system:user:view")
    public ApiResponse<List<SysUser>> getUserList() {
        try {
            List<SysUser> users = userService.getAllUsers();
            return ApiResponse.success("查询用户列表成功", users);
        } catch (Exception e) {
            logger.error("查询用户列表失败", e);
            return ApiResponse.error("查询用户列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询用户详情
     */
    @GetMapping("/{id}")
    @RequiresPermissions("system:user:view")
    public ApiResponse<SysUser> getUserById(@PathVariable Long id) {
        try {
            SysUser user = userService.getUserById(id);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            return ApiResponse.success("查询用户详情成功", user);
        } catch (Exception e) {
            logger.error("查询用户详情失败: id={}", id, e);
            return ApiResponse.error("查询用户详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户详细信息（包含角色和权限）
     */
    @GetMapping("/{id}/details")
    @RequiresPermissions("system:user:view")
    public ApiResponse<SysUser> getUserDetails(@PathVariable Long id) {
        try {
            SysUser user = userService.getUserWithRolesAndPermissions(id);
            if (user == null) {
                return ApiResponse.error("用户不存在");
            }
            return ApiResponse.success("查询用户详细信息成功", user);
        } catch (Exception e) {
            logger.error("查询用户详细信息失败: id={}", id, e);
            return ApiResponse.error("查询用户详细信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    @RequiresPermissions("system:user:add")
    public ApiResponse<SysUser> createUser(@RequestBody SysUser user) {
        try {
            boolean success = userService.createUser(user);
            if (success) {
                return ApiResponse.success("用户创建成功", user);
            } else {
                return ApiResponse.error("用户创建失败");
            }
        } catch (Exception e) {
            logger.error("创建用户失败: {}", user.getUsername(), e);
            return ApiResponse.error("用户创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @RequiresPermissions("system:user:edit")
    public ApiResponse<SysUser> updateUser(@PathVariable Long id, @RequestBody SysUser user) {
        try {
            user.setId(id);
            boolean success = userService.updateUser(user);
            if (success) {
                return ApiResponse.success("用户更新成功", user);
            } else {
                return ApiResponse.error("用户更新失败");
            }
        } catch (Exception e) {
            logger.error("更新用户失败: id={}", id, e);
            return ApiResponse.error("用户更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @RequiresPermissions("system:user:delete")
    public ApiResponse<Void> deleteUser(@PathVariable Long id) {
        try {
            boolean success = userService.deleteUser(id);
            if (success) {
                return ApiResponse.success("用户删除成功");
            } else {
                return ApiResponse.error("用户删除失败");
            }
        } catch (Exception e) {
            logger.error("删除用户失败: id={}", id, e);
            return ApiResponse.error("用户删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 启用/禁用用户
     */
    @PutMapping("/{id}/status")
    @RequiresPermissions("system:user:edit")
    public ApiResponse<Void> changeUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            boolean success = userService.changeUserStatus(id, status);
            if (success) {
                String action = status == 1 ? "启用" : "禁用";
                return ApiResponse.success("用户" + action + "成功");
            } else {
                return ApiResponse.error("用户状态修改失败");
            }
        } catch (Exception e) {
            logger.error("修改用户状态失败: id={}, status={}", id, status, e);
            return ApiResponse.error("用户状态修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/password/reset")
    @RequiresPermissions("system:user:edit")
    public ApiResponse<Void> resetPassword(@PathVariable Long id, @RequestBody ResetPasswordRequest request) {
        try {
            boolean success = userService.resetPassword(id, request.getNewPassword());
            if (success) {
                return ApiResponse.success("密码重置成功");
            } else {
                return ApiResponse.error("密码重置失败");
            }
        } catch (Exception e) {
            logger.error("重置用户密码失败: id={}", id, e);
            return ApiResponse.error("密码重置失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户的角色列表
     */
    @GetMapping("/{id}/roles")
    @RequiresPermissions("system:user:view")
    public ApiResponse<List<SysRole>> getUserRoles(@PathVariable Long id) {
        try {
            List<SysRole> roles = userService.getUserRoles(id);
            return ApiResponse.success("查询用户角色成功", roles);
        } catch (Exception e) {
            logger.error("查询用户角色失败: id={}", id, e);
            return ApiResponse.error("查询用户角色失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户的权限列表
     */
    @GetMapping("/{id}/permissions")
    @RequiresPermissions("system:user:view")
    public ApiResponse<List<SysPermission>> getUserPermissions(@PathVariable Long id) {
        try {
            List<SysPermission> permissions = userService.getUserPermissions(id);
            return ApiResponse.success("查询用户权限成功", permissions);
        } catch (Exception e) {
            logger.error("查询用户权限失败: id={}", id, e);
            return ApiResponse.error("查询用户权限失败: " + e.getMessage());
        }
    }
    
    /**
     * 为用户分配角色
     */
    @PutMapping("/{id}/roles")
    @RequiresPermissions("system:user:edit")
    public ApiResponse<Void> assignRoles(@PathVariable Long id, @RequestBody AssignRolesRequest request) {
        try {
            boolean success = userService.assignRolesToUser(id, request.getRoleIds());
            if (success) {
                return ApiResponse.success("角色分配成功");
            } else {
                return ApiResponse.error("角色分配失败");
            }
        } catch (Exception e) {
            logger.error("分配用户角色失败: id={}, roleIds={}", id, request.getRoleIds(), e);
            return ApiResponse.error("角色分配失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check/username")
    @RequiresPermissions("system:user:view")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.isUsernameExists(username);
            return ApiResponse.success("检查用户名成功", exists);
        } catch (Exception e) {
            logger.error("检查用户名失败: username={}", username, e);
            return ApiResponse.error("检查用户名失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check/email")
    @RequiresPermissions("system:user:view")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.isEmailExists(email);
            return ApiResponse.success("检查邮箱成功", exists);
        } catch (Exception e) {
            logger.error("检查邮箱失败: email={}", email, e);
            return ApiResponse.error("检查邮箱失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置密码请求对象
     */
    public static class ResetPasswordRequest {
        private String newPassword;
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
    
    /**
     * 分配角色请求对象
     */
    public static class AssignRolesRequest {
        private List<Long> roleIds;
        
        public List<Long> getRoleIds() {
            return roleIds;
        }
        
        public void setRoleIds(List<Long> roleIds) {
            this.roleIds = roleIds;
        }
    }
}
