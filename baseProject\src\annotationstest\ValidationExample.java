package annotationstest;
import annotationstest.AnnotationTestDemo.User;

public class ValidationExample {

    public static void main(String[] args) {
        // 创建用户对象
        User user1 = new User();
        user1.setUsername("john");
        user1.setEmail("invalid-email");
        user1.setPhone("12345");
        user1.setIdCard("123");

        User user2 = new User();
        user2.setUsername("alice");
        user2.setEmail("<EMAIL>");
        user2.setPhone("13800138000");
        user2.setIdCard("110101199001011234");

        // 验证用户1
        System.out.println("验证用户1:");
        ValidationResult result1 = AnnotationValidator.validate(user1);
        if (result1.isValid()) {
            System.out.println("验证通过");
        } else {
            System.out.println("验证失败:");
            for (String error : result1.getErrors()) {
                System.out.println("  " + error);
            }
        }

        // 验证用户2
        System.out.println("\n验证用户2:");
        ValidationResult result2 = AnnotationValidator.validate(user2);
        if (result2.isValid()) {
            System.out.println("验证通过");
        } else {
            System.out.println("验证失败:");
            for (String error : result2.getErrors()) {
                System.out.println("  " + error);
            }
        }
    }
}