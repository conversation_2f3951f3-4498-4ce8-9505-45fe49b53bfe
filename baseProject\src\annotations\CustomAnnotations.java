package annotations;

import java.lang.annotation.*;

/**
 * 自定义注解定义
 * 包含各种类型的自定义注解示例
 */
public class CustomAnnotations {
    
    /**
     * 验证注解
     * 用于字段验证
     */
    @Target({ElementType.FIELD, ElementType.PARAMETER})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Validate {
        
        /**
         * 验证类型
         */
        ValidationType type() default ValidationType.NOT_NULL;
        
        /**
         * 最小长度
         */
        int minLength() default 0;
        
        /**
         * 最大长度
         */
        int maxLength() default Integer.MAX_VALUE;
        
        /**
         * 正则表达式
         */
        String pattern() default "";
        
        /**
         * 错误消息
         */
        String message() default "验证失败";
        
        /**
         * 是否必填
         */
        boolean required() default true;
    }
    
    /**
     * 验证类型枚举
     */
    public enum ValidationType {
        NOT_NULL,       // 非空
        EMAIL,          // 邮箱
        PHONE,          // 手机号
        ID_CARD,        // 身份证
        CUSTOM          // 自定义正则
    }
    
    /**
     * 实体注解
     * 用于标记实体类
     */
    @Target(ElementType.TYPE)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Entity {
        
        /**
         * 实体名称
         */
        String name() default "";
        
        /**
         * 数据库表名
         */
        String tableName() default "";
        
        /**
         * 描述
         */
        String description() default "";
    }
    
    /**
     * 列注解
     * 用于标记实体字段对应的数据库列
     */
    @Target(ElementType.FIELD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Column {
        
        /**
         * 列名
         */
        String name() default "";
        
        /**
         * 列类型
         */
        String type() default "VARCHAR";
        
        /**
         * 列长度
         */
        int length() default 255;
        
        /**
         * 是否可为空
         */
        boolean nullable() default true;
        
        /**
         * 是否唯一
         */
        boolean unique() default false;
        
        /**
         * 默认值
         */
        String defaultValue() default "";
    }
    
    /**
     * 日志注解
     * 用于方法执行日志记录
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Log {
        
        /**
         * 操作描述
         */
        String value() default "";
        
        /**
         * 日志级别
         */
        LogLevel level() default LogLevel.INFO;
        
        /**
         * 是否记录参数
         */
        boolean logArgs() default true;
        
        /**
         * 是否记录返回值
         */
        boolean logResult() default true;
        
        /**
         * 是否记录执行时间
         */
        boolean logTime() default true;
    }
    
    /**
     * 日志级别枚举
     */
    public enum LogLevel {
        DEBUG, INFO, WARN, ERROR
    }
    
    /**
     * 缓存注解
     * 用于方法结果缓存
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Cache {
        
        /**
         * 缓存名称
         */
        String name();
        
        /**
         * 缓存键
         */
        String key() default "";
        
        /**
         * 过期时间（秒）
         */
        int expireTime() default 300;
        
        /**
         * 缓存条件
         */
        String condition() default "";
    }
    
    /**
     * 权限注解
     * 用于方法权限控制
     */
    @Target({ElementType.METHOD, ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface RequirePermission {
        
        /**
         * 需要的权限
         */
        String[] value();
        
        /**
         * 权限关系（AND/OR）
         */
        PermissionType type() default PermissionType.AND;
        
        /**
         * 错误消息
         */
        String message() default "权限不足";
    }
    
    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        AND, OR
    }
    
    /**
     * API版本注解
     * 用于API版本控制
     */
    @Target({ElementType.METHOD, ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface ApiVersion {
        
        /**
         * API版本号
         */
        String value();
        
        /**
         * 是否已废弃
         */
        boolean deprecated() default false;
        
        /**
         * 废弃说明
         */
        String deprecatedMessage() default "";
        
        /**
         * 最小支持版本
         */
        String minVersion() default "1.0";
    }
    
    /**
     * 限流注解
     * 用于接口限流
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface RateLimit {
        
        /**
         * 限流键
         */
        String key() default "";
        
        /**
         * 时间窗口（秒）
         */
        int window() default 60;
        
        /**
         * 最大请求数
         */
        int maxRequests() default 100;
        
        /**
         * 限流类型
         */
        RateLimitType type() default RateLimitType.IP;
        
        /**
         * 错误消息
         */
        String message() default "请求过于频繁，请稍后再试";
    }
    
    /**
     * 限流类型枚举
     */
    public enum RateLimitType {
        IP,         // 按IP限流
        USER,       // 按用户限流
        GLOBAL,     // 全局限流
        CUSTOM      // 自定义限流
    }
    
    /**
     * 重试注解
     * 用于方法重试
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Retry {
        
        /**
         * 最大重试次数
         */
        int maxAttempts() default 3;
        
        /**
         * 重试间隔（毫秒）
         */
        long delay() default 1000;
        
        /**
         * 重试的异常类型
         */
        Class<? extends Throwable>[] retryFor() default {Exception.class};
        
        /**
         * 不重试的异常类型
         */
        Class<? extends Throwable>[] noRetryFor() default {};
    }
    
    /**
     * 异步注解
     * 用于异步方法执行
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Async {
        
        /**
         * 线程池名称
         */
        String executor() default "";
        
        /**
         * 超时时间（毫秒）
         */
        long timeout() default 0;
    }
    
    /**
     * 定时任务注解
     * 用于定时任务调度
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface Scheduled {
        
        /**
         * Cron表达式
         */
        String cron() default "";
        
        /**
         * 固定延迟（毫秒）
         */
        long fixedDelay() default -1;
        
        /**
         * 固定频率（毫秒）
         */
        long fixedRate() default -1;
        
        /**
         * 初始延迟（毫秒）
         */
        long initialDelay() default 0;
        
        /**
         * 时区
         */
        String zone() default "";
    }
}
