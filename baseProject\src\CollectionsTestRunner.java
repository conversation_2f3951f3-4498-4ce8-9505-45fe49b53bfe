import collections.*;
import java.util.*;

/**
 * 集合框架测试运行器
 * 用于演示Java集合框架的各种功能
 */
public class CollectionsTestRunner {
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("    Java集合框架演示程序");
        System.out.println("========================================");
        
        try {
            // 1. 基础集合演示
            runDemo("基础集合演示", () -> BasicCollectionsDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 2. 高级集合演示
            runDemo("高级集合演示", () -> AdvancedCollectionsDemo.main(new String[]{}));
            
            waitBetweenDemos();
            
            // 3. 实际应用演示
            runDemo("实际应用演示", () -> PracticalCollectionsDemo.main(new String[]{}));
            
        } catch (Exception e) {
            System.out.println("演示程序出错: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           演示程序结束");
        System.out.println("=".repeat(50));
        
        showSummary();
    }
    
    /**
     * 运行演示
     */
    private static void runDemo(String demoName, Runnable demo) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("           " + demoName);
        System.out.println("=".repeat(50));
        
        try {
            demo.run();
        } catch (Exception e) {
            System.out.println(demoName + "出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示之间的等待
     */
    private static void waitBetweenDemos() {
        try {
            System.out.println("\n等待2秒后继续下一个演示...");
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 显示总结信息
     */
    private static void showSummary() {
        System.out.println("\n演示内容总结:");
        
        System.out.println("\n1. 基础集合演示:");
        System.out.println("   - List接口: ArrayList、LinkedList");
        System.out.println("   - Set接口: HashSet、LinkedHashSet、TreeSet");
        System.out.println("   - Queue接口: ArrayDeque、PriorityQueue");
        System.out.println("   - Map接口: HashMap、LinkedHashMap、TreeMap");
        System.out.println("   - Iterator和ListIterator的使用");
        
        System.out.println("\n2. 高级集合演示:");
        System.out.println("   - 自定义排序和Comparator");
        System.out.println("   - 集合的交集、并集、差集操作");
        System.out.println("   - Stream API与集合的结合使用");
        System.out.println("   - 性能比较和选择指南");
        System.out.println("   - 集合使用最佳实践");
        
        System.out.println("\n3. 实际应用演示:");
        System.out.println("   - 学生管理系统（Map + List + Set）");
        System.out.println("   - 购物车系统（LinkedHashMap）");
        System.out.println("   - 单词统计（HashMap + Stream）");
        System.out.println("   - 数据分析（Collectors + 分组统计）");
        
        System.out.println("\n注意: 集合工具类和陷阱演示包含在基础和高级演示中");
        
        System.out.println("\n核心知识点:");
        System.out.println("• Collection体系: List、Set、Queue");
        System.out.println("• Map体系: HashMap、TreeMap、LinkedHashMap");
        System.out.println("• 时间复杂度: O(1)、O(log n)、O(n)");
        System.out.println("• 选择原则: 根据使用场景选择合适的集合");
        System.out.println("• 线程安全: 同步集合 vs 并发集合");
        
        System.out.println("\n选择指南:");
        System.out.println("• 需要索引访问 → ArrayList");
        System.out.println("• 频繁插入删除 → LinkedList");
        System.out.println("• 需要唯一性 → HashSet/TreeSet");
        System.out.println("• 需要排序 → TreeSet/TreeMap");
        System.out.println("• 需要键值映射 → HashMap/TreeMap");
        System.out.println("• 需要保持顺序 → LinkedHashSet/LinkedHashMap");
        
        System.out.println("\n最佳实践:");
        System.out.println("1. 根据需求选择合适的集合类型");
        System.out.println("2. 设置合适的初始容量");
        System.out.println("3. 正确实现equals和hashCode");
        System.out.println("4. 避免在迭代时修改集合");
        System.out.println("5. 使用泛型确保类型安全");
        System.out.println("6. 考虑线程安全问题");
        System.out.println("7. 使用Stream API进行函数式编程");
        
        System.out.println("\n注意事项:");
        System.out.println("- 集合框架是Java编程的基础，需要熟练掌握");
        System.out.println("- 不同集合的性能特点不同，要根据场景选择");
        System.out.println("- 注意equals和hashCode的正确实现");
        System.out.println("- 在多线程环境下要考虑线程安全问题");
        System.out.println("- 建议结合IDE调试功能深入理解集合的内部结构");
    }
}

/**
 * 简化版集合演示
 * 适合快速了解集合基本概念
 */
class SimpleCollectionsDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 简化版集合演示 ===");
        
        // 1. List基本操作
        listBasics();
        
        // 2. Set基本操作
        setBasics();
        
        // 3. Map基本操作
        mapBasics();
    }
    
    /**
     * List基本操作
     */
    private static void listBasics() {
        System.out.println("\n1. List基本操作:");
        
        List<String> list = new ArrayList<>();
        list.add("苹果");
        list.add("香蕉");
        list.add("橙子");
        
        System.out.println("列表内容: " + list);
        System.out.println("第一个元素: " + list.get(0));
        System.out.println("列表大小: " + list.size());
        
        // 遍历
        System.out.println("遍历列表:");
        for (String fruit : list) {
            System.out.println("  " + fruit);
        }
    }
    
    /**
     * Set基本操作
     */
    private static void setBasics() {
        System.out.println("\n2. Set基本操作:");
        
        Set<String> set = new HashSet<>();
        set.add("红色");
        set.add("绿色");
        set.add("蓝色");
        set.add("红色"); // 重复元素不会被添加
        
        System.out.println("集合内容: " + set);
        System.out.println("集合大小: " + set.size());
        System.out.println("包含红色: " + set.contains("红色"));
    }
    
    /**
     * Map基本操作
     */
    private static void mapBasics() {
        System.out.println("\n3. Map基本操作:");
        
        Map<String, Integer> map = new HashMap<>();
        map.put("苹果", 10);
        map.put("香蕉", 20);
        map.put("橙子", 15);
        
        System.out.println("映射内容: " + map);
        System.out.println("苹果的数量: " + map.get("苹果"));
        
        // 遍历
        System.out.println("遍历映射:");
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }
    }
}

/**
 * 集合性能测试
 */
class CollectionPerformanceTest {
    
    public static void main(String[] args) {
        System.out.println("=== 集合性能测试 ===");
        
        int size = 100000;
        System.out.println("测试数据量: " + size);
        
        testListPerformance(size);
        testSetPerformance(size);
        testMapPerformance(size);
    }
    
    private static void testListPerformance(int size) {
        System.out.println("\nList性能测试:");
        
        // ArrayList添加测试
        long start = System.currentTimeMillis();
        List<Integer> arrayList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            arrayList.add(i);
        }
        long arrayListTime = System.currentTimeMillis() - start;
        
        // LinkedList添加测试
        start = System.currentTimeMillis();
        List<Integer> linkedList = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            linkedList.add(i);
        }
        long linkedListTime = System.currentTimeMillis() - start;
        
        System.out.println("ArrayList添加: " + arrayListTime + "ms");
        System.out.println("LinkedList添加: " + linkedListTime + "ms");
    }
    
    private static void testSetPerformance(int size) {
        System.out.println("\nSet性能测试:");
        
        // HashSet添加测试
        long start = System.currentTimeMillis();
        Set<Integer> hashSet = new HashSet<>();
        for (int i = 0; i < size; i++) {
            hashSet.add(i);
        }
        long hashSetTime = System.currentTimeMillis() - start;
        
        // TreeSet添加测试
        start = System.currentTimeMillis();
        Set<Integer> treeSet = new TreeSet<>();
        for (int i = 0; i < size; i++) {
            treeSet.add(i);
        }
        long treeSetTime = System.currentTimeMillis() - start;
        
        System.out.println("HashSet添加: " + hashSetTime + "ms");
        System.out.println("TreeSet添加: " + treeSetTime + "ms");
    }
    
    private static void testMapPerformance(int size) {
        System.out.println("\nMap性能测试:");
        
        // HashMap添加测试
        long start = System.currentTimeMillis();
        Map<Integer, String> hashMap = new HashMap<>();
        for (int i = 0; i < size; i++) {
            hashMap.put(i, "Value" + i);
        }
        long hashMapTime = System.currentTimeMillis() - start;
        
        // TreeMap添加测试
        start = System.currentTimeMillis();
        Map<Integer, String> treeMap = new TreeMap<>();
        for (int i = 0; i < size; i++) {
            treeMap.put(i, "Value" + i);
        }
        long treeMapTime = System.currentTimeMillis() - start;
        
        System.out.println("HashMap添加: " + hashMapTime + "ms");
        System.out.println("TreeMap添加: " + treeMapTime + "ms");
    }
}
