package com.example;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

/**
 * RBAC权限管理系统启动类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@SpringBootApplication
@EnableWebSecurity
@MapperScan("com.example.mapper")
public class RbacApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(RbacApplication.class);
    
    public static void main(String[] args) {
        logger.info("正在启动RBAC权限管理系统...");
        
        SpringApplication.run(RbacApplication.class, args);
        
        logger.info("RBAC权限管理系统启动完成！");
        logger.info("访问地址: http://localhost:8080");
        logger.info("API文档: http://localhost:8080/swagger-ui.html");
        logger.info("健康检查: http://localhost:8080/actuator/health");
        
        // 输出默认用户信息
        logger.info("=== 默认用户信息 ===");
        logger.info("管理员 - 用户名: admin, 密码: 123456");
        logger.info("经理 - 用户名: manager, 密码: 123456");
        logger.info("用户 - 用户名: user, 密码: 123456");
        logger.info("==================");
    }
}
