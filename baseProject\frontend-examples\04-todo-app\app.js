// 待办事项应用 - 主要功能文件

// ===== 应用状态管理 =====
class TodoApp {
    constructor() {
        this.tasks = [];
        this.currentFilter = 'all';
        this.currentSort = 'created';
        this.sortOrder = 'desc';
        this.searchQuery = '';
        this.selectedTasks = new Set();
        this.editingTask = null;
        
        this.init();
    }
    
    // 初始化应用
    init() {
        this.loadFromStorage();
        this.bindEvents();
        this.render();
        this.updateStats();
        
        console.log('📝 待办事项应用初始化完成！');
    }
    
    // 绑定事件监听器
    bindEvents() {
        // 添加任务
        document.getElementById('add-task-btn').addEventListener('click', () => this.addTask());
        document.getElementById('task-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addTask();
        });
        
        // 快速添加按钮
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskText = e.target.dataset.task;
                document.getElementById('task-input').value = taskText;
                this.addTask();
            });
        });
        
        // 过滤器
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });
        
        // 搜索
        document.getElementById('search-input').addEventListener('input', (e) => {
            this.setSearch(e.target.value);
        });
        
        document.getElementById('clear-search').addEventListener('click', () => {
            document.getElementById('search-input').value = '';
            this.setSearch('');
        });
        
        // 排序
        document.getElementById('sort-select').addEventListener('change', (e) => {
            this.setSort(e.target.value);
        });
        
        document.getElementById('sort-order').addEventListener('click', () => {
            this.toggleSortOrder();
        });
        
        // 批量操作
        document.getElementById('bulk-complete').addEventListener('click', () => {
            this.bulkComplete();
        });
        
        document.getElementById('bulk-delete').addEventListener('click', () => {
            this.bulkDelete();
        });
        
        document.getElementById('bulk-cancel').addEventListener('click', () => {
            this.clearSelection();
        });
        
        // 侧边栏
        document.getElementById('stats-toggle').addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        document.getElementById('sidebar-toggle').addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // 导入导出
        document.getElementById('export-btn').addEventListener('click', () => {
            this.exportData();
        });
        
        document.getElementById('import-btn').addEventListener('click', () => {
            document.getElementById('import-file').click();
        });
        
        document.getElementById('import-file').addEventListener('change', (e) => {
            this.importData(e.target.files[0]);
        });
        
        document.getElementById('clear-all-btn').addEventListener('click', () => {
            this.clearAllTasks();
        });
        
        // 模态框
        document.getElementById('modal-save').addEventListener('click', () => {
            this.saveEditTask();
        });
        
        document.getElementById('modal-cancel').addEventListener('click', () => {
            this.closeModal();
        });
        
        document.querySelector('.modal-close').addEventListener('click', () => {
            this.closeModal();
        });
        
        // 确认对话框
        document.getElementById('confirm-ok').addEventListener('click', () => {
            this.confirmAction();
        });
        
        document.getElementById('confirm-cancel').addEventListener('click', () => {
            this.closeConfirmDialog();
        });
        
        // 滚动到顶部
        document.getElementById('scroll-top').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        // 监听滚动显示/隐藏回到顶部按钮
        window.addEventListener('scroll', () => {
            const scrollTop = document.getElementById('scroll-top');
            if (window.pageYOffset > 300) {
                scrollTop.style.display = 'flex';
            } else {
                scrollTop.style.display = 'none';
            }
        });
        
        // 点击模态框背景关闭
        document.getElementById('modal').addEventListener('click', (e) => {
            if (e.target.id === 'modal') {
                this.closeModal();
            }
        });
        
        document.getElementById('confirm-dialog').addEventListener('click', (e) => {
            if (e.target.id === 'confirm-dialog') {
                this.closeConfirmDialog();
            }
        });
    }
    
    // 添加任务
    addTask() {
        const input = document.getElementById('task-input');
        const priority = document.getElementById('priority-select').value;
        const dueDate = document.getElementById('due-date').value;
        const text = input.value.trim();
        
        if (!text) {
            this.showNotification('请输入任务内容', 'warning');
            return;
        }
        
        const task = {
            id: Date.now(),
            text: text,
            completed: false,
            priority: priority,
            dueDate: dueDate || null,
            createdAt: new Date(),
            completedAt: null,
            notes: ''
        };
        
        this.tasks.unshift(task);
        
        // 清空输入
        input.value = '';
        document.getElementById('due-date').value = '';
        document.getElementById('priority-select').value = 'medium';
        
        this.saveToStorage();
        this.render();
        this.updateStats();
        
        this.showNotification('任务添加成功', 'success');
    }
    
    // 切换任务完成状态
    toggleTask(id) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            task.completed = !task.completed;
            task.completedAt = task.completed ? new Date() : null;
            
            this.saveToStorage();
            this.render();
            this.updateStats();
            
            const message = task.completed ? '任务已完成' : '任务已恢复';
            this.showNotification(message, 'success');
        }
    }
    
    // 删除任务
    deleteTask(id) {
        this.showConfirmDialog(
            '确认删除',
            '确定要删除这个任务吗？此操作无法撤销。',
            () => {
                this.tasks = this.tasks.filter(t => t.id !== id);
                this.selectedTasks.delete(id);
                
                this.saveToStorage();
                this.render();
                this.updateStats();
                
                this.showNotification('任务已删除', 'success');
            }
        );
    }
    
    // 编辑任务
    editTask(id) {
        const task = this.tasks.find(t => t.id === id);
        if (task) {
            this.editingTask = task;
            
            document.getElementById('edit-task-text').value = task.text;
            document.getElementById('edit-priority').value = task.priority;
            document.getElementById('edit-due-date').value = task.dueDate || '';
            document.getElementById('edit-notes').value = task.notes || '';
            
            this.showModal();
        }
    }
    
    // 保存编辑的任务
    saveEditTask() {
        if (!this.editingTask) return;
        
        const text = document.getElementById('edit-task-text').value.trim();
        if (!text) {
            this.showNotification('请输入任务内容', 'warning');
            return;
        }
        
        this.editingTask.text = text;
        this.editingTask.priority = document.getElementById('edit-priority').value;
        this.editingTask.dueDate = document.getElementById('edit-due-date').value || null;
        this.editingTask.notes = document.getElementById('edit-notes').value.trim();
        
        this.saveToStorage();
        this.render();
        this.updateStats();
        this.closeModal();
        
        this.showNotification('任务已更新', 'success');
    }
    
    // 切换任务选择状态
    toggleTaskSelection(id) {
        if (this.selectedTasks.has(id)) {
            this.selectedTasks.delete(id);
        } else {
            this.selectedTasks.add(id);
        }
        
        this.updateBulkActions();
        this.render();
    }
    
    // 批量完成任务
    bulkComplete() {
        let count = 0;
        this.selectedTasks.forEach(id => {
            const task = this.tasks.find(t => t.id === id);
            if (task && !task.completed) {
                task.completed = true;
                task.completedAt = new Date();
                count++;
            }
        });
        
        if (count > 0) {
            this.clearSelection();
            this.saveToStorage();
            this.render();
            this.updateStats();
            
            this.showNotification(`已完成 ${count} 个任务`, 'success');
        }
    }
    
    // 批量删除任务
    bulkDelete() {
        const count = this.selectedTasks.size;
        
        this.showConfirmDialog(
            '确认删除',
            `确定要删除选中的 ${count} 个任务吗？此操作无法撤销。`,
            () => {
                this.tasks = this.tasks.filter(t => !this.selectedTasks.has(t.id));
                this.clearSelection();
                
                this.saveToStorage();
                this.render();
                this.updateStats();
                
                this.showNotification(`已删除 ${count} 个任务`, 'success');
            }
        );
    }
    
    // 清除选择
    clearSelection() {
        this.selectedTasks.clear();
        this.updateBulkActions();
        this.render();
    }
    
    // 设置过滤器
    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        this.render();
    }
    
    // 设置搜索
    setSearch(query) {
        this.searchQuery = query.toLowerCase();
        
        // 显示/隐藏清除按钮
        const clearBtn = document.getElementById('clear-search');
        clearBtn.style.display = query ? 'block' : 'none';
        
        this.render();
    }
    
    // 设置排序
    setSort(sort) {
        this.currentSort = sort;
        this.render();
    }
    
    // 切换排序顺序
    toggleSortOrder() {
        this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        
        const icon = document.querySelector('#sort-order i');
        icon.className = this.sortOrder === 'asc' ? 'fas fa-sort-amount-up' : 'fas fa-sort-amount-down';
        
        this.render();
    }
    
    // 获取过滤后的任务
    getFilteredTasks() {
        let filtered = [...this.tasks];
        
        // 应用过滤器
        switch (this.currentFilter) {
            case 'pending':
                filtered = filtered.filter(t => !t.completed);
                break;
            case 'completed':
                filtered = filtered.filter(t => t.completed);
                break;
            case 'overdue':
                const today = new Date().toISOString().split('T')[0];
                filtered = filtered.filter(t => 
                    !t.completed && t.dueDate && t.dueDate < today
                );
                break;
        }
        
        // 应用搜索
        if (this.searchQuery) {
            filtered = filtered.filter(t => 
                t.text.toLowerCase().includes(this.searchQuery) ||
                (t.notes && t.notes.toLowerCase().includes(this.searchQuery))
            );
        }
        
        // 应用排序
        filtered.sort((a, b) => {
            let comparison = 0;
            
            switch (this.currentSort) {
                case 'priority':
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    comparison = priorityOrder[b.priority] - priorityOrder[a.priority];
                    break;
                case 'dueDate':
                    if (!a.dueDate && !b.dueDate) comparison = 0;
                    else if (!a.dueDate) comparison = 1;
                    else if (!b.dueDate) comparison = -1;
                    else comparison = new Date(a.dueDate) - new Date(b.dueDate);
                    break;
                case 'alphabetical':
                    comparison = a.text.localeCompare(b.text);
                    break;
                default: // created
                    comparison = new Date(b.createdAt) - new Date(a.createdAt);
            }
            
            return this.sortOrder === 'asc' ? comparison : -comparison;
        });
        
        return filtered;
    }
    
    // 渲染任务列表
    render() {
        const filteredTasks = this.getFilteredTasks();
        const container = document.getElementById('tasks-list');
        const emptyState = document.getElementById('empty-state');
        
        if (filteredTasks.length === 0) {
            container.innerHTML = '';
            emptyState.style.display = 'block';
            
            if (this.searchQuery) {
                emptyState.innerHTML = `
                    <div class="empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>没有找到匹配的任务</h3>
                    <p>尝试调整搜索条件或过滤器</p>
                    <button class="empty-add-btn" onclick="app.clearSearch()">
                        <i class="fas fa-times"></i>
                        清除搜索
                    </button>
                `;
            } else {
                emptyState.innerHTML = `
                    <div class="empty-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3>还没有任务</h3>
                    <p>添加你的第一个待办事项开始管理你的时间吧！</p>
                    <button class="empty-add-btn" onclick="document.getElementById('task-input').focus()">
                        <i class="fas fa-plus"></i>
                        添加第一个任务
                    </button>
                `;
            }
        } else {
            emptyState.style.display = 'none';
            container.innerHTML = filteredTasks.map(task => this.renderTask(task)).join('');
        }
        
        this.updateFilterCounts();
    }
    
    // 渲染单个任务
    renderTask(task) {
        const isSelected = this.selectedTasks.has(task.id);
        const isOverdue = this.isOverdue(task);
        const isDueToday = this.isDueToday(task);
        
        return `
            <div class="task-item ${task.completed ? 'completed' : ''} ${isSelected ? 'selected' : ''}" 
                 data-id="${task.id}">
                <div class="task-header">
                    <input type="checkbox" 
                           class="task-checkbox" 
                           ${task.completed ? 'checked' : ''}
                           onchange="app.toggleTask(${task.id})">
                    
                    <div class="task-content">
                        <div class="task-text">${this.escapeHtml(task.text)}</div>
                        <div class="task-meta">
                            <span class="priority-badge ${task.priority}">
                                ${this.getPriorityText(task.priority)}
                            </span>
                            
                            ${task.dueDate ? `
                                <span class="due-date ${isOverdue ? 'overdue' : isDueToday ? 'today' : ''}">
                                    <i class="fas fa-calendar"></i>
                                    ${this.formatDate(task.dueDate)}
                                </span>
                            ` : ''}
                            
                            <span class="created-date">
                                <i class="fas fa-clock"></i>
                                ${this.formatDateTime(task.createdAt)}
                            </span>
                            
                            ${task.notes ? `
                                <span class="has-notes" title="${this.escapeHtml(task.notes)}">
                                    <i class="fas fa-sticky-note"></i>
                                    备注
                                </span>
                            ` : ''}
                        </div>
                    </div>
                    
                    <div class="task-actions">
                        <button class="task-btn select" 
                                onclick="app.toggleTaskSelection(${task.id})"
                                title="选择任务">
                            <i class="fas fa-check-square"></i>
                        </button>
                        
                        <button class="task-btn edit" 
                                onclick="app.editTask(${task.id})"
                                title="编辑任务">
                            <i class="fas fa-edit"></i>
                        </button>
                        
                        <button class="task-btn delete" 
                                onclick="app.deleteTask(${task.id})"
                                title="删除任务">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // 更新统计信息
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const pending = total - completed;
        const overdue = this.tasks.filter(t => this.isOverdue(t)).length;

        // 头部统计
        document.getElementById('total-count').textContent = total;
        document.getElementById('pending-count').textContent = pending;
        document.getElementById('completed-count').textContent = completed;

        // 侧边栏统计
        this.updateProgressRing(total > 0 ? (completed / total) * 100 : 0);
        this.updatePriorityStats();
        this.updateTodayStats();
    }

    // 更新过滤器计数
    updateFilterCounts() {
        const all = this.tasks.length;
        const pending = this.tasks.filter(t => !t.completed).length;
        const completed = this.tasks.filter(t => t.completed).length;
        const overdue = this.tasks.filter(t => this.isOverdue(t)).length;

        document.getElementById('all-count').textContent = all;
        document.getElementById('pending-filter-count').textContent = pending;
        document.getElementById('completed-filter-count').textContent = completed;
        document.getElementById('overdue-count').textContent = overdue;
    }

    // 更新进度环
    updateProgressRing(percentage) {
        const circle = document.querySelector('.progress-ring-circle');
        const text = document.getElementById('progress-percentage');

        const circumference = 2 * Math.PI * 50; // r = 50
        const offset = circumference - (percentage / 100) * circumference;

        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = offset;
        circle.style.stroke = percentage > 0 ? 'var(--primary-color)' : 'var(--bg-tertiary)';

        text.textContent = Math.round(percentage) + '%';
    }

    // 更新优先级统计
    updatePriorityStats() {
        const high = this.tasks.filter(t => !t.completed && t.priority === 'high').length;
        const medium = this.tasks.filter(t => !t.completed && t.priority === 'medium').length;
        const low = this.tasks.filter(t => !t.completed && t.priority === 'low').length;

        document.getElementById('high-priority-count').textContent = high;
        document.getElementById('medium-priority-count').textContent = medium;
        document.getElementById('low-priority-count').textContent = low;
    }

    // 更新今日统计
    updateTodayStats() {
        const today = new Date().toISOString().split('T')[0];
        const dueToday = this.tasks.filter(t =>
            !t.completed && t.dueDate === today
        ).length;

        document.getElementById('due-today-count').textContent = dueToday;

        // 计算连续完成天数（简化版）
        const streak = this.calculateStreak();
        document.getElementById('streak-count').textContent = streak;
    }

    // 计算连续完成天数
    calculateStreak() {
        const completedTasks = this.tasks
            .filter(t => t.completed && t.completedAt)
            .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt));

        if (completedTasks.length === 0) return 0;

        let streak = 0;
        let currentDate = new Date();
        currentDate.setHours(0, 0, 0, 0);

        for (let i = 0; i < 7; i++) { // 检查最近7天
            const dateStr = currentDate.toISOString().split('T')[0];
            const hasTaskOnDate = completedTasks.some(t => {
                const completedDate = new Date(t.completedAt).toISOString().split('T')[0];
                return completedDate === dateStr;
            });

            if (hasTaskOnDate) {
                streak++;
            } else if (i > 0) { // 第一天没有任务不算中断
                break;
            }

            currentDate.setDate(currentDate.getDate() - 1);
        }

        return streak;
    }

    // 更新批量操作显示
    updateBulkActions() {
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        if (this.selectedTasks.size > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = this.selectedTasks.size;
        } else {
            bulkActions.style.display = 'none';
        }
    }

    // 切换侧边栏
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    // 显示模态框
    showModal() {
        const modal = document.getElementById('modal');
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    // 关闭模态框
    closeModal() {
        const modal = document.getElementById('modal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        this.editingTask = null;
    }

    // 显示确认对话框
    showConfirmDialog(title, message, callback) {
        document.getElementById('confirm-title').textContent = title;
        document.getElementById('confirm-message').textContent = message;

        this.confirmCallback = callback;

        const dialog = document.getElementById('confirm-dialog');
        dialog.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    // 关闭确认对话框
    closeConfirmDialog() {
        const dialog = document.getElementById('confirm-dialog');
        dialog.classList.remove('show');
        document.body.style.overflow = '';
        this.confirmCallback = null;
    }

    // 确认操作
    confirmAction() {
        if (this.confirmCallback) {
            this.confirmCallback();
        }
        this.closeConfirmDialog();
    }

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notifications');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <i class="${icons[type]}"></i>
            <div class="notification-content">
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // 关闭按钮事件
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        container.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    // 导出数据
    exportData() {
        const data = {
            tasks: this.tasks,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `todos-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification('数据导出成功', 'success');
    }

    // 导入数据
    importData(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);

                if (data.tasks && Array.isArray(data.tasks)) {
                    this.showConfirmDialog(
                        '确认导入',
                        `确定要导入 ${data.tasks.length} 个任务吗？这将替换当前所有任务。`,
                        () => {
                            this.tasks = data.tasks.map(task => ({
                                ...task,
                                createdAt: new Date(task.createdAt),
                                completedAt: task.completedAt ? new Date(task.completedAt) : null
                            }));

                            this.saveToStorage();
                            this.render();
                            this.updateStats();

                            this.showNotification('数据导入成功', 'success');
                        }
                    );
                } else {
                    this.showNotification('无效的数据格式', 'error');
                }
            } catch (error) {
                this.showNotification('文件解析失败', 'error');
            }
        };

        reader.readAsText(file);

        // 清空文件输入
        document.getElementById('import-file').value = '';
    }

    // 清空所有任务
    clearAllTasks() {
        if (this.tasks.length === 0) {
            this.showNotification('没有任务需要清空', 'info');
            return;
        }

        this.showConfirmDialog(
            '确认清空',
            `确定要清空所有 ${this.tasks.length} 个任务吗？此操作无法撤销。`,
            () => {
                this.tasks = [];
                this.selectedTasks.clear();

                this.saveToStorage();
                this.render();
                this.updateStats();

                this.showNotification('所有任务已清空', 'success');
            }
        );
    }

    // 清除搜索
    clearSearch() {
        document.getElementById('search-input').value = '';
        this.setSearch('');
    }

    // 保存到本地存储
    saveToStorage() {
        try {
            localStorage.setItem('todoApp', JSON.stringify({
                tasks: this.tasks,
                settings: {
                    currentFilter: this.currentFilter,
                    currentSort: this.currentSort,
                    sortOrder: this.sortOrder
                }
            }));
        } catch (error) {
            this.showNotification('保存失败', 'error');
        }
    }

    // 从本地存储加载
    loadFromStorage() {
        try {
            const data = localStorage.getItem('todoApp');
            if (data) {
                const parsed = JSON.parse(data);

                this.tasks = parsed.tasks.map(task => ({
                    ...task,
                    createdAt: new Date(task.createdAt),
                    completedAt: task.completedAt ? new Date(task.completedAt) : null
                }));

                if (parsed.settings) {
                    this.currentFilter = parsed.settings.currentFilter || 'all';
                    this.currentSort = parsed.settings.currentSort || 'created';
                    this.sortOrder = parsed.settings.sortOrder || 'desc';
                }
            }
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    }

    // 工具函数
    isOverdue(task) {
        if (!task.dueDate || task.completed) return false;
        const today = new Date().toISOString().split('T')[0];
        return task.dueDate < today;
    }

    isDueToday(task) {
        if (!task.dueDate || task.completed) return false;
        const today = new Date().toISOString().split('T')[0];
        return task.dueDate === today;
    }

    getPriorityText(priority) {
        const texts = {
            high: '高',
            medium: '中',
            low: '低'
        };
        return texts[priority] || '中';
    }

    formatDate(dateStr) {
        const date = new Date(dateStr);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (dateStr === today.toISOString().split('T')[0]) {
            return '今天';
        } else if (dateStr === tomorrow.toISOString().split('T')[0]) {
            return '明天';
        } else {
            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric'
            });
        }
    }

    formatDateTime(date) {
        return new Date(date).toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
const app = new TodoApp();

// 全局函数（供HTML调用）
window.app = app;

// 键盘快捷键
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter: 添加任务
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        app.addTask();
    }

    // Escape: 关闭模态框
    if (e.key === 'Escape') {
        app.closeModal();
        app.closeConfirmDialog();
    }

    // Ctrl/Cmd + A: 全选任务
    if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
        e.preventDefault();
        app.tasks.forEach(task => app.selectedTasks.add(task.id));
        app.updateBulkActions();
        app.render();
    }

    // Delete: 删除选中任务
    if (e.key === 'Delete' && app.selectedTasks.size > 0 && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
        e.preventDefault();
        app.bulkDelete();
    }
});

// 页面可见性变化时保存数据
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        app.saveToStorage();
    }
});

// 页面卸载前保存数据
window.addEventListener('beforeunload', () => {
    app.saveToStorage();
});

console.log('✅ 待办事项应用加载完成！');
console.log('🎯 功能特性:');
console.log('  • 添加、编辑、删除任务');
console.log('  • 优先级和截止日期');
console.log('  • 搜索和过滤');
console.log('  • 批量操作');
console.log('  • 数据导入导出');
console.log('  • 统计和进度跟踪');
console.log('  • 本地存储');
console.log('  • 响应式设计');
console.log('💡 快捷键:');
console.log('  • Ctrl/Cmd + Enter: 添加任务');
console.log('  • Ctrl/Cmd + A: 全选任务');
console.log('  • Delete: 删除选中任务');
console.log('  • Escape: 关闭对话框');
