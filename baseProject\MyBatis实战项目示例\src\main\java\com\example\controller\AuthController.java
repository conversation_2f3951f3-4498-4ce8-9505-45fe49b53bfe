package com.example.controller;

import com.example.entity.SysUser;
import com.example.security.UserPrincipal;
import com.example.service.SysUserService;
import com.example.vo.ApiResponse;
import com.example.vo.LoginRequest;
import com.example.vo.LoginResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * 认证控制器
 * 处理用户登录、登出等认证相关操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private SysUserService userService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@RequestBody LoginRequest loginRequest, HttpServletRequest request) {
        try {
            logger.info("用户登录请求: {}", loginRequest.getUsername());
            
            // 1. 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = 
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword());
            
            // 2. 进行认证
            Authentication authentication = authenticationManager.authenticate(authToken);
            
            // 3. 设置安全上下文
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // 4. 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // 5. 更新最后登录时间
            userService.updateLastLoginTime(userPrincipal.getId());
            
            // 6. 构建响应
            LoginResponse loginResponse = new LoginResponse();
            loginResponse.setUserId(userPrincipal.getId());
            loginResponse.setUsername(userPrincipal.getUsername());
            loginResponse.setRealName(userPrincipal.getRealName());
            loginResponse.setEmail(userPrincipal.getEmail());
            
            // 获取用户角色和权限
            SysUser userWithRoles = userService.getUserWithRolesAndPermissions(userPrincipal.getId());
            if (userWithRoles != null) {
                loginResponse.setRoles(userWithRoles.getRoles());
                loginResponse.setPermissions(userWithRoles.getPermissions());
            }
            
            // 7. 设置Session（如果需要）
            HttpSession session = request.getSession(true);
            session.setAttribute("user", userPrincipal);
            
            logger.info("用户登录成功: {}", loginRequest.getUsername());
            return ApiResponse.success("登录成功", loginResponse);
            
        } catch (Exception e) {
            logger.error("用户登录失败: {}", loginRequest.getUsername(), e);
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        try {
            // 1. 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
                UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                logger.info("用户登出: {}", userPrincipal.getUsername());
            }
            
            // 2. 清除安全上下文
            SecurityContextHolder.clearContext();
            
            // 3. 清除Session
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.invalidate();
            }
            
            return ApiResponse.success("登出成功");
            
        } catch (Exception e) {
            logger.error("用户登出失败", e);
            return ApiResponse.error("登出失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    public ApiResponse<LoginResponse> getCurrentUser() {
        try {
            // 1. 获取当前认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                return ApiResponse.error("用户未登录");
            }
            
            // 2. 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // 3. 查询完整的用户信息
            SysUser userWithRoles = userService.getUserWithRolesAndPermissions(userPrincipal.getId());
            if (userWithRoles == null) {
                return ApiResponse.error("用户信息不存在");
            }
            
            // 4. 构建响应
            LoginResponse response = new LoginResponse();
            response.setUserId(userWithRoles.getId());
            response.setUsername(userWithRoles.getUsername());
            response.setRealName(userWithRoles.getRealName());
            response.setEmail(userWithRoles.getEmail());
            response.setRoles(userWithRoles.getRoles());
            response.setPermissions(userWithRoles.getPermissions());
            
            return ApiResponse.success("获取用户信息成功", response);
            
        } catch (Exception e) {
            logger.error("获取当前用户信息失败", e);
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查用户是否已登录
     */
    @GetMapping("/check")
    public ApiResponse<Boolean> checkLogin() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            boolean isLoggedIn = authentication != null && 
                               authentication.isAuthenticated() && 
                               authentication.getPrincipal() instanceof UserPrincipal;
            
            return ApiResponse.success("检查登录状态成功", isLoggedIn);
            
        } catch (Exception e) {
            logger.error("检查登录状态失败", e);
            return ApiResponse.error("检查登录状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public ApiResponse<Void> changePassword(@RequestBody ChangePasswordRequest request) {
        try {
            // 1. 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                return ApiResponse.error("用户未登录");
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            
            // 2. 修改密码
            boolean success = userService.changePassword(
                userPrincipal.getId(), 
                request.getOldPassword(), 
                request.getNewPassword()
            );
            
            if (success) {
                logger.info("用户密码修改成功: {}", userPrincipal.getUsername());
                return ApiResponse.success("密码修改成功");
            } else {
                return ApiResponse.error("密码修改失败");
            }
            
        } catch (Exception e) {
            logger.error("修改密码失败", e);
            return ApiResponse.error("密码修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改密码请求对象
     */
    public static class ChangePasswordRequest {
        private String oldPassword;
        private String newPassword;
        
        // Getter和Setter
        public String getOldPassword() {
            return oldPassword;
        }
        
        public void setOldPassword(String oldPassword) {
            this.oldPassword = oldPassword;
        }
        
        public String getNewPassword() {
            return newPassword;
        }
        
        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }
    }
}
