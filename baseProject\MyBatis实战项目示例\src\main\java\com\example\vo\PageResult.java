package com.example.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果VO
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    private Integer total;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 数据列表
     */
    private List<T> list;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否为第一页
     */
    private Boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private Boolean isLast;
    
    /**
     * 计算总页数
     */
    public Integer getPages() {
        if (total == null || pageSize == null || pageSize == 0) {
            return 0;
        }
        return (total + pageSize - 1) / pageSize;
    }
    
    /**
     * 是否有上一页
     */
    public Boolean getHasPrevious() {
        return pageNum != null && pageNum > 1;
    }
    
    /**
     * 是否有下一页
     */
    public Boolean getHasNext() {
        return pageNum != null && pageNum < getPages();
    }
    
    /**
     * 是否为第一页
     */
    public Boolean getIsFirst() {
        return pageNum != null && pageNum == 1;
    }
    
    /**
     * 是否为最后一页
     */
    public Boolean getIsLast() {
        return pageNum != null && pageNum.equals(getPages());
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return PageResult.<T>builder()
                .pageNum(1)
                .pageSize(10)
                .total(0)
                .list(java.util.Collections.emptyList())
                .build();
    }
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(Integer pageNum, Integer pageSize, Integer total, List<T> list) {
        return PageResult.<T>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .total(total)
                .list(list)
                .build();
    }
}
