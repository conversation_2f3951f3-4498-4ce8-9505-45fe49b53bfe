package thread;

import java.util.concurrent.*;

/**
 * 基础线程示例
 * 演示线程的创建和基本使用
 */
public class BasicThreadDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 基础线程演示 ===");
        
        // 1. 继承Thread类
        threadClassDemo();
        
        // 2. 实现Runnable接口
        runnableInterfaceDemo();
        
        // 3. 实现Callable接口
        callableInterfaceDemo();
        
        // 4. 线程状态演示
        threadStateDemo();
    }
    
    /**
     * Thread类演示
     */
    public static void threadClassDemo() {
        System.out.println("\n--- Thread类演示 ---");
        
        class MyThread extends Thread {
            private String threadName;
            
            public MyThread(String name) {
                this.threadName = name;
            }
            
            @Override
            public void run() {
                for (int i = 1; i <= 3; i++) {
                    System.out.println(threadName + " - 步骤: " + i);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        System.out.println(threadName + " 被中断");
                        return;
                    }
                }
                System.out.println(threadName + " 执行完成");
            }
        }
        
        MyThread thread1 = new MyThread("线程A");
        MyThread thread2 = new MyThread("线程B");
        
        thread1.start();
        thread2.start();
        
        try {
            thread1.join(); // 等待线程A完成
            thread2.join(); // 等待线程B完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Runnable接口演示
     */
    public static void runnableInterfaceDemo() {
        System.out.println("\n--- Runnable接口演示 ---");
        
        class MyTask implements Runnable {
            private String taskName;
            
            public MyTask(String name) {
                this.taskName = name;
            }
            
            @Override
            public void run() {
                for (int i = 1; i <= 3; i++) {
                    System.out.println(taskName + " - 执行: " + i);
                    try {
                        Thread.sleep(800);
                    } catch (InterruptedException e) {
                        System.out.println(taskName + " 被中断");
                        return;
                    }
                }
                System.out.println(taskName + " 任务完成");
            }
        }
        
        Thread thread1 = new Thread(new MyTask("任务X"));
        Thread thread2 = new Thread(new MyTask("任务Y"));
        
        // 使用Lambda表达式
        Thread thread3 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                System.out.println("Lambda任务 - " + i);
                try {
                    Thread.sleep(600);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
            System.out.println("Lambda任务完成");
        });
        
        thread1.start();
        thread2.start();
        thread3.start();
        
        try {
            thread1.join();
            thread2.join();
            thread3.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Callable接口演示
     */
    public static void callableInterfaceDemo() {
        System.out.println("\n--- Callable接口演示 ---");
        
        class CalculationTask implements Callable<Integer> {
            private String taskName;
            private int number;
            
            public CalculationTask(String name, int number) {
                this.taskName = name;
                this.number = number;
            }
            
            @Override
            public Integer call() throws Exception {
                System.out.println(taskName + " 开始计算 " + number + " 的平方");
                Thread.sleep(1000); // 模拟计算时间
                int result = number * number;
                System.out.println(taskName + " 计算完成，结果: " + result);
                return result;
            }
        }
        
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        try {
            // 提交任务并获取Future
            Future<Integer> future1 = executor.submit(new CalculationTask("计算任务1", 5));
            Future<Integer> future2 = executor.submit(new CalculationTask("计算任务2", 8));
            
            // 获取结果（会阻塞直到任务完成）
            Integer result1 = future1.get();
            Integer result2 = future2.get();
            
            System.out.println("任务1结果: " + result1);
            System.out.println("任务2结果: " + result2);
            System.out.println("总和: " + (result1 + result2));
            
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
    }
    
    /**
     * 线程状态演示
     */
    public static void threadStateDemo() {
        System.out.println("\n--- 线程状态演示 ---");
        
        Thread thread = new Thread(() -> {
            try {
                System.out.println("线程开始执行");
                Thread.sleep(3000); // TIMED_WAITING状态
                System.out.println("线程执行完成");
            } catch (InterruptedException e) {
                System.out.println("线程被中断");
            }
        }, "状态演示线程");
        
        System.out.println("创建后状态: " + thread.getState()); // NEW
        
        thread.start();
        System.out.println("启动后状态: " + thread.getState()); // RUNNABLE
        
        try {
            Thread.sleep(1000);
            System.out.println("运行中状态: " + thread.getState()); // TIMED_WAITING
            
            thread.join(); // 等待线程完成
            System.out.println("完成后状态: " + thread.getState()); // TERMINATED
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

/**
 * 线程优先级和守护线程演示
 */
class ThreadPropertiesDemo {
    public static void main(String[] args) {
        System.out.println("=== 线程属性演示 ===");
        
        // 普通线程
        Thread normalThread = new Thread(() -> {
            for (int i = 1; i <= 5; i++) {
                System.out.println("普通线程 - " + i);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        });
        
        // 守护线程
        Thread daemonThread = new Thread(() -> {
            while (true) {
                System.out.println("守护线程运行中...");
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        });
        
        // 设置为守护线程
        daemonThread.setDaemon(true);
        
        // 设置线程优先级
        normalThread.setPriority(Thread.MAX_PRIORITY);
        daemonThread.setPriority(Thread.MIN_PRIORITY);
//
        System.out.println("普通线程优先级: " + normalThread.getPriority());
        System.out.println("守护线程优先级: " + daemonThread.getPriority());
        System.out.println("守护线程标志: " + daemonThread.isDaemon());
        
        normalThread.start();
        daemonThread.start();
        
        try {
            normalThread.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("主线程结束，守护线程也会结束");
    }
}
