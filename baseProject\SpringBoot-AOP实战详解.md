# Spring Boot AOP 实战详解

## 📚 目录

1. [AOP 基础概念](#aop-基础概念)
2. [Spring AOP 实现原理](#spring-aop-实现原理)
3. [AOP 实战应用](#aop-实战应用)
4. [性能监控切面](#性能监控切面)
5. [日志记录切面](#日志记录切面)
6. [权限控制切面](#权限控制切面)
7. [缓存切面](#缓存切面)
8. [异常处理切面](#异常处理切面)
9. [数据校验切面](#数据校验切面)
10. [最佳实践](#最佳实践)

---

## AOP 基础概念

### 什么是 AOP

AOP (Aspect Oriented Programming) 面向切面编程，是一种编程范式，旨在通过分离横切关注点来增加程序的模块化。

### 核心概念

- **切面 (Aspect)**: 横切关注点的模块化，如日志、事务、安全等
- **连接点 (Join Point)**: 程序执行过程中的某个特定点，如方法调用、异常抛出等
- **切点 (Pointcut)**: 匹配连接点的断言，定义了切面在何处执行
- **通知 (Advice)**: 在切点执行的代码，包括前置、后置、环绕等类型
- **目标对象 (Target Object)**: 被一个或多个切面通知的对象
- **织入 (Weaving)**: 将切面应用到目标对象的过程

### 通知类型

| 通知类型 | 注解 | 执行时机 | 说明 |
|----------|------|----------|------|
| 前置通知 | @Before | 方法执行前 | 不能阻止方法执行 |
| 后置通知 | @After | 方法执行后 | 无论是否异常都会执行 |
| 返回通知 | @AfterReturning | 方法正常返回后 | 可以访问返回值 |
| 异常通知 | @AfterThrowing | 方法抛出异常后 | 可以访问异常信息 |
| 环绕通知 | @Around | 方法执行前后 | 最强大的通知类型 |

---

## Spring AOP 实现原理

### 1. 代理模式

Spring AOP 基于代理模式实现，支持两种代理方式：

#### JDK 动态代理

```java
// 目标接口
public interface UserService {
    void createUser(String username);
    User findUser(Long id);
}

// 目标实现类
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    public void createUser(String username) {
        System.out.println("Creating user: " + username);
    }
    
    @Override
    public User findUser(Long id) {
        System.out.println("Finding user: " + id);
        return new User(id, "user" + id);
    }
}

// JDK动态代理示例
public class JdkProxyExample {
    
    public static void main(String[] args) {
        UserService target = new UserServiceImpl();
        
        UserService proxy = (UserService) Proxy.newProxyInstance(
            target.getClass().getClassLoader(),
            target.getClass().getInterfaces(),
            new InvocationHandler() {
                @Override
                public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                    System.out.println("Before method: " + method.getName());
                    Object result = method.invoke(target, args);
                    System.out.println("After method: " + method.getName());
                    return result;
                }
            }
        );
        
        proxy.createUser("john");
    }
}
```

#### CGLIB 代理

```java
// 目标类（无需实现接口）
@Service
public class UserService {
    
    public void createUser(String username) {
        System.out.println("Creating user: " + username);
    }
    
    public User findUser(Long id) {
        System.out.println("Finding user: " + id);
        return new User(id, "user" + id);
    }
}

// CGLIB代理示例
public class CglibProxyExample {
    
    public static void main(String[] args) {
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(UserService.class);
        enhancer.setCallback(new MethodInterceptor() {
            @Override
            public Object intercept(Object obj, Method method, Object[] args, MethodProxy proxy) throws Throwable {
                System.out.println("Before method: " + method.getName());
                Object result = proxy.invokeSuper(obj, args);
                System.out.println("After method: " + method.getName());
                return result;
            }
        });
        
        UserService proxy = (UserService) enhancer.create();
        proxy.createUser("john");
    }
}
```

### 2. 切点表达式详解

#### execution 表达式

```java
// 基本语法：execution(修饰符 返回类型 包名.类名.方法名(参数类型))

// 匹配所有public方法
@Pointcut("execution(public * *(..))")

// 匹配指定包下的所有方法
@Pointcut("execution(* com.example.service.*.*(..))")

// 匹配指定包及其子包下的所有方法
@Pointcut("execution(* com.example.service..*.*(..))")

// 匹配指定类的所有方法
@Pointcut("execution(* com.example.service.UserService.*(..))")

// 匹配指定方法名模式
@Pointcut("execution(* com.example.service.*.get*(..))")
@Pointcut("execution(* com.example.service.*.*User(..))")

// 匹配指定参数类型
@Pointcut("execution(* com.example.service.*.*(String))")
@Pointcut("execution(* com.example.service.*.*(String, ..))")
@Pointcut("execution(* com.example.service.*.*(.., String))")

// 匹配指定返回类型
@Pointcut("execution(String com.example.service.*.*(..))")
@Pointcut("execution(com.example.entity.User com.example.service.*.*(..))")
```

#### 其他切点表达式

```java
// 匹配注解
@Pointcut("@annotation(com.example.annotation.Log)")
@Pointcut("@annotation(org.springframework.transaction.annotation.Transactional)")

// 匹配类注解
@Pointcut("@within(org.springframework.stereotype.Service)")
@Pointcut("@within(com.example.annotation.Monitored)")

// 匹配参数注解
@Pointcut("@args(com.example.annotation.Valid)")

// 匹配目标对象注解
@Pointcut("@target(org.springframework.stereotype.Repository)")

// 匹配this对象
@Pointcut("this(com.example.service.UserService)")

// 匹配target对象
@Pointcut("target(com.example.service.UserService)")

// 匹配参数类型
@Pointcut("args(String, ..)")

// 匹配bean名称
@Pointcut("bean(userService)")
@Pointcut("bean(*Service)")
```

#### 切点组合

```java
@Component
@Aspect
public class PointcutExamples {
    
    // 定义可重用的切点
    @Pointcut("execution(* com.example.service.*.*(..))")
    public void serviceLayer() {}
    
    @Pointcut("@annotation(com.example.annotation.Log)")
    public void logAnnotation() {}
    
    @Pointcut("@annotation(org.springframework.cache.annotation.Cacheable)")
    public void cacheableAnnotation() {}
    
    // 组合切点 - AND
    @Pointcut("serviceLayer() && logAnnotation()")
    public void serviceWithLog() {}
    
    // 组合切点 - OR
    @Pointcut("logAnnotation() || cacheableAnnotation()")
    public void logOrCache() {}
    
    // 组合切点 - NOT
    @Pointcut("serviceLayer() && !logAnnotation()")
    public void serviceWithoutLog() {}
    
    // 复杂组合
    @Pointcut("(serviceLayer() || @within(org.springframework.stereotype.Repository)) && @annotation(com.example.annotation.Monitor)")
    public void complexPointcut() {}
}
```

---

## AOP 实战应用

### 1. 项目配置

#### Maven 依赖

```xml
<dependencies>
    <!-- Spring Boot Starter AOP -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    
    <!-- Spring Boot Starter Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- Spring Boot Starter Data JPA -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
    
    <!-- FastJSON -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson2</artifactId>
        <version>2.0.32</version>
    </dependency>
</dependencies>
```

#### 启用 AOP

```java
@SpringBootApplication
@EnableAspectJAutoProxy(proxyTargetClass = true) // 强制使用CGLIB代理
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 2. 自定义注解

#### 日志注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 操作类型
     */
    OperationType type() default OperationType.OTHER;
    
    /**
     * 是否记录参数
     */
    boolean recordParams() default true;
    
    /**
     * 是否记录返回值
     */
    boolean recordResult() default true;
    
    /**
     * 是否记录执行时间
     */
    boolean recordTime() default true;
    
    enum OperationType {
        CREATE, UPDATE, DELETE, QUERY, OTHER
    }
}
```

#### 性能监控注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Monitor {
    
    /**
     * 监控名称
     */
    String value() default "";
    
    /**
     * 慢查询阈值（毫秒）
     */
    long slowThreshold() default 1000;
    
    /**
     * 是否记录参数
     */
    boolean recordArgs() default false;
    
    /**
     * 是否记录返回值
     */
    boolean recordResult() default false;
}
```

#### 权限控制注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermission {
    
    /**
     * 所需权限
     */
    String[] value();
    
    /**
     * 权限关系：AND 或 OR
     */
    Logical logical() default Logical.AND;
    
    enum Logical {
        AND, OR
    }
}
```

#### 缓存注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Cache {
    
    /**
     * 缓存key前缀
     */
    String prefix() default "";
    
    /**
     * 缓存key表达式
     */
    String key() default "";
    
    /**
     * 过期时间（秒）
     */
    int expireTime() default 3600;
    
    /**
     * 缓存条件
     */
    String condition() default "";
}
```

---

## 性能监控切面

### 1. 性能监控切面实现

```java
@Aspect
@Component
@Slf4j
public class PerformanceMonitorAspect {

    private final MeterRegistry meterRegistry;
    private final RedisTemplate<String, Object> redisTemplate;

    public PerformanceMonitorAspect(MeterRegistry meterRegistry, RedisTemplate<String, Object> redisTemplate) {
        this.meterRegistry = meterRegistry;
        this.redisTemplate = redisTemplate;
    }

    @Around("@annotation(monitor)")
    public Object monitorPerformance(ProceedingJoinPoint joinPoint, Monitor monitor) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();
        String monitorName = StringUtils.hasText(monitor.value()) ? monitor.value() : methodName;

        // 创建计时器
        Timer.Sample sample = Timer.start(meterRegistry);
        long startTime = System.currentTimeMillis();

        try {
            // 执行目标方法
            Object result = joinPoint.proceed();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录成功指标
            sample.stop(Timer.builder("method.execution.time")
                .tag("method", methodName)
                .tag("status", "success")
                .register(meterRegistry));

            // 记录到Redis（用于实时监控）
            recordPerformanceMetrics(monitorName, duration, true, null);

            // 检查是否为慢查询
            if (duration > monitor.slowThreshold()) {
                logSlowMethod(joinPoint, monitor, duration);
            }

            // 记录详细日志
            if (log.isDebugEnabled()) {
                log.debug("方法执行完成: {} - 耗时: {}ms", methodName, duration);
                if (monitor.recordArgs()) {
                    log.debug("方法参数: {}", JSON.toJSONString(joinPoint.getArgs()));
                }
                if (monitor.recordResult()) {
                    log.debug("返回结果: {}", JSON.toJSONString(result));
                }
            }

            return result;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录失败指标
            sample.stop(Timer.builder("method.execution.time")
                .tag("method", methodName)
                .tag("status", "error")
                .register(meterRegistry));

            // 记录错误计数
            Counter.builder("method.execution.error")
                .tag("method", methodName)
                .tag("exception", e.getClass().getSimpleName())
                .register(meterRegistry)
                .increment();

            // 记录到Redis
            recordPerformanceMetrics(monitorName, duration, false, e.getClass().getSimpleName());

            log.error("方法执行异常: {} - 耗时: {}ms - 异常: {}", methodName, duration, e.getMessage());

            throw e;
        }
    }

    private void logSlowMethod(ProceedingJoinPoint joinPoint, Monitor monitor, long duration) {
        String methodName = joinPoint.getSignature().toShortString();

        log.warn("检测到慢方法: {} - 耗时: {}ms - 阈值: {}ms",
                methodName, duration, monitor.slowThreshold());

        // 记录慢查询指标
        Counter.builder("method.execution.slow")
            .tag("method", methodName)
            .register(meterRegistry)
            .increment();

        // 可以在这里添加告警逻辑
        sendSlowMethodAlert(methodName, duration, monitor.slowThreshold());
    }

    private void recordPerformanceMetrics(String methodName, long duration, boolean success, String errorType) {
        String key = "performance:metrics:" + methodName;
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("duration", duration);
        metrics.put("success", success);
        metrics.put("timestamp", System.currentTimeMillis());
        if (errorType != null) {
            metrics.put("errorType", errorType);
        }

        // 使用List存储最近的执行记录
        redisTemplate.opsForList().leftPush(key, metrics);
        redisTemplate.opsForList().trim(key, 0, 99); // 只保留最近100条记录
        redisTemplate.expire(key, Duration.ofHours(24)); // 24小时过期
    }

    private void sendSlowMethodAlert(String methodName, long duration, long threshold) {
        // 实现告警逻辑，如发送邮件、短信、钉钉消息等
        log.warn("发送慢方法告警: 方法={}, 耗时={}ms, 阈值={}ms", methodName, duration, threshold);
    }
}
```

### 2. 性能监控使用示例

```java
@Service
public class UserService {

    @Monitor(value = "用户查询", slowThreshold = 500, recordArgs = true)
    public User findUserById(Long id) {
        // 模拟数据库查询
        try {
            Thread.sleep(100); // 模拟查询耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return userRepository.findById(id).orElse(null);
    }

    @Monitor(value = "用户创建", slowThreshold = 1000, recordResult = true)
    public User createUser(User user) {
        // 模拟复杂的用户创建逻辑
        validateUser(user);
        encryptPassword(user);
        User savedUser = userRepository.save(user);
        sendWelcomeEmail(user.getEmail());
        return savedUser;
    }

    @Monitor("批量用户导入")
    public void importUsers(List<User> users) {
        for (User user : users) {
            createUser(user);
        }
    }
}
```

### 3. 性能监控数据查询

```java
@RestController
@RequestMapping("/api/monitor")
public class MonitorController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private MeterRegistry meterRegistry;

    @GetMapping("/performance/{methodName}")
    public ResponseEntity<List<Map<String, Object>>> getPerformanceMetrics(@PathVariable String methodName) {
        String key = "performance:metrics:" + methodName;
        List<Object> metrics = redisTemplate.opsForList().range(key, 0, -1);

        List<Map<String, Object>> result = metrics.stream()
            .map(obj -> (Map<String, Object>) obj)
            .collect(Collectors.toList());

        return ResponseEntity.ok(result);
    }

    @GetMapping("/metrics/summary")
    public ResponseEntity<Map<String, Object>> getMetricsSummary() {
        Map<String, Object> summary = new HashMap<>();

        // 获取所有计时器指标
        Collection<Timer> timers = meterRegistry.find("method.execution.time").timers();
        List<Map<String, Object>> timerMetrics = timers.stream()
            .map(timer -> {
                Map<String, Object> metric = new HashMap<>();
                metric.put("name", timer.getId().getTag("method"));
                metric.put("count", timer.count());
                metric.put("totalTime", timer.totalTime(TimeUnit.MILLISECONDS));
                metric.put("meanTime", timer.mean(TimeUnit.MILLISECONDS));
                metric.put("maxTime", timer.max(TimeUnit.MILLISECONDS));
                return metric;
            })
            .collect(Collectors.toList());

        summary.put("timers", timerMetrics);

        // 获取所有计数器指标
        Collection<Counter> counters = meterRegistry.find("method.execution.error").counters();
        List<Map<String, Object>> counterMetrics = counters.stream()
            .map(counter -> {
                Map<String, Object> metric = new HashMap<>();
                metric.put("name", counter.getId().getTag("method"));
                metric.put("exception", counter.getId().getTag("exception"));
                metric.put("count", counter.count());
                return metric;
            })
            .collect(Collectors.toList());

        summary.put("errors", counterMetrics);

        return ResponseEntity.ok(summary);
    }
}
```

---

## 日志记录切面

### 1. 日志记录切面实现

```java
@Aspect
@Component
@Slf4j
public class LoggingAspect {

    private final ObjectMapper objectMapper;
    private final HttpServletRequest request;

    public LoggingAspect(ObjectMapper objectMapper, HttpServletRequest request) {
        this.objectMapper = objectMapper;
        this.request = request;
    }

    @Around("@annotation(logAnnotation)")
    public Object logMethodExecution(ProceedingJoinPoint joinPoint, Log logAnnotation) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();
        String operation = StringUtils.hasText(logAnnotation.value()) ? logAnnotation.value() : methodName;

        // 生成请求ID
        String requestId = generateRequestId();
        MDC.put("requestId", requestId);

        // 获取用户信息
        String currentUser = getCurrentUser();
        String clientIp = getClientIp();

        long startTime = System.currentTimeMillis();

        // 记录开始日志
        LogEntry startLog = LogEntry.builder()
            .requestId(requestId)
            .operation(operation)
            .operationType(logAnnotation.type())
            .methodName(methodName)
            .user(currentUser)
            .clientIp(clientIp)
            .startTime(new Date(startTime))
            .build();

        if (logAnnotation.recordParams()) {
            startLog.setParams(serializeArgs(joinPoint.getArgs()));
        }

        log.info("开始执行操作: {}", startLog);

        try {
            Object result = joinPoint.proceed();

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录成功日志
            LogEntry successLog = startLog.toBuilder()
                .endTime(new Date(endTime))
                .duration(duration)
                .success(true)
                .build();

            if (logAnnotation.recordResult()) {
                successLog.setResult(serializeObject(result));
            }

            log.info("操作执行成功: {}", successLog);

            // 保存操作日志到数据库
            saveOperationLog(successLog);

            return result;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 记录失败日志
            LogEntry errorLog = startLog.toBuilder()
                .endTime(new Date(endTime))
                .duration(duration)
                .success(false)
                .errorMessage(e.getMessage())
                .errorType(e.getClass().getSimpleName())
                .build();

            log.error("操作执行失败: {}", errorLog, e);

            // 保存错误日志到数据库
            saveOperationLog(errorLog);

            throw e;
        } finally {
            MDC.clear();
        }
    }

    private String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private String getCurrentUser() {
        // 从SecurityContext或Session中获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            return authentication.getName();
        }
        return "anonymous";
    }

    private String getClientIp() {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private String serializeArgs(Object[] args) {
        if (args == null || args.length == 0) {
            return "[]";
        }

        try {
            // 过滤敏感参数
            Object[] filteredArgs = Arrays.stream(args)
                .map(this::filterSensitiveData)
                .toArray();
            return objectMapper.writeValueAsString(filteredArgs);
        } catch (Exception e) {
            log.warn("序列化参数失败", e);
            return "[序列化失败]";
        }
    }

    private String serializeObject(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(filterSensitiveData(obj));
        } catch (Exception e) {
            log.warn("序列化对象失败", e);
            return "[序列化失败]";
        }
    }

    private Object filterSensitiveData(Object obj) {
        if (obj == null) {
            return null;
        }

        // 如果是字符串且可能是密码，则脱敏
        if (obj instanceof String) {
            String str = (String) obj;
            if (str.length() > 6 && (str.contains("password") || str.contains("pwd"))) {
                return "******";
            }
        }

        // 如果是用户对象，脱敏密码字段
        if (obj instanceof User) {
            User user = (User) obj;
            User filtered = new User();
            BeanUtils.copyProperties(user, filtered);
            filtered.setPassword("******");
            return filtered;
        }

        return obj;
    }

    private void saveOperationLog(LogEntry logEntry) {
        // 异步保存日志到数据库
        CompletableFuture.runAsync(() -> {
            try {
                operationLogService.save(logEntry);
            } catch (Exception e) {
                log.error("保存操作日志失败", e);
            }
        });
    }
}
```
```
