package com.example.mybatisrbac.controller;

import com.example.mybatisrbac.common.Result;
import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.dto.UserCreateDTO;
import com.example.mybatisrbac.dto.UserQueryDTO;
import com.example.mybatisrbac.entity.Role;
import com.example.mybatisrbac.entity.User;
import com.example.mybatisrbac.service.UserService;
import com.example.mybatisrbac.util.BeanUtil;
import com.example.mybatisrbac.util.ResponseUtil;
import com.example.mybatisrbac.validation.ValidStatus;
import com.example.mybatisrbac.validation.ValidSortField;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 用户控制器
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Tag(name = "用户管理", description = "用户相关的增删改查操作")
@RestController
@RequestMapping("/api/users")
@Validated
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 分页查询用户列表
     */
    @Operation(summary = "分页查询用户", description = "根据条件分页查询用户列表")
    @GetMapping
    public PageResult<User> getUserList(
            @Parameter(description = "当前页码，必须大于0", example = "1") 
            @RequestParam(defaultValue = "1") 
            @Min(value = 1, message = "当前页码必须大于0") 
            @Max(value = 10000, message = "当前页码不能超过10000") 
            Long current,
            
            @Parameter(description = "每页大小，范围1-100", example = "10") 
            @RequestParam(defaultValue = "10") 
            @Min(value = 1, message = "每页大小必须大于0") 
            @Max(value = 100, message = "每页大小不能超过100") 
            Long size,
            
            @Parameter(description = "用户名关键字，长度不超过50") 
            @RequestParam(required = false) 
            @Size(max = 50, message = "用户名关键字长度不能超过50") 
            String username,
            
            @Parameter(description = "邮箱关键字，长度不超过100") 
            @RequestParam(required = false) 
            @Size(max = 100, message = "邮箱关键字长度不能超过100") 
            String email,
            
            @Parameter(description = "手机号关键字") 
            @RequestParam(required = false) 
            String phone,
            
            @Parameter(description = "真实姓名关键字，长度不超过50") 
            @RequestParam(required = false) 
            @Size(max = 50, message = "真实姓名关键字长度不能超过50") 
            String realName,
            
            @Parameter(description = "用户状态：0-禁用，1-启用") 
            @RequestParam(required = false) 
            @ValidStatus
            Integer status,
            
            @Parameter(description = "排序字段") 
            @RequestParam(defaultValue = "createTime") 
            @ValidSortField(entityType = ValidSortField.EntityType.USER)
            String sortField,
            
            @Parameter(description = "排序方向：asc-升序，desc-降序") 
            @RequestParam(defaultValue = "desc") 
            @Pattern(regexp = "^(asc|desc)$", message = "排序方向只能为asc或desc") 
            String sortOrder) {
        
        // 构建查询条件
        UserQueryDTO queryDTO = new UserQueryDTO();
        queryDTO.setCurrent(current);
        queryDTO.setSize(size);
        queryDTO.setUsername(username);
        queryDTO.setEmail(email);
        queryDTO.setPhone(phone);
        queryDTO.setRealName(realName);
        queryDTO.setStatus(status);
        queryDTO.setSortField(sortField);
        queryDTO.setSortOrder(sortOrder);
        
        return userService.getUserList(queryDTO);
    }

    /**
     * 根据ID查询用户
     */
    @Operation(summary = "查询用户详情", description = "根据用户ID查询用户详细信息")
    @GetMapping("/{id}")
    public Result<User> getUserById(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id) {
        
        User user = userService.getUserById(id);
        return ResponseUtil.success("用户查询成功", user);
    }

    /**
     * 创建用户
     */
    @Operation(summary = "创建用户", description = "创建新用户")
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody UserCreateDTO createDTO) {
        
        // 转换 DTO 为 Entity
        User user = BeanUtil.createDTOToUser(createDTO);
        
        // 创建用户
        User createdUser = userService.createUser(user);
        
        return ResponseUtil.success("用户创建成功", createdUser);
    }

    /**
     * 更新用户
     */
    @Operation(summary = "更新用户", description = "更新用户信息")
    @PutMapping("/{id}")
    public Result<User> updateUser(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id,
            @Valid @RequestBody User user) {
        
        // 更新用户
        User updatedUser = userService.updateUser(id, user);
        
        return ResponseUtil.success("用户更新成功", updatedUser);
    }

    /**
     * 删除用户
     */
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id) {
        
        userService.deleteUser(id);
        return ResponseUtil.success("用户删除成功");
    }

    /**
     * 批量删除用户
     */
    @Operation(summary = "批量删除用户", description = "根据用户ID列表批量删除用户")
    @DeleteMapping
    public Result<Void> batchDeleteUsers(@RequestBody List<Long> ids) {
        
        if (ids == null || ids.isEmpty()) {
            return ResponseUtil.error("请选择要删除的用户");
        }
        
        userService.batchDeleteUsers(ids);
        return ResponseUtil.success("批量删除成功，共删除 " + ids.size() + " 个用户");
    }

    /**
     * 重置用户密码
     */
    @Operation(summary = "重置用户密码", description = "重置指定用户的密码")
    @PutMapping("/{id}/reset-password")
    public Result<Void> resetPassword(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id,
            @Parameter(description = "新密码") 
            @RequestParam String newPassword) {
        
        userService.resetPassword(id, newPassword);
        return ResponseUtil.success("密码重置成功");
    }

    /**
     * 更新用户状态
     */
    @Operation(summary = "更新用户状态", description = "启用或禁用用户")
    @PutMapping("/{id}/status")
    public Result<Void> updateUserStatus(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id,
            @Parameter(description = "状态：0-禁用，1-启用") 
            @RequestParam @ValidStatus Integer status) {
        
        userService.updateUserStatus(id, status);
        return ResponseUtil.success("用户状态更新成功");
    }

    /**
     * 获取用户角色
     */
    @Operation(summary = "获取用户角色", description = "获取指定用户的角色列表")
    @GetMapping("/{id}/roles")
    public Result<List<Role>> getUserRoles(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id) {
        
        List<Role> roles = userService.getUserRoles(id);
        return ResponseUtil.success("用户角色查询成功", roles);
    }

    /**
     * 分配用户角色
     */
    @Operation(summary = "分配用户角色", description = "为用户分配角色")
    @PutMapping("/{id}/roles")
    public Result<Void> assignUserRoles(
            @Parameter(description = "用户ID", example = "1") 
            @PathVariable Long id,
            @RequestBody List<Long> roleIds) {
        
        userService.assignUserRoles(id, roleIds);
        return ResponseUtil.success("用户角色分配成功");
    }
}
