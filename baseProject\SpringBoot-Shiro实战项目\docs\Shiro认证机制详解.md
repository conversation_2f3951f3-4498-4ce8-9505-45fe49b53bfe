# Shiro 认证机制详解

## 📋 目录

1. [认证流程概述](#认证流程概述)
2. [密码验证机制](#密码验证机制)
3. [自定义认证逻辑](#自定义认证逻辑)
4. [多种认证方式](#多种认证方式)
5. [认证调试技巧](#认证调试技巧)

## 🔍 认证流程概述

### 1. **完整的认证流程图**

```
用户登录请求
    ↓
创建AuthenticationToken
    ↓
Subject.login(token)
    ↓
SecurityManager.login()
    ↓
Authenticator.authenticate()
    ↓
Realm.doGetAuthenticationInfo()  ← 您需要实现的方法
    ↓
CredentialsMatcher.doCredentialsMatch()  ← 密码验证
    ↓
返回AuthenticationInfo
    ↓
认证成功/失败
```

### 2. **代码层面的认证流程**

```java
// 1. 用户提交登录信息
@PostMapping("/api/auth/login")
public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
    
    // 2. 创建认证令牌
    UsernamePasswordToken token = new UsernamePasswordToken(
        request.getUsername(),    // getPrincipal() 返回这个值
        request.getPassword(),    // getCredentials() 返回这个值
        request.getRememberMe()
    );
    
    // 3. 获取Subject并执行登录
    Subject subject = SecurityUtils.getSubject();
    subject.login(token);  // 这里触发认证流程
    
    // 4. 认证成功后，getPrincipal() 返回Realm中设置的用户对象
    SysUser user = (SysUser) subject.getPrincipal();
    
    return ApiResponse.success("登录成功", buildResponse(user));
}
```

## 🔐 密码验证机制

### 1. **HashedCredentialsMatcher 配置**

```java
@Bean
public HashedCredentialsMatcher hashedCredentialsMatcher() {
    HashedCredentialsMatcher credentialsMatcher = new HashedCredentialsMatcher();
    
    // 设置加密算法
    credentialsMatcher.setHashAlgorithmName("MD5");
    
    // 设置加密次数
    credentialsMatcher.setHashIterations(1);
    
    // 设置存储格式（十六进制）
    credentialsMatcher.setStoredCredentialsHexEncoded(true);
    
    return credentialsMatcher;
}

@Bean
public MyRealm myRealm() {
    MyRealm realm = new MyRealm();
    // 设置密码验证器
    realm.setCredentialsMatcher(hashedCredentialsMatcher());
    return realm;
}
```

### 2. **密码验证过程详解**

```java
public class ShiroRealm extends AuthorizingRealm {
    
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        UsernamePasswordToken upToken = (UsernamePasswordToken) token;
        String username = upToken.getUsername();
        String inputPassword = new String(upToken.getPassword());  // 用户输入的明文密码
        
        // 1. 从数据库查询用户信息
        SysUser user = userService.getUserByUsername(username);
        
        // 2. 构建认证信息
        return new SimpleAuthenticationInfo(
            user,                                           // 主体对象
            user.getPassword(),                             // 数据库中的加密密码
            ByteSource.Util.bytes(user.getCredentialsSalt()), // 盐值
            getName()
        );
        
        // 3. Shiro自动验证密码：
        // - 使用相同的算法和盐值加密用户输入的密码
        // - 与数据库中的密码进行比较
        // - 验证过程：MD5(inputPassword + salt) == user.getPassword()
    }
}
```

### 3. **密码加密存储**

```java
@Service
public class PasswordService {
    
    /**
     * 为新用户加密密码
     */
    public void encryptPassword(SysUser user) {
        // 1. 生成随机盐值
        String salt = generateSalt();
        
        // 2. 加密密码：MD5(password + username + salt)
        String encryptedPassword = DigestUtil.md5Hex(user.getPassword() + user.getUsername() + salt);
        
        // 3. 保存到用户对象
        user.setSalt(salt);
        user.setPassword(encryptedPassword);
    }
    
    /**
     * 验证密码（手动验证，用于调试）
     */
    public boolean verifyPassword(String inputPassword, SysUser user) {
        String expectedPassword = DigestUtil.md5Hex(inputPassword + user.getCredentialsSalt());
        return expectedPassword.equals(user.getPassword());
    }
    
    private String generateSalt() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}
```

## 🛠️ 自定义认证逻辑

### 1. **自定义认证Token**

```java
// 短信验证码认证Token
public class SmsCodeToken implements AuthenticationToken {
    
    private String phone;
    private String smsCode;
    
    public SmsCodeToken(String phone, String smsCode) {
        this.phone = phone;
        this.smsCode = smsCode;
    }
    
    @Override
    public Object getPrincipal() {
        return phone;  // 主体是手机号
    }
    
    @Override
    public Object getCredentials() {
        return smsCode;  // 凭证是验证码
    }
    
    // getter和setter方法...
}

// 邮箱验证码认证Token
public class EmailCodeToken implements AuthenticationToken {
    
    private String email;
    private String emailCode;
    
    public EmailCodeToken(String email, String emailCode) {
        this.email = email;
        this.emailCode = emailCode;
    }
    
    @Override
    public Object getPrincipal() {
        return email;
    }
    
    @Override
    public Object getCredentials() {
        return emailCode;
    }
}
```

### 2. **自定义Realm处理多种认证**

```java
public class MultiAuthRealm extends AuthorizingRealm {
    
    @Override
    public boolean supports(AuthenticationToken token) {
        // 支持多种Token类型
        return token instanceof UsernamePasswordToken || 
               token instanceof SmsCodeToken || 
               token instanceof EmailCodeToken;
    }
    
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        
        if (token instanceof UsernamePasswordToken) {
            return handleUsernamePasswordAuth((UsernamePasswordToken) token);
        } 
        else if (token instanceof SmsCodeToken) {
            return handleSmsCodeAuth((SmsCodeToken) token);
        } 
        else if (token instanceof EmailCodeToken) {
            return handleEmailCodeAuth((EmailCodeToken) token);
        }
        
        throw new AuthenticationException("不支持的认证类型");
    }
    
    /**
     * 处理用户名密码认证
     */
    private AuthenticationInfo handleUsernamePasswordAuth(UsernamePasswordToken token) {
        String username = token.getUsername();
        SysUser user = userService.getUserByUsername(username);
        
        if (user == null) {
            throw new UnknownAccountException("用户不存在");
        }
        
        return new SimpleAuthenticationInfo(
            user,
            user.getPassword(),
            ByteSource.Util.bytes(user.getCredentialsSalt()),
            getName()
        );
    }
    
    /**
     * 处理短信验证码认证
     */
    private AuthenticationInfo handleSmsCodeAuth(SmsCodeToken token) {
        String phone = token.getPhone();
        String smsCode = token.getSmsCode();
        
        // 1. 验证短信验证码
        if (!smsService.verifySmsCode(phone, smsCode)) {
            throw new AuthenticationException("短信验证码错误或已过期");
        }
        
        // 2. 根据手机号查询用户
        SysUser user = userService.getUserByPhone(phone);
        if (user == null) {
            throw new UnknownAccountException("手机号未注册");
        }
        
        // 3. 短信验证码认证不需要密码验证
        return new SimpleAuthenticationInfo(
            user,           // 主体
            smsCode,        // 凭证（验证码）
            getName()       // Realm名称
        );
    }
    
    /**
     * 处理邮箱验证码认证
     */
    private AuthenticationInfo handleEmailCodeAuth(EmailCodeToken token) {
        String email = token.getEmail();
        String emailCode = token.getEmailCode();
        
        // 验证邮箱验证码
        if (!emailService.verifyEmailCode(email, emailCode)) {
            throw new AuthenticationException("邮箱验证码错误或已过期");
        }
        
        SysUser user = userService.getUserByEmail(email);
        if (user == null) {
            throw new UnknownAccountException("邮箱未注册");
        }
        
        return new SimpleAuthenticationInfo(user, emailCode, getName());
    }
}
```

### 3. **多种登录方式的Controller**

```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    /**
     * 用户名密码登录
     */
    @PostMapping("/login/password")
    public ApiResponse<LoginResponse> loginByPassword(@RequestBody PasswordLoginRequest request) {
        try {
            UsernamePasswordToken token = new UsernamePasswordToken(
                request.getUsername(), 
                request.getPassword(),
                request.getRememberMe()
            );
            
            Subject subject = SecurityUtils.getSubject();
            subject.login(token);
            
            SysUser user = (SysUser) subject.getPrincipal();
            return ApiResponse.success("登录成功", buildLoginResponse(user));
            
        } catch (AuthenticationException e) {
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 短信验证码登录
     */
    @PostMapping("/login/sms")
    public ApiResponse<LoginResponse> loginBySms(@RequestBody SmsLoginRequest request) {
        try {
            SmsCodeToken token = new SmsCodeToken(request.getPhone(), request.getSmsCode());
            
            Subject subject = SecurityUtils.getSubject();
            subject.login(token);
            
            SysUser user = (SysUser) subject.getPrincipal();
            return ApiResponse.success("登录成功", buildLoginResponse(user));
            
        } catch (AuthenticationException e) {
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }
    
    /**
     * 邮箱验证码登录
     */
    @PostMapping("/login/email")
    public ApiResponse<LoginResponse> loginByEmail(@RequestBody EmailLoginRequest request) {
        try {
            EmailCodeToken token = new EmailCodeToken(request.getEmail(), request.getEmailCode());
            
            Subject subject = SecurityUtils.getSubject();
            subject.login(token);
            
            SysUser user = (SysUser) subject.getPrincipal();
            return ApiResponse.success("登录成功", buildLoginResponse(user));
            
        } catch (AuthenticationException e) {
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }
}
```

## 🔧 多种认证方式

### 1. **第三方登录（OAuth）**

```java
// 第三方登录Token
public class OAuthToken implements AuthenticationToken {
    
    private String provider;  // 提供商：wechat, qq, github等
    private String openId;    // 第三方用户ID
    private String accessToken;  // 访问令牌
    
    public OAuthToken(String provider, String openId, String accessToken) {
        this.provider = provider;
        this.openId = openId;
        this.accessToken = accessToken;
    }
    
    @Override
    public Object getPrincipal() {
        return provider + ":" + openId;
    }
    
    @Override
    public Object getCredentials() {
        return accessToken;
    }
}

// OAuth认证处理
private AuthenticationInfo handleOAuthAuth(OAuthToken token) {
    String provider = token.getProvider();
    String openId = token.getOpenId();
    String accessToken = token.getAccessToken();
    
    // 1. 验证访问令牌
    OAuthUserInfo oauthUserInfo = oauthService.getUserInfo(provider, accessToken);
    if (oauthUserInfo == null) {
        throw new AuthenticationException("第三方登录验证失败");
    }
    
    // 2. 查找或创建本地用户
    SysUser user = userService.findOrCreateOAuthUser(provider, openId, oauthUserInfo);
    
    return new SimpleAuthenticationInfo(user, accessToken, getName());
}
```

### 2. **二维码登录**

```java
// 二维码登录Token
public class QrCodeToken implements AuthenticationToken {
    
    private String qrCodeId;
    private String scanResult;
    
    public QrCodeToken(String qrCodeId, String scanResult) {
        this.qrCodeId = qrCodeId;
        this.scanResult = scanResult;
    }
    
    @Override
    public Object getPrincipal() {
        return qrCodeId;
    }
    
    @Override
    public Object getCredentials() {
        return scanResult;
    }
}

// 二维码认证处理
private AuthenticationInfo handleQrCodeAuth(QrCodeToken token) {
    String qrCodeId = token.getQrCodeId();
    String scanResult = token.getScanResult();
    
    // 1. 验证二维码状态
    QrCodeInfo qrCodeInfo = qrCodeService.getQrCodeInfo(qrCodeId);
    if (qrCodeInfo == null || qrCodeInfo.isExpired()) {
        throw new AuthenticationException("二维码已过期");
    }
    
    // 2. 验证扫描结果
    if (!qrCodeService.verifyScanResult(qrCodeId, scanResult)) {
        throw new AuthenticationException("二维码验证失败");
    }
    
    // 3. 获取扫描用户信息
    SysUser user = userService.getUserById(qrCodeInfo.getUserId());
    
    return new SimpleAuthenticationInfo(user, scanResult, getName());
}
```

### 3. **生物识别登录**

```java
// 指纹登录Token
public class FingerprintToken implements AuthenticationToken {
    
    private String userId;
    private String fingerprintData;
    
    public FingerprintToken(String userId, String fingerprintData) {
        this.userId = userId;
        this.fingerprintData = fingerprintData;
    }
    
    @Override
    public Object getPrincipal() {
        return userId;
    }
    
    @Override
    public Object getCredentials() {
        return fingerprintData;
    }
}

// 指纹认证处理
private AuthenticationInfo handleFingerprintAuth(FingerprintToken token) {
    String userId = token.getUserId();
    String fingerprintData = token.getFingerprintData();
    
    // 1. 获取用户信息
    SysUser user = userService.getUserById(Long.parseLong(userId));
    if (user == null) {
        throw new UnknownAccountException("用户不存在");
    }
    
    // 2. 验证指纹
    if (!biometricService.verifyFingerprint(userId, fingerprintData)) {
        throw new AuthenticationException("指纹验证失败");
    }
    
    return new SimpleAuthenticationInfo(user, fingerprintData, getName());
}
```

## 🐛 认证调试技巧

### 1. **添加详细日志**

```java
@Override
protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
    UsernamePasswordToken upToken = (UsernamePasswordToken) token;
    String username = upToken.getUsername();
    String inputPassword = new String(upToken.getPassword());
    
    logger.info("=== 认证调试信息 ===");
    logger.info("用户名: {}", username);
    logger.info("输入密码长度: {}", inputPassword.length());
    logger.info("Token类型: {}", token.getClass().getSimpleName());
    
    SysUser user = userService.getUserByUsername(username);
    if (user != null) {
        logger.info("数据库用户: {}", user.getUsername());
        logger.info("数据库密码: {}", user.getPassword());
        logger.info("盐值: {}", user.getSalt());
        logger.info("凭证盐值: {}", user.getCredentialsSalt());
        
        // 手动验证密码
        String expectedPassword = DigestUtil.md5Hex(inputPassword + user.getCredentialsSalt());
        logger.info("期望密码: {}", expectedPassword);
        logger.info("密码匹配: {}", expectedPassword.equals(user.getPassword()));
    }
    logger.info("==================");
    
    // 正常的认证逻辑...
}
```

### 2. **自定义CredentialsMatcher进行调试**

```java
public class DebugCredentialsMatcher extends HashedCredentialsMatcher {
    
    private static final Logger logger = LoggerFactory.getLogger(DebugCredentialsMatcher.class);
    
    @Override
    public boolean doCredentialsMatch(AuthenticationToken token, AuthenticationInfo info) {
        logger.info("=== 密码验证调试 ===");
        
        // 获取输入的密码
        Object tokenCredentials = getCredentials(token);
        logger.info("Token凭证: {}", tokenCredentials);
        
        // 获取数据库中的密码
        Object accountCredentials = getCredentials(info);
        logger.info("账户凭证: {}", accountCredentials);
        
        // 获取盐值
        Object salt = null;
        if (info instanceof SaltedAuthenticationInfo) {
            salt = ((SaltedAuthenticationInfo) info).getCredentialsSalt();
            logger.info("盐值: {}", salt);
        }
        
        // 执行父类的验证逻辑
        boolean matches = super.doCredentialsMatch(token, info);
        logger.info("验证结果: {}", matches);
        logger.info("==================");
        
        return matches;
    }
}
```

### 3. **测试不同认证方式**

```java
@Test
public void testAuthentication() {
    // 设置SecurityManager
    SecurityUtils.setSecurityManager(securityManager);
    
    // 测试用户名密码认证
    testUsernamePasswordAuth();
    
    // 测试短信验证码认证
    testSmsCodeAuth();
    
    // 测试邮箱验证码认证
    testEmailCodeAuth();
}

private void testUsernamePasswordAuth() {
    Subject subject = SecurityUtils.getSubject();
    UsernamePasswordToken token = new UsernamePasswordToken("admin", "123456");
    
    try {
        subject.login(token);
        System.out.println("用户名密码认证成功");
        System.out.println("当前用户: " + subject.getPrincipal());
    } catch (AuthenticationException e) {
        System.out.println("用户名密码认证失败: " + e.getMessage());
    } finally {
        subject.logout();
    }
}

private void testSmsCodeAuth() {
    Subject subject = SecurityUtils.getSubject();
    SmsCodeToken token = new SmsCodeToken("13800000000", "123456");
    
    try {
        subject.login(token);
        System.out.println("短信验证码认证成功");
        System.out.println("当前用户: " + subject.getPrincipal());
    } catch (AuthenticationException e) {
        System.out.println("短信验证码认证失败: " + e.getMessage());
    } finally {
        subject.logout();
    }
}
```

## 📝 总结

1. **getPrincipal()的作用**：
   - 在Token中返回用户标识（用户名、手机号等）
   - 在认证成功后返回Realm中设置的用户对象

2. **密码验证机制**：
   - Realm负责提供用户信息和数据库密码
   - CredentialsMatcher负责验证密码
   - 支持多种加密算法和盐值

3. **自定义认证**：
   - 可以创建自定义Token支持不同认证方式
   - 在Realm中处理不同类型的认证逻辑
   - 支持短信、邮箱、第三方登录等多种方式

4. **调试技巧**：
   - 添加详细日志跟踪认证过程
   - 自定义CredentialsMatcher进行密码验证调试
   - 编写测试用例验证不同认证方式
