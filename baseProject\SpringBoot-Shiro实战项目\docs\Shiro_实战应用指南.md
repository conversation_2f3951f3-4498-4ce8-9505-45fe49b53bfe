# Shiro 实战应用指南

## 📋 目录

1. [快速开始](#快速开始)
2. [实战场景](#实战场景)
3. [常见问题](#常见问题)
4. [性能优化](#性能优化)
5. [安全加固](#安全加固)
6. [监控运维](#监控运维)
7. [扩展开发](#扩展开发)

## 🚀 快速开始

### 1. 项目初始化

```bash
# 克隆项目
git clone <project-url>
cd SpringBoot-Shiro实战项目

# 创建数据库
mysql -u root -p
CREATE DATABASE shiro_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据
mysql -u root -p shiro_demo < sql/shiro_schema.sql

# 启动项目
mvn spring-boot:run
```

### 2. 基础配置检查

```yaml
# application.yml 关键配置
spring:
  datasource:
    url: **************************************
    username: root
    password: 123456

# 验证配置
curl http://localhost:8080/actuator/health
```

### 3. 登录测试

```bash
# 管理员登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'

# 响应示例
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": 1,
    "username": "admin",
    "roles": [{"roleCode": "admin"}],
    "permissions": [{"permissionCode": "system:user:view"}]
  }
}
```

## 🎯 实战场景

### 场景1：用户注册与登录

#### 1. 用户注册

```java
@PostMapping("/api/auth/register")
public ApiResponse<Void> register(@RequestBody RegisterRequest request) {
    // 1. 参数验证
    if (StrUtil.isBlank(request.getUsername())) {
        return ApiResponse.error("用户名不能为空");
    }
    
    // 2. 检查用户名是否存在
    if (userService.isUsernameExists(request.getUsername())) {
        return ApiResponse.error("用户名已存在");
    }
    
    // 3. 创建用户
    SysUser user = new SysUser();
    user.setUsername(request.getUsername());
    user.setPassword(request.getPassword());
    user.setEmail(request.getEmail());
    user.setStatus(1);
    
    boolean success = userService.createUser(user);
    return success ? ApiResponse.success("注册成功") : ApiResponse.error("注册失败");
}
```

#### 2. 登录流程

```java
@PostMapping("/api/auth/login")
public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request) {
    try {
        // 1. 创建认证令牌
        UsernamePasswordToken token = new UsernamePasswordToken(
            request.getUsername(), 
            request.getPassword(),
            request.getRememberMe()
        );
        
        // 2. 执行登录
        Subject subject = SecurityUtils.getSubject();
        subject.login(token);
        
        // 3. 获取用户信息
        SysUser user = (SysUser) subject.getPrincipal();
        
        // 4. 构建响应
        LoginResponse response = buildLoginResponse(user);
        
        return ApiResponse.success("登录成功", response);
        
    } catch (AuthenticationException e) {
        return handleAuthenticationException(e);
    }
}
```

### 场景2：权限控制

#### 1. 方法级权限控制

```java
@RestController
@RequestMapping("/api/system/users")
public class SysUserController {
    
    // 查看用户列表 - 需要权限
    @GetMapping
    @RequiresPermissions("system:user:view")
    public ApiResponse<List<SysUser>> getUserList() {
        List<SysUser> users = userService.getAllUsers();
        return ApiResponse.success("查询成功", users);
    }
    
    // 创建用户 - 需要权限
    @PostMapping
    @RequiresPermissions("system:user:add")
    public ApiResponse<SysUser> createUser(@RequestBody SysUser user) {
        boolean success = userService.createUser(user);
        return success ? ApiResponse.success("创建成功", user) : ApiResponse.error("创建失败");
    }
    
    // 删除用户 - 需要管理员角色
    @DeleteMapping("/{id}")
    @RequiresRoles("admin")
    public ApiResponse<Void> deleteUser(@PathVariable Long id) {
        boolean success = userService.deleteUser(id);
        return success ? ApiResponse.success("删除成功") : ApiResponse.error("删除失败");
    }
}
```

#### 2. 编程式权限控制

```java
@Service
public class OrderService {
    
    public ApiResponse<Order> getOrder(Long orderId) {
        Subject subject = SecurityUtils.getSubject();
        
        // 检查是否登录
        if (!subject.isAuthenticated()) {
            return ApiResponse.error("请先登录");
        }
        
        Order order = orderDao.findById(orderId);
        
        // 检查权限：管理员可以查看所有订单，普通用户只能查看自己的订单
        if (!subject.hasRole("admin")) {
            SysUser currentUser = (SysUser) subject.getPrincipal();
            if (!order.getUserId().equals(currentUser.getId())) {
                return ApiResponse.error("权限不足");
            }
        }
        
        return ApiResponse.success("查询成功", order);
    }
}
```

### 场景3：会话管理

#### 1. 在线用户管理

```java
@Service
public class OnlineUserService {
    
    @Autowired
    private SessionManager sessionManager;
    
    /**
     * 获取在线用户列表
     */
    public List<OnlineUser> getOnlineUsers() {
        Collection<Session> activeSessions = sessionManager.getActiveSessions();
        
        return activeSessions.stream()
            .filter(session -> session.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY) != null)
            .map(this::createOnlineUser)
            .collect(Collectors.toList());
    }
    
    /**
     * 强制用户下线
     */
    public boolean forceLogout(String sessionId) {
        try {
            Session session = sessionManager.getSession(new DefaultSessionKey(sessionId));
            if (session != null) {
                session.stop();
                return true;
            }
        } catch (Exception e) {
            logger.error("强制下线失败", e);
        }
        return false;
    }
    
    private OnlineUser createOnlineUser(Session session) {
        OnlineUser onlineUser = new OnlineUser();
        onlineUser.setSessionId((String) session.getId());
        onlineUser.setStartTime(session.getStartTimestamp());
        onlineUser.setLastAccessTime(session.getLastAccessTime());
        onlineUser.setHost(session.getHost());
        
        // 获取用户信息
        Object principals = session.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY);
        if (principals instanceof SimplePrincipalCollection) {
            SysUser user = (SysUser) ((SimplePrincipalCollection) principals).getPrimaryPrincipal();
            onlineUser.setUsername(user.getUsername());
            onlineUser.setUserId(user.getId());
        }
        
        return onlineUser;
    }
}
```

#### 2. 会话监听

```java
public class SessionListener implements org.apache.shiro.session.SessionListener {
    
    private static final Logger logger = LoggerFactory.getLogger(SessionListener.class);
    
    @Override
    public void onStart(Session session) {
        logger.info("会话开始: sessionId={}, host={}", session.getId(), session.getHost());
        
        // 记录登录日志
        recordLoginLog(session);
    }
    
    @Override
    public void onStop(Session session) {
        logger.info("会话结束: sessionId={}", session.getId());
        
        // 记录登出日志
        recordLogoutLog(session);
    }
    
    @Override
    public void onExpiration(Session session) {
        logger.info("会话过期: sessionId={}", session.getId());
        
        // 记录过期日志
        recordExpirationLog(session);
    }
    
    private void recordLoginLog(Session session) {
        // 实现登录日志记录
    }
    
    private void recordLogoutLog(Session session) {
        // 实现登出日志记录
    }
    
    private void recordExpirationLog(Session session) {
        // 实现过期日志记录
    }
}
```

### 场景4：动态权限

#### 1. 权限变更通知

```java
@Service
public class PermissionService {
    
    @Autowired
    private MyRealm myRealm;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 更新用户权限
     */
    @Transactional
    public boolean updateUserPermissions(Long userId, List<Long> permissionIds) {
        // 1. 更新数据库
        boolean success = userPermissionDao.updateUserPermissions(userId, permissionIds);
        
        if (success) {
            // 2. 清除Shiro缓存
            SysUser user = userService.getUserById(userId);
            myRealm.clearCachedAuthorizationInfo(user.getUsername());
            
            // 3. 发送权限变更通知
            sendPermissionChangeNotification(userId);
            
            // 4. 记录操作日志
            recordPermissionChangeLog(userId, permissionIds);
        }
        
        return success;
    }
    
    /**
     * 发送权限变更通知
     */
    private void sendPermissionChangeNotification(Long userId) {
        // 通过Redis发布订阅通知其他节点
        PermissionChangeEvent event = new PermissionChangeEvent();
        event.setUserId(userId);
        event.setTimestamp(System.currentTimeMillis());
        
        redisTemplate.convertAndSend("permission:change", event);
    }
}

// 权限变更监听器
@Component
public class PermissionChangeListener {
    
    @Autowired
    private MyRealm myRealm;
    
    @EventListener
    public void handlePermissionChange(PermissionChangeEvent event) {
        // 清除指定用户的权限缓存
        SysUser user = userService.getUserById(event.getUserId());
        if (user != null) {
            myRealm.clearCachedAuthorizationInfo(user.getUsername());
        }
    }
}
```

### 场景5：多端登录控制

#### 1. 单点登录实现

```java
@Component
public class SingleSignOnFilter extends AccessControlFilter {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        Subject subject = getSubject(request, response);
        
        if (subject.isAuthenticated()) {
            // 检查是否允许多端登录
            SysUser user = (SysUser) subject.getPrincipal();
            String currentSessionId = (String) subject.getSession().getId();
            String lastSessionId = (String) redisTemplate.opsForValue().get("user:session:" + user.getId());
            
            // 如果不允许多端登录且当前会话不是最新会话
            if (!isMultiLoginAllowed(user) && !currentSessionId.equals(lastSessionId)) {
                subject.logout();
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        // 重定向到登录页面
        redirectToLogin(request, response);
        return false;
    }
    
    private boolean isMultiLoginAllowed(SysUser user) {
        // 根据用户配置或角色判断是否允许多端登录
        return user.hasRole("admin") || user.isMultiLoginEnabled();
    }
}
```

#### 2. 登录设备管理

```java
@Service
public class LoginDeviceService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 记录登录设备
     */
    public void recordLoginDevice(Long userId, String sessionId, HttpServletRequest request) {
        LoginDevice device = new LoginDevice();
        device.setUserId(userId);
        device.setSessionId(sessionId);
        device.setDeviceType(getDeviceType(request));
        device.setIpAddress(getClientIp(request));
        device.setUserAgent(request.getHeader("User-Agent"));
        device.setLoginTime(LocalDateTime.now());
        
        // 存储到Redis，设置过期时间
        String key = "user:devices:" + userId;
        redisTemplate.opsForHash().put(key, sessionId, device);
        redisTemplate.expire(key, 30, TimeUnit.DAYS);
    }
    
    /**
     * 获取用户登录设备列表
     */
    public List<LoginDevice> getUserDevices(Long userId) {
        String key = "user:devices:" + userId;
        Map<Object, Object> devices = redisTemplate.opsForHash().entries(key);
        
        return devices.values().stream()
            .map(device -> (LoginDevice) device)
            .sorted((d1, d2) -> d2.getLoginTime().compareTo(d1.getLoginTime()))
            .collect(Collectors.toList());
    }
    
    /**
     * 踢出指定设备
     */
    public boolean kickOutDevice(Long userId, String sessionId) {
        try {
            // 1. 停止会话
            SessionManager sessionManager = ((DefaultWebSecurityManager) SecurityUtils.getSecurityManager()).getSessionManager();
            Session session = sessionManager.getSession(new DefaultSessionKey(sessionId));
            if (session != null) {
                session.stop();
            }
            
            // 2. 从Redis中移除设备记录
            String key = "user:devices:" + userId;
            redisTemplate.opsForHash().delete(key, sessionId);
            
            return true;
        } catch (Exception e) {
            logger.error("踢出设备失败", e);
            return false;
        }
    }
}
```

## ❓ 常见问题

### 1. 认证问题

#### Q: 登录时提示"用户名或密码错误"，但数据库中有该用户

**A: 检查以下几点：**

```java
// 1. 检查密码加密方式是否一致
@Bean
public HashedCredentialsMatcher hashedCredentialsMatcher() {
    HashedCredentialsMatcher matcher = new HashedCredentialsMatcher();
    matcher.setHashAlgorithmName("MD5");  // 确保与数据库中的加密方式一致
    matcher.setHashIterations(1);         // 确保迭代次数一致
    return matcher;
}

// 2. 检查盐值设置
@Override
protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
    // 确保盐值正确
    return new SimpleAuthenticationInfo(
        user,
        user.getPassword(),
        ByteSource.Util.bytes(user.getCredentialsSalt()), // 盐值要正确
        getName()
    );
}

// 3. 调试密码验证过程
public void debugPasswordVerification() {
    String inputPassword = "123456";
    String username = "admin";
    String salt = "admin_salt";
    
    String encrypted = DigestUtil.md5Hex(inputPassword + username + salt);
    System.out.println("加密后密码: " + encrypted);
    
    // 与数据库中的密码对比
}
```

#### Q: 登录成功但权限检查失败

**A: 检查授权配置：**

```java
// 1. 确保Realm的授权方法正确实现
@Override
protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
    SysUser user = (SysUser) principals.getPrimaryPrincipal();
    
    SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
    
    // 确保角色和权限正确设置
    Set<String> roles = getUserRoles(user.getId());
    Set<String> permissions = getUserPermissions(user.getId());
    
    info.setRoles(roles);
    info.setStringPermissions(permissions);
    
    return info;
}

// 2. 检查权限字符串是否匹配
@RequiresPermissions("system:user:view")  // 确保与数据库中的权限编码一致
public void viewUser() { }
```

### 2. 会话问题

#### Q: 会话频繁过期

**A: 调整会话配置：**

```java
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    
    // 延长会话超时时间
    sessionManager.setGlobalSessionTimeout(3600000); // 1小时
    
    // 调整会话验证间隔
    sessionManager.setSessionValidationInterval(1800000); // 30分钟检查一次
    
    // 禁用会话验证调度器（如果不需要）
    sessionManager.setSessionValidationSchedulerEnabled(false);
    
    return sessionManager;
}
```

#### Q: 分布式环境下会话丢失

**A: 使用Redis存储会话：**

```java
@Bean
public SessionDAO sessionDAO() {
    RedisSessionDAO sessionDAO = new RedisSessionDAO();
    sessionDAO.setRedisManager(redisManager());
    sessionDAO.setExpire(1800); // 30分钟
    return sessionDAO;
}

@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    sessionManager.setSessionDAO(sessionDAO());
    return sessionManager;
}
```

### 3. 缓存问题

#### Q: 权限更新后不生效

**A: 及时清除缓存：**

```java
@Service
public class UserService {
    
    @Autowired
    private MyRealm myRealm;
    
    public void updateUserRoles(String username, List<String> roles) {
        // 1. 更新数据库
        userDao.updateUserRoles(username, roles);
        
        // 2. 清除Shiro缓存
        myRealm.clearCachedAuthorizationInfo(username);
        
        // 3. 如果使用Redis缓存，也要清除Redis缓存
        redisTemplate.delete("shiro:authorization:" + username);
    }
}
```

#### Q: 缓存占用内存过多

**A: 优化缓存配置：**

```xml
<!-- ehcache.xml -->
<cache name="authorizationCache"
       maxElementsInMemory="1000"     <!-- 减少内存中的元素数量 -->
       timeToIdleSeconds="600"        <!-- 10分钟空闲时间 -->
       timeToLiveSeconds="1800"       <!-- 30分钟生存时间 -->
       overflowToDisk="true"          <!-- 溢出到磁盘 -->
       diskPersistent="false"/>       <!-- 不持久化到磁盘 -->
```

### 4. 性能问题

#### Q: 权限检查导致性能下降

**A: 优化策略：**

```java
// 1. 启用缓存
@Bean
public MyRealm myRealm() {
    MyRealm realm = new MyRealm();
    realm.setCachingEnabled(true);
    realm.setAuthorizationCachingEnabled(true);
    realm.setAuthenticationCachingEnabled(true);
    return realm;
}

// 2. 批量加载权限
@Override
protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
    // 一次性加载用户的所有角色和权限，避免多次查询
    SysUser user = (SysUser) principals.getPrimaryPrincipal();
    SysUser userWithPermissions = userService.getUserWithRolesAndPermissions(user.getId());
    
    // 构建授权信息
    SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
    // ... 设置角色和权限
    
    return info;
}

// 3. 使用连接池
@Bean
public DataSource dataSource() {
    DruidDataSource dataSource = new DruidDataSource();
    dataSource.setInitialSize(5);
    dataSource.setMaxActive(20);
    dataSource.setMinIdle(5);
    dataSource.setMaxWait(60000);
    return dataSource;
}
```

## ⚡ 性能优化

### 1. 缓存优化

```java
// 1. 分层缓存策略
@Configuration
public class CacheConfig {
    
    // L1缓存：本地缓存（EhCache）
    @Bean
    public EhCacheManager ehCacheManager() {
        EhCacheManager cacheManager = new EhCacheManager();
        cacheManager.setCacheManagerConfigFile("classpath:ehcache.xml");
        return cacheManager;
    }
    
    // L2缓存：分布式缓存（Redis）
    @Bean
    public RedisCacheManager redisCacheManager() {
        RedisCacheManager cacheManager = new RedisCacheManager();
        cacheManager.setRedisManager(redisManager());
        return cacheManager;
    }
}

// 2. 缓存预热
@Component
public class CacheWarmUp {
    
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        // 预加载热点用户的权限信息
        List<String> hotUsers = Arrays.asList("admin", "manager");
        for (String username : hotUsers) {
            SysUser user = userService.getUserByUsername(username);
            if (user != null) {
                userService.getUserWithRolesAndPermissions(user.getId());
            }
        }
    }
}
```

### 2. 数据库优化

```sql
-- 1. 添加索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_status ON sys_user(status);
CREATE INDEX idx_user_role_user_id ON sys_user_role(user_id);
CREATE INDEX idx_role_permission_role_id ON sys_role_permission(role_id);

-- 2. 优化查询
-- 使用JOIN代替子查询
SELECT DISTINCT p.permission_code 
FROM sys_permission p
INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
WHERE ur.user_id = ? AND p.status = 1;
```

### 3. 会话优化

```java
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    
    // 1. 合理设置会话超时时间
    sessionManager.setGlobalSessionTimeout(1800000); // 30分钟
    
    // 2. 优化会话验证
    sessionManager.setSessionValidationSchedulerEnabled(true);
    sessionManager.setSessionValidationInterval(1800000); // 30分钟检查一次
    
    // 3. 使用高效的SessionDAO
    sessionManager.setSessionDAO(redisSessionDAO());
    
    return sessionManager;
}

// 自定义高效的SessionDAO
public class OptimizedRedisSessionDAO extends AbstractSessionDAO {
    
    @Override
    public void update(Session session) throws UnknownSessionException {
        // 只有会话属性发生变化时才更新
        if (session instanceof ValidatingSession) {
            ValidatingSession validatingSession = (ValidatingSession) session;
            if (validatingSession.isValid()) {
                // 异步更新，避免阻塞
                CompletableFuture.runAsync(() -> {
                    redisTemplate.opsForValue().set(
                        getKey(session.getId()), 
                        session, 
                        30, 
                        TimeUnit.MINUTES
                    );
                });
            }
        }
    }
}
```

## 🔒 安全加固

### 1. 密码安全

```java
@Component
public class PasswordPolicy {
    
    /**
     * 密码强度验证
     */
    public boolean validatePasswordStrength(String password) {
        // 1. 长度检查
        if (password.length() < 8) {
            return false;
        }
        
        // 2. 复杂度检查
        boolean hasUpper = password.matches(".*[A-Z].*");
        boolean hasLower = password.matches(".*[a-z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        boolean hasSpecial = password.matches(".*[!@#$%^&*()].*");
        
        int complexity = 0;
        if (hasUpper) complexity++;
        if (hasLower) complexity++;
        if (hasDigit) complexity++;
        if (hasSpecial) complexity++;
        
        return complexity >= 3;
    }
    
    /**
     * 检查密码是否在黑名单中
     */
    public boolean isPasswordInBlacklist(String password) {
        Set<String> blacklist = Set.of(
            "123456", "password", "admin", "root", 
            "qwerty", "123456789", "12345678"
        );
        return blacklist.contains(password.toLowerCase());
    }
}

// 密码加密增强
@Bean
public HashedCredentialsMatcher hashedCredentialsMatcher() {
    HashedCredentialsMatcher matcher = new HashedCredentialsMatcher();
    matcher.setHashAlgorithmName("SHA-256");  // 使用更安全的算法
    matcher.setHashIterations(10000);         // 增加迭代次数
    matcher.setStoredCredentialsHexEncoded(true);
    return matcher;
}
```

### 2. 会话安全

```java
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    
    // 1. 安全的Cookie配置
    SimpleCookie cookie = new SimpleCookie("JSESSIONID");
    cookie.setHttpOnly(true);      // 防止XSS攻击
    cookie.setSecure(true);        // 只在HTTPS下传输
    cookie.setSameSite("Strict");  // 防止CSRF攻击
    cookie.setMaxAge(-1);          // 浏览器关闭时失效
    sessionManager.setSessionIdCookie(cookie);
    
    // 2. 禁用URL重写
    sessionManager.setSessionIdUrlRewritingEnabled(false);
    
    // 3. 会话固化攻击防护
    sessionManager.setSessionIdCookieEnabled(true);
    
    return sessionManager;
}

// 会话安全监听器
public class SecuritySessionListener implements SessionListener {
    
    @Override
    public void onStart(Session session) {
        // 记录会话开始，检测异常登录
        String host = session.getHost();
        if (isAbnormalLogin(host)) {
            session.stop();
            throw new AuthenticationException("检测到异常登录");
        }
    }
    
    private boolean isAbnormalLogin(String host) {
        // 实现异常登录检测逻辑
        // 例如：检查IP地理位置、登录频率等
        return false;
    }
}
```

### 3. 防暴力破解

```java
@Component
public class LoginAttemptService {
    
    private final int MAX_ATTEMPT = 5;
    private final int LOCK_TIME_DURATION = 30; // 分钟
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 记录登录失败
     */
    public void loginFailed(String username, String ip) {
        String userKey = "login:attempt:user:" + username;
        String ipKey = "login:attempt:ip:" + ip;
        
        // 记录用户登录失败次数
        redisTemplate.opsForValue().increment(userKey);
        redisTemplate.expire(userKey, LOCK_TIME_DURATION, TimeUnit.MINUTES);
        
        // 记录IP登录失败次数
        redisTemplate.opsForValue().increment(ipKey);
        redisTemplate.expire(ipKey, LOCK_TIME_DURATION, TimeUnit.MINUTES);
    }
    
    /**
     * 检查是否被锁定
     */
    public boolean isBlocked(String username, String ip) {
        String userKey = "login:attempt:user:" + username;
        String ipKey = "login:attempt:ip:" + ip;
        
        Integer userAttempts = (Integer) redisTemplate.opsForValue().get(userKey);
        Integer ipAttempts = (Integer) redisTemplate.opsForValue().get(ipKey);
        
        return (userAttempts != null && userAttempts >= MAX_ATTEMPT) ||
               (ipAttempts != null && ipAttempts >= MAX_ATTEMPT);
    }
    
    /**
     * 登录成功，清除失败记录
     */
    public void loginSucceeded(String username, String ip) {
        redisTemplate.delete("login:attempt:user:" + username);
        redisTemplate.delete("login:attempt:ip:" + ip);
    }
}

// 在登录控制器中使用
@PostMapping("/api/auth/login")
public ApiResponse<LoginResponse> login(@RequestBody LoginRequest request, HttpServletRequest httpRequest) {
    String ip = getClientIp(httpRequest);
    
    // 检查是否被锁定
    if (loginAttemptService.isBlocked(request.getUsername(), ip)) {
        return ApiResponse.error("账户或IP已被锁定，请稍后再试");
    }
    
    try {
        // 执行登录逻辑
        // ...
        
        // 登录成功
        loginAttemptService.loginSucceeded(request.getUsername(), ip);
        return ApiResponse.success("登录成功", response);
        
    } catch (AuthenticationException e) {
        // 登录失败
        loginAttemptService.loginFailed(request.getUsername(), ip);
        return ApiResponse.error("登录失败");
    }
}
```

### 4. 权限安全

```java
// 1. 权限最小化原则
@Service
public class PermissionService {
    
    /**
     * 检查数据权限
     */
    public boolean hasDataPermission(Long userId, String resource, Long resourceId) {
        Subject subject = SecurityUtils.getSubject();
        SysUser currentUser = (SysUser) subject.getPrincipal();
        
        // 管理员有所有权限
        if (subject.hasRole("admin")) {
            return true;
        }
        
        // 检查是否是资源所有者
        if (isResourceOwner(currentUser.getId(), resource, resourceId)) {
            return true;
        }
        
        // 检查部门权限
        if (hasDepartmentPermission(currentUser.getId(), resource, resourceId)) {
            return true;
        }
        
        return false;
    }
}

// 2. 敏感操作二次验证
@PostMapping("/api/system/users/{id}/delete")
@RequiresPermissions("system:user:delete")
public ApiResponse<Void> deleteUser(@PathVariable Long id, @RequestParam String confirmPassword) {
    Subject subject = SecurityUtils.getSubject();
    SysUser currentUser = (SysUser) subject.getPrincipal();
    
    // 二次密码验证
    if (!passwordHelper.verifyPassword(confirmPassword, currentUser.getPassword(), currentUser.getSalt())) {
        return ApiResponse.error("密码验证失败");
    }
    
    // 执行删除操作
    boolean success = userService.deleteUser(id);
    return success ? ApiResponse.success("删除成功") : ApiResponse.error("删除失败");
}
```

## 📊 监控运维

### 1. 登录监控

```java
@Component
public class LoginMonitor {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 记录登录日志
     */
    @EventListener
    public void handleLoginEvent(LoginEvent event) {
        LoginLog log = new LoginLog();
        log.setUserId(event.getUserId());
        log.setUsername(event.getUsername());
        log.setIpAddress(event.getIpAddress());
        log.setUserAgent(event.getUserAgent());
        log.setLoginTime(LocalDateTime.now());
        log.setStatus(event.isSuccess() ? 1 : 0);
        
        // 异步保存日志
        CompletableFuture.runAsync(() -> {
            loginLogService.save(log);
        });
        
        // 实时统计
        updateLoginStatistics(event);
    }
    
    /**
     * 更新登录统计
     */
    private void updateLoginStatistics(LoginEvent event) {
        String date = LocalDate.now().toString();
        
        // 今日登录次数
        redisTemplate.opsForValue().increment("login:count:" + date);
        
        // 今日登录用户数
        redisTemplate.opsForSet().add("login:users:" + date, event.getUserId());
        
        // 设置过期时间（保留30天）
        redisTemplate.expire("login:count:" + date, 30, TimeUnit.DAYS);
        redisTemplate.expire("login:users:" + date, 30, TimeUnit.DAYS);
    }
    
    /**
     * 获取登录统计
     */
    public LoginStatistics getLoginStatistics(String date) {
        LoginStatistics statistics = new LoginStatistics();
        statistics.setDate(date);
        statistics.setLoginCount((Integer) redisTemplate.opsForValue().get("login:count:" + date));
        statistics.setUserCount(redisTemplate.opsForSet().size("login:users:" + date).intValue());
        return statistics;
    }
}
```

### 2. 性能监控

```java
@Component
public class ShiroPerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    public ShiroPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    /**
     * 监控认证性能
     */
    public void monitorAuthentication(String username, long duration, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("shiro.authentication")
            .tag("username", username)
            .tag("success", String.valueOf(success))
            .register(meterRegistry));
        
        // 记录认证次数
        Counter.builder("shiro.authentication.count")
            .tag("success", String.valueOf(success))
            .register(meterRegistry)
            .increment();
    }
    
    /**
     * 监控授权性能
     */
    public void monitorAuthorization(String permission, long duration, boolean granted) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("shiro.authorization")
            .tag("permission", permission)
            .tag("granted", String.valueOf(granted))
            .register(meterRegistry));
    }
    
    /**
     * 监控缓存性能
     */
    public void monitorCache(String cacheName, String operation, boolean hit) {
        Counter.builder("shiro.cache")
            .tag("cache", cacheName)
            .tag("operation", operation)
            .tag("hit", String.valueOf(hit))
            .register(meterRegistry)
            .increment();
    }
}
```

### 3. 健康检查

```java
@Component
public class ShiroHealthIndicator implements HealthIndicator {
    
    @Autowired
    private SecurityManager securityManager;
    
    @Autowired
    private SessionManager sessionManager;
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // 检查SecurityManager
            if (securityManager == null) {
                return builder.down().withDetail("SecurityManager", "not available").build();
            }
            
            // 检查SessionManager
            if (sessionManager == null) {
                return builder.down().withDetail("SessionManager", "not available").build();
            }
            
            // 检查活跃会话数
            Collection<Session> activeSessions = sessionManager.getActiveSessions();
            int activeSessionCount = activeSessions.size();
            
            // 检查缓存状态
            boolean cacheAvailable = checkCacheHealth();
            
            builder.up()
                .withDetail("SecurityManager", "available")
                .withDetail("SessionManager", "available")
                .withDetail("ActiveSessions", activeSessionCount)
                .withDetail("Cache", cacheAvailable ? "available" : "unavailable");
            
        } catch (Exception e) {
            builder.down().withException(e);
        }
        
        return builder.build();
    }
    
    private boolean checkCacheHealth() {
        try {
            // 检查缓存是否可用
            CacheManager cacheManager = ((DefaultWebSecurityManager) securityManager).getCacheManager();
            if (cacheManager != null) {
                Cache<Object, Object> cache = cacheManager.getCache("authorizationCache");
                return cache != null;
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }
}
```

## 🔧 扩展开发

### 1. 自定义认证Token

```java
// 短信验证码Token
public class SmsCodeToken implements AuthenticationToken {
    
    private String phone;
    private String smsCode;
    
    public SmsCodeToken(String phone, String smsCode) {
        this.phone = phone;
        this.smsCode = smsCode;
    }
    
    @Override
    public Object getPrincipal() {
        return phone;
    }
    
    @Override
    public Object getCredentials() {
        return smsCode;
    }
    
    // getter和setter方法
}

// 短信认证Realm
public class SmsCodeRealm extends AuthorizingRealm {
    
    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof SmsCodeToken;
    }
    
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        SmsCodeToken smsToken = (SmsCodeToken) token;
        String phone = smsToken.getPhone();
        String smsCode = smsToken.getSmsCode();
        
        // 验证短信验证码
        if (!smsService.verifySmsCode(phone, smsCode)) {
            throw new AuthenticationException("短信验证码错误");
        }
        
        // 根据手机号查询用户
        SysUser user = userService.getUserByPhone(phone);
        if (user == null) {
            throw new UnknownAccountException("用户不存在");
        }
        
        return new SimpleAuthenticationInfo(user, smsCode, getName());
    }
    
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        // 实现授权逻辑
        return null;
    }
}
```

### 2. 自定义权限注解

```java
// 数据权限注解
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermission {
    
    /**
     * 数据权限类型
     */
    DataScope value() default DataScope.ALL;
    
    /**
     * 用户ID字段名
     */
    String userIdField() default "userId";
    
    /**
     * 部门ID字段名
     */
    String deptIdField() default "deptId";
}

// 数据权限切面
@Aspect
@Component
public class DataPermissionAspect {
    
    @Around("@annotation(dataPermission)")
    public Object around(ProceedingJoinPoint point, DataPermission dataPermission) throws Throwable {
        Subject subject = SecurityUtils.getSubject();
        SysUser currentUser = (SysUser) subject.getPrincipal();
        
        // 管理员跳过数据权限检查
        if (subject.hasRole("admin")) {
            return point.proceed();
        }
        
        // 根据数据权限类型处理
        switch (dataPermission.value()) {
            case SELF:
                // 只能查看自己的数据
                addSelfDataFilter(currentUser.getId(), dataPermission.userIdField());
                break;
            case DEPT:
                // 只能查看本部门的数据
                addDeptDataFilter(currentUser.getDeptId(), dataPermission.deptIdField());
                break;
            case ALL:
            default:
                // 查看所有数据
                break;
        }
        
        return point.proceed();
    }
    
    private void addSelfDataFilter(Long userId, String userIdField) {
        // 实现自己数据过滤逻辑
    }
    
    private void addDeptDataFilter(Long deptId, String deptIdField) {
        // 实现部门数据过滤逻辑
    }
}
```

### 3. 多租户支持

```java
// 租户上下文
public class TenantContext {
    
    private static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();
    
    public static void setTenantId(Long tenantId) {
        TENANT_ID.set(tenantId);
    }
    
    public static Long getTenantId() {
        return TENANT_ID.get();
    }
    
    public static void clear() {
        TENANT_ID.remove();
    }
}

// 多租户Realm
public class MultiTenantRealm extends AuthorizingRealm {
    
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        UsernamePasswordToken upToken = (UsernamePasswordToken) token;
        String username = upToken.getUsername();
        
        // 从用户名中解析租户ID（格式：tenantId@username）
        String[] parts = username.split("@");
        if (parts.length == 2) {
            Long tenantId = Long.parseLong(parts[0]);
            String realUsername = parts[1];
            
            // 设置租户上下文
            TenantContext.setTenantId(tenantId);
            
            // 查询用户
            SysUser user = userService.getUserByUsernameAndTenant(realUsername, tenantId);
            // ...
        }
        
        return null;
    }
}

// 多租户过滤器
public class TenantFilter extends OncePerRequestFilter {
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        try {
            // 从请求头或域名中获取租户ID
            Long tenantId = extractTenantId(request);
            if (tenantId != null) {
                TenantContext.setTenantId(tenantId);
            }
            
            filterChain.doFilter(request, response);
        } finally {
            TenantContext.clear();
        }
    }
    
    private Long extractTenantId(HttpServletRequest request) {
        // 从请求头获取
        String tenantHeader = request.getHeader("X-Tenant-Id");
        if (tenantHeader != null) {
            return Long.parseLong(tenantHeader);
        }
        
        // 从子域名获取
        String serverName = request.getServerName();
        if (serverName.contains(".")) {
            String subdomain = serverName.split("\\.")[0];
            return tenantService.getTenantIdBySubdomain(subdomain);
        }
        
        return null;
    }
}
```

这个详细的Shiro实战应用指南涵盖了从基础使用到高级扩展的各个方面，包括常见问题解决、性能优化、安全加固等实际开发中会遇到的各种场景。通过这些实战案例，您可以更好地理解和应用Shiro框架。
