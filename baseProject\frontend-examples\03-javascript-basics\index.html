<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript基础示例 - 交互编程入门</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="header">
            <h1 class="main-title">⚡ JavaScript基础学习示例</h1>
            <p class="subtitle">学习JavaScript，为网页添加交互功能</p>
            <div class="live-clock" id="liveClock"></div>
        </header>

        <!-- 导航菜单 -->
        <nav class="navigation">
            <ul class="nav-list">
                <li><a href="#variables" class="nav-link">变量与数据类型</a></li>
                <li><a href="#functions" class="nav-link">函数</a></li>
                <li><a href="#dom" class="nav-link">DOM操作</a></li>
                <li><a href="#events" class="nav-link">事件处理</a></li>
                <li><a href="#async" class="nav-link">异步编程</a></li>
                <li><a href="#projects" class="nav-link">实战项目</a></li>
            </ul>
        </nav>

        <!-- 变量与数据类型 -->
        <section id="variables" class="section">
            <h2 class="section-title">📊 变量与数据类型</h2>
            
            <div class="demo-container">
                <div class="demo-item">
                    <h3>基本数据类型</h3>
                    <div class="code-demo">
                        <button onclick="demonstrateDataTypes()">运行示例</button>
                        <div id="dataTypesOutput" class="output"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>数组操作</h3>
                    <div class="array-demo">
                        <input type="text" id="arrayInput" placeholder="输入一个值">
                        <button onclick="addToArray()">添加到数组</button>
                        <button onclick="removeFromArray()">删除最后一个</button>
                        <button onclick="clearArray()">清空数组</button>
                        <div id="arrayDisplay" class="array-display"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>对象操作</h3>
                    <div class="object-demo">
                        <input type="text" id="personName" placeholder="姓名">
                        <input type="number" id="personAge" placeholder="年龄">
                        <input type="text" id="personCity" placeholder="城市">
                        <button onclick="createPerson()">创建对象</button>
                        <div id="objectDisplay" class="object-display"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 函数 -->
        <section id="functions" class="section">
            <h2 class="section-title">🔧 函数</h2>
            
            <div class="demo-container">
                <div class="demo-item">
                    <h3>计算器</h3>
                    <div class="calculator">
                        <input type="number" id="num1" placeholder="第一个数">
                        <select id="operator">
                            <option value="+">+</option>
                            <option value="-">-</option>
                            <option value="*">×</option>
                            <option value="/">/</option>
                        </select>
                        <input type="number" id="num2" placeholder="第二个数">
                        <button onclick="calculate()">=</button>
                        <div id="calcResult" class="calc-result"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>字符串处理</h3>
                    <div class="string-demo">
                        <textarea id="stringInput" placeholder="输入一些文本..."></textarea>
                        <div class="string-buttons">
                            <button onclick="stringToUpper()">转大写</button>
                            <button onclick="stringToLower()">转小写</button>
                            <button onclick="reverseString()">反转</button>
                            <button onclick="countWords()">统计单词</button>
                        </div>
                        <div id="stringOutput" class="output"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>高阶函数</h3>
                    <div class="higher-order-demo">
                        <button onclick="demonstrateMap()">Array.map()</button>
                        <button onclick="demonstrateFilter()">Array.filter()</button>
                        <button onclick="demonstrateReduce()">Array.reduce()</button>
                        <div id="higherOrderOutput" class="output"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- DOM操作 -->
        <section id="dom" class="section">
            <h2 class="section-title">🌐 DOM操作</h2>
            
            <div class="demo-container">
                <div class="demo-item">
                    <h3>元素创建与修改</h3>
                    <div class="dom-demo">
                        <input type="text" id="elementText" placeholder="输入文本">
                        <select id="elementType">
                            <option value="p">段落 (p)</option>
                            <option value="h3">标题 (h3)</option>
                            <option value="div">容器 (div)</option>
                            <option value="span">行内 (span)</option>
                        </select>
                        <button onclick="createElement()">创建元素</button>
                        <button onclick="clearElements()">清空</button>
                        <div id="dynamicContent" class="dynamic-content"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>样式控制</h3>
                    <div class="style-demo">
                        <div id="styleTarget" class="style-target">我是一个可以改变样式的元素</div>
                        <div class="style-controls">
                            <button onclick="changeColor()">改变颜色</button>
                            <button onclick="changeSize()">改变大小</button>
                            <button onclick="toggleVisibility()">显示/隐藏</button>
                            <button onclick="resetStyles()">重置样式</button>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>列表管理</h3>
                    <div class="list-demo">
                        <input type="text" id="listInput" placeholder="添加列表项">
                        <button onclick="addListItem()">添加</button>
                        <ul id="dynamicList" class="dynamic-list"></ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- 事件处理 -->
        <section id="events" class="section">
            <h2 class="section-title">🎯 事件处理</h2>
            
            <div class="demo-container">
                <div class="demo-item">
                    <h3>鼠标事件</h3>
                    <div class="mouse-demo">
                        <div id="mouseTarget" class="mouse-target">
                            鼠标交互区域
                            <div id="mouseInfo" class="mouse-info"></div>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>键盘事件</h3>
                    <div class="keyboard-demo">
                        <input type="text" id="keyboardInput" placeholder="在这里输入，观察键盘事件">
                        <div id="keyboardInfo" class="keyboard-info"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>表单事件</h3>
                    <div class="form-demo">
                        <form id="demoForm">
                            <input type="text" id="formName" placeholder="姓名" required>
                            <input type="email" id="formEmail" placeholder="邮箱" required>
                            <button type="submit">提交</button>
                        </form>
                        <div id="formOutput" class="output"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 异步编程 -->
        <section id="async" class="section">
            <h2 class="section-title">⏰ 异步编程</h2>
            
            <div class="demo-container">
                <div class="demo-item">
                    <h3>定时器</h3>
                    <div class="timer-demo">
                        <div id="timerDisplay" class="timer-display">0</div>
                        <div class="timer-controls">
                            <button onclick="startTimer()">开始</button>
                            <button onclick="pauseTimer()">暂停</button>
                            <button onclick="resetTimer()">重置</button>
                        </div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Promise示例</h3>
                    <div class="promise-demo">
                        <button onclick="simulateApiCall()">模拟API调用</button>
                        <button onclick="simulateError()">模拟错误</button>
                        <div id="promiseOutput" class="output"></div>
                    </div>
                </div>

                <div class="demo-item">
                    <h3>Fetch API</h3>
                    <div class="fetch-demo">
                        <button onclick="fetchRandomQuote()">获取随机名言</button>
                        <button onclick="fetchUserData()">获取用户数据</button>
                        <div id="fetchOutput" class="output"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 实战项目 -->
        <section id="projects" class="section">
            <h2 class="section-title">🚀 实战项目</h2>
            
            <div class="projects-container">
                <!-- 待办事项应用 -->
                <div class="project-item">
                    <h3>📝 待办事项应用</h3>
                    <div class="todo-app">
                        <div class="todo-input">
                            <input type="text" id="todoInput" placeholder="添加新任务...">
                            <button onclick="addTodo()">添加</button>
                        </div>
                        <div class="todo-filters">
                            <button onclick="filterTodos('all')" class="filter-btn active">全部</button>
                            <button onclick="filterTodos('active')" class="filter-btn">未完成</button>
                            <button onclick="filterTodos('completed')" class="filter-btn">已完成</button>
                        </div>
                        <ul id="todoList" class="todo-list"></ul>
                        <div class="todo-stats">
                            <span id="todoCount">0 个任务</span>
                            <button onclick="clearCompleted()">清除已完成</button>
                        </div>
                    </div>
                </div>

                <!-- 简单游戏 -->
                <div class="project-item">
                    <h3>🎮 猜数字游戏</h3>
                    <div class="guess-game">
                        <div class="game-info">
                            <p>我想了一个1-100之间的数字，你能猜中吗？</p>
                            <div id="gameStats" class="game-stats">
                                <span>尝试次数: <span id="attempts">0</span></span>
                                <span>最佳记录: <span id="bestScore">-</span></span>
                            </div>
                        </div>
                        <div class="game-input">
                            <input type="number" id="guessInput" min="1" max="100" placeholder="输入你的猜测">
                            <button onclick="makeGuess()">猜测</button>
                            <button onclick="resetGame()">重新开始</button>
                        </div>
                        <div id="gameOutput" class="game-output"></div>
                    </div>
                </div>

                <!-- 颜色生成器 -->
                <div class="project-item">
                    <h3>🎨 随机颜色生成器</h3>
                    <div class="color-generator">
                        <div id="colorDisplay" class="color-display"></div>
                        <div class="color-info">
                            <div id="colorCode" class="color-code">#000000</div>
                            <div class="color-controls">
                                <button onclick="generateRandomColor()">随机颜色</button>
                                <button onclick="copyColorCode()">复制代码</button>
                            </div>
                        </div>
                        <div class="color-palette" id="colorPalette"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页面底部 -->
        <footer class="footer">
            <h2>🎉 JavaScript学习完成！</h2>
            <p>恭喜您掌握了JavaScript的基础知识！现在您可以创建交互式网页了。</p>
            <div class="next-steps">
                <h3>下一步学习建议：</h3>
                <ul>
                    <li>深入学习ES6+新特性</li>
                    <li>学习前端框架 (Vue.js/React)</li>
                    <li>掌握异步编程和Promise</li>
                    <li>学习Node.js进行后端开发</li>
                    <li>了解TypeScript类型系统</li>
                </ul>
            </div>
            
            <div class="learning-progress">
                <h3>学习进度</h3>
                <div class="progress-item">
                    <span>HTML基础</span>
                    <div class="progress-bar"><div class="progress" style="width: 100%"></div></div>
                </div>
                <div class="progress-item">
                    <span>CSS样式</span>
                    <div class="progress-bar"><div class="progress" style="width: 100%"></div></div>
                </div>
                <div class="progress-item">
                    <span>JavaScript</span>
                    <div class="progress-bar"><div class="progress" style="width: 100%"></div></div>
                </div>
                <div class="progress-item">
                    <span>前端框架</span>
                    <div class="progress-bar"><div class="progress" style="width: 0%"></div></div>
                </div>
            </div>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
