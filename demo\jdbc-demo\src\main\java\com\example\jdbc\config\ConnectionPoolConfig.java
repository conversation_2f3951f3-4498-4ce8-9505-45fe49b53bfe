package com.example.jdbc.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.dbcp2.BasicDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 连接池配置类
 * 演示HikariCP和DBCP2两种连接池的配置
 */
@Configuration
public class ConnectionPoolConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(ConnectionPoolConfig.class);
    
    @Autowired
    private DatabaseConfig databaseConfig;
    
    // HikariCP配置
    @Value("${hikari.maximum-pool-size:10}")
    private int hikariMaximumPoolSize;
    
    @Value("${hikari.minimum-idle:5}")
    private int hikariMinimumIdle;
    
    @Value("${hikari.connection-timeout:30000}")
    private long hikariConnectionTimeout;
    
    @Value("${hikari.idle-timeout:600000}")
    private long hikariIdleTimeout;
    
    @Value("${hikari.max-lifetime:1800000}")
    private long hikariMaxLifetime;
    
    @Value("${hikari.leak-detection-threshold:60000}")
    private long hikariLeakDetectionThreshold;
    
    // DBCP2配置
    @Value("${dbcp2.initial-size:5}")
    private int dbcp2InitialSize;
    
    @Value("${dbcp2.max-total:10}")
    private int dbcp2MaxTotal;
    
    @Value("${dbcp2.max-idle:8}")
    private int dbcp2MaxIdle;
    
    @Value("${dbcp2.min-idle:2}")
    private int dbcp2MinIdle;
    
    @Value("${dbcp2.max-wait-millis:10000}")
    private long dbcp2MaxWaitMillis;
    
    @Value("${dbcp2.validation-query:SELECT 1}")
    private String dbcp2ValidationQuery;
    
    @Value("${dbcp2.test-on-borrow:true}")
    private boolean dbcp2TestOnBorrow;
    
    @Value("${dbcp2.test-while-idle:true}")
    private boolean dbcp2TestWhileIdle;
    
    /**
     * HikariCP连接池配置
     * HikariCP是Spring Boot默认的连接池，性能优秀
     */
    @Bean(name = "hikariDataSource")
    public DataSource hikariDataSource() {
        DatabaseConfig.DatabaseInfo dbInfo = databaseConfig.getCurrentDatabaseInfo();
        
        HikariConfig config = new HikariConfig();
        
        // 基本连接信息
        config.setJdbcUrl(dbInfo.getUrl());
        config.setUsername(dbInfo.getUsername());
        config.setPassword(dbInfo.getPassword());
        config.setDriverClassName(dbInfo.getDriver());
        
        // 连接池配置
        config.setMaximumPoolSize(hikariMaximumPoolSize);
        config.setMinimumIdle(hikariMinimumIdle);
        config.setConnectionTimeout(hikariConnectionTimeout);
        config.setIdleTimeout(hikariIdleTimeout);
        config.setMaxLifetime(hikariMaxLifetime);
        config.setLeakDetectionThreshold(hikariLeakDetectionThreshold);
        
        // 连接池名称
        config.setPoolName("HikariCP-Pool");
        
        // 连接测试查询
        if (dbInfo.getUrl().contains("mysql")) {
            config.setConnectionTestQuery("SELECT 1");
        } else if (dbInfo.getUrl().contains("postgresql")) {
            config.setConnectionTestQuery("SELECT 1");
        } else if (dbInfo.getUrl().contains("h2")) {
            config.setConnectionTestQuery("SELECT 1");
        }
        
        // 其他配置
        config.setAutoCommit(true);
        config.setConnectionInitSql("SELECT 1");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        logger.info("HikariCP连接池配置完成");
        logger.info("最大连接数: {}, 最小空闲连接数: {}", hikariMaximumPoolSize, hikariMinimumIdle);
        
        return dataSource;
    }
    
    /**
     * DBCP2连接池配置
     * Apache Commons DBCP2是另一个流行的连接池
     */
    @Bean(name = "dbcp2DataSource")
    public DataSource dbcp2DataSource() {
        DatabaseConfig.DatabaseInfo dbInfo = databaseConfig.getCurrentDatabaseInfo();
        
        BasicDataSource dataSource = new BasicDataSource();
        
        // 基本连接信息
        dataSource.setUrl(dbInfo.getUrl());
        dataSource.setUsername(dbInfo.getUsername());
        dataSource.setPassword(dbInfo.getPassword());
        dataSource.setDriverClassName(dbInfo.getDriver());
        
        // 连接池配置
        dataSource.setInitialSize(dbcp2InitialSize);
        dataSource.setMaxTotal(dbcp2MaxTotal);
        dataSource.setMaxIdle(dbcp2MaxIdle);
        dataSource.setMinIdle(dbcp2MinIdle);
        dataSource.setMaxWaitMillis(dbcp2MaxWaitMillis);
        
        // 连接验证
        dataSource.setValidationQuery(dbcp2ValidationQuery);
        dataSource.setTestOnBorrow(dbcp2TestOnBorrow);
        dataSource.setTestWhileIdle(dbcp2TestWhileIdle);
        dataSource.setTestOnReturn(false);
        
        // 其他配置
        dataSource.setDefaultAutoCommit(true);
        dataSource.setTimeBetweenEvictionRunsMillis(30000);
        dataSource.setMinEvictableIdleTimeMillis(60000);
        
        logger.info("DBCP2连接池配置完成");
        logger.info("初始连接数: {}, 最大连接数: {}", dbcp2InitialSize, dbcp2MaxTotal);
        
        return dataSource;
    }
    
    /**
     * 连接池测试方法
     */
    public void testConnectionPool(DataSource dataSource, String poolName) {
        logger.info("=== 测试{}连接池 ===", poolName);
        
        try {
            // 测试获取连接
            long startTime = System.currentTimeMillis();
            Connection conn = dataSource.getConnection();
            long endTime = System.currentTimeMillis();
            
            logger.info("获取连接耗时: {}ms", (endTime - startTime));
            logger.info("连接是否有效: {}", conn.isValid(5));
            logger.info("连接URL: {}", conn.getMetaData().getURL());
            logger.info("数据库产品: {}", conn.getMetaData().getDatabaseProductName());
            
            conn.close();
            logger.info("{}连接池测试成功", poolName);
            
        } catch (SQLException e) {
            logger.error("{}连接池测试失败: {}", poolName, e.getMessage(), e);
        }
        
        logger.info("==================");
    }
    
    /**
     * 连接池性能测试
     */
    public void performanceTest(DataSource dataSource, String poolName, int testCount) {
        logger.info("=== {}连接池性能测试 ===", poolName);
        logger.info("测试次数: {}", testCount);
        
        long totalTime = 0;
        int successCount = 0;
        
        for (int i = 0; i < testCount; i++) {
            try {
                long startTime = System.nanoTime();
                Connection conn = dataSource.getConnection();
                conn.close();
                long endTime = System.nanoTime();
                
                totalTime += (endTime - startTime);
                successCount++;
                
            } catch (SQLException e) {
                logger.error("第{}次连接失败: {}", i + 1, e.getMessage());
            }
        }
        
        if (successCount > 0) {
            double avgTime = totalTime / (double) successCount / 1_000_000; // 转换为毫秒
            logger.info("成功连接次数: {}/{}", successCount, testCount);
            logger.info("平均连接时间: {:.2f}ms", avgTime);
            logger.info("总耗时: {:.2f}ms", totalTime / 1_000_000.0);
        }
        
        logger.info("========================");
    }
}
