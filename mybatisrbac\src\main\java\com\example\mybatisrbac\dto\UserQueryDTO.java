package com.example.mybatisrbac.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 用户查询请求对象
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户查询请求对象")
public class UserQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名关键字
     */
    @Schema(description = "用户名关键字", example = "admin")
    private String username;

    /**
     * 邮箱关键字
     */
    @Schema(description = "邮箱关键字", example = "<EMAIL>")
    private String email;

    /**
     * 手机号关键字
     */
    @Schema(description = "手机号关键字", example = "138")
    private String phone;

    /**
     * 真实姓名关键字
     */
    @Schema(description = "真实姓名关键字", example = "张")
    private String realName;

    /**
     * 用户状态：0-禁用，1-启用
     */
    @Schema(description = "用户状态：0-禁用，1-启用", example = "1")
    private Integer status;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Long size = 10L;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String sortField;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @Schema(description = "排序方向：asc-升序，desc-降序", example = "desc")
    private String sortOrder = "desc";
}
