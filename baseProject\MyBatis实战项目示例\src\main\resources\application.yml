# Spring Boot应用配置
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: rbac-demo

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************
    username: root
    password: 123456
    
    # HikariCP连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: MybatisCacheHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    
    # Lettuce连接池配置
    lettuce:
      pool:
        max-active: 20    # 最大连接数
        max-idle: 10      # 最大空闲连接
        min-idle: 2       # 最小空闲连接
        max-wait: 3000ms  # 最大等待时间
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000  # 默认过期时间30分钟（毫秒）
      cache-null-values: false  # 不缓存空值
      key-prefix: "cache:"      # key前缀
      use-key-prefix: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: simple
    default-statement-timeout: 25
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    safe-result-handler-enabled: true
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    default-scripting-language: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 日志配置
logging:
  level:
    com.example.mapper: DEBUG
    org.springframework.security: DEBUG
    com.example.security: DEBUG
    com.example.service: INFO
    com.example.controller: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/rbac-demo.log
    max-size: 10MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,caches
  endpoint:
    health:
      show-details: when-authorized
    caches:
      enabled: true

# 应用信息
info:
  app:
    name: RBAC权限管理系统
    description: 基于Spring Boot + Security + MyBatis的RBAC权限管理系统
    version: 1.0.0
  java:
    version: ${java.version}
  spring:
    version: ${spring-boot.version}
