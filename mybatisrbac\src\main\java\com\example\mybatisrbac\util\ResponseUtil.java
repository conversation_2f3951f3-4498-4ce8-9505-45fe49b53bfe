package com.example.mybatisrbac.util;

import com.example.mybatisrbac.common.Result;
import com.example.mybatisrbac.common.PageResult;
import com.example.mybatisrbac.common.ResultCode;

import java.util.List;

/**
 * 响应工具类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public class ResponseUtil {

    /**
     * 构建成功响应
     */
    public static <T> Result<T> success() {
        return buildResult(ResultCode.SUCCESS, null);
    }

    /**
     * 构建成功响应
     */
    public static <T> Result<T> success(T data) {
        return buildResult(ResultCode.SUCCESS, data);
    }

    /**
     * 构建成功响应
     */
    public static <T> Result<T> success(String message) {
        Result<T> result = buildResult(ResultCode.SUCCESS, null);
        result.setMessage(message);
        return result;
    }

    /**
     * 构建成功响应
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = buildResult(ResultCode.SUCCESS, data);
        result.setMessage(message);
        return result;
    }

    /**
     * 构建失败响应
     */
    public static <T> Result<T> error() {
        return buildResult(ResultCode.INTERNAL_SERVER_ERROR, null);
    }

    /**
     * 构建失败响应
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = buildResult(ResultCode.INTERNAL_SERVER_ERROR, null);
        result.setMessage(message);
        return result;
    }

    /**
     * 构建失败响应
     */
    public static <T> Result<T> error(ResultCode resultCode) {
        return buildResult(resultCode, null);
    }

    /**
     * 构建失败响应
     */
    public static <T> Result<T> error(ResultCode resultCode, String message) {
        Result<T> result = buildResult(resultCode, null);
        result.setMessage(message);
        return result;
    }

    /**
     * 构建失败响应
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    /**
     * 构建响应结果
     */
    private static <T> Result<T> buildResult(ResultCode resultCode, T data) {
        Result<T> result = new Result<>();
        result.setCode(resultCode.getCode());
        result.setMessage(resultCode.getMessage());
        result.setData(data);
        return result;
    }

    // ==================== 分页响应方法 ====================

    /**
     * 构建成功的分页响应
     */
    public static <T> PageResult<T> successPage(List<T> records, Long current, Long size, Long total) {
        return PageResult.success(records, current, size, total);
    }

    /**
     * 构建成功的分页响应
     */
    public static <T> PageResult<T> successPage(String message, List<T> records, Long current, Long size, Long total) {
        return PageResult.success(message, records, current, size, total);
    }

    /**
     * 构建失败的分页响应
     */
    public static <T> PageResult<T> errorPage(String message) {
        return PageResult.error(message);
    }

    /**
     * 构建失败的分页响应
     */
    public static <T> PageResult<T> errorPage(ResultCode resultCode) {
        return PageResult.error(resultCode);
    }

    /**
     * 构建失败的分页响应
     */
    public static <T> PageResult<T> errorPage(Integer code, String message) {
        return PageResult.error(code, message);
    }
}
