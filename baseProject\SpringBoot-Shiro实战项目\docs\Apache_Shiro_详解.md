# Apache Shiro 详解文档

## 📋 目录

1. [<PERSON><PERSON>简介](#shiro简介)
2. [核心概念](#核心概念)
3. [架构设计](#架构设计)
4. [认证机制](#认证机制)
5. [授权机制](#授权机制)
6. [会话管理](#会话管理)
7. [缓存机制](#缓存机制)
8. [加密机制](#加密机制)
9. [过滤器链](#过滤器链)
10. [与Spring Boot集成](#与spring-boot集成)
11. [最佳实践](#最佳实践)

## 🎯 Shiro简介

### 什么是Apache Shiro？

Apache Shiro是一个功能强大且易于使用的Java安全框架，提供了认证、授权、加密和会话管理功能。它可以用于任何环境：从最简单的命令行应用程序到最大的企业应用程序。

### 为什么选择Shiro？

- **易于理解**: API简单直观，学习成本低
- **全面的安全功能**: 认证、授权、加密、会话管理一应俱全
- **灵活的架构**: 可以在任何环境中运行
- **强大的缓存支持**: 提高应用性能
- **插件架构**: 易于扩展和定制

### Shiro vs Spring Security

| 特性 | Apache Shiro | Spring Security |
|------|-------------|-----------------|
| 学习曲线 | 简单易学 | 相对复杂 |
| 配置方式 | 简洁明了 | 配置较多 |
| 功能完整性 | 基础功能完善 | 功能更全面 |
| 社区支持 | 活跃 | 非常活跃 |
| Spring集成 | 需要额外配置 | 原生支持 |

## 🏗️ 核心概念

### 1. Subject（主体）

Subject代表当前用户，是Shiro中最重要的概念。

```java
// 获取当前用户
Subject currentUser = SecurityUtils.getSubject();

// 判断是否已认证
if (currentUser.isAuthenticated()) {
    // 用户已登录
}

// 判断是否有角色
if (currentUser.hasRole("admin")) {
    // 用户有admin角色
}

// 判断是否有权限
if (currentUser.isPermitted("user:create")) {
    // 用户有创建用户的权限
}
```

**Subject的主要方法：**

| 方法 | 描述 |
|------|------|
| `login(AuthenticationToken)` | 登录 |
| `logout()` | 登出 |
| `isAuthenticated()` | 是否已认证 |
| `hasRole(String)` | 是否有指定角色 |
| `isPermitted(String)` | 是否有指定权限 |
| `getSession()` | 获取会话 |
| `getPrincipal()` | 获取主体信息 |

### 2. SecurityManager（安全管理器）

SecurityManager是Shiro的核心，管理所有Subject的安全操作。

```java
// 创建SecurityManager
DefaultSecurityManager securityManager = new DefaultSecurityManager();

// 设置Realm
securityManager.setRealm(new MyRealm());

// 设置为全局SecurityManager
SecurityUtils.setSecurityManager(securityManager);
```

**SecurityManager的职责：**
- 管理所有Subject
- 协调各个组件工作
- 提供安全服务

### 3. Realm（域）

Realm是Shiro与数据源之间的桥梁，负责获取安全数据。

```java
public class MyRealm extends AuthorizingRealm {
    
    // 授权：获取用户的角色和权限
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        String username = (String) principals.getPrimaryPrincipal();
        
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        
        // 添加角色
        info.addRole("user");
        
        // 添加权限
        info.addStringPermission("user:view");
        
        return info;
    }
    
    // 认证：验证用户身份
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        UsernamePasswordToken upToken = (UsernamePasswordToken) token;
        String username = upToken.getUsername();
        
        // 从数据库获取用户信息
        User user = userService.findByUsername(username);
        
        if (user == null) {
            throw new UnknownAccountException("用户不存在");
        }
        
        // 返回认证信息
        return new SimpleAuthenticationInfo(
            user,                    // 主体
            user.getPassword(),      // 密码
            ByteSource.Util.bytes(user.getSalt()), // 盐值
            getName()               // Realm名称
        );
    }
}
```

## 🔧 架构设计

### Shiro整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        Application                          │
│                           │                                 │
│                           ▼                                 │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                   Subject                           │   │
│  │  ┌─────────────────────────────────────────────┐   │   │
│  │  │              SecurityManager                │   │   │
│  │  │  ┌─────────────────────────────────────┐   │   │   │
│  │  │  │            Authenticator            │   │   │   │
│  │  │  └─────────────────────────────────────┘   │   │   │
│  │  │  ┌─────────────────────────────────────┐   │   │   │
│  │  │  │            Authorizer               │   │   │   │
│  │  │  └─────────────────────────────────────┘   │   │   │
│  │  │  ┌─────────────────────────────────────┐   │   │   │
│  │  │  │          SessionManager             │   │   │   │
│  │  │  └─────────────────────────────────────┘   │   │   │
│  │  │  ┌─────────────────────────────────────┐   │   │   │
│  │  │  │           CacheManager              │   │   │   │
│  │  │  └─────────────────────────────────────┘   │   │   │
│  │  │  ┌─────────────────────────────────────┐   │   │   │
│  │  │  │             Realms                  │   │   │   │
│  │  │  └─────────────────────────────────────┘   │   │   │
│  │  └─────────────────────────────────────────┘   │   │
│  └─────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件说明

| 组件 | 职责 |
|------|------|
| **Subject** | 当前用户的安全操作接口 |
| **SecurityManager** | 安全管理器，协调各组件 |
| **Authenticator** | 认证器，处理登录逻辑 |
| **Authorizer** | 授权器，处理权限检查 |
| **SessionManager** | 会话管理器 |
| **CacheManager** | 缓存管理器 |
| **Realm** | 数据源，获取用户、角色、权限信息 |

## 🔐 认证机制

### 认证流程

```
1. 用户提交用户名/密码
        ↓
2. Subject.login(token)
        ↓
3. SecurityManager.login()
        ↓
4. Authenticator.authenticate()
        ↓
5. Realm.doGetAuthenticationInfo()
        ↓
6. 密码匹配验证
        ↓
7. 返回AuthenticationInfo
```

### 认证代码示例

```java
// 1. 获取Subject
Subject subject = SecurityUtils.getSubject();

// 2. 创建认证令牌
UsernamePasswordToken token = new UsernamePasswordToken("username", "password");

// 3. 设置记住我
token.setRememberMe(true);

try {
    // 4. 执行登录
    subject.login(token);
    
    // 5. 登录成功
    System.out.println("登录成功");
    
} catch (UnknownAccountException e) {
    // 用户不存在
    System.out.println("用户不存在");
} catch (IncorrectCredentialsException e) {
    // 密码错误
    System.out.println("密码错误");
} catch (LockedAccountException e) {
    // 账户被锁定
    System.out.println("账户被锁定");
} catch (AuthenticationException e) {
    // 其他认证异常
    System.out.println("认证失败：" + e.getMessage());
}
```

### 自定义认证Token

```java
public class CaptchaUsernamePasswordToken extends UsernamePasswordToken {
    
    private String captcha;
    
    public CaptchaUsernamePasswordToken(String username, String password, String captcha) {
        super(username, password);
        this.captcha = captcha;
    }
    
    public String getCaptcha() {
        return captcha;
    }
}
```

### 多Realm认证

```java
@Bean
public ModularRealmAuthenticator modularRealmAuthenticator() {
    ModularRealmAuthenticator authenticator = new ModularRealmAuthenticator();
    
    // 设置认证策略
    authenticator.setAuthenticationStrategy(new AtLeastOneSuccessfulStrategy());
    
    return authenticator;
}

@Bean
public SecurityManager securityManager() {
    DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
    
    // 设置多个Realm
    List<Realm> realms = Arrays.asList(
        new DatabaseRealm(),
        new LdapRealm()
    );
    securityManager.setRealms(realms);
    
    return securityManager;
}
```

## 🛡️ 授权机制

### 授权方式

Shiro提供了三种授权方式：

#### 1. 编程式授权

```java
Subject subject = SecurityUtils.getSubject();

// 角色检查
if (subject.hasRole("admin")) {
    // 有admin角色
}

// 权限检查
if (subject.isPermitted("user:create")) {
    // 有创建用户权限
}

// 多角色检查（AND关系）
if (subject.hasAllRoles(Arrays.asList("admin", "user"))) {
    // 同时有admin和user角色
}

// 多权限检查（AND关系）
if (subject.isPermittedAll("user:create", "user:update")) {
    // 同时有创建和更新用户权限
}
```

#### 2. 注解式授权

```java
// 需要登录
@RequiresAuthentication
public void updateAccount() {
    // 需要登录才能访问
}

// 需要特定角色
@RequiresRoles("admin")
public void deleteUser() {
    // 需要admin角色
}

// 需要多个角色（AND关系）
@RequiresRoles({"admin", "manager"})
public void sensitiveOperation() {
    // 需要同时有admin和manager角色
}

// 需要特定权限
@RequiresPermissions("user:create")
public void createUser() {
    // 需要创建用户权限
}

// 需要多个权限（AND关系）
@RequiresPermissions({"user:create", "user:update"})
public void manageUser() {
    // 需要创建和更新用户权限
}

// 游客访问（未登录）
@RequiresGuest
public void welcome() {
    // 只有未登录用户才能访问
}

// 记住我或已认证
@RequiresUser
public void showProfile() {
    // 记住我或已认证用户可以访问
}
```

#### 3. JSP标签授权

```jsp
<!-- 已认证用户 -->
<shiro:authenticated>
    欢迎回来！
</shiro:authenticated>

<!-- 未认证用户 -->
<shiro:notAuthenticated>
    请<a href="login.jsp">登录</a>
</shiro:notAuthenticated>

<!-- 有特定角色 -->
<shiro:hasRole name="admin">
    <a href="admin.jsp">管理面板</a>
</shiro:hasRole>

<!-- 有特定权限 -->
<shiro:hasPermission name="user:create">
    <button>创建用户</button>
</shiro:hasPermission>

<!-- 记住我用户 -->
<shiro:user>
    <span>用户信息</span>
</shiro:user>

<!-- 游客 -->
<shiro:guest>
    <span>游客模式</span>
</shiro:guest>
```

### 权限字符串

Shiro使用权限字符串来表示权限，支持通配符：

```java
// 简单权限
"user:view"          // 查看用户
"user:create"        // 创建用户
"user:update"        // 更新用户
"user:delete"        // 删除用户

// 通配符权限
"user:*"             // 用户的所有权限
"*:view"             // 所有资源的查看权限
"*"                  // 所有权限

// 实例级权限
"user:view:1"        // 查看ID为1的用户
"user:update:1"      // 更新ID为1的用户
"document:read:*"    // 读取所有文档

// 复杂权限表达式
"printer:print:lp7200"           // 在lp7200打印机上打印
"printer:*:lp7200"               // 在lp7200打印机上的所有操作
"printer:print,query:lp7200"     // 在lp7200打印机上打印和查询
```

### 权限验证实现

```java
public class MyRealm extends AuthorizingRealm {
    
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        String username = (String) principals.getPrimaryPrincipal();
        
        SimpleAuthorizationInfo authorizationInfo = new SimpleAuthorizationInfo();
        
        // 从数据库获取用户角色
        List<String> roles = userService.getUserRoles(username);
        authorizationInfo.setRoles(new HashSet<>(roles));
        
        // 从数据库获取用户权限
        List<String> permissions = userService.getUserPermissions(username);
        authorizationInfo.setStringPermissions(new HashSet<>(permissions));
        
        return authorizationInfo;
    }
}
```

## 📅 会话管理

### 会话概念

Shiro的会话管理不依赖于Web容器，可以在任何环境中使用。

```java
// 获取会话
Subject subject = SecurityUtils.getSubject();
Session session = subject.getSession();

// 会话操作
session.setAttribute("key", "value");  // 设置属性
Object value = session.getAttribute("key");  // 获取属性
session.removeAttribute("key");  // 移除属性

// 会话信息
Date startTime = session.getStartTimestamp();  // 开始时间
Date lastTime = session.getLastAccessTime();   // 最后访问时间
String sessionId = (String) session.getId();   // 会话ID
```

### 会话配置

```java
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    
    // 设置会话超时时间（毫秒）
    sessionManager.setGlobalSessionTimeout(1800000); // 30分钟
    
    // 设置会话验证调度器
    sessionManager.setSessionValidationSchedulerEnabled(true);
    sessionManager.setSessionValidationInterval(1800000); // 30分钟检查一次
    
    // 删除无效会话
    sessionManager.setDeleteInvalidSessions(true);
    
    // 设置SessionId Cookie
    sessionManager.setSessionIdCookie(sessionIdCookie());
    sessionManager.setSessionIdCookieEnabled(true);
    
    // 禁用URL重写
    sessionManager.setSessionIdUrlRewritingEnabled(false);
    
    return sessionManager;
}

@Bean
public SimpleCookie sessionIdCookie() {
    SimpleCookie cookie = new SimpleCookie("JSESSIONID");
    cookie.setHttpOnly(true);  // 防止XSS攻击
    cookie.setMaxAge(-1);      // 浏览器关闭时失效
    cookie.setPath("/");       // 路径
    return cookie;
}
```

### 会话监听器

```java
public class MySessionListener implements SessionListener {
    
    @Override
    public void onStart(Session session) {
        System.out.println("会话开始：" + session.getId());
    }
    
    @Override
    public void onStop(Session session) {
        System.out.println("会话结束：" + session.getId());
    }
    
    @Override
    public void onExpiration(Session session) {
        System.out.println("会话过期：" + session.getId());
    }
}

// 配置会话监听器
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    
    // 添加会话监听器
    Collection<SessionListener> listeners = new ArrayList<>();
    listeners.add(new MySessionListener());
    sessionManager.setSessionListeners(listeners);
    
    return sessionManager;
}
```

### 分布式会话

```java
// Redis会话DAO
public class RedisSessionDAO extends AbstractSessionDAO {
    
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    protected Serializable doCreate(Session session) {
        Serializable sessionId = generateSessionId(session);
        assignSessionId(session, sessionId);
        
        // 保存到Redis
        redisTemplate.opsForValue().set(
            "session:" + sessionId, 
            session, 
            30, 
            TimeUnit.MINUTES
        );
        
        return sessionId;
    }
    
    @Override
    protected Session doReadSession(Serializable sessionId) {
        return (Session) redisTemplate.opsForValue().get("session:" + sessionId);
    }
    
    @Override
    public void update(Session session) {
        redisTemplate.opsForValue().set(
            "session:" + session.getId(), 
            session, 
            30, 
            TimeUnit.MINUTES
        );
    }
    
    @Override
    public void delete(Session session) {
        redisTemplate.delete("session:" + session.getId());
    }
    
    @Override
    public Collection<Session> getActiveSessions() {
        Set<String> keys = redisTemplate.keys("session:*");
        return keys.stream()
                  .map(key -> (Session) redisTemplate.opsForValue().get(key))
                  .collect(Collectors.toList());
    }
}
```

## 💾 缓存机制

### 缓存的作用

Shiro使用缓存来提高性能，避免频繁访问数据源：

- **认证缓存**: 缓存用户的认证信息
- **授权缓存**: 缓存用户的角色和权限信息
- **会话缓存**: 缓存会话信息

### EhCache配置

```xml
<!-- ehcache.xml -->
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd">

    <!-- 认证缓存 -->
    <cache name="authenticationCache"
           maxElementsInMemory="2000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="1800"
           overflowToDisk="false"
           statistics="true"/>

    <!-- 授权缓存 -->
    <cache name="authorizationCache"
           maxElementsInMemory="2000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="1800"
           overflowToDisk="false"
           statistics="true"/>

    <!-- 会话缓存 -->
    <cache name="activeSessionCache"
           maxElementsInMemory="2000"
           eternal="false"
           timeToIdleSeconds="1800"
           timeToLiveSeconds="1800"
           overflowToDisk="false"
           statistics="true"/>

</ehcache>
```

### 缓存管理器配置

```java
@Bean
public EhCacheManager ehCacheManager() {
    EhCacheManager cacheManager = new EhCacheManager();
    cacheManager.setCacheManagerConfigFile("classpath:ehcache.xml");
    return cacheManager;
}

@Bean
public MyRealm myRealm() {
    MyRealm realm = new MyRealm();
    
    // 启用缓存
    realm.setCachingEnabled(true);
    
    // 启用认证缓存
    realm.setAuthenticationCachingEnabled(true);
    realm.setAuthenticationCacheName("authenticationCache");
    
    // 启用授权缓存
    realm.setAuthorizationCachingEnabled(true);
    realm.setAuthorizationCacheName("authorizationCache");
    
    return realm;
}
```

### Redis缓存

```java
@Bean
public RedisCacheManager redisCacheManager() {
    RedisCacheManager cacheManager = new RedisCacheManager();
    cacheManager.setRedisManager(redisManager());
    return cacheManager;
}

@Bean
public RedisManager redisManager() {
    RedisManager redisManager = new RedisManager();
    redisManager.setHost("localhost");
    redisManager.setPort(6379);
    redisManager.setDatabase(0);
    redisManager.setTimeout(1800); // 30分钟
    return redisManager;
}
```

### 缓存操作

```java
public class MyRealm extends AuthorizingRealm {
    
    // 清除用户缓存
    public void clearCachedAuthorizationInfo(String username) {
        SimplePrincipalCollection principals = new SimplePrincipalCollection(username, getName());
        clearCachedAuthorizationInfo(principals);
    }
    
    // 清除所有缓存
    public void clearAllCachedAuthorizationInfo() {
        getAuthorizationCache().clear();
    }
}

// 在Service中使用
@Service
public class UserService {
    
    @Autowired
    private MyRealm myRealm;
    
    public void updateUserRoles(String username, List<String> roles) {
        // 更新数据库中的角色信息
        userDao.updateUserRoles(username, roles);
        
        // 清除缓存，下次访问时重新加载
        myRealm.clearCachedAuthorizationInfo(username);
    }
}

## 🔒 加密机制

### 密码加密

Shiro提供了强大的加密功能，支持多种加密算法。

#### 1. 哈希加密

```java
// MD5加密
String password = "123456";
String salt = "randomSalt";
String hashedPassword = new Md5Hash(password, salt).toHex();

// SHA-256加密
String sha256Password = new Sha256Hash(password, salt).toHex();

// 多次迭代加密（提高安全性）
String iteratedPassword = new Sha256Hash(password, salt, 1024).toHex();
```

#### 2. 密码验证器

```java
@Bean
public HashedCredentialsMatcher hashedCredentialsMatcher() {
    HashedCredentialsMatcher credentialsMatcher = new HashedCredentialsMatcher();

    // 设置加密算法
    credentialsMatcher.setHashAlgorithmName("SHA-256");

    // 设置加密次数
    credentialsMatcher.setHashIterations(1024);

    // 设置存储格式（十六进制）
    credentialsMatcher.setStoredCredentialsHexEncoded(true);

    return credentialsMatcher;
}

@Bean
public MyRealm myRealm() {
    MyRealm realm = new MyRealm();
    realm.setCredentialsMatcher(hashedCredentialsMatcher());
    return realm;
}
```

#### 3. 在Realm中使用加密

```java
public class MyRealm extends AuthorizingRealm {

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) {
        UsernamePasswordToken upToken = (UsernamePasswordToken) token;
        String username = upToken.getUsername();

        // 从数据库获取用户信息
        User user = userService.findByUsername(username);

        if (user == null) {
            throw new UnknownAccountException("用户不存在");
        }

        // 返回认证信息，Shiro会自动进行密码验证
        return new SimpleAuthenticationInfo(
            user,                                    // 主体
            user.getPassword(),                      // 数据库中的加密密码
            ByteSource.Util.bytes(user.getSalt()),  // 盐值
            getName()                                // Realm名称
        );
    }
}
```

#### 4. 密码工具类

```java
@Component
public class PasswordHelper {

    private static final String ALGORITHM_NAME = "SHA-256";
    private static final int HASH_ITERATIONS = 1024;

    /**
     * 生成随机盐值
     */
    public String generateSalt() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 加密密码
     */
    public String encryptPassword(String password, String salt) {
        return new Sha256Hash(password, salt, HASH_ITERATIONS).toHex();
    }

    /**
     * 验证密码
     */
    public boolean verifyPassword(String plainPassword, String hashedPassword, String salt) {
        String encrypted = encryptPassword(plainPassword, salt);
        return encrypted.equals(hashedPassword);
    }

    /**
     * 为用户设置密码
     */
    public void encryptPassword(User user) {
        String salt = generateSalt();
        String encryptedPassword = encryptPassword(user.getPassword(), salt);

        user.setSalt(salt);
        user.setPassword(encryptedPassword);
    }
}
```

### 对称加密

```java
// AES加密
AesCipherService aesCipherService = new AesCipherService();
Key key = aesCipherService.generateNewKey();

String text = "Hello World";
String encrypted = aesCipherService.encrypt(text.getBytes(), key.getEncoded()).toHex();
String decrypted = new String(aesCipherService.decrypt(Hex.decode(encrypted), key.getEncoded()).getBytes());
```

### 非对称加密

```java
// RSA加密
RsaCipherService rsaCipherService = new RsaCipherService();
KeyPair keyPair = rsaCipherService.generateNewKeyPair();

String text = "Hello World";
String encrypted = rsaCipherService.encrypt(text.getBytes(), keyPair.getPublic().getEncoded()).toHex();
String decrypted = new String(rsaCipherService.decrypt(Hex.decode(encrypted), keyPair.getPrivate().getEncoded()).getBytes());
```

## 🔗 过滤器链

### 内置过滤器

Shiro提供了多种内置过滤器：

| 过滤器 | 说明 |
|--------|------|
| **anon** | 匿名过滤器，不需要登录 |
| **authc** | 认证过滤器，需要登录 |
| **authcBasic** | HTTP Basic认证过滤器 |
| **logout** | 登出过滤器 |
| **user** | 用户过滤器，记住我或已认证 |
| **roles** | 角色过滤器 |
| **perms** | 权限过滤器 |
| **port** | 端口过滤器 |
| **rest** | REST风格过滤器 |
| **ssl** | SSL过滤器 |

### 过滤器配置

```java
@Bean
public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
    ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
    shiroFilterFactoryBean.setSecurityManager(securityManager);

    // 设置登录页面
    shiroFilterFactoryBean.setLoginUrl("/login");
    // 设置登录成功页面
    shiroFilterFactoryBean.setSuccessUrl("/index");
    // 设置未授权页面
    shiroFilterFactoryBean.setUnauthorizedUrl("/unauthorized");

    // 配置过滤器链
    Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();

    // 静态资源
    filterChainDefinitionMap.put("/css/**", "anon");
    filterChainDefinitionMap.put("/js/**", "anon");
    filterChainDefinitionMap.put("/images/**", "anon");

    // 公开接口
    filterChainDefinitionMap.put("/api/public/**", "anon");
    filterChainDefinitionMap.put("/login", "anon");
    filterChainDefinitionMap.put("/logout", "logout");

    // 需要认证
    filterChainDefinitionMap.put("/api/**", "authc");

    // 需要特定角色
    filterChainDefinitionMap.put("/admin/**", "authc,roles[admin]");

    // 需要特定权限
    filterChainDefinitionMap.put("/user/create", "authc,perms[user:create]");
    filterChainDefinitionMap.put("/user/update", "authc,perms[user:update]");
    filterChainDefinitionMap.put("/user/delete", "authc,perms[user:delete]");

    // 其他请求需要认证
    filterChainDefinitionMap.put("/**", "authc");

    shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);

    return shiroFilterFactoryBean;
}
```

### 自定义过滤器

```java
public class CaptchaValidateFilter extends AccessControlFilter {

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) {
        // 如果不是登录请求，直接放行
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (!"POST".equals(httpRequest.getMethod()) || !"/login".equals(httpRequest.getRequestURI())) {
            return true;
        }

        // 验证验证码
        String captcha = httpRequest.getParameter("captcha");
        String sessionCaptcha = (String) httpRequest.getSession().getAttribute("captcha");

        return captcha != null && captcha.equalsIgnoreCase(sessionCaptcha);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        // 验证码错误，返回错误信息
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setContentType("application/json;charset=UTF-8");
        httpResponse.getWriter().write("{\"code\":400,\"message\":\"验证码错误\"}");
        return false;
    }
}

// 注册自定义过滤器
@Bean
public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
    ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();

    // 注册自定义过滤器
    Map<String, Filter> filters = new HashMap<>();
    filters.put("captcha", new CaptchaValidateFilter());
    shiroFilterFactoryBean.setFilters(filters);

    // 使用自定义过滤器
    Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();
    filterChainDefinitionMap.put("/login", "captcha,anon");

    shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);

    return shiroFilterFactoryBean;
}
```

## 🌱 与Spring Boot集成

### 1. 依赖配置

```xml
<dependencies>
    <!-- Shiro核心 -->
    <dependency>
        <groupId>org.apache.shiro</groupId>
        <artifactId>shiro-core</artifactId>
        <version>1.12.0</version>
    </dependency>

    <!-- Shiro Web -->
    <dependency>
        <groupId>org.apache.shiro</groupId>
        <artifactId>shiro-web</artifactId>
        <version>1.12.0</version>
    </dependency>

    <!-- Shiro Spring -->
    <dependency>
        <groupId>org.apache.shiro</groupId>
        <artifactId>shiro-spring</artifactId>
        <version>1.12.0</version>
    </dependency>

    <!-- Shiro EhCache -->
    <dependency>
        <groupId>org.apache.shiro</groupId>
        <artifactId>shiro-ehcache</artifactId>
        <version>1.12.0</version>
    </dependency>
</dependencies>
```

### 2. 配置类

```java
@Configuration
@EnableConfigurationProperties
public class ShiroConfig {

    @Bean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(SecurityManager securityManager) {
        // 过滤器工厂配置
    }

    @Bean
    public SecurityManager securityManager() {
        // 安全管理器配置
    }

    @Bean
    public MyRealm myRealm() {
        // 自定义Realm配置
    }

    @Bean
    public HashedCredentialsMatcher hashedCredentialsMatcher() {
        // 密码匹配器配置
    }

    @Bean
    public EhCacheManager ehCacheManager() {
        // 缓存管理器配置
    }

    @Bean
    public SessionManager sessionManager() {
        // 会话管理器配置
    }

    @Bean
    public CookieRememberMeManager rememberMeManager() {
        // 记住我管理器配置
    }
}
```

### 3. 启用注解支持

```java
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ShiroConfig {

    /**
     * 开启Shiro注解支持
     */
    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor = new AuthorizationAttributeSourceAdvisor();
        authorizationAttributeSourceAdvisor.setSecurityManager(securityManager);
        return authorizationAttributeSourceAdvisor;
    }

    /**
     * 解决@RequiresPermissions注解无效问题
     */
    @Bean
    public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator defaultAAPC = new DefaultAdvisorAutoProxyCreator();
        defaultAAPC.setProxyTargetClass(true);
        return defaultAAPC;
    }
}
```

### 4. 异常处理

```java
@ControllerAdvice
public class ShiroExceptionHandler {

    @ExceptionHandler(UnauthenticatedException.class)
    @ResponseBody
    public ApiResponse handleUnauthenticatedException(UnauthenticatedException e) {
        return ApiResponse.error(401, "用户未认证");
    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseBody
    public ApiResponse handleUnauthorizedException(UnauthorizedException e) {
        return ApiResponse.error(403, "权限不足");
    }

    @ExceptionHandler(AuthenticationException.class)
    @ResponseBody
    public ApiResponse handleAuthenticationException(AuthenticationException e) {
        return ApiResponse.error(401, "认证失败：" + e.getMessage());
    }

    @ExceptionHandler(AuthorizationException.class)
    @ResponseBody
    public ApiResponse handleAuthorizationException(AuthorizationException e) {
        return ApiResponse.error(403, "授权失败：" + e.getMessage());
    }
}
```

## 🎯 最佳实践

### 1. 安全配置

```java
// 密码安全
@Bean
public HashedCredentialsMatcher hashedCredentialsMatcher() {
    HashedCredentialsMatcher credentialsMatcher = new HashedCredentialsMatcher();
    credentialsMatcher.setHashAlgorithmName("SHA-256");  // 使用SHA-256
    credentialsMatcher.setHashIterations(1024);          // 多次迭代
    credentialsMatcher.setStoredCredentialsHexEncoded(true);
    return credentialsMatcher;
}

// 会话安全
@Bean
public SessionManager sessionManager() {
    DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
    sessionManager.setGlobalSessionTimeout(1800000);     // 30分钟超时
    sessionManager.setDeleteInvalidSessions(true);       // 删除无效会话
    sessionManager.setSessionValidationSchedulerEnabled(true); // 启用会话验证

    // 安全的Cookie配置
    SimpleCookie cookie = new SimpleCookie("JSESSIONID");
    cookie.setHttpOnly(true);    // 防止XSS
    cookie.setSecure(false);     // 生产环境应设为true（HTTPS）
    cookie.setMaxAge(-1);        // 浏览器关闭时失效
    sessionManager.setSessionIdCookie(cookie);

    return sessionManager;
}
```

### 2. 权限设计

```java
// 权限命名规范
"模块:操作:实例"

// 示例
"user:view"          // 查看用户
"user:create"        // 创建用户
"user:update:1"      // 更新ID为1的用户
"order:delete:own"   // 删除自己的订单

// 角色命名规范
"ROLE_ADMIN"         // 管理员角色
"ROLE_USER"          // 普通用户角色
"ROLE_GUEST"         // 访客角色
```

### 3. 缓存策略

```java
// 合理设置缓存时间
@Bean
public EhCacheManager ehCacheManager() {
    EhCacheManager cacheManager = new EhCacheManager();
    cacheManager.setCacheManagerConfigFile("classpath:ehcache.xml");
    return cacheManager;
}

// 及时清除缓存
@Service
public class UserService {

    @Autowired
    private MyRealm myRealm;

    public void updateUserPermissions(String username) {
        // 更新权限后清除缓存
        myRealm.clearCachedAuthorizationInfo(username);
    }
}
```

### 4. 性能优化

```java
// 1. 启用缓存
realm.setCachingEnabled(true);
realm.setAuthenticationCachingEnabled(true);
realm.setAuthorizationCachingEnabled(true);

// 2. 合理设置会话超时
sessionManager.setGlobalSessionTimeout(1800000); // 30分钟

// 3. 定期清理无效会话
sessionManager.setSessionValidationSchedulerEnabled(true);
sessionManager.setSessionValidationInterval(1800000);

// 4. 使用连接池
@Bean
public DataSource dataSource() {
    DruidDataSource dataSource = new DruidDataSource();
    dataSource.setInitialSize(5);
    dataSource.setMaxActive(20);
    dataSource.setMinIdle(5);
    return dataSource;
}
```

### 5. 日志配置

```yaml
logging:
  level:
    org.apache.shiro: DEBUG
    com.example.shiro: DEBUG
    org.apache.shiro.cache: INFO
    org.apache.shiro.session: INFO
```

### 6. 测试建议

```java
@RunWith(SpringRunner.class)
@SpringBootTest
public class ShiroTest {

    @Autowired
    private SecurityManager securityManager;

    @Test
    public void testAuthentication() {
        // 设置SecurityManager
        SecurityUtils.setSecurityManager(securityManager);

        // 测试认证
        Subject subject = SecurityUtils.getSubject();
        UsernamePasswordToken token = new UsernamePasswordToken("admin", "123456");

        try {
            subject.login(token);
            assertTrue(subject.isAuthenticated());
        } finally {
            subject.logout();
        }
    }

    @Test
    public void testAuthorization() {
        // 测试授权
        Subject subject = SecurityUtils.getSubject();
        // ... 登录逻辑

        assertTrue(subject.hasRole("admin"));
        assertTrue(subject.isPermitted("user:create"));
    }
}
```

## 📚 总结

Apache Shiro是一个功能强大且易于使用的Java安全框架，它提供了：

1. **简单易用的API**: 直观的Subject、SecurityManager、Realm设计
2. **全面的安全功能**: 认证、授权、会话管理、加密
3. **灵活的架构**: 可在任何环境中运行
4. **强大的缓存支持**: 提高应用性能
5. **丰富的过滤器**: 满足各种安全需求

通过合理的配置和使用，Shiro可以为Java应用提供企业级的安全保障。在实际项目中，建议根据具体需求选择合适的认证方式、授权策略和缓存方案，并注意安全配置和性能优化。
```
