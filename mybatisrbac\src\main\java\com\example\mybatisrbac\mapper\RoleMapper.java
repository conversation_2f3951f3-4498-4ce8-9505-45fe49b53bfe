package com.example.mybatisrbac.mapper;

import com.example.mybatisrbac.dto.RoleQueryDTO;
import com.example.mybatisrbac.entity.Role;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色数据访问层
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface RoleMapper {

    /**
     * 根据条件查询角色总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM sys_role WHERE 1=1 " +
            "<if test='roleName != null and roleName != \"\"'>" +
            "AND role_name LIKE CONCAT('%', #{roleName}, '%') " +
            "</if>" +
            "<if test='roleCode != null and roleCode != \"\"'>" +
            "AND role_code LIKE CONCAT('%', #{roleCode}, '%') " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "</script>")
    Long countByCondition(RoleQueryDTO queryDTO);

    /**
     * 根据条件分页查询角色列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_role WHERE 1=1 " +
            "<if test='queryDTO.roleName != null and queryDTO.roleName != \"\"'>" +
            "AND role_name LIKE CONCAT('%', #{queryDTO.roleName}, '%') " +
            "</if>" +
            "<if test='queryDTO.roleCode != null and queryDTO.roleCode != \"\"'>" +
            "AND role_code LIKE CONCAT('%', #{queryDTO.roleCode}, '%') " +
            "</if>" +
            "<if test='queryDTO.status != null'>" +
            "AND status = #{queryDTO.status} " +
            "</if>" +
            "ORDER BY ${sortField} ${sortOrder} " +
            "LIMIT #{offset}, #{size}" +
            "</script>")
    List<Role> selectByCondition(@Param("queryDTO") RoleQueryDTO queryDTO,
                                @Param("offset") Long offset,
                                @Param("size") Long size,
                                @Param("sortField") String sortField,
                                @Param("sortOrder") String sortOrder);

    /**
     * 根据ID查询角色
     */
    @Select("SELECT * FROM sys_role WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "roleName", column = "role_name"),
        @Result(property = "roleCode", column = "role_code"),
        @Result(property = "description", column = "description"),
        @Result(property = "status", column = "status"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "createBy", column = "create_by"),
        @Result(property = "updateBy", column = "update_by")
    })
    Role selectById(Long id);

    /**
     * 根据角色编码查询角色
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode}")
    Role selectByRoleCode(String roleCode);

    /**
     * 插入角色
     */
    @Insert("INSERT INTO sys_role (role_name, role_code, description, status, create_time, update_time, create_by, update_by) " +
            "VALUES (#{roleName}, #{roleCode}, #{description}, #{status}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Role role);

    /**
     * 更新角色
     */
    @Update("<script>" +
            "UPDATE sys_role SET " +
            "<if test='roleName != null'>role_name = #{roleName},</if>" +
            "<if test='roleCode != null'>role_code = #{roleCode},</if>" +
            "<if test='description != null'>description = #{description},</if>" +
            "<if test='status != null'>status = #{status},</if>" +
            "update_time = #{updateTime}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}" +
            "</script>")
    int updateById(Role role);

    /**
     * 根据ID删除角色
     */
    @Delete("DELETE FROM sys_role WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 批量删除角色
     */
    @Delete("<script>" +
            "DELETE FROM sys_role WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查角色编码是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_code = #{roleCode} AND id != #{excludeId}")
    int checkRoleCodeExists(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);

    /**
     * 检查角色名称是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_name = #{roleName} AND id != #{excludeId}")
    int checkRoleNameExists(@Param("roleName") String roleName, @Param("excludeId") Long excludeId);

    /**
     * 查询所有启用的角色
     */
    @Select("SELECT * FROM sys_role WHERE status = 1 ORDER BY create_time DESC")
    List<Role> selectEnabledRoles();
}
