package com.example.util;

import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 验证工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class ValidationUtil {
    
    // 正则表达式常量
    private static final String USERNAME_PATTERN = "^[a-zA-Z0-9_]{3,50}$";
    private static final String EMAIL_PATTERN = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    private static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";
    private static final String PASSWORD_PATTERN = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]{6,32}$";
    private static final String ID_CARD_PATTERN = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    
    // 编译后的Pattern对象，提高性能
    private static final Pattern USERNAME_COMPILED = Pattern.compile(USERNAME_PATTERN);
    private static final Pattern EMAIL_COMPILED = Pattern.compile(EMAIL_PATTERN);
    private static final Pattern PHONE_COMPILED = Pattern.compile(PHONE_PATTERN);
    private static final Pattern PASSWORD_COMPILED = Pattern.compile(PASSWORD_PATTERN);
    private static final Pattern ID_CARD_COMPILED = Pattern.compile(ID_CARD_PATTERN);
    
    /**
     * 验证用户名格式
     * 用户名只能包含字母、数字和下划线，长度3-50位
     * 
     * @param username 用户名
     * @return 验证结果
     */
    public static boolean isValidUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return USERNAME_COMPILED.matcher(username).matches();
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱
     * @return 验证结果
     */
    public static boolean isValidEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return EMAIL_COMPILED.matcher(email).matches();
    }
    
    /**
     * 验证手机号格式
     * 中国大陆手机号格式：1开头，第二位3-9，总共11位数字
     * 
     * @param phone 手机号
     * @return 验证结果
     */
    public static boolean isValidPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return PHONE_COMPILED.matcher(phone).matches();
    }
    
    /**
     * 验证密码格式
     * 密码必须包含字母和数字，长度6-32位，可包含特殊字符
     * 
     * @param password 密码
     * @return 验证结果
     */
    public static boolean isValidPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return false;
        }
        return PASSWORD_COMPILED.matcher(password).matches();
    }
    
    /**
     * 验证身份证号格式
     * 18位身份证号码格式验证
     * 
     * @param idCard 身份证号
     * @return 验证结果
     */
    public static boolean isValidIdCard(String idCard) {
        if (!StringUtils.hasText(idCard)) {
            return false;
        }
        
        if (!ID_CARD_COMPILED.matcher(idCard).matches()) {
            return false;
        }
        
        // 验证校验码
        return validateIdCardChecksum(idCard);
    }
    
    /**
     * 验证年龄范围
     * 
     * @param age 年龄
     * @return 验证结果
     */
    public static boolean isValidAge(Integer age) {
        return age != null && age >= 0 && age <= 150;
    }
    
    /**
     * 验证性别值
     * 
     * @param gender 性别：0-未知，1-男，2-女
     * @return 验证结果
     */
    public static boolean isValidGender(Integer gender) {
        return gender != null && gender >= 0 && gender <= 2;
    }
    
    /**
     * 验证状态值
     * 
     * @param status 状态：0-禁用，1-启用，2-锁定
     * @return 验证结果
     */
    public static boolean isValidStatus(Integer status) {
        return status != null && status >= 0 && status <= 2;
    }
    
    /**
     * 验证URL格式
     * 
     * @param url URL地址
     * @return 验证结果
     */
    public static boolean isValidUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }
        
        String urlPattern = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
        return Pattern.matches(urlPattern, url);
    }
    
    /**
     * 验证IP地址格式
     * 
     * @param ip IP地址
     * @return 验证结果
     */
    public static boolean isValidIp(String ip) {
        if (!StringUtils.hasText(ip)) {
            return false;
        }
        
        String ipPattern = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";
        return Pattern.matches(ipPattern, ip);
    }
    
    /**
     * 验证IPv6地址格式
     * 
     * @param ipv6 IPv6地址
     * @return 验证结果
     */
    public static boolean isValidIpv6(String ipv6) {
        if (!StringUtils.hasText(ipv6)) {
            return false;
        }
        
        String ipv6Pattern = "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$";
        return Pattern.matches(ipv6Pattern, ipv6);
    }
    
    /**
     * 验证MAC地址格式
     * 
     * @param mac MAC地址
     * @return 验证结果
     */
    public static boolean isValidMac(String mac) {
        if (!StringUtils.hasText(mac)) {
            return false;
        }
        
        String macPattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$";
        return Pattern.matches(macPattern, mac);
    }
    
    /**
     * 验证中文姓名格式
     * 
     * @param name 姓名
     * @return 验证结果
     */
    public static boolean isValidChineseName(String name) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        
        String namePattern = "^[\\u4e00-\\u9fa5]{2,10}$";
        return Pattern.matches(namePattern, name);
    }
    
    /**
     * 验证英文姓名格式
     * 
     * @param name 姓名
     * @return 验证结果
     */
    public static boolean isValidEnglishName(String name) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        
        String namePattern = "^[a-zA-Z\\s]{2,50}$";
        return Pattern.matches(namePattern, name);
    }
    
    /**
     * 验证银行卡号格式
     * 
     * @param bankCard 银行卡号
     * @return 验证结果
     */
    public static boolean isValidBankCard(String bankCard) {
        if (!StringUtils.hasText(bankCard)) {
            return false;
        }
        
        // 银行卡号通常为13-19位数字
        String bankCardPattern = "^\\d{13,19}$";
        if (!Pattern.matches(bankCardPattern, bankCard)) {
            return false;
        }
        
        // 使用Luhn算法验证银行卡号
        return validateLuhn(bankCard);
    }
    
    /**
     * 验证邮政编码格式
     * 
     * @param zipCode 邮政编码
     * @return 验证结果
     */
    public static boolean isValidZipCode(String zipCode) {
        if (!StringUtils.hasText(zipCode)) {
            return false;
        }
        
        String zipPattern = "^\\d{6}$";
        return Pattern.matches(zipPattern, zipCode);
    }
    
    /**
     * 验证QQ号格式
     * 
     * @param qq QQ号
     * @return 验证结果
     */
    public static boolean isValidQQ(String qq) {
        if (!StringUtils.hasText(qq)) {
            return false;
        }
        
        String qqPattern = "^[1-9]\\d{4,10}$";
        return Pattern.matches(qqPattern, qq);
    }
    
    /**
     * 验证微信号格式
     * 
     * @param wechat 微信号
     * @return 验证结果
     */
    public static boolean isValidWechat(String wechat) {
        if (!StringUtils.hasText(wechat)) {
            return false;
        }
        
        String wechatPattern = "^[a-zA-Z][a-zA-Z0-9_-]{5,19}$";
        return Pattern.matches(wechatPattern, wechat);
    }
    
    // ===== 私有辅助方法 =====
    
    /**
     * 验证身份证号校验码
     * 
     * @param idCard 身份证号
     * @return 验证结果
     */
    private static boolean validateIdCardChecksum(String idCard) {
        if (idCard.length() != 18) {
            return false;
        }
        
        // 权重因子
        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        // 校验码对应值
        char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char c = idCard.charAt(i);
            if (!Character.isDigit(c)) {
                return false;
            }
            sum += (c - '0') * weights[i];
        }
        
        int checkIndex = sum % 11;
        char expectedCheck = checkCodes[checkIndex];
        char actualCheck = Character.toUpperCase(idCard.charAt(17));
        
        return expectedCheck == actualCheck;
    }
    
    /**
     * Luhn算法验证
     * 
     * @param number 数字字符串
     * @return 验证结果
     */
    private static boolean validateLuhn(String number) {
        int sum = 0;
        boolean alternate = false;
        
        for (int i = number.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(number.charAt(i));
            
            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = (digit % 10) + 1;
                }
            }
            
            sum += digit;
            alternate = !alternate;
        }
        
        return sum % 10 == 0;
    }
}
