package com.example.mapper;

import com.example.entity.SysRole;
import com.example.entity.SysPermission;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 系统角色Mapper接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper
public interface SysRoleMapper {
    
    /**
     * 根据ID查询角色
     */
    @Select("SELECT * FROM sys_role WHERE id = #{id}")
    SysRole selectById(Long id);
    
    /**
     * 根据角色编码查询角色
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode}")
    SysRole selectByRoleCode(String roleCode);
    
    /**
     * 查询所有角色
     */
    @Select("SELECT * FROM sys_role ORDER BY create_time DESC")
    List<SysRole> selectAll();
    
    /**
     * 根据状态查询角色
     */
    @Select("SELECT * FROM sys_role WHERE status = #{status} ORDER BY create_time DESC")
    List<SysRole> selectByStatus(Integer status);
    
    /**
     * 插入角色
     */
    @Insert("INSERT INTO sys_role(role_name, role_code, description, status, create_time, update_time, create_by) " +
            "VALUES(#{roleName}, #{roleCode}, #{description}, #{status}, #{createTime}, #{updateTime}, #{createBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(SysRole role);
    
    /**
     * 更新角色
     */
    @Update("UPDATE sys_role SET role_name = #{roleName}, role_code = #{roleCode}, description = #{description}, " +
            "status = #{status}, update_time = #{updateTime}, update_by = #{updateBy} WHERE id = #{id}")
    int update(SysRole role);
    
    /**
     * 删除角色
     */
    @Delete("DELETE FROM sys_role WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 检查角色名称是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_name = #{roleName}")
    int countByRoleName(String roleName);
    
    /**
     * 检查角色编码是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_code = #{roleCode}")
    int countByRoleCode(String roleCode);
    
    /**
     * 检查角色名称是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_name = #{roleName} AND id != #{id}")
    int countByRoleNameExcludeId(@Param("roleName") String roleName, @Param("id") Long id);
    
    /**
     * 检查角色编码是否存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM sys_role WHERE role_code = #{roleCode} AND id != #{id}")
    int countByRoleCodeExcludeId(@Param("roleCode") String roleCode, @Param("id") Long id);
    
    /**
     * 查询角色的权限列表
     */
    @Select("SELECT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 " +
            "ORDER BY p.sort_order")
    List<SysPermission> selectRolePermissions(Long roleId);
    
    /**
     * 查询角色详细信息（包含权限）
     */
    SysRole selectRoleWithPermissions(Long roleId);
    
    /**
     * 为角色分配权限
     */
    @Insert("INSERT INTO sys_role_permission(role_id, permission_id, create_time, create_by) " +
            "VALUES(#{roleId}, #{permissionId}, #{createTime}, #{createBy})")
    int insertRolePermission(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId, 
                            @Param("createTime") java.time.LocalDateTime createTime, @Param("createBy") Long createBy);
    
    /**
     * 删除角色的所有权限
     */
    @Delete("DELETE FROM sys_role_permission WHERE role_id = #{roleId}")
    int deleteRolePermissions(Long roleId);
    
    /**
     * 删除角色的指定权限
     */
    @Delete("DELETE FROM sys_role_permission WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int deleteRolePermission(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);
    
    /**
     * 检查角色是否有指定权限
     */
    @Select("SELECT COUNT(*) FROM sys_role_permission WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int countRolePermission(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);
    
    /**
     * 检查角色是否被用户使用
     */
    @Select("SELECT COUNT(*) FROM sys_user_role WHERE role_id = #{roleId}")
    int countRoleUsers(Long roleId);
    
    /**
     * 分页查询角色
     */
    List<SysRole> selectByPage(@Param("offset") int offset, @Param("limit") int limit, 
                              @Param("roleName") String roleName, @Param("roleCode") String roleCode, 
                              @Param("status") Integer status);
    
    /**
     * 统计角色总数
     */
    @Select("SELECT COUNT(*) FROM sys_role")
    int countTotal();
    
    /**
     * 根据条件统计角色数
     */
    int countByCondition(@Param("roleName") String roleName, @Param("roleCode") String roleCode, @Param("status") Integer status);
}
