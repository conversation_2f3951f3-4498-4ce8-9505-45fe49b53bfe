# MyBatis快速参考指南

## 🚀 快速开始

### 1. 添加依赖
```xml
<dependency>
    <groupId>org.mybatis</groupId>
    <artifactId>mybatis</artifactId>
    <version>3.5.13</version>
</dependency>
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>8.0.33</version>
</dependency>
```

### 2. 基本配置
```xml
<!-- mybatis-config.xml -->
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" 
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.cj.jdbc.Driver"/>
                <property name="url" value="********************************"/>
                <property name="username" value="root"/>
                <property name="password" value="password"/>
            </dataSource>
        </environment>
    </environments>
    <mappers>
        <mapper resource="mapper/UserMapper.xml"/>
    </mappers>
</configuration>
```

### 3. 创建SqlSessionFactory
```java
public class MyBatisUtil {
    private static SqlSessionFactory sqlSessionFactory;
    
    static {
        try {
            String resource = "mybatis-config.xml";
            InputStream inputStream = Resources.getResourceAsStream(resource);
            sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    
    public static SqlSession getSqlSession() {
        return sqlSessionFactory.openSession();
    }
}
```

## 📋 核心注解

### Mapper接口注解
```java
public interface UserMapper {
    
    @Select("SELECT * FROM user WHERE id = #{id}")
    User selectById(@Param("id") Long id);
    
    @Insert("INSERT INTO user(username, email) VALUES(#{username}, #{email})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
    
    @Update("UPDATE user SET username = #{username} WHERE id = #{id}")
    int update(User user);
    
    @Delete("DELETE FROM user WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "username", column = "username"),
        @Result(property = "createTime", column = "create_time")
    })
    @Select("SELECT * FROM user")
    List<User> selectAll();
}
```

## 🔧 动态SQL速查

### if标签
```xml
<select id="selectByCondition" resultType="User">
    SELECT * FROM user WHERE 1=1
    <if test="username != null and username != ''">
        AND username = #{username}
    </if>
    <if test="age != null">
        AND age = #{age}
    </if>
</select>
```

### where标签
```xml
<select id="selectByCondition" resultType="User">
    SELECT * FROM user
    <where>
        <if test="username != null">AND username = #{username}</if>
        <if test="age != null">AND age = #{age}</if>
    </where>
</select>
```

### choose/when/otherwise
```xml
<select id="selectByChoice" resultType="User">
    SELECT * FROM user WHERE 1=1
    <choose>
        <when test="username != null">
            AND username = #{username}
        </when>
        <when test="email != null">
            AND email = #{email}
        </when>
        <otherwise>
            AND status = 1
        </otherwise>
    </choose>
</select>
```

### set标签
```xml
<update id="updateSelective">
    UPDATE user
    <set>
        <if test="username != null">username = #{username},</if>
        <if test="email != null">email = #{email},</if>
        <if test="age != null">age = #{age},</if>
        update_time = NOW()
    </set>
    WHERE id = #{id}
</update>
```

### foreach标签
```xml
<!-- IN查询 -->
<select id="selectByIds" resultType="User">
    SELECT * FROM user WHERE id IN
    <foreach collection="list" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
</select>

<!-- 批量插入 -->
<insert id="batchInsert">
    INSERT INTO user (username, email) VALUES
    <foreach collection="list" item="user" separator=",">
        (#{user.username}, #{user.email})
    </foreach>
</insert>
```

### trim标签
```xml
<select id="selectByTrim" resultType="User">
    SELECT * FROM user
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test="username != null">AND username = #{username}</if>
        <if test="email != null">AND email = #{email}</if>
    </trim>
</select>
```

## 🗺️ 结果映射

### 基本ResultMap
```xml
<resultMap id="UserResultMap" type="User">
    <id property="id" column="id"/>
    <result property="username" column="username"/>
    <result property="email" column="email"/>
    <result property="createTime" column="create_time"/>
</resultMap>
```

### 一对一关联
```xml
<resultMap id="UserWithProfileResultMap" type="User">
    <id property="id" column="id"/>
    <result property="username" column="username"/>
    <association property="profile" javaType="UserProfile">
        <id property="id" column="profile_id"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
    </association>
</resultMap>
```

### 一对多关联
```xml
<resultMap id="UserWithRolesResultMap" type="User">
    <id property="id" column="id"/>
    <result property="username" column="username"/>
    <collection property="roles" ofType="Role">
        <id property="id" column="role_id"/>
        <result property="roleName" column="role_name"/>
    </collection>
</resultMap>
```

## 💾 缓存配置

### 一级缓存（默认开启）
```java
SqlSession session = sqlSessionFactory.openSession();
UserMapper mapper = session.getMapper(UserMapper.class);

// 第一次查询，从数据库获取
User user1 = mapper.selectById(1L);
// 第二次查询，从一级缓存获取
User user2 = mapper.selectById(1L);

session.close();
```

### 二级缓存
```xml
<!-- 在Mapper.xml中开启 -->
<cache eviction="LRU" flushInterval="60000" size="512" readOnly="true"/>

<select id="selectById" resultType="User" useCache="true">
    SELECT * FROM user WHERE id = #{id}
</select>
```

## 🔌 插件开发

### 分页插件示例
```java
@Intercepts({
    @Signature(type = Executor.class, method = "query", 
               args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class PagePlugin implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 插件逻辑
        return invocation.proceed();
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 设置属性
    }
}
```

## ⚙️ 常用配置

### settings配置
```xml
<settings>
    <!-- 驼峰命名自动映射 -->
    <setting name="mapUnderscoreToCamelCase" value="true"/>
    <!-- 延迟加载 -->
    <setting name="lazyLoadingEnabled" value="true"/>
    <!-- 二级缓存 -->
    <setting name="cacheEnabled" value="true"/>
    <!-- 超时时间 -->
    <setting name="defaultStatementTimeout" value="30"/>
    <!-- 日志实现 -->
    <setting name="logImpl" value="STDOUT_LOGGING"/>
</settings>
```

### 类型别名
```xml
<typeAliases>
    <typeAlias type="com.example.entity.User" alias="User"/>
    <package name="com.example.entity"/>
</typeAliases>
```

### 类型处理器
```java
public class DateTypeHandler extends BaseTypeHandler<Date> {
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Date parameter, JdbcType jdbcType) throws SQLException {
        ps.setTimestamp(i, new Timestamp(parameter.getTime()));
    }
    
    @Override
    public Date getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Timestamp timestamp = rs.getTimestamp(columnName);
        return timestamp != null ? new Date(timestamp.getTime()) : null;
    }
    
    @Override
    public Date getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Timestamp timestamp = rs.getTimestamp(columnIndex);
        return timestamp != null ? new Date(timestamp.getTime()) : null;
    }
    
    @Override
    public Date getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Timestamp timestamp = cs.getTimestamp(columnIndex);
        return timestamp != null ? new Date(timestamp.getTime()) : null;
    }
}
```

## 🛠️ 常用工具方法

### 通用Mapper基类
```java
public abstract class BaseMapper<T> {
    
    public abstract T selectById(@Param("id") Long id);
    public abstract List<T> selectAll();
    public abstract int insert(T entity);
    public abstract int update(T entity);
    public abstract int deleteById(@Param("id") Long id);
    
    // 分页查询
    public List<T> selectByPage(@Param("offset") int offset, @Param("limit") int limit) {
        // 子类实现
        return null;
    }
    
    // 条件查询
    public List<T> selectByCondition(T condition) {
        // 子类实现
        return null;
    }
}
```

### 分页工具类
```java
public class PageHelper {
    
    public static <T> PageResult<T> page(int pageNum, int pageSize, Supplier<List<T>> supplier) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 执行查询
        List<T> list = supplier.get();
        
        // 构造分页结果
        PageResult<T> result = new PageResult<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setList(list);
        
        return result;
    }
}

public class PageResult<T> {
    private int pageNum;
    private int pageSize;
    private long total;
    private List<T> list;
    
    // getter/setter方法
}
```

## 🚨 常见问题

### 1. N+1查询问题
```xml
<!-- 问题：循环查询导致性能问题 -->
<!-- 解决：使用关联查询 -->
<select id="selectUsersWithRoles" resultMap="UserWithRolesResultMap">
    SELECT u.*, r.id as role_id, r.role_name
    FROM user u
    LEFT JOIN user_role ur ON u.id = ur.user_id
    LEFT JOIN role r ON ur.role_id = r.id
</select>
```

### 2. 参数传递问题
```java
// 单个参数
User selectById(Long id);

// 多个参数需要使用@Param
User selectByUsernameAndEmail(@Param("username") String username, @Param("email") String email);

// 对象参数
List<User> selectByCondition(UserQuery query);

// Map参数
List<User> selectByMap(Map<String, Object> params);
```

### 3. 事务管理
```java
public void transferMoney(Long fromId, Long toId, BigDecimal amount) {
    SqlSession session = MyBatisUtil.getSqlSession();
    try {
        AccountMapper mapper = session.getMapper(AccountMapper.class);
        
        // 扣款
        mapper.deduct(fromId, amount);
        // 加款
        mapper.add(toId, amount);
        
        session.commit(); // 提交事务
    } catch (Exception e) {
        session.rollback(); // 回滚事务
        throw e;
    } finally {
        session.close();
    }
}
```

## 📊 性能优化

### 1. 批量操作
```xml
<insert id="batchInsert" parameterType="list">
    INSERT INTO user (username, email) VALUES
    <foreach collection="list" item="user" separator=",">
        (#{user.username}, #{user.email})
    </foreach>
</insert>
```

### 2. 延迟加载
```xml
<association property="profile" javaType="UserProfile" 
             select="selectProfileByUserId" column="id" fetchType="lazy"/>
```

### 3. 结果集处理
```java
// 使用ResultHandler处理大量数据
@Select("SELECT * FROM user")
void selectAllUsers(ResultHandler<User> handler);
```

## 🔍 调试技巧

### 1. 开启SQL日志
```xml
<setting name="logImpl" value="STDOUT_LOGGING"/>
```

### 2. 使用MyBatis日志
```java
private static final Logger logger = LoggerFactory.getLogger(UserMapper.class);
```

### 3. SQL执行时间监控
```java
@Intercepts({@Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class})})
public class SqlTimeInterceptor implements Interceptor {
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = invocation.proceed();
        long end = System.currentTimeMillis();
        System.out.println("SQL执行时间: " + (end - start) + "ms");
        return result;
    }
}
```

这个快速参考指南涵盖了MyBatis的核心功能和常用配置，可以作为日常开发的速查手册。
