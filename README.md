# Spring Boot 入门示例

这是一个简单的Spring Boot应用示例，用于演示如何创建和运行Spring Boot项目。

## 项目结构
```
demo/
├── src/main/java/
│   └── com/example/demo/
│       ├── DemoApplication.java        # 主应用程序类
│       └── controller/
│           └── HelloController.java    # REST控制器
├── src/main/resources/
│   └── application.properties          # 应用配置
└── pom.xml                             # Maven配置
```

## 先决条件

要运行此项目，您需要：
1. JDK 11或更高版本
2. Maven 3.6或更高版本

## 安装步骤

### 1. 安装JDK (Java Development Kit)
1. 访问 [Oracle JDK下载页面](https://www.oracle.com/java/technologies/javase-jdk11-downloads.html) 或 [OpenJDK](https://adoptopenjdk.net/)
2. 选择适合Windows系统的安装包
3. 运行安装程序，按照向导完成安装
4. 配置环境变量:
   - 设置JAVA_HOME环境变量指向JDK安装目录
   - 将%JAVA_HOME%\bin添加到PATH环境变量

### 2. 安装Maven
1. 从[Maven官网](https://maven.apache.org/download.cgi)下载最新版本
2. 解压到选定的文件夹（如C:\Program Files\Maven）
3. 设置环境变量:
   - 设置MAVEN_HOME环境变量指向Maven安装目录
   - 将%MAVEN_HOME%\bin添加到PATH环境变量

## 运行应用程序

1. 在项目根目录打开命令提示符
2. 执行以下命令编译并运行应用程序:
   ```
   mvn spring-boot:run
   ```
3. 打开浏览器访问: http://localhost:8080/hello
4. 可以通过URL参数自定义问候语: http://localhost:8080/hello?name=你的名字

## API 端点

- GET `/hello` - 返回问候语
  - 可选参数: `name` - 自定义问候对象

## 打包应用程序

要将应用程序打包为可执行JAR文件:
```
mvn clean package
```

然后可以运行:
```
java -jar target/demo-0.0.1-SNAPSHOT.jar
``` 