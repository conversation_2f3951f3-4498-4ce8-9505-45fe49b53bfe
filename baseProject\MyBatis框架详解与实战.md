# MyBatis框架详解与实战

## 目录
1. [MyBatis概述](#mybatis概述)
2. [核心组件详解](#核心组件详解)
3. [配置文件详解](#配置文件详解)
4. [映射器详解](#映射器详解)
5. [动态SQL](#动态sql)
6. [缓存机制](#缓存机制)
7. [插件机制](#插件机制)
8. [实战示例](#实战示例)

## MyBatis概述

### 什么是MyBatis

MyBatis是一款优秀的持久层框架，它支持自定义SQL、存储过程以及高级映射。MyBatis免除了几乎所有的JDBC代码以及设置参数和获取结果集的工作。MyBatis可以通过简单的XML或注解来配置和映射原始类型、接口和Java POJO为数据库中的记录。

### MyBatis的优势

1. **简单易学** - 本身就很小且简单，没有任何第三方依赖
2. **灵活** - 不会对应用程序或者数据库的现有设计强加任何影响
3. **解除SQL与程序代码的耦合** - 通过提供DAO层，将业务逻辑和数据访问逻辑分离
4. **提供映射标签** - 支持对象与数据库的ORM字段关系映射
5. **提供对象关系映射标签** - 支持对象关系组建维护
6. **提供XML标签** - 支持编写动态SQL

### MyBatis架构

```
┌─────────────────┐
│   应用程序      │
└─────────────────┘
         │
┌─────────────────┐
│   MyBatis API   │
└─────────────────┘
         │
┌─────────────────┐
│  SqlSession     │
└─────────────────┘
         │
┌─────────────────┐
│   Executor      │
└─────────────────┘
         │
┌─────────────────┐
│ StatementHandler│
└─────────────────┘
         │
┌─────────────────┐
│ ParameterHandler│
└─────────────────┘
         │
┌─────────────────┐
│ ResultSetHandler│
└─────────────────┘
         │
┌─────────────────┐
│   数据库        │
└─────────────────┘
```

## 核心组件详解

### 1. SqlSessionFactory

SqlSessionFactory是MyBatis的核心接口之一，用于创建SqlSession实例。

```java
// 创建SqlSessionFactory
public class MyBatisUtil {
    private static SqlSessionFactory sqlSessionFactory;
    
    static {
        try {
            String resource = "mybatis-config.xml";
            InputStream inputStream = Resources.getResourceAsStream(resource);
            sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
        } catch (IOException e) {
            throw new RuntimeException("初始化MyBatis失败", e);
        }
    }
    
    public static SqlSession getSqlSession() {
        return sqlSessionFactory.openSession();
    }
    
    public static SqlSession getSqlSession(boolean autoCommit) {
        return sqlSessionFactory.openSession(autoCommit);
    }
}
```

### 2. SqlSession

SqlSession是MyBatis中用于和数据库交互的顶层类，通常将它与ThreadLocal绑定，一个会话使用一个SqlSession，并且在使用完毕后需要close。

```java
public class UserService {
    
    public User getUserById(Long id) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectById(id);
        } finally {
            sqlSession.close();
        }
    }
    
    public void saveUser(User user) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            mapper.insert(user);
            sqlSession.commit(); // 手动提交事务
        } catch (Exception e) {
            sqlSession.rollback(); // 回滚事务
            throw e;
        } finally {
            sqlSession.close();
        }
    }
}
```

### 3. Mapper接口

Mapper接口是MyBatis中定义SQL操作的接口，通过接口方法与XML中的SQL语句进行绑定。

```java
public interface UserMapper {
    
    // 根据ID查询用户
    User selectById(@Param("id") Long id);
    
    // 查询所有用户
    List<User> selectAll();
    
    // 根据条件查询用户
    List<User> selectByCondition(UserQuery query);
    
    // 插入用户
    int insert(User user);
    
    // 更新用户
    int update(User user);
    
    // 删除用户
    int deleteById(@Param("id") Long id);
    
    // 批量插入
    int batchInsert(@Param("users") List<User> users);
    
    // 分页查询
    List<User> selectByPage(@Param("offset") int offset, @Param("limit") int limit);
    
    // 统计数量
    int count();
}
```

### 4. 实体类

```java
public class User {
    private Long id;
    private String username;
    private String email;
    private String password;
    private Integer age;
    private Date createTime;
    private Date updateTime;
    private Integer status; // 0-禁用 1-启用
    
    // 构造函数
    public User() {}
    
    public User(String username, String email, String password) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.createTime = new Date();
        this.updateTime = new Date();
        this.status = 1;
    }
    
    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }
    
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
    
    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }
    
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", age=" + age +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}

// 查询条件类
public class UserQuery {
    private String username;
    private String email;
    private Integer minAge;
    private Integer maxAge;
    private Integer status;
    private Date startDate;
    private Date endDate;
    
    // 构造函数和Getter/Setter方法
    public UserQuery() {}
    
    // Getter和Setter方法
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public Integer getMinAge() { return minAge; }
    public void setMinAge(Integer minAge) { this.minAge = minAge; }
    
    public Integer getMaxAge() { return maxAge; }
    public void setMaxAge(Integer maxAge) { this.maxAge = maxAge; }
    
    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }
    
    public Date getStartDate() { return startDate; }
    public void setStartDate(Date startDate) { this.startDate = startDate; }
    
    public Date getEndDate() { return endDate; }
    public void setEndDate(Date endDate) { this.endDate = endDate; }
}
```

## 配置文件详解

### 1. MyBatis主配置文件 (mybatis-config.xml)

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    
    <!-- 属性配置 -->
    <properties resource="database.properties">
        <property name="username" value="root"/>
        <property name="password" value="123456"/>
    </properties>
    
    <!-- 设置 -->
    <settings>
        <!-- 开启驼峰命名自动映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 开启延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 设置积极的延迟加载 -->
        <setting name="aggressiveLazyLoading" value="false"/>
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="30"/>
        <!-- 设置日志实现 -->
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>
    
    <!-- 类型别名 -->
    <typeAliases>
        <!-- 单个别名定义 -->
        <typeAlias type="com.example.entity.User" alias="User"/>
        <!-- 包扫描 -->
        <package name="com.example.entity"/>
    </typeAliases>
    
    <!-- 类型处理器 -->
    <typeHandlers>
        <typeHandler handler="com.example.handler.DateTypeHandler"/>
    </typeHandlers>
    
    <!-- 插件 -->
    <plugins>
        <plugin interceptor="com.example.plugin.PaginationPlugin">
            <property name="dialect" value="mysql"/>
        </plugin>
    </plugins>
    
    <!-- 环境配置 -->
    <environments default="development">
        <!-- 开发环境 -->
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="${driver}"/>
                <property name="url" value="${url}"/>
                <property name="username" value="${username}"/>
                <property name="password" value="${password}"/>
                <!-- 连接池配置 -->
                <property name="poolMaximumActiveConnections" value="20"/>
                <property name="poolMaximumIdleConnections" value="5"/>
                <property name="poolMaximumCheckoutTime" value="20000"/>
                <property name="poolTimeToWait" value="20000"/>
            </dataSource>
        </environment>
        
        <!-- 生产环境 -->
        <environment id="production">
            <transactionManager type="JDBC"/>
            <dataSource type="JNDI">
                <property name="data_source" value="java:comp/env/jdbc/MyDataSource"/>
            </dataSource>
        </environment>
    </environments>
    
    <!-- 映射器 -->
    <mappers>
        <!-- 单个映射文件 -->
        <mapper resource="mapper/UserMapper.xml"/>
        <!-- 接口映射 -->
        <mapper class="com.example.mapper.UserMapper"/>
        <!-- 包扫描 -->
        <package name="com.example.mapper"/>
    </mappers>
    
</configuration>
```

### 2. 数据库配置文件 (database.properties)

```properties
# MySQL数据库配置
driver=com.mysql.cj.jdbc.Driver
url=*******************************************************************************************************************
username=root
password=123456

# 连接池配置
initialSize=5
maxActive=20
maxIdle=10
minIdle=5
maxWait=60000
```

## 映射器详解

### 1. UserMapper.xml

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">
    
    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="email" column="email"/>
        <result property="password" column="password"/>
        <result property="age" column="age"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
    </resultMap>
    
    <!-- SQL片段 -->
    <sql id="Base_Column_List">
        id, username, email, password, age, create_time, update_time, status
    </sql>
    
    <sql id="Base_Where_Clause">
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email = #{email}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </sql>
    
    <!-- 查询操作 -->
    <select id="selectById" parameterType="long" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE id = #{id}
    </select>
    
    <select id="selectAll" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE status = 1
        ORDER BY create_time DESC
    </select>
    
    <select id="selectByCondition" parameterType="UserQuery" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email = #{email}
            </if>
            <if test="minAge != null">
                AND age >= #{minAge}
            </if>
            <if test="maxAge != null">
                AND age <= #{maxAge}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND create_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND create_time <= #{endDate}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectByPage" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user
        WHERE status = 1
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <select id="count" resultType="int">
        SELECT COUNT(*)
        FROM user
        WHERE status = 1
    </select>
    
    <!-- 插入操作 -->
    <insert id="insert" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (username, email, password, age, create_time, update_time, status)
        VALUES (#{username}, #{email}, #{password}, #{age}, #{createTime}, #{updateTime}, #{status})
    </insert>
    
    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO user (username, email, password, age, create_time, update_time, status)
        VALUES
        <foreach collection="users" item="user" separator=",">
            (#{user.username}, #{user.email}, #{user.password}, #{user.age}, 
             #{user.createTime}, #{user.updateTime}, #{user.status})
        </foreach>
    </insert>
    
    <!-- 更新操作 -->
    <update id="update" parameterType="User">
        UPDATE user
        <set>
            <if test="username != null and username != ''">
                username = #{username},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="age != null">
                age = #{age},
            </if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>
    
    <!-- 删除操作 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM user WHERE id = #{id}
    </delete>

</mapper>

## 动态SQL

MyBatis的动态SQL是其强大功能之一，能够根据不同条件动态生成SQL语句。

### 1. if标签

```xml
<!-- 根据条件查询用户 -->
<select id="selectUserByCondition" parameterType="User" resultType="User">
    SELECT * FROM user
    WHERE 1=1
    <if test="username != null and username != ''">
        AND username = #{username}
    </if>
    <if test="email != null and email != ''">
        AND email = #{email}
    </if>
    <if test="age != null">
        AND age = #{age}
    </if>
</select>
```

### 2. where标签

```xml
<!-- 使用where标签优化查询 -->
<select id="selectUserByCondition" parameterType="User" resultType="User">
    SELECT * FROM user
    <where>
        <if test="username != null and username != ''">
            AND username = #{username}
        </if>
        <if test="email != null and email != ''">
            AND email = #{email}
        </if>
        <if test="age != null">
            AND age = #{age}
        </if>
    </where>
</select>
```

### 3. choose、when、otherwise标签

```xml
<!-- 多条件选择查询 -->
<select id="selectUserByChoice" parameterType="User" resultType="User">
    SELECT * FROM user
    WHERE 1=1
    <choose>
        <when test="username != null and username != ''">
            AND username = #{username}
        </when>
        <when test="email != null and email != ''">
            AND email = #{email}
        </when>
        <otherwise>
            AND status = 1
        </otherwise>
    </choose>
</select>
```

### 4. set标签

```xml
<!-- 动态更新用户信息 -->
<update id="updateUserSelective" parameterType="User">
    UPDATE user
    <set>
        <if test="username != null and username != ''">
            username = #{username},
        </if>
        <if test="email != null and email != ''">
            email = #{email},
        </if>
        <if test="age != null">
            age = #{age},
        </if>
        update_time = NOW()
    </set>
    WHERE id = #{id}
</update>
```

### 5. foreach标签

```xml
<!-- 批量查询 -->
<select id="selectUserByIds" parameterType="list" resultType="User">
    SELECT * FROM user
    WHERE id IN
    <foreach collection="list" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
</select>

<!-- 批量插入 -->
<insert id="batchInsertUsers" parameterType="list">
    INSERT INTO user (username, email, age, create_time)
    VALUES
    <foreach collection="list" item="user" separator=",">
        (#{user.username}, #{user.email}, #{user.age}, NOW())
    </foreach>
</insert>

<!-- 批量更新 -->
<update id="batchUpdateUsers" parameterType="list">
    <foreach collection="list" item="user" separator=";">
        UPDATE user
        SET username = #{user.username},
            email = #{user.email},
            age = #{user.age},
            update_time = NOW()
        WHERE id = #{user.id}
    </foreach>
</update>
```

### 6. trim标签

```xml
<!-- 使用trim标签处理动态SQL -->
<select id="selectUserByTrim" parameterType="User" resultType="User">
    SELECT * FROM user
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test="username != null and username != ''">
            AND username = #{username}
        </if>
        <if test="email != null and email != ''">
            AND email = #{email}
        </if>
        <if test="age != null">
            AND age = #{age}
        </if>
    </trim>
</select>

<update id="updateUserByTrim" parameterType="User">
    UPDATE user
    <trim prefix="SET" suffixOverrides=",">
        <if test="username != null and username != ''">
            username = #{username},
        </if>
        <if test="email != null and email != ''">
            email = #{email},
        </if>
        <if test="age != null">
            age = #{age},
        </if>
        update_time = NOW()
    </trim>
    WHERE id = #{id}
</update>
```

### 7. bind标签

```xml
<!-- 使用bind标签创建变量 -->
<select id="selectUserByLikeName" parameterType="string" resultType="User">
    <bind name="pattern" value="'%' + username + '%'"/>
    SELECT * FROM user
    WHERE username LIKE #{pattern}
</select>
```

## 缓存机制

MyBatis提供了一级缓存和二级缓存来提高查询性能。

### 1. 一级缓存

一级缓存是SqlSession级别的缓存，默认开启。

```java
public class CacheDemo {

    public void firstLevelCacheDemo() {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);

            // 第一次查询，从数据库获取
            User user1 = mapper.selectById(1L);
            System.out.println("第一次查询: " + user1);

            // 第二次查询，从一级缓存获取
            User user2 = mapper.selectById(1L);
            System.out.println("第二次查询: " + user2);

            System.out.println("两次查询结果是否相同: " + (user1 == user2)); // true

            // 执行更新操作会清空一级缓存
            user1.setUsername("newName");
            mapper.update(user1);

            // 第三次查询，重新从数据库获取
            User user3 = mapper.selectById(1L);
            System.out.println("第三次查询: " + user3);

        } finally {
            sqlSession.close();
        }
    }
}
```

### 2. 二级缓存

二级缓存是Mapper级别的缓存，需要手动开启。

```xml
<!-- 在Mapper.xml中开启二级缓存 -->
<mapper namespace="com.example.mapper.UserMapper">

    <!-- 开启二级缓存 -->
    <cache
        eviction="LRU"
        flushInterval="60000"
        size="512"
        readOnly="true"/>

    <!-- 或者使用自定义缓存 -->
    <cache type="com.example.cache.RedisCache">
        <property name="host" value="localhost"/>
        <property name="port" value="6379"/>
    </cache>

    <!-- 查询语句，使用二级缓存 -->
    <select id="selectById" parameterType="long" resultType="User" useCache="true">
        SELECT * FROM user WHERE id = #{id}
    </select>

    <!-- 查询语句，不使用二级缓存 -->
    <select id="selectByIdNoCache" parameterType="long" resultType="User" useCache="false">
        SELECT * FROM user WHERE id = #{id}
    </select>

</mapper>
```

### 3. 自定义缓存实现

```java
public class RedisCache implements Cache {

    private final String id;
    private RedisTemplate<String, Object> redisTemplate;

    public RedisCache(String id) {
        this.id = id;
        // 初始化Redis连接
        this.redisTemplate = SpringContextUtil.getBean(RedisTemplate.class);
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public void putObject(Object key, Object value) {
        String redisKey = getRedisKey(key);
        redisTemplate.opsForValue().set(redisKey, value, 30, TimeUnit.MINUTES);
    }

    @Override
    public Object getObject(Object key) {
        String redisKey = getRedisKey(key);
        return redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public Object removeObject(Object key) {
        String redisKey = getRedisKey(key);
        Object value = redisTemplate.opsForValue().get(redisKey);
        redisTemplate.delete(redisKey);
        return value;
    }

    @Override
    public void clear() {
        Set<String> keys = redisTemplate.keys(id + ":*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    @Override
    public int getSize() {
        Set<String> keys = redisTemplate.keys(id + ":*");
        return keys != null ? keys.size() : 0;
    }

    private String getRedisKey(Object key) {
        return id + ":" + key.toString();
    }
}
```

## 插件机制

MyBatis允许在映射语句执行过程中的某一点进行拦截调用。

### 1. 分页插件

```java
@Intercepts({
    @Signature(type = Executor.class, method = "query",
               args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class PaginationPlugin implements Interceptor {

    private String dialect = "mysql";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        RowBounds rowBounds = (RowBounds) args[2];

        // 如果不需要分页，直接执行原方法
        if (rowBounds == RowBounds.DEFAULT) {
            return invocation.proceed();
        }

        // 获取原始SQL
        BoundSql boundSql = ms.getBoundSql(parameter);
        String originalSql = boundSql.getSql();

        // 构造分页SQL
        String pageSql = buildPageSql(originalSql, rowBounds);

        // 创建新的BoundSql
        BoundSql newBoundSql = new BoundSql(ms.getConfiguration(), pageSql,
                                           boundSql.getParameterMappings(), parameter);

        // 创建新的MappedStatement
        MappedStatement newMs = copyMappedStatement(ms, new BoundSqlSqlSource(newBoundSql));
        args[0] = newMs;
        args[2] = RowBounds.DEFAULT;

        return invocation.proceed();
    }

    private String buildPageSql(String originalSql, RowBounds rowBounds) {
        StringBuilder sql = new StringBuilder(originalSql);

        if ("mysql".equals(dialect)) {
            sql.append(" LIMIT ").append(rowBounds.getOffset())
               .append(", ").append(rowBounds.getLimit());
        } else if ("oracle".equals(dialect)) {
            // Oracle分页逻辑
            sql.insert(0, "SELECT * FROM (SELECT ROWNUM rn, t.* FROM (")
               .append(") t WHERE ROWNUM <= ")
               .append(rowBounds.getOffset() + rowBounds.getLimit())
               .append(") WHERE rn > ")
               .append(rowBounds.getOffset());
        }

        return sql.toString();
    }

    private MappedStatement copyMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder = new MappedStatement.Builder(
                ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());

        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());

        return builder.build();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        this.dialect = properties.getProperty("dialect", "mysql");
    }

    public static class BoundSqlSqlSource implements SqlSource {
        private BoundSql boundSql;

        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}
```

### 2. SQL执行时间监控插件

```java
@Intercepts({
    @Signature(type = StatementHandler.class, method = "query",
               args = {Statement.class, ResultHandler.class}),
    @Signature(type = StatementHandler.class, method = "update",
               args = {Statement.class}),
    @Signature(type = StatementHandler.class, method = "batch",
               args = {Statement.class})
})
public class SqlExecutionTimePlugin implements Interceptor {

    private static final Logger logger = LoggerFactory.getLogger(SqlExecutionTimePlugin.class);

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        try {
            Object result = invocation.proceed();
            return result;
        } finally {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            // 获取SQL语句
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            BoundSql boundSql = statementHandler.getBoundSql();
            String sql = boundSql.getSql();

            // 记录执行时间
            if (executionTime > 1000) { // 超过1秒的慢SQL
                logger.warn("慢SQL检测 - 执行时间: {}ms, SQL: {}", executionTime, sql);
            } else {
                logger.info("SQL执行时间: {}ms, SQL: {}", executionTime, sql);
            }
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件读取参数
    }
}

## 实战示例

### 1. 项目结构

```
mybatis-demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── example/
│   │   │           ├── entity/
│   │   │           │   ├── User.java
│   │   │           │   ├── Role.java
│   │   │           │   └── UserRole.java
│   │   │           ├── mapper/
│   │   │           │   ├── UserMapper.java
│   │   │           │   ├── RoleMapper.java
│   │   │           │   └── UserRoleMapper.java
│   │   │           ├── service/
│   │   │           │   ├── UserService.java
│   │   │           │   └── impl/
│   │   │           │       └── UserServiceImpl.java
│   │   │           ├── util/
│   │   │           │   └── MyBatisUtil.java
│   │   │           └── MyBatisDemo.java
│   │   └── resources/
│   │       ├── mapper/
│   │       │   ├── UserMapper.xml
│   │       │   ├── RoleMapper.xml
│   │       │   └── UserRoleMapper.xml
│   │       ├── mybatis-config.xml
│   │       ├── database.properties
│   │       └── logback.xml
│   └── test/
│       └── java/
│           └── com/
│               └── example/
│                   └── UserMapperTest.java
├── pom.xml
└── README.md
```

### 2. Maven依赖配置 (pom.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>mybatis-demo</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mybatis.version>3.5.13</mybatis.version>
        <mysql.version>8.0.33</mysql.version>
        <junit.version>4.13.2</junit.version>
        <logback.version>1.2.12</logback.version>
    </properties>

    <dependencies>
        <!-- MyBatis核心依赖 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>

        <!-- MySQL驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>

        <!-- 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.18</version>
        </dependency>

        <!-- 日志框架 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <!-- 测试框架 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- JSON处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

### 3. 数据库表结构

```sql
-- 创建数据库
CREATE DATABASE mybatis_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mybatis_demo;

-- 用户表
CREATE TABLE user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    age INT COMMENT '年龄',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) COMMENT '用户表';

-- 角色表
CREATE TABLE role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_desc VARCHAR(200) COMMENT '角色描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) COMMENT '角色表';

-- 用户角色关联表
CREATE TABLE user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE
) COMMENT '用户角色关联表';

-- 插入测试数据
INSERT INTO user (username, email, password, age) VALUES
('admin', '<EMAIL>', 'admin123', 30),
('user1', '<EMAIL>', 'user123', 25),
('user2', '<EMAIL>', 'user123', 28);

INSERT INTO role (role_name, role_desc) VALUES
('ADMIN', '系统管理员'),
('USER', '普通用户'),
('GUEST', '访客用户');

INSERT INTO user_role (user_id, role_id) VALUES
(1, 1), -- admin用户拥有ADMIN角色
(1, 2), -- admin用户拥有USER角色
(2, 2), -- user1用户拥有USER角色
(3, 3); -- user2用户拥有GUEST角色
```

### 4. 完整的实体类

```java
// Role.java
public class Role {
    private Long id;
    private String roleName;
    private String roleDesc;
    private Date createTime;
    private Date updateTime;
    private Integer status;

    // 关联属性
    private List<User> users; // 拥有该角色的用户列表

    // 构造函数
    public Role() {}

    public Role(String roleName, String roleDesc) {
        this.roleName = roleName;
        this.roleDesc = roleDesc;
        this.createTime = new Date();
        this.updateTime = new Date();
        this.status = 1;
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getRoleName() { return roleName; }
    public void setRoleName(String roleName) { this.roleName = roleName; }

    public String getRoleDesc() { return roleDesc; }
    public void setRoleDesc(String roleDesc) { this.roleDesc = roleDesc; }

    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public Date getUpdateTime() { return updateTime; }
    public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }

    public List<User> getUsers() { return users; }
    public void setUsers(List<User> users) { this.users = users; }

    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", roleName='" + roleName + '\'' +
                ", roleDesc='" + roleDesc + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}

// UserRole.java
public class UserRole {
    private Long id;
    private Long userId;
    private Long roleId;
    private Date createTime;

    // 关联对象
    private User user;
    private Role role;

    // 构造函数
    public UserRole() {}

    public UserRole(Long userId, Long roleId) {
        this.userId = userId;
        this.roleId = roleId;
        this.createTime = new Date();
    }

    // Getter和Setter方法
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public Long getRoleId() { return roleId; }
    public void setRoleId(Long roleId) { this.roleId = roleId; }

    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }

    public Role getRole() { return role; }
    public void setRole(Role role) { this.role = role; }

    @Override
    public String toString() {
        return "UserRole{" +
                "id=" + id +
                ", userId=" + userId +
                ", roleId=" + roleId +
                ", createTime=" + createTime +
                '}';
    }
}

// 扩展User类，添加角色关联
public class User {
    private Long id;
    private String username;
    private String email;
    private String password;
    private Integer age;
    private Date createTime;
    private Date updateTime;
    private Integer status;

    // 关联属性
    private List<Role> roles; // 用户拥有的角色列表

    // 构造函数和其他方法保持不变...

    public List<Role> getRoles() { return roles; }
    public void setRoles(List<Role> roles) { this.roles = roles; }
}
```

### 5. Mapper接口定义

```java
// RoleMapper.java
public interface RoleMapper {

    // 基本CRUD操作
    Role selectById(@Param("id") Long id);
    List<Role> selectAll();
    List<Role> selectByCondition(Role role);
    int insert(Role role);
    int update(Role role);
    int deleteById(@Param("id") Long id);

    // 关联查询
    List<Role> selectRolesByUserId(@Param("userId") Long userId);
    Role selectRoleWithUsers(@Param("id") Long id);

    // 统计查询
    int countByStatus(@Param("status") Integer status);
}

// UserRoleMapper.java
public interface UserRoleMapper {

    // 基本操作
    UserRole selectById(@Param("id") Long id);
    List<UserRole> selectAll();
    int insert(UserRole userRole);
    int deleteById(@Param("id") Long id);
    int deleteByUserId(@Param("userId") Long userId);
    int deleteByRoleId(@Param("roleId") Long roleId);

    // 关联查询
    List<UserRole> selectByUserId(@Param("userId") Long userId);
    List<UserRole> selectByRoleId(@Param("roleId") Long roleId);
    UserRole selectByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);

    // 批量操作
    int batchInsert(@Param("userRoles") List<UserRole> userRoles);
    int batchDeleteByUserIds(@Param("userIds") List<Long> userIds);
}

// 扩展UserMapper接口
public interface UserMapper {

    // 原有方法保持不变...

    // 关联查询方法
    User selectUserWithRoles(@Param("id") Long id);
    List<User> selectUsersWithRoles();
    List<User> selectUsersByRoleId(@Param("roleId") Long roleId);

    // 复杂查询
    List<User> selectUsersByRoleName(@Param("roleName") String roleName);
    List<User> selectUsersWithMultipleRoles(@Param("roleNames") List<String> roleNames);

    // 统计查询
    int countUsersByRoleId(@Param("roleId") Long roleId);
    Map<String, Object> getUserStatistics();
}
```

### 6. XML映射文件

```xml
<!-- RoleMapper.xml -->
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.RoleMapper">

    <!-- 结果映射 -->
    <resultMap id="RoleResultMap" type="Role">
        <id property="id" column="id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleDesc" column="role_desc"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <!-- 角色和用户的关联映射 -->
    <resultMap id="RoleWithUsersResultMap" type="Role" extends="RoleResultMap">
        <collection property="users" ofType="User">
            <id property="id" column="user_id"/>
            <result property="username" column="username"/>
            <result property="email" column="email"/>
            <result property="age" column="age"/>
            <result property="createTime" column="user_create_time"/>
            <result property="status" column="user_status"/>
        </collection>
    </resultMap>

    <!-- SQL片段 -->
    <sql id="Base_Column_List">
        id, role_name, role_desc, create_time, update_time, status
    </sql>

    <!-- 基本查询 -->
    <select id="selectById" parameterType="long" resultMap="RoleResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="RoleResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <select id="selectByCondition" parameterType="Role" resultMap="RoleResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM role
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 关联查询 -->
    <select id="selectRolesByUserId" parameterType="long" resultMap="RoleResultMap">
        SELECT r.id, r.role_name, r.role_desc, r.create_time, r.update_time, r.status
        FROM role r
        INNER JOIN user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.status = 1
    </select>

    <select id="selectRoleWithUsers" parameterType="long" resultMap="RoleWithUsersResultMap">
        SELECT r.id, r.role_name, r.role_desc, r.create_time, r.update_time, r.status,
               u.id as user_id, u.username, u.email, u.age,
               u.create_time as user_create_time, u.status as user_status
        FROM role r
        LEFT JOIN user_role ur ON r.id = ur.role_id
        LEFT JOIN user u ON ur.user_id = u.id
        WHERE r.id = #{id}
    </select>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="Role" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO role (role_name, role_desc, create_time, update_time, status)
        VALUES (#{roleName}, #{roleDesc}, #{createTime}, #{updateTime}, #{status})
    </insert>

    <!-- 更新操作 -->
    <update id="update" parameterType="Role">
        UPDATE role
        <set>
            <if test="roleName != null and roleName != ''">
                role_name = #{roleName},
            </if>
            <if test="roleDesc != null">
                role_desc = #{roleDesc},
            </if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除操作 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM role WHERE id = #{id}
    </delete>

    <!-- 统计查询 -->
    <select id="countByStatus" parameterType="int" resultType="int">
        SELECT COUNT(*) FROM role WHERE status = #{status}
    </select>

</mapper>
```

### 7. 扩展的UserMapper.xml

```xml
<!-- 在UserMapper.xml中添加关联查询 -->

<!-- 用户和角色的关联映射 -->
<resultMap id="UserWithRolesResultMap" type="User" extends="UserResultMap">
    <collection property="roles" ofType="Role">
        <id property="id" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleDesc" column="role_desc"/>
        <result property="createTime" column="role_create_time"/>
        <result property="status" column="role_status"/>
    </collection>
</resultMap>

<!-- 查询用户及其角色信息 -->
<select id="selectUserWithRoles" parameterType="long" resultMap="UserWithRolesResultMap">
    SELECT u.id, u.username, u.email, u.password, u.age,
           u.create_time, u.update_time, u.status,
           r.id as role_id, r.role_name, r.role_desc,
           r.create_time as role_create_time, r.status as role_status
    FROM user u
    LEFT JOIN user_role ur ON u.id = ur.user_id
    LEFT JOIN role r ON ur.role_id = r.id
    WHERE u.id = #{id}
</select>

<!-- 查询所有用户及其角色信息 -->
<select id="selectUsersWithRoles" resultMap="UserWithRolesResultMap">
    SELECT u.id, u.username, u.email, u.password, u.age,
           u.create_time, u.update_time, u.status,
           r.id as role_id, r.role_name, r.role_desc,
           r.create_time as role_create_time, r.status as role_status
    FROM user u
    LEFT JOIN user_role ur ON u.id = ur.user_id
    LEFT JOIN role r ON ur.role_id = r.id
    WHERE u.status = 1
    ORDER BY u.create_time DESC
</select>

<!-- 根据角色ID查询用户 -->
<select id="selectUsersByRoleId" parameterType="long" resultMap="UserResultMap">
    SELECT u.id, u.username, u.email, u.password, u.age,
           u.create_time, u.update_time, u.status
    FROM user u
    INNER JOIN user_role ur ON u.id = ur.user_id
    WHERE ur.role_id = #{roleId} AND u.status = 1
</select>

<!-- 根据角色名称查询用户 -->
<select id="selectUsersByRoleName" parameterType="string" resultMap="UserResultMap">
    SELECT u.id, u.username, u.email, u.password, u.age,
           u.create_time, u.update_time, u.status
    FROM user u
    INNER JOIN user_role ur ON u.id = ur.user_id
    INNER JOIN role r ON ur.role_id = r.id
    WHERE r.role_name = #{roleName} AND u.status = 1 AND r.status = 1
</select>

<!-- 查询拥有多个角色的用户 -->
<select id="selectUsersWithMultipleRoles" parameterType="list" resultMap="UserResultMap">
    SELECT DISTINCT u.id, u.username, u.email, u.password, u.age,
                    u.create_time, u.update_time, u.status
    FROM user u
    INNER JOIN user_role ur ON u.id = ur.user_id
    INNER JOIN role r ON ur.role_id = r.id
    WHERE r.role_name IN
    <foreach collection="roleNames" item="roleName" open="(" separator="," close=")">
        #{roleName}
    </foreach>
    AND u.status = 1 AND r.status = 1
    GROUP BY u.id
    HAVING COUNT(DISTINCT r.id) = #{roleNames.size}
</select>

<!-- 统计指定角色的用户数量 -->
<select id="countUsersByRoleId" parameterType="long" resultType="int">
    SELECT COUNT(DISTINCT u.id)
    FROM user u
    INNER JOIN user_role ur ON u.id = ur.user_id
    WHERE ur.role_id = #{roleId} AND u.status = 1
</select>

<!-- 获取用户统计信息 -->
<select id="getUserStatistics" resultType="map">
    SELECT
        COUNT(*) as totalUsers,
        COUNT(CASE WHEN status = 1 THEN 1 END) as activeUsers,
        COUNT(CASE WHEN status = 0 THEN 1 END) as inactiveUsers,
        AVG(age) as averageAge,
        MIN(create_time) as earliestUser,
        MAX(create_time) as latestUser
    FROM user
</select>
```

### 8. Service层实现

```java
// UserService.java
public interface UserService {

    // 基本CRUD操作
    User getUserById(Long id);
    List<User> getAllUsers();
    List<User> getUsersByCondition(UserQuery query);
    boolean saveUser(User user);
    boolean updateUser(User user);
    boolean deleteUser(Long id);

    // 关联操作
    User getUserWithRoles(Long id);
    List<User> getUsersWithRoles();
    boolean assignRoleToUser(Long userId, Long roleId);
    boolean removeRoleFromUser(Long userId, Long roleId);
    boolean assignRolesToUser(Long userId, List<Long> roleIds);

    // 业务操作
    boolean changeUserStatus(Long id, Integer status);
    boolean resetPassword(Long id, String newPassword);
    List<User> getUsersByRoleName(String roleName);
    Map<String, Object> getUserStatistics();

    // 分页查询
    List<User> getUsersByPage(int pageNum, int pageSize);
}

// UserServiceImpl.java
public class UserServiceImpl implements UserService {

    private UserMapper userMapper;
    private RoleMapper roleMapper;
    private UserRoleMapper userRoleMapper;

    public UserServiceImpl() {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        this.userMapper = sqlSession.getMapper(UserMapper.class);
        this.roleMapper = sqlSession.getMapper(RoleMapper.class);
        this.userRoleMapper = sqlSession.getMapper(UserRoleMapper.class);
    }

    @Override
    public User getUserById(Long id) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectById(id);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public List<User> getAllUsers() {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectAll();
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public List<User> getUsersByCondition(UserQuery query) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectByCondition(query);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean saveUser(User user) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);

            // 设置创建时间和更新时间
            Date now = new Date();
            user.setCreateTime(now);
            user.setUpdateTime(now);
            user.setStatus(1); // 默认启用

            int result = mapper.insert(user);
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("保存用户失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean updateUser(User user) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);

            // 设置更新时间
            user.setUpdateTime(new Date());

            int result = mapper.update(user);
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("更新用户失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean deleteUser(Long id) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper userMapper = sqlSession.getMapper(UserMapper.class);
            UserRoleMapper userRoleMapper = sqlSession.getMapper(UserRoleMapper.class);

            // 先删除用户角色关联
            userRoleMapper.deleteByUserId(id);

            // 再删除用户
            int result = userMapper.deleteById(id);
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("删除用户失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public User getUserWithRoles(Long id) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectUserWithRoles(id);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public List<User> getUsersWithRoles() {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectUsersWithRoles();
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean assignRoleToUser(Long userId, Long roleId) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserRoleMapper mapper = sqlSession.getMapper(UserRoleMapper.class);

            // 检查是否已存在该关联
            UserRole existing = mapper.selectByUserIdAndRoleId(userId, roleId);
            if (existing != null) {
                return true; // 已存在，直接返回成功
            }

            UserRole userRole = new UserRole(userId, roleId);
            int result = mapper.insert(userRole);
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("分配角色失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean removeRoleFromUser(Long userId, Long roleId) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserRoleMapper mapper = sqlSession.getMapper(UserRoleMapper.class);

            UserRole userRole = mapper.selectByUserIdAndRoleId(userId, roleId);
            if (userRole == null) {
                return true; // 不存在，直接返回成功
            }

            int result = mapper.deleteById(userRole.getId());
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("移除角色失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean assignRolesToUser(Long userId, List<Long> roleIds) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserRoleMapper mapper = sqlSession.getMapper(UserRoleMapper.class);

            // 先删除用户现有的所有角色
            mapper.deleteByUserId(userId);

            // 批量插入新的角色关联
            if (roleIds != null && !roleIds.isEmpty()) {
                List<UserRole> userRoles = new ArrayList<>();
                for (Long roleId : roleIds) {
                    userRoles.add(new UserRole(userId, roleId));
                }
                mapper.batchInsert(userRoles);
            }

            sqlSession.commit();
            return true;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("分配角色失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean changeUserStatus(Long id, Integer status) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);

            User user = new User();
            user.setId(id);
            user.setStatus(status);
            user.setUpdateTime(new Date());

            int result = mapper.update(user);
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("修改用户状态失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public boolean resetPassword(Long id, String newPassword) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);

            User user = new User();
            user.setId(id);
            user.setPassword(newPassword); // 实际应用中应该加密
            user.setUpdateTime(new Date());

            int result = mapper.update(user);
            sqlSession.commit();
            return result > 0;
        } catch (Exception e) {
            sqlSession.rollback();
            throw new RuntimeException("重置密码失败", e);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public List<User> getUsersByRoleName(String roleName) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.selectUsersByRoleName(roleName);
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public Map<String, Object> getUserStatistics() {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            return mapper.getUserStatistics();
        } finally {
            sqlSession.close();
        }
    }

    @Override
    public List<User> getUsersByPage(int pageNum, int pageSize) {
        SqlSession sqlSession = MyBatisUtil.getSqlSession();
        try {
            UserMapper mapper = sqlSession.getMapper(UserMapper.class);
            int offset = (pageNum - 1) * pageSize;
            return mapper.selectByPage(offset, pageSize);
        } finally {
            sqlSession.close();
        }
    }
}
```

### 9. 测试代码

```java
// UserMapperTest.java
public class UserMapperTest {

    private UserService userService;

    @Before
    public void setUp() {
        userService = new UserServiceImpl();
    }

    @Test
    public void testGetUserById() {
        User user = userService.getUserById(1L);
        assertNotNull(user);
        assertEquals("admin", user.getUsername());
        System.out.println("查询用户: " + user);
    }

    @Test
    public void testSaveUser() {
        User user = new User("testuser", "<EMAIL>", "test123");
        user.setAge(25);

        boolean result = userService.saveUser(user);
        assertTrue(result);
        assertNotNull(user.getId());
        System.out.println("保存用户成功，ID: " + user.getId());
    }

    @Test
    public void testUpdateUser() {
        User user = userService.getUserById(1L);
        assertNotNull(user);

        user.setAge(35);
        user.setEmail("<EMAIL>");

        boolean result = userService.updateUser(user);
        assertTrue(result);

        // 验证更新结果
        User updatedUser = userService.getUserById(1L);
        assertEquals(Integer.valueOf(35), updatedUser.getAge());
        assertEquals("<EMAIL>", updatedUser.getEmail());
    }

    @Test
    public void testGetUserWithRoles() {
        User user = userService.getUserWithRoles(1L);
        assertNotNull(user);
        assertNotNull(user.getRoles());
        assertTrue(user.getRoles().size() > 0);

        System.out.println("用户: " + user.getUsername());
        System.out.println("角色列表:");
        for (Role role : user.getRoles()) {
            System.out.println("  - " + role.getRoleName() + ": " + role.getRoleDesc());
        }
    }

    @Test
    public void testAssignRoleToUser() {
        boolean result = userService.assignRoleToUser(2L, 1L); // 给user1分配ADMIN角色
        assertTrue(result);

        User user = userService.getUserWithRoles(2L);
        boolean hasAdminRole = user.getRoles().stream()
                .anyMatch(role -> "ADMIN".equals(role.getRoleName()));
        assertTrue(hasAdminRole);
    }

    @Test
    public void testGetUsersByRoleName() {
        List<User> adminUsers = userService.getUsersByRoleName("ADMIN");
        assertNotNull(adminUsers);
        assertTrue(adminUsers.size() > 0);

        System.out.println("拥有ADMIN角色的用户:");
        for (User user : adminUsers) {
            System.out.println("  - " + user.getUsername());
        }
    }

    @Test
    public void testGetUserStatistics() {
        Map<String, Object> stats = userService.getUserStatistics();
        assertNotNull(stats);

        System.out.println("用户统计信息:");
        System.out.println("  总用户数: " + stats.get("totalUsers"));
        System.out.println("  活跃用户数: " + stats.get("activeUsers"));
        System.out.println("  非活跃用户数: " + stats.get("inactiveUsers"));
        System.out.println("  平均年龄: " + stats.get("averageAge"));
    }

    @Test
    public void testBatchOperations() {
        // 批量插入用户
        List<User> users = Arrays.asList(
            new User("batch1", "<EMAIL>", "pass123"),
            new User("batch2", "<EMAIL>", "pass123"),
            new User("batch3", "<EMAIL>", "pass123")
        );

        for (User user : users) {
            userService.saveUser(user);
        }

        // 验证批量插入结果
        List<User> allUsers = userService.getAllUsers();
        assertTrue(allUsers.size() >= 3);

        System.out.println("批量插入完成，当前用户总数: " + allUsers.size());
    }

    @Test
    public void testTransactionRollback() {
        try {
            // 模拟事务回滚场景
            User user = new User("erroruser", "<EMAIL>", "pass123");
            userService.saveUser(user);

            // 故意制造错误（比如插入重复的用户名）
            User duplicateUser = new User("erroruser", "<EMAIL>", "pass123");
            userService.saveUser(duplicateUser); // 这里应该会失败

            fail("应该抛出异常");
        } catch (Exception e) {
            System.out.println("事务回滚测试成功: " + e.getMessage());
        }
    }
}

// MyBatisDemo.java - 主程序演示
public class MyBatisDemo {

    public static void main(String[] args) {
        System.out.println("=== MyBatis框架演示程序 ===");

        UserService userService = new UserServiceImpl();

        try {
            // 1. 基本查询演示
            System.out.println("\n1. 基本查询演示:");
            List<User> allUsers = userService.getAllUsers();
            System.out.println("所有用户数量: " + allUsers.size());

            // 2. 关联查询演示
            System.out.println("\n2. 关联查询演示:");
            User userWithRoles = userService.getUserWithRoles(1L);
            if (userWithRoles != null) {
                System.out.println("用户: " + userWithRoles.getUsername());
                System.out.println("角色: " + userWithRoles.getRoles().stream()
                        .map(Role::getRoleName)
                        .collect(Collectors.joining(", ")));
            }

            // 3. 条件查询演示
            System.out.println("\n3. 条件查询演示:");
            UserQuery query = new UserQuery();
            query.setStatus(1);
            query.setMinAge(20);
            List<User> filteredUsers = userService.getUsersByCondition(query);
            System.out.println("符合条件的用户数量: " + filteredUsers.size());

            // 4. 统计查询演示
            System.out.println("\n4. 统计查询演示:");
            Map<String, Object> stats = userService.getUserStatistics();
            System.out.println("用户统计: " + stats);

            // 5. 分页查询演示
            System.out.println("\n5. 分页查询演示:");
            List<User> pageUsers = userService.getUsersByPage(1, 2);
            System.out.println("第1页用户(每页2条): " + pageUsers.size());

            // 6. 角色操作演示
            System.out.println("\n6. 角色操作演示:");
            List<User> adminUsers = userService.getUsersByRoleName("ADMIN");
            System.out.println("拥有ADMIN角色的用户: " + adminUsers.stream()
                    .map(User::getUsername)
                    .collect(Collectors.joining(", ")));

            // 7. 新增用户演示
            System.out.println("\n7. 新增用户演示:");
            User newUser = new User("demo_user", "<EMAIL>", "demo123");
            newUser.setAge(28);
            boolean saveResult = userService.saveUser(newUser);
            System.out.println("新增用户结果: " + (saveResult ? "成功" : "失败"));
            if (saveResult) {
                System.out.println("新用户ID: " + newUser.getId());

                // 为新用户分配角色
                boolean assignResult = userService.assignRoleToUser(newUser.getId(), 2L);
                System.out.println("分配角色结果: " + (assignResult ? "成功" : "失败"));
            }

            // 8. 更新用户演示
            System.out.println("\n8. 更新用户演示:");
            if (newUser.getId() != null) {
                newUser.setAge(30);
                newUser.setEmail("<EMAIL>");
                boolean updateResult = userService.updateUser(newUser);
                System.out.println("更新用户结果: " + (updateResult ? "成功" : "失败"));
            }

        } catch (Exception e) {
            System.err.println("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n=== 演示程序结束 ===");
    }
}
```

### 10. 最佳实践和注意事项

#### 性能优化建议

1. **合理使用缓存**
```java
// 在Mapper.xml中配置二级缓存
<cache eviction="LRU" flushInterval="60000" size="512" readOnly="true"/>

// 对于频繁查询且变化不大的数据使用缓存
<select id="selectById" resultType="User" useCache="true">
    SELECT * FROM user WHERE id = #{id}
</select>
```

2. **避免N+1查询问题**
```xml
<!-- 使用关联查询替代多次单独查询 -->
<select id="selectUsersWithRoles" resultMap="UserWithRolesResultMap">
    SELECT u.*, r.id as role_id, r.role_name, r.role_desc
    FROM user u
    LEFT JOIN user_role ur ON u.id = ur.user_id
    LEFT JOIN role r ON ur.role_id = r.id
</select>
```

3. **合理使用批量操作**
```xml
<!-- 批量插入 -->
<insert id="batchInsert" parameterType="list">
    INSERT INTO user (username, email, password) VALUES
    <foreach collection="list" item="user" separator=",">
        (#{user.username}, #{user.email}, #{user.password})
    </foreach>
</insert>
```

#### 安全性建议

1. **防止SQL注入**
```xml
<!-- 正确使用参数绑定 -->
<select id="selectByUsername" parameterType="string" resultType="User">
    SELECT * FROM user WHERE username = #{username}
</select>

<!-- 避免直接拼接SQL -->
<!-- 错误示例：SELECT * FROM user WHERE username = '${username}' -->
```

2. **敏感信息处理**
```java
// 查询时排除敏感字段
<select id="selectUserInfo" resultType="User">
    SELECT id, username, email, age, create_time, status
    FROM user WHERE id = #{id}
    <!-- 不查询password字段 -->
</select>
```

#### 事务管理建议

1. **合理控制事务范围**
```java
public boolean transferUserRole(Long fromUserId, Long toUserId, Long roleId) {
    SqlSession sqlSession = MyBatisUtil.getSqlSession();
    try {
        UserRoleMapper mapper = sqlSession.getMapper(UserRoleMapper.class);

        // 在同一个事务中完成角色转移
        mapper.deleteByUserIdAndRoleId(fromUserId, roleId);
        mapper.insert(new UserRole(toUserId, roleId));

        sqlSession.commit();
        return true;
    } catch (Exception e) {
        sqlSession.rollback();
        throw new RuntimeException("角色转移失败", e);
    } finally {
        sqlSession.close();
    }
}
```

2. **异常处理**
```java
public void handleUserOperation() {
    SqlSession sqlSession = MyBatisUtil.getSqlSession();
    try {
        // 业务操作
        performBusinessLogic(sqlSession);
        sqlSession.commit();
    } catch (BusinessException e) {
        sqlSession.rollback();
        // 记录业务异常日志
        logger.warn("业务操作失败: {}", e.getMessage());
        throw e;
    } catch (Exception e) {
        sqlSession.rollback();
        // 记录系统异常日志
        logger.error("系统异常: ", e);
        throw new SystemException("系统异常", e);
    } finally {
        sqlSession.close();
    }
}
```

#### 配置优化建议

1. **连接池配置**
```xml
<dataSource type="POOLED">
    <property name="driver" value="${driver}"/>
    <property name="url" value="${url}"/>
    <property name="username" value="${username}"/>
    <property name="password" value="${password}"/>
    <!-- 连接池优化配置 -->
    <property name="poolMaximumActiveConnections" value="20"/>
    <property name="poolMaximumIdleConnections" value="5"/>
    <property name="poolMaximumCheckoutTime" value="20000"/>
    <property name="poolTimeToWait" value="20000"/>
    <property name="poolPingEnabled" value="true"/>
    <property name="poolPingQuery" value="SELECT 1"/>
    <property name="poolPingConnectionsNotUsedFor" value="19000"/>
</dataSource>
```

2. **日志配置**
```xml
<!-- logback.xml -->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- MyBatis SQL日志 -->
    <logger name="com.example.mapper" level="DEBUG"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
```

这个完整的MyBatis框架详解和实战文档涵盖了从基础概念到高级特性的所有内容，包括：

- MyBatis核心组件和架构
- 详细的配置文件说明
- 动态SQL的各种用法
- 缓存机制和插件开发
- 完整的实战项目示例
- 最佳实践和性能优化建议

通过这个文档，您可以全面掌握MyBatis框架的使用方法和最佳实践。
```
```
```
```
