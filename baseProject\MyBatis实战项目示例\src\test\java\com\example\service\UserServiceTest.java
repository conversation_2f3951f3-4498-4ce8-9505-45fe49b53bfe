package com.example.service;

import com.example.entity.User;
import com.example.dto.UserQueryDTO;
import com.example.vo.PageResult;
import com.example.service.impl.UserServiceImpl;
import com.example.mapper.UserMapper;
import com.example.exception.BusinessException;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户服务测试类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class UserServiceTest {
    
    @Mock
    private UserMapper userMapper;
    
    @InjectMocks
    private UserServiceImpl userService;
    
    private User testUser;
    
    @Before
    public void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .password("test123")
                .nickname("测试用户")
                .phone("***********")
                .gender(1)
                .age(25)
                .status(1)
                .emailVerified(0)
                .phoneVerified(0)
                .loginCount(0)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .version(1)
                .deleted(0)
                .build();
    }
    
    @Test
    public void testGetUserById_Success() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        
        // When
        User result = userService.getUserById(1L);
        
        // Then
        assertNotNull(result);
        assertEquals(testUser.getId(), result.getId());
        assertEquals(testUser.getUsername(), result.getUsername());
        verify(userMapper, times(1)).selectById(1L);
    }
    
    @Test(expected = BusinessException.class)
    public void testGetUserById_NullId() {
        // When
        userService.getUserById(null);
        
        // Then - expect BusinessException
    }
    
    @Test
    public void testGetUserByUsername_Success() {
        // Given
        when(userMapper.selectByUsername("testuser")).thenReturn(testUser);
        
        // When
        User result = userService.getUserByUsername("testuser");
        
        // Then
        assertNotNull(result);
        assertEquals(testUser.getUsername(), result.getUsername());
        verify(userMapper, times(1)).selectByUsername("testuser");
    }
    
    @Test(expected = BusinessException.class)
    public void testGetUserByUsername_EmptyUsername() {
        // When
        userService.getUserByUsername("");
        
        // Then - expect BusinessException
    }
    
    @Test
    public void testGetAllUsers_Success() {
        // Given
        List<User> users = Arrays.asList(testUser);
        when(userMapper.selectAll()).thenReturn(users);
        
        // When
        List<User> result = userService.getAllUsers();
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testUser.getId(), result.get(0).getId());
        verify(userMapper, times(1)).selectAll();
    }
    
    @Test
    public void testGetUsersByPage_Success() {
        // Given
        UserQueryDTO query = new UserQueryDTO();
        query.setPageNum(1);
        query.setPageSize(10);
        
        List<User> users = Arrays.asList(testUser);
        when(userMapper.countByCondition(any(UserQueryDTO.class))).thenReturn(1);
        when(userMapper.selectByCondition(any(UserQueryDTO.class))).thenReturn(users);
        
        // When
        PageResult<User> result = userService.getUsersByPage(query);
        
        // Then
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getPageNum());
        assertEquals(Integer.valueOf(10), result.getPageSize());
        assertEquals(Integer.valueOf(1), result.getTotal());
        assertEquals(1, result.getList().size());
        verify(userMapper, times(1)).countByCondition(any(UserQueryDTO.class));
        verify(userMapper, times(1)).selectByCondition(any(UserQueryDTO.class));
    }
    
    @Test
    public void testSaveUser_Success() {
        // Given
        User newUser = User.builder()
                .username("newuser")
                .email("<EMAIL>")
                .password("new123")
                .build();
        
        when(userMapper.existsByUsername(eq("newuser"), isNull())).thenReturn(false);
        when(userMapper.existsByEmail(eq("<EMAIL>"), isNull())).thenReturn(false);
        when(userMapper.insert(any(User.class))).thenReturn(1);
        
        // When
        boolean result = userService.saveUser(newUser);
        
        // Then
        assertTrue(result);
        assertNotNull(newUser.getCreateTime());
        assertNotNull(newUser.getUpdateTime());
        assertEquals(Integer.valueOf(1), newUser.getStatus());
        assertEquals(Integer.valueOf(0), newUser.getEmailVerified());
        assertEquals(Integer.valueOf(0), newUser.getPhoneVerified());
        verify(userMapper, times(1)).insert(any(User.class));
    }
    
    @Test(expected = BusinessException.class)
    public void testSaveUser_NullUser() {
        // When
        userService.saveUser(null);
        
        // Then - expect BusinessException
    }
    
    @Test(expected = BusinessException.class)
    public void testSaveUser_EmptyUsername() {
        // Given
        User newUser = User.builder()
                .username("")
                .email("<EMAIL>")
                .password("new123")
                .build();
        
        // When
        userService.saveUser(newUser);
        
        // Then - expect BusinessException
    }
    
    @Test(expected = BusinessException.class)
    public void testSaveUser_DuplicateUsername() {
        // Given
        User newUser = User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("new123")
                .build();
        
        when(userMapper.existsByUsername(eq("testuser"), isNull())).thenReturn(true);
        
        // When
        userService.saveUser(newUser);
        
        // Then - expect BusinessException
    }
    
    @Test
    public void testUpdateUser_Success() {
        // Given
        User updateUser = User.builder()
                .id(1L)
                .username("updateduser")
                .email("<EMAIL>")
                .nickname("更新用户")
                .version(1)
                .build();
        
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.existsByUsername(eq("updateduser"), eq(1L))).thenReturn(false);
        when(userMapper.existsByEmail(eq("<EMAIL>"), eq(1L))).thenReturn(false);
        when(userMapper.update(any(User.class))).thenReturn(1);
        
        // When
        boolean result = userService.updateUser(updateUser);
        
        // Then
        assertTrue(result);
        assertNotNull(updateUser.getUpdateTime());
        verify(userMapper, times(1)).selectById(1L);
        verify(userMapper, times(1)).update(any(User.class));
    }
    
    @Test(expected = BusinessException.class)
    public void testUpdateUser_UserNotExists() {
        // Given
        User updateUser = User.builder()
                .id(999L)
                .username("updateduser")
                .email("<EMAIL>")
                .build();
        
        when(userMapper.selectById(999L)).thenReturn(null);
        
        // When
        userService.updateUser(updateUser);
        
        // Then - expect BusinessException
    }
    
    @Test
    public void testDeleteUser_Success() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.deleteById(1L)).thenReturn(1);
        
        // When
        boolean result = userService.deleteUser(1L);
        
        // Then
        assertTrue(result);
        verify(userMapper, times(1)).selectById(1L);
        verify(userMapper, times(1)).deleteById(1L);
    }
    
    @Test(expected = BusinessException.class)
    public void testDeleteUser_UserNotExists() {
        // Given
        when(userMapper.selectById(999L)).thenReturn(null);
        
        // When
        userService.deleteUser(999L);
        
        // Then - expect BusinessException
    }
    
    @Test
    public void testLogicDeleteUser_Success() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.logicDelete(1L, 1L)).thenReturn(1);
        
        // When
        boolean result = userService.logicDeleteUser(1L, 1L);
        
        // Then
        assertTrue(result);
        verify(userMapper, times(1)).selectById(1L);
        verify(userMapper, times(1)).logicDelete(1L, 1L);
    }
    
    @Test
    public void testGetUserWithRoles_Success() {
        // Given
        when(userMapper.selectUserWithRoles(1L)).thenReturn(testUser);
        
        // When
        User result = userService.getUserWithRoles(1L);
        
        // Then
        assertNotNull(result);
        assertEquals(testUser.getId(), result.getId());
        verify(userMapper, times(1)).selectUserWithRoles(1L);
    }
    
    @Test
    public void testGetUserCount_Success() {
        // Given
        when(userMapper.count()).thenReturn(10);
        
        // When
        int result = userService.getUserCount();
        
        // Then
        assertEquals(10, result);
        verify(userMapper, times(1)).count();
    }
    
    @Test
    public void testGetUserCountByStatus_Success() {
        // Given
        when(userMapper.countByStatus(1)).thenReturn(8);
        
        // When
        int result = userService.getUserCountByStatus(1);
        
        // Then
        assertEquals(8, result);
        verify(userMapper, times(1)).countByStatus(1);
    }
    
    @Test
    public void testGetUserStatistics_Success() {
        // Given
        Map<String, Object> stats = Map.of(
                "totalUsers", 100,
                "activeUsers", 80,
                "disabledUsers", 20
        );
        when(userMapper.getUserStatistics()).thenReturn(stats);
        
        // When
        Map<String, Object> result = userService.getUserStatistics();
        
        // Then
        assertNotNull(result);
        assertEquals(100, result.get("totalUsers"));
        assertEquals(80, result.get("activeUsers"));
        assertEquals(20, result.get("disabledUsers"));
        verify(userMapper, times(1)).getUserStatistics();
    }
    
    @Test
    public void testIsUsernameExists_True() {
        // Given
        when(userMapper.existsByUsername("testuser", null)).thenReturn(true);
        
        // When
        boolean result = userService.isUsernameExists("testuser", null);
        
        // Then
        assertTrue(result);
        verify(userMapper, times(1)).existsByUsername("testuser", null);
    }
    
    @Test
    public void testIsUsernameExists_False() {
        // Given
        when(userMapper.existsByUsername("newuser", null)).thenReturn(false);
        
        // When
        boolean result = userService.isUsernameExists("newuser", null);
        
        // Then
        assertFalse(result);
        verify(userMapper, times(1)).existsByUsername("newuser", null);
    }
    
    @Test
    public void testUpdateUserStatus_Success() {
        // Given
        when(userMapper.selectById(1L)).thenReturn(testUser);
        when(userMapper.updateStatus(1L, 0, 1L)).thenReturn(1);
        
        // When
        boolean result = userService.updateUserStatus(1L, 0, 1L);
        
        // Then
        assertTrue(result);
        verify(userMapper, times(1)).selectById(1L);
        verify(userMapper, times(1)).updateStatus(1L, 0, 1L);
    }
    
    @Test(expected = BusinessException.class)
    public void testUpdateUserStatus_InvalidStatus() {
        // When
        userService.updateUserStatus(1L, 5, 1L);
        
        // Then - expect BusinessException
    }
    
    @Test
    public void testLogin_Success() {
        // Given
        User loginUser = User.builder()
                .id(1L)
                .username("testuser")
                .password("$2a$10$encrypted_password")
                .salt("salt123")
                .status(1)
                .build();
        
        when(userMapper.selectByUsername("testuser")).thenReturn(loginUser);
        when(userMapper.updateLastLogin(eq(1L), any(LocalDateTime.class), eq("127.0.0.1"))).thenReturn(1);
        
        // Mock password verification (this would need actual implementation)
        // For this test, we'll assume password verification passes
        
        // When & Then would need actual password verification implementation
        // This is a simplified test structure
    }
}
