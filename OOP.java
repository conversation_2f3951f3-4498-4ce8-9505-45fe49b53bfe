/**
 * 这个程序展示Java中的面向对象编程概念
 */
public class OOP {
    public static void main(String[] args) {
        // 创建学生对象
        Student student1 = new Student(1001, "张三", 20);
        Student student2 = new Student(1002, "李四", 21);
        
        // 调用方法
        student1.study("Java编程");
        student2.study("数据库");
        
        // 获取和设置属性
        System.out.println(student1.getName() + "的年龄是: " + student1.getAge());
        student1.setAge(22);
        System.out.println("修改后，" + student1.getName() + "的年龄是: " + student1.getAge());
        
        // 创建教师对象
        Teacher teacher = new Teacher(2001, "王教授", "计算机科学");
        teacher.teach();
        
        // 多态
        Person person1 = new Student(1003, "赵六", 19);  // 学生是一个人
        Person person2 = new Teacher(2002, "刘老师", "数学");  // 教师是一个人
        
        // 调用多态方法
        System.out.println("\n===== 多态示例 =====");
        person1.introduce();  // 调用学生的introduce方法
        person2.introduce();  // 调用教师的introduce方法
    }
}

// 基类
class Person {
    private int id;
    private String name;
    
    public Person(int id, String name) {
        this.id = id;
        this.name = name;
    }
    
    public int getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    // 可以被子类重写的方法
    public void introduce() {
        System.out.println("你好，我是" + name);
    }
}

// 学生类（继承自Person）
class Student extends Person {
    private int age;
    
    public Student(int id, String name, int age) {
        super(id, name);  // 调用父类构造函数
        this.age = age;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
    
    public void study(String subject) {
        System.out.println(getName() + "正在学习" + subject);
    }
    
    // 重写父类方法
    @Override
    public void introduce() {
        System.out.println("你好，我是学生" + getName() + "，今年" + age + "岁");
    }
}

// 教师类（继承自Person）
class Teacher extends Person {
    private String subject;
    
    public Teacher(int id, String name, String subject) {
        super(id, name);
        this.subject = subject;
    }
    
    public void teach() {
        System.out.println(getName() + "老师正在教授" + subject);
    }
    
    // 重写父类方法
    @Override
    public void introduce() {
        System.out.println("你好，我是" + getName() + "老师，教授" + subject + "课程");
    }
} 