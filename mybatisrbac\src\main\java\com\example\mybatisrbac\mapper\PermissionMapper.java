package com.example.mybatisrbac.mapper;

import com.example.mybatisrbac.dto.PermissionQueryDTO;
import com.example.mybatisrbac.entity.Permission;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 权限数据访问层
 * 
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface PermissionMapper {

    /**
     * 根据条件查询权限总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM sys_permission WHERE 1=1 " +
            "<if test='permissionName != null and permissionName != \"\"'>" +
            "AND permission_name LIKE CONCAT('%', #{permissionName}, '%') " +
            "</if>" +
            "<if test='permissionCode != null and permissionCode != \"\"'>" +
            "AND permission_code LIKE CONCAT('%', #{permissionCode}, '%') " +
            "</if>" +
            "<if test='permissionType != null'>" +
            "AND permission_type = #{permissionType} " +
            "</if>" +
            "<if test='parentId != null'>" +
            "AND parent_id = #{parentId} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND status = #{status} " +
            "</if>" +
            "</script>")
    Long countByCondition(PermissionQueryDTO queryDTO);

    /**
     * 根据条件分页查询权限列表
     */
    @Select("<script>" +
            "SELECT * FROM sys_permission WHERE 1=1 " +
            "<if test='queryDTO.permissionName != null and queryDTO.permissionName != \"\"'>" +
            "AND permission_name LIKE CONCAT('%', #{queryDTO.permissionName}, '%') " +
            "</if>" +
            "<if test='queryDTO.permissionCode != null and queryDTO.permissionCode != \"\"'>" +
            "AND permission_code LIKE CONCAT('%', #{queryDTO.permissionCode}, '%') " +
            "</if>" +
            "<if test='queryDTO.permissionType != null'>" +
            "AND permission_type = #{queryDTO.permissionType} " +
            "</if>" +
            "<if test='queryDTO.parentId != null'>" +
            "AND parent_id = #{queryDTO.parentId} " +
            "</if>" +
            "<if test='queryDTO.status != null'>" +
            "AND status = #{queryDTO.status} " +
            "</if>" +
            "ORDER BY parent_id ASC, sort_order ASC, ${sortField} ${sortOrder} " +
            "LIMIT #{offset}, #{size}" +
            "</script>")
    List<Permission> selectByCondition(@Param("queryDTO") PermissionQueryDTO queryDTO, 
                                      @Param("offset") Long offset, 
                                      @Param("size") Long size,
                                      @Param("sortField") String sortField,
                                      @Param("sortOrder") String sortOrder);

    /**
     * 根据ID查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "permissionName", column = "permission_name"),
        @Result(property = "permissionCode", column = "permission_code"),
        @Result(property = "permissionType", column = "permission_type"),
        @Result(property = "parentId", column = "parent_id"),
        @Result(property = "path", column = "path"),
        @Result(property = "component", column = "component"),
        @Result(property = "icon", column = "icon"),
        @Result(property = "sortOrder", column = "sort_order"),
        @Result(property = "status", column = "status"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "updateTime", column = "update_time"),
        @Result(property = "createBy", column = "create_by"),
        @Result(property = "updateBy", column = "update_by")
    })
    Permission selectById(Long id);

    /**
     * 根据权限编码查询权限
     */
    @Select("SELECT * FROM sys_permission WHERE permission_code = #{permissionCode}")
    Permission selectByPermissionCode(String permissionCode);

    /**
     * 插入权限
     */
    @Insert("INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, path, component, icon, sort_order, status, create_time, update_time, create_by, update_by) " +
            "VALUES (#{permissionName}, #{permissionCode}, #{permissionType}, #{parentId}, #{path}, #{component}, #{icon}, #{sortOrder}, #{status}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Permission permission);

    /**
     * 更新权限
     */
    @Update("<script>" +
            "UPDATE sys_permission SET " +
            "<if test='permissionName != null'>permission_name = #{permissionName},</if>" +
            "<if test='permissionCode != null'>permission_code = #{permissionCode},</if>" +
            "<if test='permissionType != null'>permission_type = #{permissionType},</if>" +
            "<if test='parentId != null'>parent_id = #{parentId},</if>" +
            "<if test='path != null'>path = #{path},</if>" +
            "<if test='component != null'>component = #{component},</if>" +
            "<if test='icon != null'>icon = #{icon},</if>" +
            "<if test='sortOrder != null'>sort_order = #{sortOrder},</if>" +
            "<if test='status != null'>status = #{status},</if>" +
            "update_time = #{updateTime}, " +
            "update_by = #{updateBy} " +
            "WHERE id = #{id}" +
            "</script>")
    int updateById(Permission permission);

    /**
     * 根据ID删除权限
     */
    @Delete("DELETE FROM sys_permission WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 批量删除权限
     */
    @Delete("<script>" +
            "DELETE FROM sys_permission WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteByIds(@Param("ids") List<Long> ids);

    /**
     * 检查权限编码是否存在
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE permission_code = #{permissionCode} AND id != #{excludeId}")
    int checkPermissionCodeExists(@Param("permissionCode") String permissionCode, @Param("excludeId") Long excludeId);

    /**
     * 查询所有启用的权限
     */
    @Select("SELECT * FROM sys_permission WHERE status = 1 ORDER BY parent_id ASC, sort_order ASC")
    List<Permission> selectEnabledPermissions();

    /**
     * 查询子权限
     */
    @Select("SELECT * FROM sys_permission WHERE parent_id = #{parentId} ORDER BY sort_order ASC")
    List<Permission> selectChildrenByParentId(Long parentId);

    /**
     * 查询角色的权限列表
     */
    @Select("SELECT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 " +
            "ORDER BY p.parent_id ASC, p.sort_order ASC")
    List<Permission> selectRolePermissions(Long roleId);

    /**
     * 查询用户的权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.status = 1 " +
            "ORDER BY p.parent_id ASC, p.sort_order ASC")
    List<Permission> selectUserPermissions(Long userId);
}
