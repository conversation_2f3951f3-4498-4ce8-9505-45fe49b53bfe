package com.example.mybatisdemo.mapper;

import com.example.mybatisdemo.entity.Article;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文章Mapper接口
 */
@Mapper
public interface ArticleMapper {
    
    /**
     * 根据ID查询文章
     * @param id 文章ID
     * @return 文章信息
     */
    Article selectById(@Param("id") Long id);
    
    /**
     * 查询所有文章
     * @return 文章列表
     */
    List<Article> selectAll();
    
    /**
     * 根据作者ID查询文章
     * @param authorId 作者ID
     * @return 文章列表
     */
    List<Article> selectByAuthorId(@Param("authorId") Long authorId);
    
    /**
     * 根据分类查询文章
     * @param category 分类
     * @return 文章列表
     */
    List<Article> selectByCategory(@Param("category") String category);
    
    /**
     * 根据状态查询文章
     * @param status 文章状态
     * @return 文章列表
     */
    List<Article> selectByStatus(@Param("status") Integer status);
    
    /**
     * 分页查询文章
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 文章列表
     */
    List<Article> selectByPage(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 根据关键字搜索文章
     * @param keyword 关键字
     * @return 文章列表
     */
    List<Article> searchArticles(@Param("keyword") String keyword);
    
    /**
     * 查询热门文章（按浏览量排序）
     * @param limit 限制数量
     * @return 文章列表
     */
    List<Article> selectPopularArticles(@Param("limit") Integer limit);
    
    /**
     * 插入文章
     * @param article 文章信息
     * @return 影响行数
     */
    int insert(Article article);
    
    /**
     * 更新文章信息
     * @param article 文章信息
     * @return 影响行数
     */
    int update(Article article);
    
    /**
     * 增加浏览次数
     * @param id 文章ID
     * @return 影响行数
     */
    int incrementViewCount(@Param("id") Long id);
    
    /**
     * 根据ID删除文章
     * @param id 文章ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 批量删除文章
     * @param ids 文章ID列表
     * @return 影响行数
     */
    int deleteBatch(@Param("ids") List<Long> ids);
    
    /**
     * 统计文章总数
     * @return 文章总数
     */
    Long countAll();
    
    /**
     * 根据作者统计文章数
     * @param authorId 作者ID
     * @return 文章数量
     */
    Long countByAuthorId(@Param("authorId") Long authorId);
    
    /**
     * 根据分类统计文章数
     * @param category 分类
     * @return 文章数量
     */
    Long countByCategory(@Param("category") String category);
}
