<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>待办事项应用 - 前端实战项目</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 应用头部 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-tasks"></i>
                    我的待办事项
                </h1>
                <div class="header-stats">
                    <span class="stat-item">
                        <i class="fas fa-list"></i>
                        总计: <span id="total-count">0</span>
                    </span>
                    <span class="stat-item">
                        <i class="fas fa-clock"></i>
                        待完成: <span id="pending-count">0</span>
                    </span>
                    <span class="stat-item">
                        <i class="fas fa-check"></i>
                        已完成: <span id="completed-count">0</span>
                    </span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 添加新任务 -->
            <section class="add-task-section">
                <div class="add-task-container">
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="task-input" 
                            placeholder="输入新的待办事项..."
                            maxlength="100"
                        >
                        <select id="priority-select">
                            <option value="low">低优先级</option>
                            <option value="medium" selected>中优先级</option>
                            <option value="high">高优先级</option>
                        </select>
                        <input type="date" id="due-date" title="截止日期">
                        <button id="add-task-btn" class="add-btn">
                            <i class="fas fa-plus"></i>
                            添加任务
                        </button>
                    </div>
                    <div class="quick-add">
                        <span>快速添加:</span>
                        <button class="quick-btn" data-task="学习JavaScript">📚 学习JavaScript</button>
                        <button class="quick-btn" data-task="锻炼身体">💪 锻炼身体</button>
                        <button class="quick-btn" data-task="阅读书籍">📖 阅读书籍</button>
                        <button class="quick-btn" data-task="写代码">💻 写代码</button>
                    </div>
                </div>
            </section>

            <!-- 过滤和搜索 -->
            <section class="filter-section">
                <div class="filter-container">
                    <div class="filter-tabs">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-list"></i>
                            全部 (<span id="all-count">0</span>)
                        </button>
                        <button class="filter-btn" data-filter="pending">
                            <i class="fas fa-clock"></i>
                            待完成 (<span id="pending-filter-count">0</span>)
                        </button>
                        <button class="filter-btn" data-filter="completed">
                            <i class="fas fa-check"></i>
                            已完成 (<span id="completed-filter-count">0</span>)
                        </button>
                        <button class="filter-btn" data-filter="overdue">
                            <i class="fas fa-exclamation-triangle"></i>
                            已逾期 (<span id="overdue-count">0</span>)
                        </button>
                    </div>
                    
                    <div class="search-sort">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="search-input" placeholder="搜索任务...">
                            <button id="clear-search" class="clear-btn" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        
                        <select id="sort-select">
                            <option value="created">按创建时间</option>
                            <option value="priority">按优先级</option>
                            <option value="dueDate">按截止日期</option>
                            <option value="alphabetical">按字母顺序</option>
                        </select>
                        
                        <button id="sort-order" class="sort-btn" title="切换排序顺序">
                            <i class="fas fa-sort-amount-down"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 任务列表 -->
            <section class="tasks-section">
                <div class="tasks-container">
                    <!-- 空状态 -->
                    <div id="empty-state" class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3>还没有任务</h3>
                        <p>添加你的第一个待办事项开始管理你的时间吧！</p>
                        <button class="empty-add-btn" onclick="document.getElementById('task-input').focus()">
                            <i class="fas fa-plus"></i>
                            添加第一个任务
                        </button>
                    </div>

                    <!-- 任务列表 -->
                    <div id="tasks-list" class="tasks-list"></div>
                </div>
            </section>

            <!-- 批量操作 -->
            <section class="bulk-actions" id="bulk-actions" style="display: none;">
                <div class="bulk-container">
                    <span class="bulk-info">
                        已选择 <span id="selected-count">0</span> 个任务
                    </span>
                    <div class="bulk-buttons">
                        <button id="bulk-complete" class="bulk-btn complete">
                            <i class="fas fa-check"></i>
                            标记完成
                        </button>
                        <button id="bulk-delete" class="bulk-btn delete">
                            <i class="fas fa-trash"></i>
                            删除选中
                        </button>
                        <button id="bulk-cancel" class="bulk-btn cancel">
                            <i class="fas fa-times"></i>
                            取消选择
                        </button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>
                    <i class="fas fa-chart-pie"></i>
                    统计信息
                </h3>
                <button id="sidebar-toggle" class="sidebar-toggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="sidebar-content">
                <!-- 进度图表 -->
                <div class="progress-section">
                    <h4>完成进度</h4>
                    <div class="progress-circle">
                        <svg class="progress-ring" width="120" height="120">
                            <circle class="progress-ring-circle" cx="60" cy="60" r="50"></circle>
                        </svg>
                        <div class="progress-text">
                            <span id="progress-percentage">0%</span>
                        </div>
                    </div>
                </div>

                <!-- 优先级分布 -->
                <div class="priority-section">
                    <h4>优先级分布</h4>
                    <div class="priority-stats">
                        <div class="priority-item high">
                            <span class="priority-label">高优先级</span>
                            <span class="priority-count" id="high-priority-count">0</span>
                        </div>
                        <div class="priority-item medium">
                            <span class="priority-label">中优先级</span>
                            <span class="priority-count" id="medium-priority-count">0</span>
                        </div>
                        <div class="priority-item low">
                            <span class="priority-label">低优先级</span>
                            <span class="priority-count" id="low-priority-count">0</span>
                        </div>
                    </div>
                </div>

                <!-- 今日任务 -->
                <div class="today-section">
                    <h4>今日任务</h4>
                    <div class="today-stats">
                        <div class="today-item">
                            <i class="fas fa-calendar-day"></i>
                            <span>今日到期: <strong id="due-today-count">0</strong></span>
                        </div>
                        <div class="today-item">
                            <i class="fas fa-fire"></i>
                            <span>连续完成: <strong id="streak-count">0</strong> 天</span>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="actions-section">
                    <button id="export-btn" class="action-btn">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                    <button id="import-btn" class="action-btn">
                        <i class="fas fa-upload"></i>
                        导入数据
                    </button>
                    <input type="file" id="import-file" accept=".json" style="display: none;">
                    <button id="clear-all-btn" class="action-btn danger">
                        <i class="fas fa-trash-alt"></i>
                        清空所有
                    </button>
                </div>
            </div>
        </aside>

        <!-- 浮动按钮 -->
        <div class="floating-buttons">
            <button id="stats-toggle" class="float-btn stats" title="显示/隐藏统计">
                <i class="fas fa-chart-bar"></i>
            </button>
            <button id="scroll-top" class="float-btn scroll-top" title="回到顶部" style="display: none;">
                <i class="fas fa-arrow-up"></i>
            </button>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">编辑任务</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-form">
                    <div class="form-group">
                        <label for="edit-task-text">任务内容</label>
                        <textarea id="edit-task-text" rows="3" maxlength="200"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-priority">优先级</label>
                            <select id="edit-priority">
                                <option value="low">低优先级</option>
                                <option value="medium">中优先级</option>
                                <option value="high">高优先级</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="edit-due-date">截止日期</label>
                            <input type="date" id="edit-due-date">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit-notes">备注</label>
                        <textarea id="edit-notes" rows="2" placeholder="添加备注..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="modal-cancel">取消</button>
                <button type="button" class="btn-primary" id="modal-save">保存</button>
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-dialog" class="modal">
        <div class="modal-content small">
            <div class="modal-header">
                <h3 id="confirm-title">确认操作</h3>
            </div>
            <div class="modal-body">
                <p id="confirm-message">确定要执行此操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="confirm-cancel">取消</button>
                <button type="button" class="btn-danger" id="confirm-ok">确定</button>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notifications" class="notifications"></div>

    <script src="app.js"></script>
</body>
</html>
