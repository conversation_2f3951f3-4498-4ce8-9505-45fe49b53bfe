# 角色管理分页查询改造说明

## 改造概述

将原来的简单查询接口改造为支持分页、搜索、排序的完整查询功能。

## 改造内容

### 1. 创建查询DTO

**RoleQueryDTO.java** - 角色查询参数
```java
@Data
public class RoleQueryDTO {
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;
    
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 10;
    
    private String roleName;      // 角色名称（模糊查询）
    private String roleCode;      // 角色编码（模糊查询）
    private Integer status;       // 状态筛选
    private String keyword;       // 关键字搜索
    private String sortField;     // 排序字段
    private String sortDirection; // 排序方向
}
```

**PageResult.java** - 分页结果封装
```java
@Data
public class PageResult<T> {
    private List<T> records;     // 数据列表
    private Long total;          // 总记录数
    private Integer page;        // 当前页码
    private Integer size;        // 每页大小
    private Integer totalPages;  // 总页数
    private Boolean hasNext;     // 是否有下一页
    private Boolean hasPrevious; // 是否有上一页
    private Boolean isFirst;     // 是否为第一页
    private Boolean isLast;      // 是否为最后一页
}
```

### 2. Controller改造

**原来的接口：**
```java
@GetMapping
public Result<List<Role>> getAllRoles() {
    List<Role> roles = roleService.getAllRoles();
    return Result.success(roles);
}
```

**改造后的接口：**
```java
// 分页查询角色列表
@GetMapping
public Result<PageResult<Role>> getRoles(@Valid RoleQueryDTO queryDTO) {
    PageResult<Role> pageResult = roleService.getRolesByPage(queryDTO);
    return Result.success(pageResult);
}

// 查询所有角色（不分页，用于下拉选择等场景）
@GetMapping("/all")
public Result<List<Role>> getAllRoles() {
    List<Role> roles = roleService.getAllRoles();
    return Result.success(roles);
}

// 根据关键字搜索角色
@GetMapping("/search")
public Result<PageResult<Role>> searchRoles(
        @RequestParam @NotBlank(message = "关键字不能为空") String keyword,
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "10") Integer size) {
    // 搜索逻辑
}
```

### 3. Service层扩展

**新增方法：**
```java
public interface RoleService {
    // 原有方法
    List<Role> getAllRoles();
    
    // 新增方法
    PageResult<Role> getRolesByPage(RoleQueryDTO queryDTO);
    Role getRoleById(Long id);
    Role getRoleByCode(String roleCode);
    Role createRole(Role role);
    Role updateRole(Role role);
    boolean deleteRole(Long id);
    boolean enableRole(Long id);
    boolean disableRole(Long id);
}
```

### 4. Mapper层扩展

**新增方法：**
```java
public interface RoleMapper {
    // 原有方法
    List<Role> selectAll();
    
    // 新增方法
    List<Role> selectByPage(RoleQueryDTO queryDTO);
    Long countByCondition(RoleQueryDTO queryDTO);
    Role selectById(@Param("id") Long id);
    Role selectByCode(@Param("roleCode") String roleCode);
}
```

### 5. XML映射文件扩展

**新增SQL：**
```xml
<!-- 查询条件 -->
<sql id="Query_Condition">
    <where>
        1=1
        <if test="roleName != null and roleName != ''">
            AND role_name LIKE CONCAT('%', #{roleName}, '%')
        </if>
        <if test="roleCode != null and roleCode != ''">
            AND role_code LIKE CONCAT('%', #{roleCode}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (role_name LIKE CONCAT('%', #{keyword}, '%') 
                 OR role_code LIKE CONCAT('%', #{keyword}, '%')
                 OR role_desc LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </where>
</sql>

<!-- 分页查询 -->
<select id="selectByPage" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM role
    <include refid="Query_Condition"/>
    <include refid="Order_By"/>
    LIMIT #{offset}, #{size}
</select>

<!-- 统计数量 -->
<select id="countByCondition" resultType="java.lang.Long">
    SELECT COUNT(*) FROM role
    <include refid="Query_Condition"/>
</select>
```

## API使用示例

### 1. 分页查询角色

**请求：**
```
GET /api/roles?page=1&size=10&status=1&sortField=createTime&sortDirection=desc
```

**响应：**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "roleName": "管理员",
        "roleCode": "ADMIN",
        "roleDesc": "系统管理员",
        "status": 1,
        "createTime": "2025-07-22 10:00:00"
      }
    ],
    "total": 50,
    "page": 1,
    "size": 10,
    "totalPages": 5,
    "hasNext": true,
    "hasPrevious": false,
    "isFirst": true,
    "isLast": false
  },
  "timestamp": "2025-07-22 10:00:00"
}
```

### 2. 搜索角色

**请求：**
```
GET /api/roles/search?keyword=管理&page=1&size=10
```

### 3. 按条件查询

**请求：**
```
GET /api/roles?roleName=管理员&status=1&page=1&size=10
```

### 4. 获取所有角色（不分页）

**请求：**
```
GET /api/roles/all
```

## 功能特性

### 1. 分页功能
- 支持自定义页码和每页大小
- 返回完整的分页信息
- 自动计算总页数、是否有上下页等

### 2. 搜索功能
- 支持按角色名称模糊搜索
- 支持按角色编码模糊搜索
- 支持关键字全文搜索
- 支持按状态精确筛选

### 3. 排序功能
- 支持多字段排序
- 支持升序/降序
- 默认按创建时间降序

### 4. 参数校验
- 页码必须大于0
- 每页大小限制在1-100之间
- 关键字长度限制
- 使用Bean Validation注解

### 5. 异常处理
- 参数校验异常统一处理
- 业务异常统一处理
- 返回友好的错误信息

## 性能优化建议

### 1. 数据库索引
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_role_name ON role(role_name);
CREATE INDEX idx_role_code ON role(role_code);
CREATE INDEX idx_role_status ON role(status);
CREATE INDEX idx_role_create_time ON role(create_time);
```

### 2. 查询优化
- 避免使用 `SELECT *`
- 合理使用 LIMIT
- 优化 WHERE 条件顺序

### 3. 缓存策略
- 对不经常变化的角色列表可以考虑缓存
- 使用 Redis 缓存热点数据

## 扩展建议

### 1. 导出功能
- 支持导出查询结果为Excel
- 支持导出所有数据或当前页数据

### 2. 批量操作
- 批量启用/禁用角色
- 批量删除角色

### 3. 高级搜索
- 支持多条件组合搜索
- 支持日期范围搜索
- 支持自定义搜索条件

这样改造后，角色管理功能就具备了完整的分页、搜索、排序能力，能够满足实际项目的需求。
