-- Spring Boot + Shiro权限管理系统数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS shiro_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE shiro_demo;

-- 1. 用户表
CREATE TABLE sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    salt VARCHAR(50) NOT NULL COMMENT '密码盐值',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(200) COMMENT '头像URL',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 角色表
CREATE TABLE sys_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 3. 权限表
CREATE TABLE sys_permission (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_type VARCHAR(20) DEFAULT 'MENU' COMMENT '权限类型：MENU-菜单，BUTTON-按钮',
    url VARCHAR(200) COMMENT '权限URL',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    icon VARCHAR(50) COMMENT '图标',
    description VARCHAR(200) COMMENT '权限描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 4. 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 5. 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES sys_permission(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 插入初始数据

-- 插入默认用户（密码都是123456，使用MD5+盐值加密）
INSERT INTO sys_user (username, password, salt, email, real_name, status) VALUES
('admin', 'e10adc3949ba59abbe56e057f20f883e', 'admin_salt', '<EMAIL>', '系统管理员', 1),
('manager', 'e10adc3949ba59abbe56e057f20f883e', 'manager_salt', '<EMAIL>', '部门经理', 1),
('user', 'e10adc3949ba59abbe56e057f20f883e', 'user_salt', '<EMAIL>', '普通用户', 1);

-- 插入默认角色
INSERT INTO sys_role (role_name, role_code, description) VALUES
('超级管理员', 'admin', '系统超级管理员，拥有所有权限'),
('部门经理', 'manager', '部门经理，拥有部门管理权限'),
('普通用户', 'user', '普通用户，拥有基本权限');

-- 插入默认权限
INSERT INTO sys_permission (permission_name, permission_code, permission_type, url, parent_id, sort_order, description) VALUES
-- 系统管理
('系统管理', 'system', 'MENU', '/system', 0, 1, '系统管理菜单'),
('用户管理', 'system:user', 'MENU', '/system/user', 1, 1, '用户管理菜单'),
('角色管理', 'system:role', 'MENU', '/system/role', 1, 2, '角色管理菜单'),
('权限管理', 'system:permission', 'MENU', '/system/permission', 1, 3, '权限管理菜单'),

-- 用户管理权限
('用户查询', 'system:user:view', 'BUTTON', '', 2, 1, '查询用户列表'),
('用户新增', 'system:user:add', 'BUTTON', '', 2, 2, '新增用户'),
('用户修改', 'system:user:edit', 'BUTTON', '', 2, 3, '修改用户'),
('用户删除', 'system:user:delete', 'BUTTON', '', 2, 4, '删除用户'),

-- 角色管理权限
('角色查询', 'system:role:view', 'BUTTON', '', 3, 1, '查询角色列表'),
('角色新增', 'system:role:add', 'BUTTON', '', 3, 2, '新增角色'),
('角色修改', 'system:role:edit', 'BUTTON', '', 3, 3, '修改角色'),
('角色删除', 'system:role:delete', 'BUTTON', '', 3, 4, '删除角色'),

-- 权限管理权限
('权限查询', 'system:permission:view', 'BUTTON', '', 4, 1, '查询权限列表'),
('权限新增', 'system:permission:add', 'BUTTON', '', 4, 2, '新增权限'),
('权限修改', 'system:permission:edit', 'BUTTON', '', 4, 3, '修改权限'),
('权限删除', 'system:permission:delete', 'BUTTON', '', 4, 4, '删除权限'),

-- 个人中心
('个人中心', 'profile', 'MENU', '/profile', 0, 2, '个人中心菜单'),
('个人信息', 'profile:info', 'BUTTON', '', 17, 1, '查看个人信息'),
('修改密码', 'profile:password', 'BUTTON', '', 17, 2, '修改密码');

-- 分配用户角色
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1), -- admin -> admin
(2, 2), -- manager -> manager  
(3, 3); -- user -> user

-- 分配角色权限
-- 超级管理员拥有所有权限
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 1, id FROM sys_permission;

-- 部门经理拥有用户管理和个人中心权限
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(2, 2), (2, 5), (2, 6), (2, 7), (2, 8), -- 用户管理相关
(2, 17), (2, 18), (2, 19); -- 个人中心相关

-- 普通用户只有个人中心权限
INSERT INTO sys_role_permission (role_id, permission_id) VALUES
(3, 17), (3, 18), (3, 19); -- 个人中心相关

-- 创建索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_email ON sys_user(email);
CREATE INDEX idx_user_status ON sys_user(status);
CREATE INDEX idx_role_code ON sys_role(role_code);
CREATE INDEX idx_permission_code ON sys_permission(permission_code);
CREATE INDEX idx_permission_parent ON sys_permission(parent_id);
CREATE INDEX idx_user_role_user ON sys_user_role(user_id);
CREATE INDEX idx_user_role_role ON sys_user_role(role_id);
CREATE INDEX idx_role_permission_role ON sys_role_permission(role_id);
CREATE INDEX idx_role_permission_permission ON sys_role_permission(permission_id);
